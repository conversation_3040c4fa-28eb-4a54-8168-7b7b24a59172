// 定义关于counter的store
import { defineStore } from 'pinia'
import userApi from '@/api/ccp/user.ts'

/* defineStore 是需要传参数的，其中第一个参数是id，就是一个唯一的值，
简单点说就可以理解成是一个命名空间.
第二个参数就是一个对象，里面有三个模块需要处理，第一个是 state，
第二个是 getters，第三个是 actions。
*/
const userModule = defineStore('ccp-user', {
  state: () => ({
    count: 66,
    // 会员用户列表
    userList: [],
    qps_qb_stat: {}, // 汽配商统计
    qpsSeeDetail: null, // 查看的汽配商详情信息
    letter: [...Array(26)].map((_, i) => String.fromCharCode(i + 65)),
    shopcircle: null, // 商圈详情
  }),
  getters: {},
  actions: {
    async incrementCounter(e: any) {
      console.log('incrementCounter', 123456)
      // 这里可以是你的公共逻辑
      return e
    },
    get_qps_info(id: any) {
      return new Promise((resolve: any) => {
        userApi.qps_info({ id }).then((res: any) => {
          this.$state.qpsSeeDetail = res
          resolve(res)
        })
      })
    },
  },
})

// 暴露这个useCounter模块
export default userModule

// 使用方法
// import userModule from '@/store/ccp/user.ts'
// const userStore = userModule()
// const userInfo : any = computed(() => userStore.qpsSeeDetail);
