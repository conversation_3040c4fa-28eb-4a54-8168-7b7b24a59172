import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/organizationManagement',
  component: Layout,
  // redirect: '/one/1',
  name: 'organizationManagement',
  meta: {
    title: '合规组织管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/organizationManagement/1',
      name: 'organizationManagement/1',
      component: () => import('@/views/hegui/prevention/organizationManagement/g_view.vue'),
      meta: {
        title: '组织结构视图',
      },
      // redirect: '/one/1',
    },
    {
      path: '/organizationManagement/commissioner',
      name: 'organizationManagement/commissioner',
      component: () => import('@/views/hegui/prevention/organizationManagement/commissioner/index.vue'),
      meta: {
        title: '合规专员管理',
      },
      children: [
        {
          path: '/organizationManagement/commissioner/addEdit',
          name: '/organizationManagement/commissioner/addEdit',
          component: () => import('@/views/hegui/prevention/organizationManagement/commissioner/addEdit.vue'),
          meta: {
            title: '合规委员会详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        // {
        //   path: '/organizationManagement/committee/addEdit',
        //   name: '/organizationManagement/committee/addEdit',
        //   component: () => import('@/views/hegui/prevention/organizationManagement/committee/addEdit.vue'),
        //   meta: {
        //     title: '合规委员会/add/edit',
        //     sidebar: false,
        //     breadcrumb: false,
        //   },
        // },
      ],
    },
    {
      path: '/organizationManagement/committee',
      name: 'organizationManagement/committee',
      component: () => import('@/views/hegui/prevention/organizationManagement/committee/index.vue'),
      meta: {
        title: '合规委员会',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/organizationManagement/committee/detail',
          name: '/organizationManagement/committee/detail',
          component: () => import('@/views/hegui/prevention/organizationManagement/committee/detail.vue'),
          meta: {
            title: '合规委员会详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/organizationManagement/committee/addEdit',
          name: '/organizationManagement/committee/addEdit',
          component: () => import('@/views/hegui/prevention/organizationManagement/committee/addEdit.vue'),
          meta: {
            title: '合规委员会/add/edit',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/organizationManagement/contactPerson',
      name: 'organizationManagement/contactPerson',
      component: () => import('@/views/hegui/prevention/organizationManagement/contactPerson/index.vue'),
      meta: {
        title: '合规联络人',
      },
      children: [
        // {
        //   path: '/organizationManagement/rightsAndResponsibilities/addEdit',
        //   name: '/organizationManagement/rightsAndResponsibilities/addEdit',
        //   component: () => import('@/views/hegui/prevention/organizationManagement/rightsAndResponsibilities/addEdit.vue'),
        //   meta: {
        //     title: '权责分配矩阵add/edit',
        //     sidebar: false,
        //     breadcrumb: false,
        //   },
        // },
        {
          path: '/organizationManagement/contactPerson/detail',
          name: '/organizationManagement/contactPerson/detail',
          component: () => import('@/views/hegui/prevention/organizationManagement/contactPerson/detail.vue'),
          meta: {
            title: '权责分配矩阵详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
      // redirect: '/one/1',
    },
    {
      path: '/organizationManagement/rightsAndResponsibilities',
      name: 'organizationManagement/rightsAndResponsibilities',
      component: () => import('@/views/hegui/prevention/organizationManagement/rightsAndResponsibilities/index.vue'),
      meta: {
        title: '权责分配矩阵',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/organizationManagement/rightsAndResponsibilities/addEdit',
          name: '/organizationManagement/rightsAndResponsibilities/addEdit',
          component: () => import('@/views/hegui/prevention/organizationManagement/rightsAndResponsibilities/addEdit.vue'),
          meta: {
            title: '权责分配矩阵add/edit',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/organizationManagement/rightsAndResponsibilities/detail',
          name: '/organizationManagement/rightsAndResponsibilities/detail',
          component: () => import('@/views/hegui/prevention/organizationManagement/rightsAndResponsibilities/detail.vue'),
          meta: {
            title: '权责分配矩阵详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    // D:/xm/whiskerguard-ui-system-admin/src/views/hegui/prevention/organizationManagement/rightsAndResponsibilities/index.vue
  ],
}

export default routes
