<script setup lang="ts">
import type { UploadProps } from 'element-plus'
import { ElMessage } from 'element-plus'

import axios from 'axios'
import { getCurrentInstance, ref } from 'vue'
import useUserStore from '@/store/modules/user'

defineOptions({
  name: 'ImagesUpload',
})
const props = withDefaults(
  defineProps<{
    action: UploadProps['action']
    // headers?: UploadProps['headers']
    headers?: any
    data?: UploadProps['data']
    name?: UploadProps['name']
    url?: string[]
    size?: number
    max?: number
    width?: number
    height?: number
    placeholder?: string
    notip?: boolean
    ext?: string[]
    type?: string
    dirname?: string
    imgtype?: string
    code?: string
  }>(),
  {
    name: 'file',
    url: () => [],
    size: 10,
    max: 3,
    width: 150,
    height: 150,
    type: '1',
    placeholder: '',
    notip: false,
    ext: () => ['jpg', 'png', 'gif', 'bmp'],
    action: `${import.meta.env.VITE_APP_API_BASEURL}Upload/QiniuUpload`,
    headers: {
      Authorization: `Bearer ${useUserStore().token}`,
    },
    dirname: 'picture', // 上传类型
    imgtype: 'xx200',
    code: '', // 上传目录code
  },
)
// 主目录：dirname-字段
// adv（广告位模块）；
// user（用户模块）：/user/merchant/（汽配商户），/user/member/（用户），/user/car/（汽修厂）
// find（发现模块）；/find/trends/（动态），/find/memorabilia/（大事记），/find/business/（汽配商圈）
// set（系统）；/set/vehiclemodel/（品牌），/set/carseries/（车系），/set/category/（类目），/set/agreement/（协议）

const emits = defineEmits<{
  'update:url': [
			url: string[],
  ]
  'onSuccess': [
			res: any,
  ]
}>()
const percent: any = ref(false)
const percentnum: any = ref(0)
const loadicon = new URL('@/assets/images/loadicon.gif', import.meta.url).href
const fullscreenLoading = ref(false)
const { pictureEcho, pictureType } = getCurrentInstance()!.appContext.config.globalProperties.$tools() // 引入自定义挂载公共方法
const uploadData = ref({
  dialogImageIndex: 0,
  imageViewerVisible: false,
  progress: {
    preview: '',
    percent: 0,
  },
})

// 预览
function preview(index: number) {
  uploadData.value.dialogImageIndex = index
  uploadData.value.imageViewerVisible = true
}
// 关闭预览
function previewClose() {
  uploadData.value.imageViewerVisible = false
}
// 移除
function remove(index: number) {
  const url = props.url
  url.splice(index, 1)
  emits('update:url', url)
}
// 移动
function move(index: number, type: 'left' | 'right') {
  const url = props.url
  if (type === 'left' && index !== 0) {
    url[index] = url.splice(index - 1, 1, url[index])[0]
  }
  if (type === 'right' && index !== url.length - 1) {
    url[index] = url.splice(index + 1, 1, url[index])[0]
  }
  emits('update:url', url)
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const fileName = file.name.split('.')
  const fileExt = fileName.at(-1) ?? ''
  const isTypeOk = props.ext.includes(fileExt)
  const isSizeOk = file.size / 1024 / 1024 < props.size

  if (!isTypeOk) {
    ElMessage.error(`上传图片只支持 ${props.ext.join(' / ')} 格式！`)
  }
  if (!isSizeOk) {
    ElMessage.error(`上传图片大小不能超过 ${props.size}MB！`)
  }
  if (isTypeOk && isSizeOk) {
    uploadData.value.progress.preview = URL.createObjectURL(file)
  }
  return isTypeOk && isSizeOk
}
const onProgress: UploadProps['onProgress'] = (file) => {
  uploadData.value.progress.percent = ~~file.percent
}
const onSuccess: UploadProps['onSuccess'] = (res) => {
  uploadData.value.progress.preview = ''
  uploadData.value.progress.percent = 0
  // emits('onSuccess', res)
}

// 自定义上传
async function httpRequest(file: any) {
  // if (pictureType(props.dirname) === false) {
  // 	ElMessage.error('未配置图片上传参数项')
  // 	return false
  // }
  const fd = new FormData()
  // fd.append('dirname', !props.code ? pictureType(props.dirname) : `${pictureType(props.dirname)}/${props.code}`)// 传参数

  fd.append('file', file.file)// 传文件
  // const loadingInstance = ElLoading.service({
  // 	lock: true,
  // 	text: 'Loading',
  // 	background: 'rgba(0, 0, 0, 0.7)',
  // })
  fullscreenLoading.value = true
  const res = await axios({
    method: 'POST',
    url: props.action,
    data: fd,
    headers: props.headers,
  })
  console.log('ImagesUpload', res.data.data)//
  if (res) {
    fullscreenLoading.value = false
    emits('onSuccess', res.data.data)
  }
  else {
    fullscreenLoading.value = false
  }
}

function closeLoad() {
  fullscreenLoading.value = false
}
</script>

<template>
  <div class="upload-container">
    <div v-for="(item, index) in (url as string[])" :key="index" class="images">
      <el-image
        v-if="index < max && item " :src="pictureEcho(item)"
        :style="`width:${width}px;height:${height}px;`" fit="cover"
      />
      <div class="mask">
        <div class="actions">
          <span title="预览" @click="preview(index)">
            <svg-icon name="ep:zoom-in" />
          </span>
          <span title="移除" @click="remove(index)">
            <svg-icon name="ep:delete" />
          </span>
          <span
            v-show="url.length > 1" title="左移" :class="{ disabled: index === 0 }"
            @click="move(index, 'left')"
          >
            <svg-icon name="ep:back" />
          </span>
          <span
            v-show="url.length > 1" title="右移" :class="{ disabled: index === url.length - 1 }"
            @click="move(index, 'right')"
          >
            <svg-icon name="ep:right" />
          </span>
        </div>
      </div>
    </div>
    <el-upload
      v-show="url.length < max" :show-file-list="false" :headers="headers" :action="action" :data="data"
      :name="name" :before-upload="beforeUpload" :on-progress="onProgress" :http-request="httpRequest"
      :on-success="onSuccess" drag class="images-upload"
    >
      <div class="image-slot" :style="`width:${width}px;height:${height}px;`">
        <svg-icon name="ep:plus" />
      </div>
      <div v-show="uploadData.progress.percent" class="progress" :style="`width:${width}px;height:${height}px;`">
        <el-image
          :src="uploadData.progress.preview" :style="`width:${width}px;height:${height}px;`"
          fit="fill"
        />
        <el-progress
          type="circle" :width="Math.min(width, height) * 0.8"
          :percentage="uploadData.progress.percent"
        />
      </div>
    </el-upload>
    <div v-if="notip" class="el-upload__tip">
      <div style="display: inline-block;">
        <el-alert
          :title="`图片大小不超过 ${size}MB，建议图片尺寸为 ${width}:${height}，且图片数量不超过 ${max} 张`" type="info"
          show-icon :closable="false"
        />
      </div>
    </div>
    <el-image-viewer
      v-if="uploadData.imageViewerVisible" :url-list="pictureEcho(url)"
      :initial-index="uploadData.dialogImageIndex" teleported @close="previewClose"
    />
    <!-- 上传时的加载遮罩层 -->
    <!-- <div v-if="fullscreenLoading" class="loading-mask">
			<div class="flex justify-center items-center d-c ">
				<div class=" c-[#409eff]">
					<img class="w-5 h-5" :src="loadicon" alt>
				</div>
				<div class="my-4 c-[#409eff]">上传中...</div>
				<div style="position: fixed; top:60px;right:60px; z-index:1000000;  border-radius: 50%; background-color: rgba(255, 255, 255, 0.8);"
					class="w-16 h-16 flex justify-center items-center font-size-10 cursor-pointer c-[#333]"
					@click="closeLoad">
					<svg-icon name="ep:close" />
				</div>
			</div>
		</div> -->
    <div
      v-if="percent" class="demo-progress fixed left-50% top-50% w-130 bg-[#fff]"
      style=" z-index: 99999; border-radius: 8px; box-shadow: 0 0 8px 0 #999;transform: translate(-50%, -50%);"
    >
      <!-- <div class="w-full text-end pr-4 pt-4 font-size-6 cursor-pointer" @click="closepercent">
				<svg-icon name="ep:close" />
			</div> -->
      <div class="d-c h-16 h-40 w-full flex items-center justify-center py-2" style="">
        <el-progress
          :width="500" striped striped-flow :text-inside="true" :stroke-width="24"
          :percentage="percentnum" status="success"
        />
        <div>
          <div v-if="percentnum > 0" class="my-4 c-[#67c23a]">
            上传中...
          </div>
          <div v-else class="my-4 c-[#e67c28]">
            正在解析文件流...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .upload-container {
    line-height: initial;
  }

  .loading-mask {
    position: fixed;
    inset: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(0 0 0 / 70%);

    /* 确保在el-loading之上 */
  }

  .el-loading {
    position: relative;
  }

  .demo-progress .el-progress--line {
    width: 450px;
    height: 30px;
    margin-bottom: 15px;
  }

  .el-loading .el-button {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .el-image {
    display: block;
  }

  .images {
    position: relative;
    display: inline-block;
    margin-right: 10px;
    overflow: hidden;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;

    .mask {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: var(--el-overlay-color-lighter);
      opacity: 0;
      transition: opacity 0.3s;

      .actions {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 100px;

        @include position-center(xy);

        span {
          width: 50%;
          color: var(--el-color-white);
          text-align: center;
          cursor: pointer;
          transition: color 0.1s, transform 0.1s;

          &.disabled {
            color: var(--el-text-color-disabled);
            cursor: not-allowed;
          }

          &:hover:not(.disabled) {
            transform: scale(1.5);
          }

          .icon {
            font-size: 24px;
          }
        }
      }
    }

    &:hover .mask {
      opacity: 1;
    }
  }

  .images-upload {
    display: inline-block;
    vertical-align: top;
  }

  :deep(.el-upload) {
    .el-upload-dragger {
      display: inline-block;
      padding: 0;

      &.is-dragover {
        border-width: 1px;
      }

      .image-slot {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: var(--el-text-color-placeholder);
        background-color: transparent;

        .icon {
          font-size: 30px;
        }
      }

      .progress {
        position: absolute;
        top: 0;

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          content: "";
          background-color: var(--el-overlay-color-lighter);
        }

        .el-progress {
          z-index: 1;

          @include position-center(xy);

          .el-progress__text {
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }
  }
</style>
