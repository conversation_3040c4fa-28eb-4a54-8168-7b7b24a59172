import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/operationLog',
  component: Layout,
  name: '/systemSettings/operationLog',
  meta: {
    title: '操作日志',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/operationLog/logQuery',
      name: '/systemSettings/operationLog/logQuery',
      component: () => import('@/views/hegui/systemSettings/operationLog/logQuery/index.vue'),
      meta: {
        title: '日志查询',
      },
    },
  ],
}

export default routes
