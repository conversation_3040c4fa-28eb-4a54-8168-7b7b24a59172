import api from '@/api/index'

export default {
  // 用户管理
  userApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/employees/${params.id}`, {
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/employees`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/employees/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/employees/${params.id}`)
      default:
        return api.post(`/whiskerguardorgservice/api/employees/page`, params)
    }
  },
  // 租户详情
  tenantInfo(tenantId: any) {
    return api.get(`/whiskerguardorgservice/api/tenants/${tenantId}`)
  },
  // 更新租户详情
  updateTenantInfo(id: number, data: any) {
    return api.patch(`/whiskerguardorgservice/api/tenants/${id}`, data)
  },
  // 下载企业员工信息模板
  downloadEmployeeTemplate() {
    return api.get('/whiskerguardorgservice/api/employees/template', {
      responseType: 'blob',
    })
  },
  // 导入员工
  importEmployees(formData: FormData) {
    return api.post('/whiskerguardorgservice/api/employees/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
