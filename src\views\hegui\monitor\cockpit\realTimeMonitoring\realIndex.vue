<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Operation,
  Download,
  Refresh,
  Upload,
  Top,
  Bottom,
  ArrowRight,
  // LightBulb,
  TrendCharts,
} from '@element-plus/icons-vue'

const searchText = ref('')
const timeRange = ref('month')
const department = ref('all')
const riskType = ref('all')
const timeDimension = ref('month')
const showRiskIndex = ref(true)
const showAlertCount = ref(true)
const showEventCount = ref(false)

const alerts = ref([
  {
    title: '财务部门异常交易',
    detail: '涉及金额: ¥1,250,000',
    department: '财务部',
    time: '10分钟前',
    color: 'red',
  },
  {
    title: '合同合规性审查逾期',
    detail: '合同编号: HT-2023-0856',
    department: '法务部',
    time: '1小时前',
    color: 'orange',
  },
  {
    title: '员工合规培训未完成',
    detail: '5名员工未完成',
    department: '人力资源部',
    time: '3小时前',
    color: 'yellow',
  },
  {
    title: '供应商资质过期',
    detail: '供应商: 上海宏远科技',
    department: '采购部',
    time: '5小时前',
    color: 'red',
  },
])

const tasks = ref([
  {
    title: '审批财务异常交易报告',
    deadline: '今天 18:00',
    color: 'red',
  },
  {
    title: '处理供应商资质问题',
    deadline: '明天 12:00',
    color: 'orange',
  },
  {
    title: '审核合规培训计划',
    deadline: '后天 15:00',
    color: 'blue',
  },
  {
    title: '更新合规政策文档',
    deadline: '本周五',
    color: 'gray',
  },
])

const miniChart1 = ref<HTMLElement>()
const miniChart2 = ref<HTMLElement>()
const miniChart3 = ref<HTMLElement>()
const miniChart4 = ref<HTMLElement>()
const trendChart = ref<HTMLElement>()

// 初始化迷你图表
function initMiniCharts() {
  const option1 = {
    animation: false,
    xAxis: { show: false },
    yAxis: { show: false },
    grid: { top: 0, right: 0, bottom: 0, left: 0 },
    series: [{
      type: 'line',
      data: [5, 10, 8, 12, 15, 20, 18],
      symbol: 'none',
      lineStyle: { color: '#4CAF50', width: 2 },
      areaStyle: { color: 'rgba(76, 175, 80, 0.1)' },
    }],
  }

  const option2 = {
    animation: false,
    xAxis: { show: false },
    yAxis: { show: false },
    grid: { top: 0, right: 0, bottom: 0, left: 0 },
    series: [{
      type: 'line',
      data: [20, 18, 15, 12, 10, 8, 5],
      symbol: 'none',
      lineStyle: { color: '#F44336', width: 2 },
      areaStyle: { color: 'rgba(244, 67, 54, 0.1)' },
    }],
  }

  const option3 = {
    animation: false,
    xAxis: { show: false },
    yAxis: { show: false },
    grid: { top: 0, right: 0, bottom: 0, left: 0 },
    series: [{
      type: 'line',
      data: [80, 82, 85, 88, 90, 91, 92],
      symbol: 'none',
      lineStyle: { color: '#4CAF50', width: 2 },
      areaStyle: { color: 'rgba(76, 175, 80, 0.1)' },
    }],
  }

  const option4 = {
    animation: false,
    xAxis: { show: false },
    yAxis: { show: false },
    grid: { top: 0, right: 0, bottom: 0, left: 0 },
    series: [{
      type: 'line',
      data: [70, 72, 75, 78, 80, 82, 85],
      symbol: 'none',
      lineStyle: { color: '#4CAF50', width: 2 },
      areaStyle: { color: 'rgba(76, 175, 80, 0.1)' },
    }],
  }

  if (miniChart1.value) {
    const chart1 = echarts.init(miniChart1.value)
    chart1.setOption(option1)
  }

  if (miniChart2.value) {
    const chart2 = echarts.init(miniChart2.value)
    chart2.setOption(option2)
  }

  if (miniChart3.value) {
    const chart3 = echarts.init(miniChart3.value)
    chart3.setOption(option3)
  }

  if (miniChart4.value) {
    const chart4 = echarts.init(miniChart4.value)
    chart4.setOption(option4)
  }
}

// 初始化趋势图表
function initTrendChart() {
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
    },
    legend: {
      data: ['风险指数', '预警数量'],
      right: 10,
      top: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
      axisLine: { lineStyle: { color: '#E0E0E0' } },
      axisLabel: { color: '#666' },
    },
    yAxis: [
      {
        type: 'value',
        name: '风险指数',
        min: 0,
        max: 100,
        axisLine: { show: true, lineStyle: { color: '#E0E0E0' } },
        axisLabel: { color: '#666' },
        splitLine: { lineStyle: { color: '#F5F5F5' } },
      },
      {
        type: 'value',
        name: '预警数量',
        min: 0,
        max: 30,
        axisLine: { show: true, lineStyle: { color: '#E0E0E0' } },
        axisLabel: { color: '#666' },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: '风险指数',
        type: 'line',
        smooth: true,
        data: [65, 70, 75, 78, 80, 82, 85],
        itemStyle: { color: '#1E88E5' },
        lineStyle: { width: 3 },
        symbolSize: 6,
      },
      {
        name: '预警数量',
        type: 'bar',
        yAxisIndex: 1,
        data: [15, 12, 10, 14, 16, 12, 10],
        itemStyle: { color: '#FF9800' },
        barWidth: '40%',
      },
    ],
  }

  if (trendChart.value) {
    const chart = echarts.init(trendChart.value)
    chart.setOption(option)
  }
}

onMounted(() => {
  nextTick(() => {
    initMiniCharts()
    initTrendChart()
    initChart33()
    window.addEventListener('resize', () => {
      if (miniChart1.value) { echarts.getInstanceByDom(miniChart1.value)?.resize() }
      if (miniChart2.value) { echarts.getInstanceByDom(miniChart2.value)?.resize() }
      if (miniChart3.value) { echarts.getInstanceByDom(miniChart3.value)?.resize() }
      if (miniChart4.value) { echarts.getInstanceByDom(miniChart4.value)?.resize() }
      if (trendChart.value) { echarts.getInstanceByDom(trendChart.value)?.resize() }
      if (chart33Ref.value) { echarts.getInstanceByDom(chart33Ref.value)?.resize() }
    })
  })
})
const chart33Ref = ref(null)
function initChart33() {
  const chart1 = echarts.init(chart33Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [10, 20, 30, 20, 40, 20, 30],
        type: 'line',
        smooth: true,
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
const router = useRouter()
function goDeatil(item: Object) {
  console.log(item)
  router.push({
    name: '/monitor/cockpit/1/detail',
    query: {},
  })
}
</script>

<template>
  <div>
    <div class="absolute-container">
      <div style="height: 100%;overflow-y: auto;">
        <page-header title="" content="">
          <template #content>
            <div class="aic jcsb flex">
              <div class="f-28">
                <span class="mr-10 c-[#000]">合规驾驶舱</span>
              </div>
              <div>
                <div class="flex space-x-3">
                  <el-button v-auth="'/monitor/cockpit/realTimeMonitoring/index/configuration'" type="primary" :icon="Operation" class="!rounded-button whitespace-nowrap">
                    个性化配置
                  </el-button>
                  <el-button v-auth="'/monitor/cockpit/realTimeMonitoring/index/save'" :icon="Download" class="!rounded-button whitespace-nowrap">
                    保存布局
                  </el-button>
                  <el-button v-auth="'/monitor/cockpit/realTimeMonitoring/index/reset'" :icon="Refresh" class="!rounded-button whitespace-nowrap">
                    重置默认
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </page-header>
        <PageMain style="background-color: transparent;">
          <el-card shadow="hover">
            <!--          <template #header>
            <div class="f-16 fw-600">基本信息</div>
          </template> -->
            <div class="mb-6">
              <div class="flex items-center rounded-lg bg-white p-4 space-x-6">
                <div class="flex items-center">
                  <span class="mr-2 text-sm text-gray-600">数据时间范围：</span>
                  <el-select v-model="timeRange" class="w-32">
                    <el-option label="本月" value="month" />
                    <el-option label="本季度" value="quarter" />
                    <el-option label="本年" value="year" />
                  </el-select>
                </div>
                <el-button type="text" :icon="Refresh" class="text-primary">
                  刷新数据
                </el-button>
                <el-button v-auth="'/monitor/cockpit/realTimeMonitoring/index/layout'" :icon="Upload" class="!rounded-button whitespace-nowrap">
                  导出报告
                </el-button>
              </div>
            </div>
          </el-card>
          <el-row :gutter="20" class="mt-20">
            <el-col :span="6">
              <el-card shadow="hover" class="card-shadow">
                <div class="flex items-start justify-between">
                  <div>
                    <h3 class="mb-1 text-sm text-gray-500">
                      合规风险指数
                    </h3>
                    <div class="flex items-end">
                      <span class="mr-2 text-2xl text-gray-800 font-bold">85</span>
                      <span class="flex items-center text-sm text-green-500">
                        <el-icon>
                          <Top />
                        </el-icon>
                        <span class="ml-1">5%</span>
                      </span>
                    </div>
                  </div>
                  <div ref="miniChart1" class="h-10 w-16" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="card-shadow">
                <div class="flex items-start justify-between">
                  <div>
                    <h3 class="mb-1 text-sm text-gray-500">
                      待处理预警
                    </h3>
                    <div class="flex items-end">
                      <span class="mr-2 text-2xl text-gray-800 font-bold">12</span>
                      <span class="flex items-center text-sm text-red-500">
                        <el-icon>
                          <Bottom />
                        </el-icon>
                        <span class="ml-1">15%</span>
                      </span>
                    </div>
                  </div>
                  <div ref="miniChart2" class="h-10 w-16" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="card-shadow">
                <div class="flex items-start justify-between">
                  <div>
                    <h3 class="mb-1 text-sm text-gray-500">
                      合规审查完成率
                    </h3>
                    <div class="flex items-end">
                      <span class="mr-2 text-2xl text-gray-800 font-bold">92%</span>
                      <span class="flex items-center text-sm text-green-500">
                        <el-icon>
                          <Top />
                        </el-icon>
                        <span class="ml-1">3%</span>
                      </span>
                    </div>
                  </div>
                  <div ref="miniChart3" class="h-10 w-16" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="card-shadow">
                <div class="flex items-start justify-between">
                  <div>
                    <h3 class="mb-1 text-sm text-gray-500">
                      合规培训覆盖率
                    </h3>
                    <div class="flex items-end">
                      <span class="mr-2 text-2xl text-gray-800 font-bold">85%</span>
                      <span class="flex items-center text-sm text-green-500">
                        <el-icon>
                          <Top />
                        </el-icon>
                        <span class="ml-1">7%</span>
                      </span>
                    </div>
                  </div>
                  <div ref="miniChart4" class="h-10 w-16" />
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mt-20">
            <el-col :span="16">
              <el-card shadow="hover" class="card-shadow">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h2 class="text-base text-gray-800 font-bold">
                      风险分布热力图
                    </h2>
                    <div class="flex space-x-2">
                      <el-button type="primary" size="small" class="!rounded-button">
                        矩阵视图
                      </el-button>
                      <el-button size="small" class="!rounded-button">
                        地图视图
                      </el-button>
                    </div>
                  </div>
                </template>
                <div class="mb-4 flex items-center space-x-4">
                  <div class="flex items-center">
                    <span class="mr-2 text-sm text-gray-600">业务部门：</span>
                    <el-select v-model="department" size="small" class="w-32">
                      <el-option label="全部" value="all" />
                      <el-option label="财务部" value="finance" />
                      <el-option label="法务部" value="legal" />
                    </el-select>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-2 text-sm text-gray-600">风险类型：</span>
                    <el-select v-model="riskType" size="small" class="w-32">
                      <el-option label="全部" value="all" />
                      <el-option label="财务风险" value="finance" />
                      <el-option label="法律风险" value="legal" />
                    </el-select>
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-2">
                  <div class="heatmap-cell cursor-pointer bg-green-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      5
                    </div>
                    <div class="text-xs text-gray-500">
                      低风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-yellow-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      12
                    </div>
                    <div class="text-xs text-gray-500">
                      中风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-orange-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      8
                    </div>
                    <div class="text-xs text-gray-500">
                      高风险
                    </div>
                  </div>

                  <div class="flex items-center justify-center text-xs text-gray-500">
                    中
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-yellow-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      7
                    </div>
                    <div class="text-xs text-gray-500">
                      中风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-orange-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      15
                    </div>
                    <div class="text-xs text-gray-500">
                      高风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-red-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      10
                    </div>
                    <div class="text-xs text-gray-500">
                      极高风险
                    </div>
                  </div>

                  <div class="flex items-center justify-center text-xs text-gray-500">
                    高
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-orange-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      3
                    </div>
                    <div class="text-xs text-gray-500">
                      高风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-red-100 p-2 text-center">
                    <div class="text-sm font-medium">
                      6
                    </div>
                    <div class="text-xs text-gray-500">
                      极高风险
                    </div>
                  </div>
                  <div class="heatmap-cell cursor-pointer bg-red-200 p-2 text-center">
                    <div class="text-sm font-medium">
                      9
                    </div>
                    <div class="text-xs text-gray-500">
                      紧急风险
                    </div>
                  </div>
                </div>
              </el-card>
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h2 class="text-base text-gray-800 font-bold">
                      合规趋势分析
                    </h2>
                    <div class="flex space-x-2">
                      <el-button type="primary" size="small" class="!rounded-button">
                        折线图
                      </el-button>
                      <el-button size="small" class="!rounded-button">
                        柱状图
                      </el-button>
                      <el-button size="small" class="!rounded-button">
                        面积图
                      </el-button>
                    </div>
                  </div>
                  <!-- <div ref="chart33Ref" style="width: 100%;height: 300px;" /> -->
                  <div class="mb-4 flex items-center space-x-4">
                    <div class="flex items-center">
                      <span class="mr-2 text-sm text-gray-600">时间维度：</span>
                      <el-select v-model="timeDimension" size="small" class="w-32">
                        <el-option label="月" value="month" />
                        <el-option label="季度" value="quarter" />
                        <el-option label="年" value="year" />
                      </el-select>
                    </div>
                    <div class="flex items-center space-x-3">
                      <el-checkbox v-model="showRiskIndex" label="风险指数" />
                      <el-checkbox v-model="showAlertCount" label="预警数量" />
                      <el-checkbox v-model="showEventCount" label="合规事件数" />
                    </div>
                  </div>
                  <div ref="trendChart" class="chart-container" />
                </template>
              </el-card>
            </el-col>
            <el-col :span="8">
              <!-- <el-card shadow="hover" class="card-shadow">
              <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template>
            </el-card> -->
              <el-card shadow="hover" class="card-shadow" @click="goDeatil()">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h2 class="text-base text-gray-800 font-bold">
                      实时预警
                    </h2>
                    <el-link type="primary" :underline="false">
                      查看全部预警
                    </el-link>
                  </div>
                </template>
                <div class="space-y-3">
                  <div v-for="(alert, index) in alerts" :key="index" class="warning-item cursor-pointer rounded p-2">
                    <div class="flex items-start">
                      <div :class="`w-2 h-2 bg-${alert.color}-500 rounded-full mt-1.5 mr-2`" />
                      <div>
                        <div class="text-sm font-medium">
                          {{ alert.title }}
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                          {{ alert.detail }}
                        </div>
                        <div class="mt-1 flex items-center text-xs text-gray-500">
                          <span>{{ alert.department }}</span>
                          <span class="mx-1">·</span>
                          <span>{{ alert.time }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
              <!-- 待办任务区 -->
              <el-card shadow="hover" class="mt-20">
                <template #header>
                  <div class="flex items-center justify-between">
                    <h2 class="text-base text-gray-800 font-bold">
                      待办事项
                    </h2>
                    <el-link type="primary" :underline="false">
                      查看全部
                    </el-link>
                  </div>
                </template>
                <div class="space-y-3">
                  <div v-for="(task, index) in tasks" :key="index" class="task-item cursor-pointer rounded p-2">
                    <div class="flex items-center">
                      <div :class="`w-2 h-2 bg-${task.color}-500 rounded-full mr-2`" />
                      <div class="flex-1">
                        <div class="text-sm font-medium">
                          {{ task.title }}
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                          截止: {{ task.deadline }}
                        </div>
                      </div>
                      <el-icon>
                        <ArrowRight />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <!-- AI洞察区 -->
          <el-card shadow="hover" class="mt-20">
            <template #header>
              <div class="flex items-center justify-between">
                <h2 class="text-base text-gray-800 font-bold">
                  AI洞察
                </h2>
                <el-link type="primary" :underline="false">
                  查看详细分析
                </el-link>
              </div>
            </template>
            <div class="rounded bg-blue-50 p-3">
              <div class="mb-2 text-sm text-gray-800">
                <el-icon class="mr-2 text-yellow-500">
                  <!-- <LightBulb /> -->
                </el-icon>
                <span class="font-medium">风险趋势预测</span>
              </div>
              <p class="text-xs text-gray-600">
                根据历史数据分析，财务部门在未来两周内出现异常交易的风险概率上升12%，建议加强监控和审计。
              </p>
            </div>
            <div class="mt-3 rounded bg-blue-50 p-3">
              <div class="mb-2 text-sm text-gray-800">
                <el-icon class="mr-2 text-green-500">
                  <TrendCharts />
                </el-icon>
                <span class="font-medium">合规效率提升</span>
              </div>
              <p class="text-xs text-gray-600">
                法务部合同审查流程平均耗时较行业标准长1.5天，优化流程可提升整体合规效率约18%。
              </p>
            </div>
          </el-card>
        </PageMain>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .font-logo {
    font-family: Pacifico, cursive;
  }

  .sidebar-item:hover {
    background-color: rgb(255 255 255 / 10%);
  }

  .card-shadow {
    box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  }

  .chart-container {
    width: 100%;
    height: 300px;
  }

  .heatmap-cell {
    transition: all 0.3s;
  }

  .heatmap-cell:hover {
    transform: scale(1.05);
  }

  .warning-item:hover {
    background-color: #f8f9fa;
  }

  .task-item:hover {
    background-color: #f8f9fa;
  }
</style>
