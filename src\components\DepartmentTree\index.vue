<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ElIcon, ElTag, ElTree } from 'element-plus'
import { Loading, OfficeBuilding, User } from '@element-plus/icons-vue'
import type { FilterNodeMethodFunction } from 'element-plus/es/components/tree/src/tree.type'
import storage from '@/utils/storage'

interface TreeNode {
  id: number
  name: string
  code: string
  type: 'COMPANY' | 'SUBSIDIARY' | 'DEPARTMENT'
  level: number
  status: number
  sortOrder: number
  description: string
  parentId: number | null
  children: TreeNode[]
}

interface Props {
  // 是否显示复选框
  showCheckbox?: boolean
  // 是否默认展开所有节点
  defaultExpandAll?: boolean
  // 是否在点击节点的时候展开或者收缩节点
  expandOnClickNode?: boolean
  // 是否在点击节点的时候选中节点
  checkOnClickNode?: boolean
  // 是否高亮当前选中节点
  highlightCurrent?: boolean
  // 节点的唯一标识
  nodeKey?: string
  // 默认展开的节点的 key 的数组
  defaultExpandedKeys?: Array<string | number>
  // 默认勾选的节点的 key 的数组
  defaultCheckedKeys?: Array<string | number>
  // 是否显示节点编码
  showCode?: boolean
  // 是否显示节点类型标签
  showType?: boolean
  // 过滤关键字
  filterText?: string
}

interface Emits {
  (e: 'nodeClick', data: TreeNode, node: any, component: any): void
  (e: 'checkChange', data: TreeNode, checked: boolean, indeterminate: boolean): void
  (e: 'currentChange', data: TreeNode, node: any): void
}

const props = withDefaults(defineProps<Props>(), {
  showCheckbox: false,
  defaultExpandAll: false,
  expandOnClickNode: false,
  checkOnClickNode: false,
  highlightCurrent: true,
  nodeKey: 'id',
  defaultExpandedKeys: () => [],
  defaultCheckedKeys: () => [],
  showCode: false,
  showType: true,
  filterText: '',
})

const emit = defineEmits<Emits>()

const treeRef = ref<InstanceType<typeof ElTree>>()
const loading = ref(false)
const treeData = ref<TreeNode[]>([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id',
}

// 获取组织树数据
function loadOrgTreeData() {
  try {
    loading.value = true
    const orgTreeStr = storage.local.get('orgTree')
    if (orgTreeStr) {
      const orgTreeData = JSON.parse(orgTreeStr)
      treeData.value = Array.isArray(orgTreeData) ? orgTreeData : []
    }
    else {
      treeData.value = []
    }
  }
  catch (error) {
    console.error('解析组织树数据失败:', error)
    treeData.value = []
  }
  finally {
    loading.value = false
  }
}

// 获取节点图标
function getNodeIcon(type: string) {
  switch (type) {
    case 'COMPANY':
      return OfficeBuilding
    case 'SUBSIDIARY':
      return OfficeBuilding
    case 'DEPARTMENT':
      return User
    default:
      return OfficeBuilding
  }
}

// 获取节点图标样式类
function getNodeIconClass(type: string) {
  switch (type) {
    case 'COMPANY':
      return 'company-icon'
    case 'SUBSIDIARY':
      return 'subsidiary-icon'
    case 'DEPARTMENT':
      return 'department-icon'
    default:
      return 'default-icon'
  }
}

// 获取类型标签类型
function getTypeTagType(type: string) {
  switch (type) {
    case 'COMPANY':
      return 'danger'
    case 'SUBSIDIARY':
      return 'warning'
    case 'DEPARTMENT':
      return 'success'
    default:
      return 'info'
  }
}

// 获取类型标签文本
function getTypeLabel(type: string) {
  switch (type) {
    case 'COMPANY':
      return '公司'
    case 'SUBSIDIARY':
      return '分公司'
    case 'DEPARTMENT':
      return '部门'
    default:
      return '未知'
  }
}

// 节点过滤方法
const filterNode: FilterNodeMethodFunction = (value: string, data: any) => {
  if (!value) {
    return true
  }
  const nodeData = data as TreeNode
  return nodeData.name.includes(value) || nodeData.code.includes(value)
}

// 节点点击事件
function handleNodeClick(data: TreeNode, node: any, component: any) {
  emit('nodeClick', data, node, component)
}

// 复选框状态改变事件
function handleCheckChange(data: TreeNode, checked: boolean, indeterminate: boolean) {
  emit('checkChange', data, checked, indeterminate)
}

// 当前选中节点改变事件
function handleCurrentChange(data: TreeNode, node: any) {
  emit('currentChange', data, node)
}

// 获取选中的节点
function getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
  return treeRef.value?.getCheckedNodes(leafOnly, includeHalfChecked) || []
}

// 获取选中的节点key
function getCheckedKeys(leafOnly = false) {
  return treeRef.value?.getCheckedKeys(leafOnly) || []
}

// 设置选中的节点
function setCheckedNodes(nodes: any[]) {
  treeRef.value?.setCheckedNodes(nodes)
}

// 设置选中的节点key
function setCheckedKeys(keys: Array<string | number>) {
  treeRef.value?.setCheckedKeys(keys)
}

// 获取当前选中的节点
function getCurrentNode() {
  return treeRef.value?.getCurrentNode()
}

// 获取当前选中的节点key
function getCurrentKey() {
  return treeRef.value?.getCurrentKey()
}

// 设置当前选中的节点
function setCurrentNode(node: any) {
  treeRef.value?.setCurrentNode(node)
}

// 设置当前选中的节点key
function setCurrentKey(key: string | number) {
  treeRef.value?.setCurrentKey(key)
}

// 过滤节点
function filter(value: string) {
  treeRef.value?.filter(value)
}

// 刷新数据
function refresh() {
  loadOrgTreeData()
}

// 监听过滤文本变化
watch(
  () => props.filterText,
  (val) => {
    filter(val)
  }
)

// 暴露方法供外部调用
defineExpose({
  getCheckedNodes,
  getCheckedKeys,
  setCheckedNodes,
  setCheckedKeys,
  getCurrentNode,
  getCurrentKey,
  setCurrentNode,
  setCurrentKey,
  filter,
  refresh,
  treeRef,
})

// 组件挂载时加载数据
onMounted(() => {
  loadOrgTreeData()
})
</script>

<template>
  <div class="department-tree">
    <div v-if="loading" class="loading-container">
      <ElIcon class="is-loading">
        <Loading />
      </ElIcon>
      <span>加载中...</span>
    </div>
    <ElTree
      v-else
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      :default-expand-all="defaultExpandAll"
      :expand-on-click-node="expandOnClickNode"
      :check-on-click-node="checkOnClickNode"
      :show-checkbox="showCheckbox"
      :node-key="nodeKey"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      :highlight-current="highlightCurrent"
      :filter-node-method="filterNode"
      @node-click="handleNodeClick"
      @check-change="handleCheckChange"
      @current-change="handleCurrentChange"
    >
      <template #default="{ data }">
        <div class="tree-node">
          <ElIcon class="node-icon" :class="getNodeIconClass(data.type)">
            <component :is="getNodeIcon(data.type)" />
          </ElIcon>
          <span class="node-label">{{ data.name }}</span>
          <span v-if="showCode" class="node-code">({{ data.code }})</span>
          <ElTag v-if="showType" :type="getTypeTagType(data.type)" size="small" class="node-type">
            {{ getTypeLabel(data.type) }}
          </ElTag>
        </div>
      </template>
    </ElTree>
  </div>
</template>

<style lang="scss" scoped>
.department-tree {
  width: 100%;
  height: 100%;

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--el-text-color-secondary);

    .el-icon {
      margin-right: 8px;
    }
  }

  .tree-node {
    display: flex;
    align-items: center;
    flex: 1;
    padding-right: 8px;
    gap: 8px;

    .node-icon {
      font-size: 16px;

      &.company-icon {
        color: var(--el-color-danger);
      }

      &.subsidiary-icon {
        color: var(--el-color-warning);
      }

      &.department-icon {
        color: var(--el-color-success);
      }
    }

    .node-label {
      flex: 1;
      font-weight: 500;
      font-size: 14px;
    }

    .node-code {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .node-type {
      margin-left: 8px;
    }
  }

  :deep(.el-tree-node__content) {
    height: 36px;

    &:hover {
      background-color: var(--el-tree-node-hover-bg-color);
    }
  }

  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
}
</style>