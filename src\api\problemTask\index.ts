import api from '@/api/index'

// 定义响应接口
interface TaskResponse {
  code: number
  data: any
  message?: string
}

// 定义查询参数接口
export interface ProblemInvestigateTaskReq {
  title?: string
  investigateCode?: string
  investigateType?: 'ADVERTISING_COMPLIANCE' | 'SUPPLIER_MANAGEMENT' | 'EMPLOYEE_TRAINING' | 'FINANCIAL_AUDITING'
  investigateSource?: 'INTERNAL_REPORT' | 'EXTERNAL_REPORT' | 'REGULATORY_REQUIREMENT' | 'INTERNAL_AUDIT' | 'RISK_IDENTIFICATION' | 'OTHER'
  level?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  startDate?: string
  finishDate?: string
  dutyEmployeeId?: number
  coordinateEmployeeId?: number
  status?: 'NO_START' | 'PROGRESSING' | 'FINISHED' | 'PAUSED' | 'CANCELED'
  violationDetailId?: number
}

// 定义调查记录接口
export interface ProblemInvestigateRecordDTO {
  id?: number
  investigateId: number
  recordCode: string
  location: string
  recordType: string
  content: string
  discover: string
  attachmentList?: ProblemInvestigateAttachmentDTO[]
  involveList?: {
    involve: number
    involveId: number
    remark: string
  }[]
  createdBy?: string
  createdAt?: {
    seconds: number
    nanos: number
  }
  updatedBy?: string
  updatedAt?: {
    seconds: number
    nanos: number
  }
  isDeleted?: boolean
}

// 定义调查报告附件接口
export interface ProblemInvestigateAttachmentDTO {
  id?: number
  relatedId: number
  relatedType: number
  fileName: string
  filePath: string
  fileType: string
  fileSize?: string
  fileDesc?: string
  metadata?: string
  version?: number
  createdBy: string
  createdAt?: {
    seconds: number
    nanos: number
  }
  updatedBy?: string
  updatedAt?: {
    seconds: number
    nanos: number
  }
  isDeleted?: boolean
}

// 定义调查报告接口
export interface ProblemInvestigateReportDTO {
  id?: number
  title?: string
  reportCode: string
  investigateId: number
  reportType?: 'SPECIAL_INVESTIGE' | 'REGULAR_INVESTIGE' | 'QUICK_INVESTIGE'
  investigateSource?: 'INTERNAL_REPORT' | 'EXTERNAL_REPORT' | 'REGULATORY_REQUIREMENT' | 'INTERNAL_AUDIT' | 'RISK_IDENTIFICATION' | 'OTHER'
  employeeId: number
  orgId: number
  establishDate?: string
  level?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  summary?: string
  status?: 'MODIFY' | 'PENDING' | 'PUBLISHED' | 'REVIEWING' | 'REVOKE'
  investigateBackground?: string
  investigateMethod?: string
  investigateProcess?: string
  investigateFound?: string
  investigateConclusion?: string
  recommendMeasure?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: {
    seconds: number
    nanos: number
  }
  updatedBy?: string
  updatedAt?: {
    seconds: number
    nanos: number
  }
  isDeleted?: boolean
  attachmentList?: ProblemInvestigateAttachmentDTO[]
}

// 调查任务相关接口
const problemTaskApi = {
  /**
   * 创建或更新调查任务
   * @param data 调查任务数据
   * @returns Promise
   */
  createOrUpdateTask(data: any): Promise<any> {
    return api.post(`/whiskerguardviolationservice/api/problem/investigate/tasks`,
      data,
    )
  },

  /**
   * 获取调查任务详情
   * @param id 调查任务ID
   * @returns Promise
   */
  getTaskDetail(id: string): Promise<TaskResponse> {
    return api.get(`/whiskerguardviolationservice/api/problem/investigate/tasks/${id}`)
  },
  // 调查任务更新
  updateTask(id: number, data: any): Promise<any> {
    return api.patch(`/whiskerguardviolationservice/api/problem/investigate/tasks/${id}`,
      data,
    )
  },
  /**
   * 分页查询所有问题调查任务列表
   * @param params 查询参数
   * @param page 页码
   * @param size 每页记录数
   * @returns Promise
   */
  searchTasks(params: ProblemInvestigateTaskReq, page: number = 0, size: number = 10): Promise<TaskResponse> {
    return api.post(`/whiskerguardviolationservice/api/problem/investigate/tasks/search?page=${page}&size=${size}`,
      params,
    )
  },

  /**
   * 创建新的问题调查报告
   * @param data 调查报告数据
   * @returns Promise
   */
  createReport(data: ProblemInvestigateReportDTO): Promise<TaskResponse> {
    return api.post('/whiskerguardviolationservice/api/problem/investigate/reports',
      data,
    )
  },

  /**
   * 更新问题调查报告
   * @param id 报告ID
   * @param data 调查报告数据
   * @returns Promise
   */
  updateReport(id: number, data: ProblemInvestigateReportDTO): Promise<TaskResponse> {
    return api.put(`/whiskerguardviolationservice/api/problem/investigate/reports/${id}`,
      data,
    )
  },

  /**
   * 获取调查报告详情
   * @param id 报告ID
   * @returns Promise
   */
  getReportDetail(id: number): Promise<TaskResponse> {
    return api.get(`/whiskerguardviolationservice/api/problem/investigate/reports/${id}`)
  },

  /**
   * 删除调查报告
   * @param id 报告ID
   * @returns Promise
   */
  deleteReport(id: number): Promise<TaskResponse> {
    return api.delete(`/whiskerguardviolationservice/api/problem/investigate/reports/${id}`)
  },

  /**
   * 创建调查记录
   * @param data 调查记录数据
   * @returns Promise
   */
  createInvestigateRecord(data: ProblemInvestigateRecordDTO): Promise<TaskResponse> {
    return api.post('/whiskerguardviolationservice/api/problem/investigate/records', data)
  },

  /**
   * 更新调查记录
   * @param id 记录ID
   * @param data 调查记录数据
   * @returns Promise
   */
  updateInvestigateRecord(id: number, data: ProblemInvestigateRecordDTO): Promise<TaskResponse> {
    return api.put(`/whiskerguardviolationservice/api/problem/investigate/records/${id}`, data)
  },

  /**
   * 获取调查记录详情
   * @param id 记录ID
   * @returns Promise
   */
  getInvestigateRecord(id: number): Promise<TaskResponse> {
    return api.get(`/whiskerguardviolationservice/api/problem/investigate/records/${id}`)
  },

  /**
   * 删除调查记录
   * @param id 记录ID
   * @returns Promise
   */
  deleteInvestigateRecord(id: number): Promise<TaskResponse> {
    return api.delete(`/whiskerguardviolationservice/api/problem/investigate/records/${id}`)
  },

  /**
   * 分页查询调查记录列表
   * @param investigateId 调查任务ID
   * @param page 页码
   * @param size 每页记录数
   * @returns Promise
   */
  getInvestigateRecords(investigateId: number, page: number = 0, size: number = 10): Promise<TaskResponse> {
    return api.get(`/whiskerguardviolationservice/api/problem/investigate/records?investigateId=${investigateId}&page=${page}&size=${size}`)
  },
}

export default problemTaskApi
