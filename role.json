[{"id": 1, "tenantId": 1, "serviceName": "org-service", "code": "ORG_MANAGE", "name": "组织管理", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "组织架构管理功能", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": []}, {"id": 3, "tenantId": 1, "serviceName": "org-service", "code": "EMPLOYEE_MANAGE", "name": "员工管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理员工信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": [{"id": 2, "tenantId": 1, "serviceName": "org-service", "code": "EMPLOYEE_VIEW", "name": "员工查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看员工信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": {"id": 3, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 5, "tenantId": 1, "serviceName": "org-service", "code": "ROLE_MANAGE", "name": "角色管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理角色信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": [{"id": 4, "tenantId": 1, "serviceName": "org-service", "code": "ROLE_VIEW", "name": "角色查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看角色信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": {"id": 5, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 6, "tenantId": 1, "serviceName": "org-service", "code": "PERMISSION_VIEW", "name": "权限查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看权限信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": []}, {"id": 7, "tenantId": 1, "serviceName": "org-service", "code": "PERMISSION_MANAGE", "name": "权限管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理权限信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": []}, {"id": 9, "tenantId": 1, "serviceName": "org-service", "code": "ORG_UNIT_MANAGE", "name": "组织单元管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理组织单元信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": [{"id": 8, "tenantId": 1, "serviceName": "org-service", "code": "ORG_UNIT_VIEW", "name": "组织单元查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看组织单元信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": {"id": 9, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 11, "tenantId": 1, "serviceName": "org-service", "code": "POSITION_MANAGE", "name": "职位管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理职位信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": [{"id": 10, "tenantId": 1, "serviceName": "org-service", "code": "POSITION_VIEW", "name": "职位查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看职位信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": {"id": 11, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 12, "tenantId": 1, "serviceName": "org-service", "code": "SYSTEM_SETTINGS", "name": "系统设置", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "系统设置功能", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": [{"id": 13, "tenantId": 1, "serviceName": "org-service", "code": "LOG_VIEW", "name": "日志查看", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "查看系统日志", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": {"id": 12, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 14, "tenantId": 1, "serviceName": "org-service", "code": "TENANT_MANAGE", "name": "租户管理", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": "管理租户信息", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-05-21T22:33:25Z", "updatedBy": "system", "updatedAt": "2025-05-21T22:33:25Z", "isDeleted": false, "parent": null, "children": []}]