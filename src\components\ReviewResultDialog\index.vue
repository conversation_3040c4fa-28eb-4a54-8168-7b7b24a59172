<script lang="ts" setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import contractApi from '@/api/review/contract'

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  contractInfo: null,
})

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const router = useRouter()

// Props
interface Props {
  visible: boolean
  contractId?: string | number
  contractInfo?: any
}

// 响应式数据
const dialogVisible = ref(false)
const activeTab = ref('result')
const loading = ref(false)
const aiReviewData = ref<any>(null)
const reviewRecords = ref<any[]>([])
const tableLoading = ref(false)

// 审查操作相关数据
const reviewDecision = ref('') // 'pass' 或 'reject'
const reviewComment = ref('')
const submitting = ref(false)

// 审查记录详情弹窗相关数据
const showRecordDetailDialog = ref(false)
const currentRecord = ref<any>(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initDialog()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 初始化弹窗
async function initDialog() {
  if (props.contractId) {
    await Promise.all([
      getAiReviewData(),
      getReviewRecords(),
    ])
  }
}

// 获取AI审查数据
async function getAiReviewData() {
  try {
    loading.value = true
    const response = await contractApi.aiContract(props.contractId)
    aiReviewData.value = response
  }
  catch (error) {
    console.error('获取AI审查数据失败:', error)
    ElMessage.error('获取AI审查数据失败')
  }
  finally {
    loading.value = false
  }
}

// 获取审查记录
async function getReviewRecords() {
  try {
    tableLoading.value = true
    const response = await contractApi.queryAllReviews(props.contractId!)
    reviewRecords.value = response.data || response || []
  }
  catch (error) {
    console.error('获取审查记录失败:', error)
    ElMessage.error('获取审查记录失败')
  }
  finally {
    tableLoading.value = false
  }
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
  // 重置数据
  activeTab.value = 'result'
  aiReviewData.value = null
  reviewRecords.value = []
  reviewDecision.value = ''
  reviewComment.value = ''
}

// 提交审查结果
async function handleSubmitReview() {
  // 验证输入
  if (!reviewDecision.value) {
    ElMessage.warning('请选择审查结果')
    return
  }

  if (reviewDecision.value === 'reject' && !reviewComment.value.trim()) {
    ElMessage.warning('选择不通过时必须输入意见')
    return
  }

  // 验证AI审查内容不能为空
  if (!aiReviewData.value || !aiReviewData.value.trim()) {
    ElMessage.warning('AI审查内容不能为空，请先获取审查结果')
    return
  }

  try {
    submitting.value = true

    // 准备接口参数
    const params = {
      contractId: props.contractId, // 合同主键ID
      content: aiReviewData.value, // AI生成的内容
      opinion: reviewComment.value.trim(), // 审查意见
      conclusion: reviewDecision.value === 'pass' ? 1 : 2, // 1代表通过，2代表不通过
    }

    // 调用提交审查结果的API
    await contractApi.contractMessageReview(params)

    ElMessage.success('审查结果提交成功')
    handleClose()

    // 返回到合同审查列表页面
    // window.location.href = '/monitor/examination/contractReview'
    router.back()
  }
  catch (error) {
    console.error('提交审查结果失败:', error)
    ElMessage.error('提交审查结果失败')
  }
  finally {
    submitting.value = false
  }
}

// 获取审查状态标签类型
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: 'warning',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    REJECTED: 'danger',
  }
  return statusMap[status] || 'info'
}

// 获取审查状态文本
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    PENDING: '待审查',
    IN_PROGRESS: '审查中',
    COMPLETED: '已完成',
    REJECTED: '已拒绝',
  }
  return statusMap[status] || status
}

// 查看审查记录详情
function handleViewRecordDetail(record: any) {
  currentRecord.value = record
  showRecordDetailDialog.value = true
}

// 获取意见类型
function getOpinionType(opinion: string) {
  const opinionMap: Record<string, string> = {
    1: '通过',
    2: '不通过',
    pass: '通过',
    reject: '不通过',
  }
  return opinionMap[opinion] || opinion || '-'
}

// 格式化审查记录内容
function formatRecordContent(content: string) {
  if (!content) { return '' }

  // 将换行符转换为 <br> 标签
  let formatted = content.replace(/\n/g, '<br>')

  // 处理标题格式（以 ## 或 ### 开头的行）
  formatted = formatted.replace(/^(#{2,3})\s*(.+)$/gm, '<h$1>$2</h$1>')
  formatted = formatted.replace(/<h##>/g, '<h3>').replace(/<\/h##>/g, '</h3>')
  formatted = formatted.replace(/<h###>/g, '<h4>').replace(/<\/h###>/g, '</h4>')

  // 处理粗体文本（**文本**）
  formatted = formatted.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')

  // 处理列表项（以 - 或 * 开头的行）
  formatted = formatted.replace(/^[-*]\s*(.+)$/gm, '<li>$1</li>')

  // 处理分隔线
  formatted = formatted.replace(/^---+$/gm, '<hr>')

  return formatted
}

// 打印审查记录
function handlePrintRecord() {
  if (!currentRecord.value) { return }

  const printContent = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="text-align: center; margin-bottom: 30px;">审查记录详情</h2>

      <div style="margin-bottom: 20px;">
        <h3 style="border-bottom: 2px solid #ccc; padding-bottom: 5px;">基本信息</h3>
        <p><strong>审查人：</strong> ${currentRecord.value.createdBy || '-'}</p>
        <p><strong>创建时间：</strong> ${currentRecord.value.createdAt || '-'}</p>
        <p><strong>审查意见：</strong> ${getOpinionType(currentRecord.value.opinion)}</p>
      </div>

      <div>
        <h3 style="border-bottom: 2px solid #ccc; padding-bottom: 5px;">审查内容</h3>
        <div style="line-height: 1.6; color: #333;">
          ${formatRecordContent(currentRecord.value.content)}
        </div>
      </div>
    </div>
  `

  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <html>
        <head>
          <title>审查记录详情</title>
          <style>
            body { margin: 0; padding: 0; }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}
</script>

<template>
  <el-dialog v-model="dialogVisible" title="审查结果" width="900px" :close-on-click-modal="false" @close="handleClose">
    <el-tabs v-model="activeTab" class="review-tabs">
      <!-- 审查结果 Tab -->
      <el-tab-pane label="审查结果" name="result">
        <div v-loading="loading" class="result-content">
          <div v-if="aiReviewData" class="review-info">
            <!-- 基本信息 -->
            <div class="info-section">
              <h3 class="section-title">
                基本信息
              </h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">合同名称：</span>
                    <span class="value">{{ contractInfo?.name || '-' }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!-- 审查内容 -->
            <div class="info-section">
              <h3 class="section-title">
                审查内容
              </h3>
              <div class="content-box">
                <div v-if="aiReviewData" class="result-text">
                  <el-scrollbar height="400px">
                    <pre class="whitespace-pre-wrap p-4">{{ aiReviewData }}</pre>
                  </el-scrollbar>
                </div>
                <div v-else class="no-content">
                  暂无审查内容
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无审查结果" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 审查记录 Tab -->
      <el-tab-pane label="审查记录" name="records">
        <div v-loading="tableLoading" class="records-content">
          <el-table :data="reviewRecords" style="width: 100%" max-height="500px" empty-text="暂无审查记录">
            <el-table-column prop="opinion" label="审查意见" width="120">
              <template #default="{ row }">
                <div class="opinion-cell" :title="row.opinion">
                  {{ row.opinion }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createdBy" label="审查人" width="100" />

            <el-table-column prop="createdAt" label="创建时间" width="160" />
            <el-table-column prop="content" label="审查记录" min-width="200">
              <template #default="{ row }">
                <div class="flex items-center justify-between">
                  <span class="mr-2 flex-1 truncate" :title="row.content">{{ row.content }}</span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    @click="handleViewRecordDetail(row)"
                  >
                    查看详情
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <!-- 审查操作区域 -->
        <div class="review-actions">
          <div class="decision-section">
            <span class="decision-label">审查结果：</span>
            <el-radio-group v-model="reviewDecision" class="decision-options">
              <el-radio value="pass" size="large">
                <span class="pass-text">通过</span>
              </el-radio>
              <el-radio value="reject" size="large">
                <span class="reject-text">不通过</span>
              </el-radio>
            </el-radio-group>
          </div>

          <div class="comment-section">
            <el-input
              v-model="reviewComment"
              type="textarea"
              :rows="3"
              :placeholder="reviewDecision === 'reject' ? '请输入不通过的原因（必填）' : '请输入审查意见（可选）'"
              :class="{ 'required-field': reviewDecision === 'reject' }"
              maxlength="500"
              show-word-limit
            />
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="button-section">
          <el-button @click="handleClose">
            关闭
          </el-button>

          <el-button
            type="primary"
            :loading="submitting"
            @click="handleSubmitReview"
          >
            确认
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 审查记录详情弹窗 -->
  <el-dialog
    v-model="showRecordDetailDialog"
    title="审查记录详情"
    width="900px"
    :close-on-click-modal="false"
    class="record-detail-dialog"
  >
    <div v-if="currentRecord" class="record-detail-content">
      <!-- 基本信息 -->
      <div class="record-header mb-6">
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-lg text-gray-800 font-semibold">
            基本信息
          </h3>
          <el-tag :type="getOpinionType(currentRecord.opinion)" size="large">
            {{ currentRecord.opinion }}
          </el-tag>
        </div>
        <div class="grid grid-cols-2 gap-4 rounded-lg bg-gray-50 p-4">
          <div>
            <span class="text-sm text-gray-600">审查人：</span>
            <span class="font-medium">{{ currentRecord.createdBy }}</span>
          </div>
          <div>
            <span class="text-sm text-gray-600">创建时间：</span>
            <span class="font-medium">{{ currentRecord.createdAt }}</span>
          </div>
        </div>
      </div>

      <!-- 审查内容 -->
      <div class="record-content">
        <h3 class="mb-4 text-lg text-gray-800 font-semibold">
          审查内容
        </h3>
        <div class="max-w-none prose">
          <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
            <div class="record-text" v-html="formatRecordContent(currentRecord.content)" />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showRecordDetailDialog = false">
          关闭
        </el-button>
        <!-- <el-button type="primary" @click="handlePrintRecord">打印</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.review-tabs {
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.result-content {
  max-height: 600px;
  // overflow-y: auto;
}

.review-info {
  .info-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e4e7ed;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        font-weight: 500;
        color: #606266;
        min-width: 80px;
        margin-right: 8px;
      }

      .value {
        color: #303133;
        flex: 1;
      }
    }
  }
}

.content-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  line-height: 1.6;
  color: #495057;

  .result-text {
    white-space: pre-wrap;
  }

  .no-content {
    color: #909399;
    text-align: center;
    font-style: italic;
  }
}

.risk-list {
  .risk-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.records-content {
  min-height: 300px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.dialog-footer {
  .review-actions {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .decision-section {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .decision-label {
        font-weight: 500;
        color: #303133;
        margin-right: 16px;
        min-width: 80px;
      }

      .decision-options {
        .pass-text {
          color: #67c23a;
          font-weight: 500;
        }

        .reject-text {
          color: #f56c6c;
          font-weight: 500;
        }
      }
    }

    .comment-section {
      :deep(.el-textarea) {
        .el-textarea__inner {
          border-radius: 4px;
        }

        &.required-field .el-textarea__inner {
          border-color: #f56c6c;

          &:focus {
            border-color: #f56c6c;
            box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
          }
        }
      }
    }
  }

  .button-section {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 审查记录详情弹窗样式
.record-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 24px;
  }
}

.record-detail-content {
  .record-header {
    .grid {
      display: grid;
    }

    .grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .gap-4 {
      gap: 1rem;
    }

    .rounded-lg {
      border-radius: 0.5rem;
    }

    .bg-gray-50 {
      background-color: #f9fafb;
    }

    .p-4 {
      padding: 1rem;
    }

    .text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }

    .text-gray-600 {
      color: #4b5563;
    }

    .font-medium {
      font-weight: 500;
    }
  }

  .mb-4 {
    margin-bottom: 1rem;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .text-gray-800 {
    color: #1f2937;
  }

  .font-semibold {
    font-weight: 600;
  }

  .record-content {
    .max-w-none {
      max-width: none;
    }

    .prose {
      color: #374151;
      max-width: none;
    }

    .border {
      border-width: 1px;
    }

    .border-gray-200 {
      border-color: #e5e7eb;
    }

    .rounded-lg {
      border-radius: 0.5rem;
    }

    .bg-white {
      background-color: #ffffff;
    }

    .p-6 {
      padding: 1.5rem;
    }

    .shadow-sm {
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .record-text {
    line-height: 1.8;
    color: #333;

    :deep(h3) {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 20px 0 12px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }

    :deep(h4) {
      font-size: 16px;
      font-weight: 600;
      color: #606266;
      margin: 16px 0 8px 0;
    }

    :deep(p) {
      margin: 12px 0;
      text-indent: 2em;
    }

    :deep(li) {
      margin: 8px 0;
      padding-left: 8px;
      list-style: disc inside;
    }

    :deep(strong) {
      font-weight: 600;
      color: #303133;
    }

    :deep(hr) {
      margin: 20px 0;
      border: none;
      border-top: 1px solid #e4e7ed;
    }
  }
}
</style>
