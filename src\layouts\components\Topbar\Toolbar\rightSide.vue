<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import Tools from './tools.vue'
import eventBus from '@/utils/eventBus'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { i18nTitleInjectionKey } from '@/utils/injectionKeys'
import storage from '@/utils/storage'
import userApi from '@/api/modules/user.ts'
import uploadApi from '@/api/upload'

defineOptions({
  name: 'ToolbarRightSide',
})
const userDetail: any = ref(JSON.parse(storage.local.get('userDetail') || '') ?? '')
const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()
const userinfo: any = ref(JSON.parse(storage.local.get('userinfo') || '') ?? '')
// console.log(userinfo.value, 'userinfo')
const { t } = useI18n()

const generateI18nTitle = inject(i18nTitleInjectionKey, Function, true)

const avatarError = ref(false)
const avatarUrl = ref('')

// 获取头像URL
async function getAvatarUrl(key: string) {
  try {
    const response = await uploadApi.getFileUrl(key)
    return response || ''
  }
  catch (error) {
    console.error('获取头像URL失败:', error)
    return ''
  }
}

// 监听userStore.avatar变化，更新头像URL
watch(() => userDetail.value.avatar, async (newAvatar) => {
  if (newAvatar) {
    // 如果avatar是完整的URL，直接使用
    if (newAvatar.startsWith('http')) {
      avatarUrl.value = newAvatar
    }
    else {
      // 否则通过接口获取文件URL
      avatarUrl.value = await getAvatarUrl(newAvatar)
    }
  }
  else {
    avatarUrl.value = ''
  }
  if (avatarError.value) {
    avatarError.value = false
  }
}, { immediate: true })
function logout() {
  ElMessageBox.confirm(
    '是否退出当前系统?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      userApi.logout({}).then(() => {
        userStore.logout()
        // console.log(res)
      }).catch(() => {
        userStore.logout()
        // console.log(err)
      })
      // ElMessage({
      //   type: 'success',
      //   message: 'Delete completed',
      // })
    })
    .catch(() => {
      // ElMessage({
      //   type: 'info',
      //   message: 'Delete canceled',
      // })
    })
}
</script>

<template>
  <div class="flex items-center">
    <Tools mode="right-side" />
    <HDropdownMenu
      :items="[
        [
          { label: generateI18nTitle('route.home', settingsStore.settings.home.title), handle: () => router.push({ path: settingsStore.settings.home.fullPath }), hide: !settingsStore.settings.home.enable },
        ],
        [
          { label: t('app.resetPwd'), handle: () => eventBus.emit('reswpd'), hide: settingsStore.mode !== 'pc' },
        ],
        [
          { label: t('app.hotkeys'), handle: () => eventBus.emit('global-hotkeys-intro-toggle'), hide: settingsStore.mode !== 'pc' },
        ],
        [
          { label: t('app.logout'), handle: () => logout() },
        ],
      ]" class="flex-center cursor-pointer px-2"
    >
      <div class="flex-center gap-1">
        <img
          v-if="avatarUrl && !avatarError" :src="avatarUrl" :onerror="() => (avatarError = true)"
          class="h-[24px] w-[24px] rounded-full"
        >
        <SvgIcon v-else name="i-carbon:user-avatar-filled-alt" :size="24" class="text-gray-400" />
        <!-- {{ userStore.account }} -->
        {{ userinfo.username }}
        <SvgIcon name="i-ep:caret-bottom" />
      </div>
    </HDropdownMenu>
  </div>
</template>
