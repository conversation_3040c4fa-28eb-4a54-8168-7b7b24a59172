// 页面布局 CSS 变量
:root {
  // 这是一个复合变量
  // 当页宽模式为 adaption-min-width 时，它代表 最小宽度
  // 当页宽模式为 center 时，它代表 固定宽度
  // 当页宽模式为 center-max-width 时，它代表 最大宽度
  --g-app-width: #{$g-app-width};
  // 头部宽度（默认自适应宽度，可固定宽度，固定宽度后为居中显示）
  --g-header-width: #{$g-header-width};
  // 头部高度
  --g-header-height: 60px;
  // 侧边栏宽度
  --g-main-sidebar-width: 80px;
  --g-sub-sidebar-width: 220px;
  --g-sub-sidebar-collapse-width: 64px;
  // 侧边栏 Logo 区域高度
  --g-sidebar-logo-height: 50px;
  // 标签栏高度
  --g-tabbar-height: 50px;
  // 工具栏高度
  --g-toolbar-height: 50px;
  // 标签页最大最小宽度，两个宽度为同一数值时，则为固定宽度，反之将宽度设置为 unset 则为自适应
  --g-tabbar-tab-max-width: 150px;
  --g-tabbar-tab-min-width: 150px;
}

// 明暗模式 CSS 变量
/* stylelint-disable-next-line no-duplicate-selectors */
:root {
  color-scheme: light;

  &::view-transition-old(root),
  &::view-transition-new(root) {
    mix-blend-mode: normal;
    animation: none;
  }

  &::view-transition-old(root) {
    z-index: 1;
  }

  &::view-transition-new(root) {
    z-index: 9999;
  }

  --g-box-shadow-color: rgb(0 0 0 / 12%);

  &.dark {
    color-scheme: dark;

    &::view-transition-old(root) {
      z-index: 9999;
    }

    &::view-transition-new(root) {
      z-index: 1;
    }

    --g-box-shadow-color: rgb(0 0 0 / 72%);
  }
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(0 0 0 / 40%);
  background-clip: padding-box;
  border: 3px solid transparent;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(0 0 0 / 50%);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

html,
body {
  height: 100%;
}

body {
  box-sizing: border-box;
  margin: 0;
  font-family: Lato, "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: var(--g-container-bg);
  -webkit-tap-highlight-color: transparent;

  &.overflow-hidden {
    overflow: hidden;
  }
}

* {
  box-sizing: inherit;
}

// 右侧内容区针对fixed元素，有横向铺满的需求，可在fixed元素上设置 [data-fixed-calc-width]
[data-fixed-calc-width] {
  position: fixed;
  inset-inline: 50% 0;
}

[data-app-width-mode="adaption"],
[data-app-width-mode="adaption-min-width"] {
  [data-fixed-calc-width] {
    width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));

    [dir="rtl"] & {
      transform: translateX(50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2 * -1)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2 * -1));
    }
  }
}

[data-app-width-mode="center"],
[data-app-width-mode="center-max-width"] {
  [data-fixed-calc-width] {
    width: calc(var(--g-app-width) - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
    transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));

    [dir="rtl"] & {
      transform: translateX(50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2 * -1)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2 * -1));
    }
  }

  @media screen and (max-width: $g-app-width) {
    [data-fixed-calc-width] {
      width: calc(100% - var(--g-main-sidebar-actual-width) - var(--g-sub-sidebar-actual-width));
      transform: translateX(-50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2));

      [dir="rtl"] & {
        transform: translateX(50%) translateX(calc(var(--g-main-sidebar-actual-width) / 2 * -1)) translateX(calc(var(--g-sub-sidebar-actual-width) / 2 * -1));
      }
    }
  }
}

[data-mode="mobile"] {
  [data-fixed-calc-width] {
    width: 100% !important;
    transform: translateX(-50%) !important;

    [dir="rtl"] & {
      transform: translateX(50%) !important;
    }
  }
}

// textarea 字体跟随系统
textarea {
  font-family: inherit;
}

/* Overrides Floating Vue */
.v-popper--theme-dropdown,
.v-popper--theme-tooltip {
  --at-apply: inline-flex;
}

.v-popper--theme-dropdown .v-popper__inner,
.v-popper--theme-tooltip .v-popper__inner {
  --at-apply: bg-white dark:bg-stone-8 text-dark dark:text-white rounded shadow ring-1 ring-gray-200 dark:ring-gray-800 border border-solid border-stone/20 text-xs font-normal;

  box-shadow: 0 6px 30px rgb(0 0 0 / 10%);
}

.v-popper--theme-tooltip .v-popper__arrow-inner,
.v-popper--theme-dropdown .v-popper__arrow-inner {
  visibility: visible;

  --at-apply: border-white dark:border-stone-8;
}

.v-popper--theme-tooltip .v-popper__arrow-outer,
.v-popper--theme-dropdown .v-popper__arrow-outer {
  --at-apply: border-stone/20;
}

.v-popper--theme-tooltip.v-popper--shown,
.v-popper--theme-tooltip.v-popper--shown * {
  transition: none !important;
}

[data-overlayscrollbars-contents] {
  overscroll-behavior: contain;
}

// medium-zoom
.medium-zoom-overlay,
.medium-zoom-image {
  z-index: 3000;
}

/* display */
.d-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.d-s-c {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.d-s-s {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.d-s-e {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}

.d-e-c {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.d-b-c {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.d-b-s {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.d-a-c {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.d-s-stretch {
  display: flex;
  align-items: stretch;
  justify-content: space-around;
}

.d-c {
  flex-direction: column;
}

.d-r {
  flex-direction: row;
}

.f-w {
  flex-wrap: wrap;
}

.d-f {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.radius {
  border-radius: 50%;
}
// ck-新增css
// 自定义样式
$custom_weight: 400 500 600 700 800 900; // 字体粗细 fw-###

/* 字体粗细 */
@each $value in $custom_weight {
  .fw-#{$value} {
    font-weight: #{$value};
  }
}

.rel {
  position: relative;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

/* 透明背景 */
.bg-transparent {
  background: transparent;
}

/* 宽高 */
.w-fc {
  width: fit-content;
}

.w100 {
  width: 100%;
}

.h-fc {
  height: fit-content;
}

.h100 {
  height: 100%;
}

/* 字体-粗细 */
.fw-normal {
  font-weight: normal;

  /* 普通 = 400 */
}

.fw-bold {
  font-weight: bold;

  /* 粗体 = 700 */
}

.fw-lighter {
  font-weight: lighter;

  /* 相对于父元素更细 */
}

.fw-bolder {
  font-weight: bolder;

  /* 相对于父元素更粗 */
}

/* flex布局 */
.flex {
  display: flex;
}

/* flex布局 */
.flex-1 {
  flex: 1;
}

.jcc {
  justify-content: center;
}

.jcsb {
  justify-content: space-between;
}

.jcse {
  justify-content: space-evenly;
}

.fdc {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.aic {
  align-items: center;
}

.aife {
  align-items: flex-end;
}

.aifs {
  align-items: flex-start;
}

.wsn {
  white-space: nowrap;
}

/* 固定边距 */
$customSize: px; //单位rpx 偶数单位
$customMinSize: 2; //最小
$customMaxSize: 200; // 最大

@for $i from $customMinSize through $customMaxSize {
  @if $i % 2 == 0 {
    .m-#{$i} {
      margin: #{$i}$customSize;
    }
    .mt-#{$i} {
      margin-top: #{$i}$customSize;
    }
    .ml-#{$i} {
      margin-left: #{$i}$customSize;
    }
    .mr-#{$i} {
      margin-right: #{$i}$customSize;
    }
    .mb-#{$i} {
      margin-bottom: #{$i}$customSize;
    }
    .p-#{$i} {
      padding: #{$i}$customSize;
    }
    .pt-#{$i} {
      padding-top: #{$i}$customSize;
    }
    .pb-#{$i} {
      padding-bottom: #{$i}$customSize;
    }
    .pl-#{$i} {
      padding-left: #{$i}$customSize;
    }
    .pr-#{$i} {
      padding-right: #{$i}$customSize;
    }
    .f-#{$i} {
      font-size: #{$i}$customSize !important;
    }
  }
}

.text-2 {
  /* 将对象作为弹性伸缩盒子模型显示 */
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;

  /* 限制在一个块元素显示的文本的行数 */

  /* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端； */
  -webkit-line-clamp: 2;

  /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-box-orient: vertical;
}

.custom-style .el-segmented {
  :deep(.is-selected) {
    // border-bottom: 2px solid #18a058;
  }

  --el-segmented-item-selected-color: #fff;

  /* 激活颜色 */
  --el-segmented-item-selected-bg-color: rgb(112 182 255);

  /* 激活背景色 */
  --el-border-radius-base: 20px;

  /* 激活圆角 */
  font-size: 18px;
  font-weight: 600;
  color: #8f9294;
}

.css-new-status {
  display: flex;
  align-items: center;
  justify-content: center;

  .left-box {
    width: 6px;
    height: 6px;
    margin-right: 6px;
    background-color: var(--color, #18a058);
    border-radius: 50%;
  }
}
