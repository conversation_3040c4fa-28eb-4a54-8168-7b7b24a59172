<script setup lang="ts">
import * as Echarts from 'echarts'
import { onMounted, ref } from 'vue'
import { Check, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

onMounted(() => {

})
// 路由实例
const router = useRouter()
const form = ref({})
const activeName = ref(1)

const responseMeasures = ref(false)
const activities = [
  {
    content: 'Custom icon',
    timestamp: '2018-04-12 20:46',
    size: 'large',
    type: 'primary',
    icon: Check,
    color: '#0bbd87',
    content: '完成月度合规检查',
  },
  {
    content: 'Custom hollow',
    timestamp: '2018-04-03 20:46',
    type: 'primary',
    icon: Refresh,
    color: '#1677FF',
    size: 'large',
    content: '季度风险评估进行中',
  },
]

const dataList_qd = ref([
  {
    id: 1,
    name: '负责公司财务战略规划的制定与执行，确保符合相关法律法规要求',
    lx: '管理类',
    fx: '风险等级',
    fg: '法规依据',
  },
  {
    id: 2,
    name: '监督财务报表的编制和披露，确保财务信息的真实性和完整性，确保符合相关法律法规要求',
    lx: '管理类',
    fx: '风险等级',
    fg: '《企业会计准则》',
  },
])

function goEdit(item: any) {
  console.log(item)
}

onMounted(() => {
  initChart2()
  initChart3()
})

const chart2Ref = ref(null)
const chart3Ref = ref(null)
function initChart2() {
  const chart1 = Echarts.init(chart2Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [10, 20, 30, 20, 40, 20, 30],
        type: 'line',
        smooth: true,
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
function initChart3() {
  const chart1 = Echarts.init(chart3Ref.value)
  // 配置数据
  const option = {
    xAxis: {
      type: 'category',
      data: ['技术部', '运营部', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [120, 200, 150, 80, 70, 110, 130],
        type: 'bar',
      },
      {
        data: [120, 200, 150, 80, 70, 110, 130],
        type: 'bar',
      },
    ],
  }
  // 传入数据
  chart1.setOption(option)
}
</script>

<template>
  <div>
    <el-form ref="formRef" :rules="formRules" :model="form" label-width="110px" label-position="left">
      <page-header title="" content="">
        <template #content>
          <div class="aic jcsb flex">
            <div class="f-28">
              <span class="mr-10 c-[#000]">2024年Q1合规培训季度报告</span>
            </div>
            <div>
              123
            </div>
          </div>
        </template>
      </page-header>
      <page-main style="background-color: transparent;">
        <!-- <el-row> -->
        <!-- <el-col :span="24" class="pr-10"> -->
        <el-card shadow="hover" style="margin-top: -16px;">
          <!--              <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
          <div class="f-14">
            <el-row>
              <el-col :span="8">
                <span class="c-[#999]">报告名称：</span>
                <span>2024年Q1合规培训季度报告</span>
              </el-col>
              <el-col :span="8">
                <span class="c-[#999]">生成日期：</span>
                <span>2024-04-05</span>
              </el-col>
              <el-col :span="8">
                <span class="c-[#999]">报告类型：</span>
                <span>季度报告</span>
              </el-col>
            </el-row>
          </div>
          <div class="f-14 mt-24">
            <el-row>
              <el-col :span="8">
                <span class="c-[#999]">生成人：</span>
                <span>王明</span>
              </el-col>
              <el-col :span="8">
                <span class="c-[#999]">覆盖时间：</span>
                <span>2024-01-01至2024-03-31</span>
              </el-col>
              <el-col :span="8">
                <span class="c-[#999]">更新日期：</span>
                <span><el-button type="danger">2024-04-08</el-button></span>
              </el-col>
            </el-row>
          </div>
          <div class="f-14 mt-24">
            <el-row>
              <el-col :span="8">
                <span class="c-[#999]">覆盖范围：</span>
                <span>全体员工</span>
              </el-col>
              <el-col :span="8">
                <span class="c-[#999]">报告状态：</span>
                <!-- <el-tag>Tag 1</el-tag> -->
                <el-tag type="success">
                  已审核
                </el-tag>
                <!-- <el-tag class="ml-2" type="info">Tag 3</el-tag> -->
                <!-- <el-tag class="ml-2" type="warning">Tag 4</el-tag> -->
                <!-- <el-tag class="ml-2" type="danger">Tag 5</el-tag> -->
              </el-col>
            </el-row>
          </div>
        </el-card>
        <el-card shadow="hover" class="mt-20">
          <template #header>
            <div class="f-16 fw-600">
              报告摘要
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div
                class="fdc aic jcc flex"
                style="width: 100%;height: 134px;background-color: #f5f5f5;border-radius: 4px;"
              >
                <div class="f-24 c-[#000]">
                  95%
                </div>
                <div class="f-14 mt-10 c-[#666666]">
                  培训覆盖率
                </div>
                <div class="aic mt-10 flex">
                  <img
                    class="wh-20"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAM5JREFUOE+10d0JwjAQwPG7C/jsBqZO4LNV6AY6gB/pBOIEuoITWCv46ggVWvHVBdS6hticpFCR6kOsmOf75X8kCBUPVnTwXyiT0ZgQZLGdvtHCqihj5QkGqYWeIEBLEzlW0JTkfjBDJgeAGyyE/wab++GSAVsMdz91N8dXdOmEykmG0Rs0CACvmihA1lvmuw9APVMyKL8kVl7aDXbPYoHObjjPBw5KioyXTHA8t1fT8rflsIxs/hZlpOqilvVP7jqwAcWM9at+XPWb0s/FBxZWSZPljIYxAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-8 c-[#52C41A]">较上季度 +5%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div
                class="fdc aic jcc flex"
                style="width: 100%;height: 134px;background-color: #f5f5f5;border-radius: 4px;"
              >
                <div class="f-24 c-[#000]">
                  95%
                </div>
                <div class="f-14 mt-10 c-[#666666]">
                  培训覆盖率
                </div>
                <div class="aic mt-10 flex">
                  <img
                    class="wh-20"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAM5JREFUOE+10d0JwjAQwPG7C/jsBqZO4LNV6AY6gB/pBOIEuoITWCv46ggVWvHVBdS6hticpFCR6kOsmOf75X8kCBUPVnTwXyiT0ZgQZLGdvtHCqihj5QkGqYWeIEBLEzlW0JTkfjBDJgeAGyyE/wab++GSAVsMdz91N8dXdOmEykmG0Rs0CACvmihA1lvmuw9APVMyKL8kVl7aDXbPYoHObjjPBw5KioyXTHA8t1fT8rflsIxs/hZlpOqilvVP7jqwAcWM9at+XPWb0s/FBxZWSZPljIYxAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-8 c-[#52C41A]">较上季度 +5%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div
                class="fdc aic jcc flex"
                style="width: 100%;height: 134px;background-color: #f5f5f5;border-radius: 4px;"
              >
                <div class="f-24 c-[#000]">
                  95%
                </div>
                <div class="f-14 mt-10 c-[#666666]">
                  培训覆盖率
                </div>
                <div class="aic mt-10 flex">
                  <img
                    class="wh-20"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAM5JREFUOE+10d0JwjAQwPG7C/jsBqZO4LNV6AY6gB/pBOIEuoITWCv46ggVWvHVBdS6hticpFCR6kOsmOf75X8kCBUPVnTwXyiT0ZgQZLGdvtHCqihj5QkGqYWeIEBLEzlW0JTkfjBDJgeAGyyE/wab++GSAVsMdz91N8dXdOmEykmG0Rs0CACvmihA1lvmuw9APVMyKL8kVl7aDXbPYoHObjjPBw5KioyXTHA8t1fT8rflsIxs/hZlpOqilvVP7jqwAcWM9at+XPWb0s/FBxZWSZPljIYxAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-8 c-[#52C41A]">较上季度 +5%</span>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div
                class="fdc aic jcc flex"
                style="width: 100%;height: 134px;background-color: #f5f5f5;border-radius: 4px;"
              >
                <div class="f-24 c-[#000]">
                  95%
                </div>
                <div class="f-14 mt-10 c-[#666666]">
                  培训覆盖率
                </div>
                <div class="aic mt-10 flex">
                  <img
                    class="wh-20"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAM5JREFUOE+10d0JwjAQwPG7C/jsBqZO4LNV6AY6gB/pBOIEuoITWCv46ggVWvHVBdS6hticpFCR6kOsmOf75X8kCBUPVnTwXyiT0ZgQZLGdvtHCqihj5QkGqYWeIEBLEzlW0JTkfjBDJgeAGyyE/wab++GSAVsMdz91N8dXdOmEykmG0Rs0CACvmihA1lvmuw9APVMyKL8kVl7aDXbPYoHObjjPBw5KioyXTHA8t1fT8rflsIxs/hZlpOqilvVP7jqwAcWM9at+XPWb0s/FBxZWSZPljIYxAAAAAElFTkSuQmCC"
                    alt=""
                  >
                  <span class="f-14 ml-8 c-[#52C41A]">较上季度 +5%</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        报告状态：
        <el-card shadow="hover" class="mt-20">
          <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="培训概览" :name="1">
              <div class="flex-1">
                <div class="f-16 fw-500">
                  培训趋势分析
                </div>
                <div ref="chart2Ref" style="width: 100%;height: 300px;" />
              </div>
              <div class="flex-1">
                <div class="f-16 fw-500">
                  部门参与情况
                </div>
                <div ref="chart3Ref" style="width: 100%;height: 290px;" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="部门分析" name="third99" />
            <el-tab-pane label="课程分析" name="third2" />
            <el-tab-pane label="员工分析" name="third123" />
            <el-tab-pane label="改进建议" name="third123" />
          </el-tabs>
        </el-card>
        <!-- </el-col> -->
        <!-- </el-row> -->
      </page-main>
    </el-form>

    <HDialog v-model="responseMeasures" :title="form.id ? '创建应对措施' : '创建应对措施'" modulewidth="50vw">
      <el-form ref="formRef" :model="form" class="px-2" :rules="formRules" label-position="top" label-width="66px">
        <div class="mb-10">
          <el-button color="#1677FF">
            AI辅助
          </el-button>
        </div>
        <el-form-item label="措施名称：" prop="name">
          <el-input v-model="form.name" size="large" placeholder="请输入对应措施名称" clearable />
        </el-form-item>
        <el-form-item label="措施描述:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施描述">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="措施类型:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择措施类型">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="责任部门:" prop="name">
          <el-select v-model="value" class="m-2" placeholder="请选择责任部门">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="完成期限：" prop="name" label-width="100">
          <el-date-picker
            v-model="form.send_time" value-format="x" style="width: 100%;" type="date"
            placeholder="请选择完成期限"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="responseMeasures = false, form = {} ">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
