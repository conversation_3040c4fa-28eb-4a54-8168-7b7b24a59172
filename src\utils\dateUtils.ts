import dayjs from './dayjs'

/**
 * 禁用今天之前的日期
 * @returns 返回一个函数，用于el-date-picker的disabled-date属性
 */
export function disablePastDates(time: Date) {
  // 8.64e7 是一天的毫秒数
  return time.getTime() < Date.now() - 8.64e7
}

/**
 * 禁用指定日期之前的日期
 * @param date 指定日期
 * @returns 返回一个函数，用于el-date-picker的disabled-date属性
 */
export function disableDatesBefore(date: Date) {
  return (time: Date) => {
    return time.getTime() < date.getTime()
  }
}

/**
 * 禁用指定日期之后的日期
 * @param date 指定日期
 * @returns 返回一个函数，用于el-date-picker的disabled-date属性
 */
export function disableDatesAfter(date: Date) {
  return (time: Date) => {
    return time.getTime() > date.getTime()
  }
}

/**
 * 格式化日期为YYYY-MM-DD
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number) {
  return dayjs(date).format('YYYY-MM-DD')
}

/**
 * 格式化日期时间为YYYY-MM-DD HH:mm:ss
 * @param date 日期
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: Date | string | number) {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}