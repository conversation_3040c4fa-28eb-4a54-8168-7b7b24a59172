<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Lock,
  Plus,
  User,
} from '@element-plus/icons-vue'
import useUserStore from '@/store/modules/user'
import userApi from '@/api/organizational/user'
// import dictApi from '@/api/modules/system/dict'

const userStore = useUserStore()
const router = useRouter()
const _activeMenu = ref('2-1')
const loading = ref(false)
const tenantId = ref(userStore.tenantId) // 这里应该从用户信息或路由参数中获取
const formRef = ref()

// 定义表单数据类型
interface FormData {
  id: number | null
  companyName: string
  companyType: string
  industry: string
  scale: string
  establishDate: string
  legalRepresentative: string
  creditCode: string
  description: string
  contactPerson: string
  contactPhone: string
  email: string
  website: string
  address: {
    region: string[]
    detail: string
  }
  logo: string
  systemNameDisplay: 'default' | 'custom'
  customSystemName: string
  themeColor: string
  loginBackground: string
  // API字段映射
  registrationNumber: string
  registrationDate: string
  registeredCapital: number
  businessScope: string
  taxRegistrationNumber: string
  organizationCode: string
  registeredAddress: string
  postalCode: string
  fax: string
  contactMobile: string
  contactEmail: string
  bankName: string
  bankAccount: string
  businessLicensePath: string
  legalPerson: string
  legalPersonId: string
  tenantCode: string
  tenant?: {
    id?: number
    name?: string
    contactEmail?: string
    contactPhone?: string
    subscriptionPlan?: string
  }
  version?: number
}

const form = ref<FormData>({
  id: null,
  companyName: '',
  companyType: '',
  industry: '',
  scale: '',
  establishDate: '',
  legalRepresentative: '',
  creditCode: '',
  description: '',
  contactPerson: '',
  contactPhone: '',
  email: '',
  website: '',
  address: {
    region: [],
    detail: '',
  },
  logo: '',
  systemNameDisplay: 'default',
  customSystemName: '',
  themeColor: '#1E88E5',
  loginBackground: '',
  // API字段映射
  registrationNumber: '',
  registrationDate: '',
  registeredCapital: 0,
  businessScope: '',
  taxRegistrationNumber: '',
  organizationCode: '',
  registeredAddress: '',
  postalCode: '',
  fax: '',
  contactMobile: '',
  contactEmail: '',
  bankName: '',
  bankAccount: '',
  businessLicensePath: '',
  legalPerson: '',
  legalPersonId: '',
  tenantCode: '',
})

const _regionOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      {
        value: 'beijing',
        label: '北京市',
        children: [
          { value: 'haidian', label: '海淀区' },
          { value: 'chaoyang', label: '朝阳区' },
          { value: 'xicheng', label: '西城区' },
        ],
      },
    ],
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      {
        value: 'shanghai',
        label: '上海市',
        children: [
          { value: 'pudong', label: '浦东新区' },
          { value: 'huangpu', label: '黄浦区' },
          { value: 'xuhui', label: '徐汇区' },
        ],
      },
    ],
  },
]

const presetColors = ref([
  '#1E88E5',
  '#43A047',
  '#FB8C00',
  '#E53935',
  '#8E24AA',
])

// 表单校验规则
const rules = {
  companyName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
  ],
  industry: [
    { required: true, message: '请输入所属行业', trigger: 'blur' },
  ],
  creditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
  ],
  registeredCapital: [
    { required: true, message: '请输入注册资本', trigger: 'blur' },
  ],
  tenantCode: [
    { required: true, message: '请输入租户编号', trigger: 'blur' },
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
  ],
}

function handleLogoSuccess(response: any, file: any) {
  form.value.logo = URL.createObjectURL(file.raw)
}

function beforeLogoUpload(file: any) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

function handleBgSuccess(response: any, file: any) {
  form.value.loginBackground = URL.createObjectURL(file.raw)
}

function beforeBgUpload(file: any) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!')
  }
  return isJPG && isLt5M
}

// 获取租户详情数据
async function fetchTenantInfo() {
  try {
    loading.value = true
    const response = await userApi.tenantInfo(tenantId.value)

    if (response && response.profile) {
      // 映射API数据到表单，数据主要来自profile对象
      const data = response.profile
      const tenant = response // 顶层租户信息

      form.value = {
        ...form.value,
        id: data.id,
        companyName: tenant.name || '',
        companyType: data.companyType || '',
        industry: data.industry || '',
        scale: '', // 根据employeeCount设置规模
        establishDate: data.registrationDate || '',
        legalRepresentative: data.legalPerson || '',
        creditCode: data.registrationNumber || '',
        description: data.businessScope || '',
        contactPerson: data.contactPerson || '',
        contactPhone: data.contactMobile || '',
        email: data.contactEmail || '',
        website: data.website || '',
        address: {
          region: [],
          detail: data.registeredAddress || '',
        },
        // API原始字段
        registrationNumber: data.registrationNumber || '',
        registrationDate: data.registrationDate || '',
        registeredCapital: data.registeredCapital || 0,
        businessScope: data.businessScope || '',
        taxRegistrationNumber: data.taxRegistrationNumber || '',
        organizationCode: data.organizationCode || '',
        registeredAddress: data.registeredAddress || '',
        postalCode: data.postalCode || '',
        fax: data.fax || '',
        contactMobile: data.contactMobile || '',
        contactEmail: data.contactEmail || '',
        bankName: data.bankName || '',
        bankAccount: data.bankAccount || '',
        businessLicensePath: data.businessLicensePath || '',
        legalPerson: data.legalPerson || '',
        legalPersonId: data.legalPersonId || '',
        version: data.version || 0,
        tenantCode: tenant.tenantCode || '',
        tenant: {
          id: tenant.id,
          name: tenant.name,
          contactEmail: tenant.contactEmail,
          contactPhone: tenant.contactPhone,
          subscriptionPlan: tenant.subscriptionPlan,
        },
      }

      // 根据员工数量设置企业规模
      if (data.employeeCount) {
        if (data.employeeCount < 50) {
          form.value.scale = '0-50'
        }
        else if (data.employeeCount < 200) {
          form.value.scale = '50-200'
        }
        else if (data.employeeCount < 500) {
          form.value.scale = '200-500'
        }
        else if (data.employeeCount < 1000) {
          form.value.scale = '500-1000'
        }
        else {
          form.value.scale = '1000+'
        }
      }

      // 设置企业类型映射
      if (tenant.subscriptionPlan) {
        form.value.companyType = mapCompanyType(tenant.subscriptionPlan)
      }

      // 如果tenantCode不存在，则调用接口获取
      // if (!form.value.tenantCode) {
      //   try {
      //     const codeResponse = await dictApi.getCode('DECISION')
      //     if (codeResponse) {
      //       form.value.tenantCode = codeResponse
      //     }
      //   }
      //   catch (codeError) {
      //     console.error('获取tenantCode失败:', codeError)
      //   }
      // }
    }
  }
  catch (error) {
    console.error('获取租户信息失败:', error)
    ElMessage.error('获取企业信息失败')
  }
  finally {
    loading.value = false
  }
}

// 企业类型映射
function mapCompanyType(subscriptionPlan: string) {
  const typeMap: Record<string, string> = {
    BASIC: 'private',
    PREMIUM: 'state-owned',
    ENTERPRISE: 'foreign',
  }
  return typeMap[subscriptionPlan] || 'other'
}

// 提交表单
async function onSubmit() {
  // 先进行表单验证
  if (!formRef.value) {
    return
  }

  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) {
    ElMessage.error('请填写完整的必填信息')
    return
  }

  try {
    loading.value = true

    // 构建更新数据，按照API结构组织
    const updateData = {
      // 租户基本信息
      id: form.value.tenant?.id,
      name: form.value.companyName,
      contactEmail: form.value.email,
      contactPhone: form.value.contactPhone,
      subscriptionPlan: getSubscriptionPlan(form.value.companyType),
      tenantCode: form.value.tenantCode,
      // Profile详细信息
      profile: {
        id: form.value.id,
        registrationNumber: form.value.creditCode || form.value.registrationNumber,
        registrationDate: form.value.establishDate || form.value.registrationDate,
        registeredCapital: form.value.registeredCapital,
        companyType: form.value.companyType,
        businessScope: form.value.description || form.value.businessScope,
        industry: form.value.industry,
        taxRegistrationNumber: form.value.taxRegistrationNumber,
        organizationCode: form.value.organizationCode,
        registeredAddress: form.value.address.detail || form.value.registeredAddress,
        postalCode: form.value.postalCode,
        website: form.value.website,
        fax: form.value.fax,
        contactPerson: form.value.contactPerson,
        contactMobile: form.value.contactPhone || form.value.contactMobile,
        contactEmail: form.value.email || form.value.contactEmail,
        bankName: form.value.bankName,
        bankAccount: form.value.bankAccount,
        businessLicensePath: form.value.businessLicensePath,
        legalPerson: form.value.legalRepresentative || form.value.legalPerson,
        legalPersonId: form.value.legalPersonId,
        version: form.value.version || 0,
        // 根据企业规模计算员工数量（中位数）
        employeeCount: getEmployeeCountFromScale(form.value.scale),
      },
    }

    await userApi.updateTenantInfo(Number(tenantId.value), updateData)

    ElMessage.success('企业信息更新成功')
    // 重新获取数据以刷新页面
    await fetchTenantInfo()
  }
  catch (error) {
    console.error('更新企业信息失败:', error)
    ElMessage.error('更新企业信息失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

// 根据企业规模获取员工数量（估算中位数）
function getEmployeeCountFromScale(scale: string) {
  const scaleMap: Record<string, number> = {
    '0-50': 25,
    '50-200': 125,
    '200-500': 350,
    '500-1000': 750,
    '1000+': 1500,
  }
  return scaleMap[scale] || 0
}

// 根据企业类型获取套餐类型
function getSubscriptionPlan(companyType: string) {
  const planMap: Record<string, string> = {
    'private': 'BASIC',
    'state-owned': 'PREMIUM',
    'foreign': 'ENTERPRISE',
    'joint-venture': 'PREMIUM',
    'other': 'BASIC',
  }
  return planMap[companyType] || 'BASIC'
}

// 取消操作
function onCancel() {
  ElMessageBox.confirm('确定要取消吗？未保存的更改将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.back()
  }).catch(() => {
    // 用户取消
  })
}

// 页面加载时获取数据
onMounted(() => {
  fetchTenantInfo()
})
</script>

<template>
  <div class="absolute-container" style="padding-bottom: 80px;">
    <!-- <page-header title="" content="">
      <template #content>

      </template>
    </page-header> -->
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="!mx-0">
          <el-col :span="18">
            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="text-gray-800 font-bold">
                  基本信息
                </div>
              </template>
              <el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-4">
                  <el-form-item label="企业名称" prop="companyName" required>
                    <el-input v-model="form.companyName" placeholder="请输入企业名称" :disabled="loading" />
                  </el-form-item>
                  <el-form-item label="企业类型" prop="companyType">
                    <el-select v-model="form.companyType" placeholder="请选择企业类型" class="w-full" :disabled="loading">
                      <el-option label="国有企业" value="state-owned" />
                      <el-option label="民营企业" value="private" />
                      <el-option label="外资企业" value="foreign" />
                      <el-option label="合资企业" value="joint-venture" />
                      <el-option label="其他" value="other" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="所属行业" prop="industry" required>
                    <el-input v-model="form.industry" placeholder="请输入所属行业" :disabled="loading" />
                    <!-- <el-select v-model="form.industry" placeholder="请选择所属行业" filterable class="w-full" :disabled="loading">
                      <el-option label="互联网/IT" value="internet" />
                      <el-option label="金融" value="finance" />
                      <el-option label="制造业" value="manufacturing" />
                      <el-option label="医疗健康" value="healthcare" />
                      <el-option label="教育" value="education" />
                    </el-select> -->
                  </el-form-item>
                  <el-form-item label="企业规模" prop="scale">
                    <el-select v-model="form.scale" placeholder="请选择企业规模" class="w-full" :disabled="loading">
                      <el-option label="50人以下" value="0-50" />
                      <el-option label="50-200人" value="50-200" />
                      <el-option label="200-500人" value="200-500" />
                      <el-option label="500-1000人" value="500-1000" />
                      <el-option label="1000人以上" value="1000+" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="成立日期" prop="establishDate">
                    <el-date-picker v-model="form.establishDate" type="date" placeholder="选择日期" class="w-full" :disabled="loading" />
                  </el-form-item>
                  <el-form-item label="法定代表人" prop="legalRepresentative">
                    <el-input v-model="form.legalRepresentative" placeholder="请输入法定代表人姓名" :disabled="loading" />
                  </el-form-item>
                  <el-form-item label="统一社会信用代码" prop="creditCode" required>
                    <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" :disabled="loading" />
                  </el-form-item>
                  <el-form-item label="注册资本" prop="registeredCapital" required>
                    <el-input-number v-model="form.registeredCapital" placeholder="请输入注册资本" :disabled="loading" />
                  </el-form-item>
                  <el-form-item label="租户编号" prop="tenantCode" required>
                    <el-input v-model="form.tenantCode" placeholder="请输入租户编号" :disabled="loading" />
                  </el-form-item>
                </div>
                <!-- <el-form-item label="统一社会信用代码" prop="creditCode" required>
                  <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码" :disabled="loading" />
                </el-form-item> -->
                <el-form-item label="企业简介" prop="description">
                  <el-input v-model="form.description" type="textarea" :rows="4" placeholder="请输入企业简介" />
                </el-form-item>
              </el-form>
            </el-card>

            <el-card shadow="hover" class="mb-6">
              <template #header>
                <div class="text-gray-800 font-bold">
                  联系方式
                </div>
              </template>
              <el-form :model="form" :rules="rules" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-4">
                  <el-form-item label="联系人" prop="contactPerson" required>
                    <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
                  </el-form-item>
                  <el-form-item label="联系电话" prop="contactPhone" required>
                    <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
                  </el-form-item>
                  <el-form-item label="电子邮箱" prop="email">
                    <el-input v-model="form.email" placeholder="请输入电子邮箱" />
                  </el-form-item>
                  <el-form-item label="企业官网" prop="website">
                    <el-input v-model="form.website" placeholder="请输入企业官网地址" />
                  </el-form-item>
                </div>
                <el-form-item label="企业地址" prop="address">
                  <!-- <el-cascader
                    v-model="form.address.region" :options="regionOptions" placeholder="请选择省市区"
                    class="mb-2 w-full"
                  /> -->
                  <el-input v-model="form.address.detail" placeholder="请输入详细地址" />
                </el-form-item>
              </el-form>
            </el-card>

            <el-card v-if="false" shadow="hover">
              <template #header>
                <div class="text-gray-800 font-bold">
                  品牌设置
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <el-form-item label="企业Logo" prop="logo">
                  <el-upload
                    class="avatar-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                    :show-file-list="false" :on-success="handleLogoSuccess" :before-upload="beforeLogoUpload"
                  >
                    <img v-if="form.logo" :src="form.logo" class="avatar">
                    <el-icon v-else class="avatar-uploader-icon">
                      <Plus />
                    </el-icon>
                  </el-upload>
                  <div class="mt-2 text-xs text-gray-500">
                    建议尺寸：200×200px，支持JPG/PNG格式
                  </div>
                </el-form-item>
                <el-form-item label="系统名称显示" prop="systemNameDisplay">
                  <el-radio-group v-model="form.systemNameDisplay">
                    <el-radio label="default">
                      使用默认名称
                    </el-radio>
                    <el-radio label="custom">
                      使用自定义名称
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="form.systemNameDisplay === 'custom'" label="自定义系统名称" prop="customSystemName">
                  <el-input v-model="form.customSystemName" placeholder="请输入自定义系统名称" />
                </el-form-item>
                <el-form-item label="主题色" prop="themeColor">
                  <el-color-picker v-model="form.themeColor" />
                  <div class="mt-2 flex gap-2">
                    <div
                      v-for="color in presetColors" :key="color" class="h-6 w-6 cursor-pointer rounded-full"
                      :style="{ backgroundColor: color }" @click="form.themeColor = color"
                    />
                  </div>
                </el-form-item>
                <el-form-item label="登录页背景" prop="loginBackground">
                  <el-upload
                    class="background-uploader" action="https://jsonplaceholder.typicode.com/posts/"
                    :show-file-list="false" :on-success="handleBgSuccess" :before-upload="beforeBgUpload"
                  >
                    <img v-if="form.loginBackground" :src="form.loginBackground" class="background">
                    <div v-else class="background-uploader-placeholder">
                      <el-icon>
                        <picture />
                      </el-icon>
                      <span class="ml-2">点击上传背景图</span>
                    </div>
                  </el-upload>
                  <div class="mt-2 text-xs text-gray-500">
                    建议尺寸：1920×1080px，支持JPG/PNG格式
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover">
              <template #header>
                <div class="text-gray-800 font-bold">
                  预览效果
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    Logo预览
                  </h3>
                  <div class="flex justify-center">
                    <img
                      v-if="form.logo" :src="form.logo"
                      class="h-32 w-32 border border-gray-200 rounded object-contain"
                    >
                    <div
                      v-else
                      class="h-32 w-32 flex items-center justify-center border border-gray-300 rounded border-dashed text-gray-400"
                    >
                      <el-icon>
                        <picture />
                      </el-icon>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    系统导航栏预览
                  </h3>
                  <div class="border border-gray-200 rounded p-4">
                    <div class="flex items-center justify-between bg-white p-2">
                      <div class="flex items-center">
                        <img v-if="form.logo" :src="form.logo" class="mr-2 h-6 w-6">
                        <span class="text-sm font-medium">
                          {{ form.systemNameDisplay === 'custom' && form.customSystemName ? form.customSystemName : '猫伯伯合规管家' }}
                        </span>
                      </div>
                      <el-avatar
                        :size="24"
                        src="https://ai-public.mastergo.com/ai/img_res/f9e2af8545b4e1c2bdb51eacbcdd1d2d.jpg"
                      />
                    </div>
                    <div class="mt-2">
                      <div class="h-8 flex items-center rounded bg-[#1A1F37] px-3">
                        <span class="text-xs text-white">导航菜单</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-700 font-medium">
                    登录页面预览
                  </h3>
                  <div class="overflow-hidden border border-gray-200 rounded">
                    <div
                      class="h-40 flex items-center justify-center bg-cover bg-center"
                      :style="{ backgroundImage: form.loginBackground ? `url(${form.loginBackground})` : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }"
                    >
                      <div class="w-3/4 rounded bg-white bg-opacity-90 p-4 shadow-sm">
                        <div class="mb-2 flex justify-center">
                          <img v-if="form.logo" :src="form.logo" class="h-10 w-10">
                        </div>
                        <h3 class="mb-3 text-center text-sm font-medium">
                          {{ form.systemNameDisplay === 'custom' && form.customSystemName ? form.customSystemName : '猫伯伯合规管家' }}
                        </h3>
                        <el-input placeholder="用户名" size="small" class="mb-2">
                          <template #prefix>
                            <el-icon>
                              <User />
                            </el-icon>
                          </template>
                        </el-input>
                        <el-input placeholder="密码" size="small" type="password" class="mb-2">
                          <template #prefix>
                            <el-icon>
                              <Lock />
                            </el-icon>
                          </template>
                        </el-input>
                        <el-button
                          type="primary" size="small"
                          class="!rounded-button w-full whitespace-nowrap"
                        >
                          登录
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
    <fixed-action-bar>
      <el-button v-auth="'essentialInformation/index/submit'" type="primary" size="large" :loading="loading" :disabled="loading" @click="onSubmit">
        提交
      </el-button>
      <el-button v-auth="'essentialInformation/index/cancel'" size="large" :disabled="loading" @click="onCancel">
        取消
      </el-button>
    </fixed-action-bar>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .avatar-uploader .avatar {
    display: block;
    width: 120px;
    height: 120px;
    object-fit: contain;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }

  .avatar-uploader .el-upload {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #1e88e5;
  }

  .avatar-uploader-icon {
    width: 120px;
    height: 120px;
    font-size: 28px;
    line-height: 120px;
    color: #8c939d;
    text-align: center;
  }

  .background-uploader .background {
    display: block;
    width: 100%;
    height: 120px;
    object-fit: cover;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }

  .background-uploader-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    color: #8c939d;
    background-color: #f5f7fa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
  }
</style>
