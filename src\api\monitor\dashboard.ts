import api from '@/api/index'

const baseUrl = '/services/whiskerguarddashboardservice/api/'

export default {
  // 获取仪表板统计汇总信息
  queryComplianceDashboard() {
    return api({
      url: `${baseUrl}task/statistics/dashboard`,
      method: 'get',
    })
  },
  // 获取风险分布统计
  queryRiskDistribution() {
    return api({
      url: `${baseUrl}task/statistics/risk/distribution`,
      method: 'get',
    })
  },
  // 获取任务状态趋势
  queryTaskStatusTrend() {
    return api({
      url: `${baseUrl}task/statistics/trend`,
      method: 'get',
    })
  },
}
