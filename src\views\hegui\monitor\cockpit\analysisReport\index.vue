<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { computed, ref } from 'vue'
import {
  ElAlert,
  ElButton,
  ElCard,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElInput,
  ElOption,
  ElRate,
  ElSelect,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElTabs,
  ElTag,
} from 'element-plus'

const reportType = ref('all')
const timeRange = ref('all')
const status = ref('all')
const searchQuery = ref('')
const activeTab = ref('summary')
const rating = ref(4)
const feedback = ref('')
const paging = ref({
  page: 1,
  limit: 1,
  total: 1,
})

const reports = ref([
  {
    id: 1,
    name: '2023年第三季度全面风险分析报告',
    type: '全面分析报告',
    scope: '全公司',
    time: '2023-10-05 14:30',
    status: '已完成',
  },
  {
    id: 2,
    name: '财务部2023年10月风险专项报告',
    type: '部门风险报告',
    scope: '财务部',
    time: '2023-11-02 09:15',
    status: '生成中',
  },
  {
    id: 3,
    name: '数据安全合规专项检查报告',
    type: '专项风险报告',
    scope: 'IT部',
    time: '2023-09-20 16:45',
    status: '已过期',
  },
  {
    id: 4,
    name: '2023年第二季度全面风险分析报告',
    type: '全面分析报告',
    scope: '全公司',
    time: '2023-07-10 11:20',
    status: '已完成',
  },
  {
    id: 5,
    name: '市场部营销活动风险评估报告',
    type: '专项风险报告',
    scope: '市场部',
    time: '2023-08-15 14:00',
    status: '生成失败',
  },
])

const historyReports = ref([
  { id: 1, name: '2023年第二季度全面风险分析报告', date: '2023-07-10' },
  { id: 2, name: '2023年第一季度全面风险分析报告', date: '2023-04-12' },
  { id: 3, name: '2022年年度风险分析报告', date: '2023-01-20' },
  { id: 4, name: '2022年第四季度全面风险分析报告', date: '2022-10-15' },
])

const resources = ref([
  { id: 1, name: '公司风险管理政策', icon: 'fas fa-file-alt' },
  { id: 2, name: '行业合规法规汇编', icon: 'fas fa-gavel' },
  { id: 3, name: '风险管理操作手册', icon: 'fas fa-book' },
  { id: 4, name: '风险指标计算指南', icon: 'fas fa-chart-line' },
])

const filteredReports = computed(() => {
  return reports.value.filter((report) => {
    const matchesType = reportType.value === 'all'
      || (reportType.value === 'full' && report.type === '全面分析报告')
      || (reportType.value === 'department' && report.type === '部门风险报告')
      || (reportType.value === 'special' && report.type === '专项风险报告')

    const matchesStatus = status.value === 'all'
      || (status.value === 'generating' && report.status === '生成中')
      || (status.value === 'completed' && report.status === '已完成')
      || (status.value === 'expired' && report.status === '已过期')
      || (status.value === 'failed' && report.status === '生成失败')

    const matchesSearch = report.name.toLowerCase().includes(searchQuery.value.toLowerCase())

    return matchesType && matchesStatus && matchesSearch
  })
})

function getStatusType(status: string) {
  switch (status) {
    case '已完成': return 'success'
    case '生成中': return 'primary'
    case '已过期': return 'info'
    case '生成失败': return 'danger'
    default: return ''
  }
}

function viewReport(report: any) {
  console.log('查看报告:', report)
}

function exportReport(report: any) {
  console.log('导出报告:', report)
}

function shareReport(report: any) {
  console.log('分享报告:', report)
}

function deleteReport(report: any) {
  console.log('删除报告:', report)
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex c-[#000000]">
          <div class="flex items-center">
            <h1 class="mb-6 text-2xl text-gray-800 font-bold">
              风险分析报告
            </h1>
          </div>
          <div>
            <div class="flex items-center space-x-4">
              <!-- <ElDropdown>
                <button
                  class="rounded-button flex items-center whitespace-nowrap border border-gray-300 bg-white px-3 py-2 text-sm hover:bg-gray-50"
                >
                  <span>全面分析报告</span>
                  <el-icon class="ml-2">
                    <i class="fas fa-chevron-down" />
                  </el-icon>
                </button>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem>全面分析报告</ElDropdownItem>
                    <ElDropdownItem>部门风险报告</ElDropdownItem>
                    <ElDropdownItem>专项风险报告</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
              <ElDropdown>
                <button
                  class="rounded-button flex items-center whitespace-nowrap border border-gray-300 bg-white px-3 py-2 text-sm hover:bg-gray-50"
                >
                  <span>本月</span>
                  <el-icon class="ml-2">
                    <i class="fas fa-chevron-down" />
                  </el-icon>
                </button>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem>本月</ElDropdownItem>
                    <ElDropdownItem>本季度</ElDropdownItem>
                    <ElDropdownItem>本年</ElDropdownItem>
                    <ElDropdownItem>自定义</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown> -->
              <!-- <ElButton type="primary" class="rounded-button whitespace-nowrap">
                生成报告
              </ElButton> -->
            </div>
          </div>
        </div>
      </template>
    </page-header>
    <div style="height: 100%;overflow-y: auto;">
      <PageMain style="background-color: transparent;">
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <ElCard class="">
              <!-- 报告列表区 -->
              <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
                <div class="mb-6 flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <ElSelect v-model="reportType" placeholder="全部报告类型" class="w-40">
                      <ElOption label="全部报告类型" value="all" />
                      <ElOption label="全面分析报告" value="full" />
                      <ElOption label="部门风险报告" value="department" />
                      <ElOption label="专项风险报告" value="special" />
                    </ElSelect>
                    <ElSelect v-model="timeRange" placeholder="全部时间范围" class="w-40">
                      <ElOption label="全部时间范围" value="all" />
                      <ElOption label="最近一周" value="week" />
                      <ElOption label="最近一月" value="month" />
                      <ElOption label="最近一季度" value="quarter" />
                      <ElOption label="最近一年" value="year" />
                    </ElSelect>
                    <ElSelect v-model="status" placeholder="全部状态" class="w-40">
                      <ElOption label="全部状态" value="all" />
                      <ElOption label="生成中" value="generating" />
                      <ElOption label="已完成" value="completed" />
                      <ElOption label="已过期" value="expired" />
                      <ElOption label="生成失败" value="failed" />
                    </ElSelect>
                  </div>
                  <ElInput v-model="searchQuery" placeholder="搜索报告名称..." class="w-64">
                    <template #prefix>
                      <el-icon><i class="fas fa-search" /></el-icon>
                    </template>
                  </ElInput>
                </div>

                <ElTable :data="filteredReports" style="width: 100%;">
                  <ElTableColumn prop="name" label="报告名称" width="300" />
                  <ElTableColumn prop="type" label="报告类型" width="150" />
                  <ElTableColumn prop="scope" label="覆盖范围" width="150" />
                  <ElTableColumn prop="time" label="生成时间" width="180" />
                  <ElTableColumn prop="status" label="状态" width="120">
                    <template #default="{ row }">
                      <ElTag :type="getStatusType(row.status)" size="small">
                        {{ row.status }}
                      </ElTag>
                    </template>
                  </ElTableColumn>
                  <ElTableColumn label="操作" width="350">
                    <template #default="{ row }">
                      <ElButton v-auth="['analysisReport/index/view']" size="small" type="primary" @click="viewReport(row)">
                        查看
                      </ElButton>
                      <ElButton v-auth="['analysisReport/index/export']" size="small" type="success" :disabled="row.status !== '已完成'" @click="exportReport(row)">
                        导出
                      </ElButton>
                      <ElButton v-auth="['analysisReport/index/share']" size="small" :disabled="row.status !== '已完成'" @click="shareReport(row)">
                        分享
                      </ElButton>
                      <ElButton v-auth="['analysisReport/index/delete']" size="small" type="danger" @click="deleteReport(row)">
                        删除
                      </ElButton>
                    </template>
                  </ElTableColumn>
                </ElTable>
                <page-compon
                  :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                  @pag-change="pagChange"
                />
              </div>
            </ElCard>
            <ElCard class="mt-20">
              <!-- 报告详情区 -->
              <div class="mb-8 rounded-lg bg-white p-6 shadow-sm">
                <div class="mb-6 flex items-center justify-between">
                  <div>
                    <h2 class="text-xl text-gray-800 font-bold">
                      2023年第三季度全面风险分析报告
                    </h2>
                    <div class="mt-2 flex items-center text-sm text-gray-500">
                      <span class="mr-4">报告类型: 全面分析报告</span>
                      <span class="mr-4">覆盖范围: 全公司</span>
                      <span class="mr-4">生成时间: 2023-10-05 14:30</span>
                      <span>生成人: 张明远</span>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <ElButton v-auth="['analysisReport/index/pdf']" class="rounded-button whitespace-nowrap">
                      <el-icon class="mr-2">
                        <i class="fas fa-file-pdf" />
                      </el-icon>
                      PDF
                    </ElButton>
                    <ElButton v-auth="['analysisReport/index/word']" class="rounded-button whitespace-nowrap">
                      <el-icon class="mr-2">
                        <i class="fas fa-file-word" />
                      </el-icon>
                      Word
                    </ElButton>
                    <ElButton v-auth="['analysisReport/index/print']" class="rounded-button whitespace-nowrap">
                      <el-icon class="mr-2">
                        <i class="fas fa-print" />
                      </el-icon>
                      打印
                    </ElButton>
                    <ElButton v-auth="['analysisReport/index/share']" class="rounded-button whitespace-nowrap">
                      <el-icon class="mr-2">
                        <i class="fas fa-share-alt" />
                      </el-icon>
                      分享
                    </ElButton>
                  </div>
                </div>

                <ElTabs v-model="activeTab" class="mb-6">
                  <ElTabPane label="摘要" name="summary" />
                  <ElTabPane label="风险概览" name="overview" />
                  <ElTabPane label="重点风险" name="keyRisks" />
                  <ElTabPane label="趋势分析" name="trend" />
                  <ElTabPane label="改进建议" name="suggestions" />
                </ElTabs>

                <div v-if="activeTab === 'summary'">
                  <h3 class="mb-4 text-lg text-gray-800 font-bold">
                    报告摘要
                  </h3>
                  <p class="mb-6 text-sm text-gray-600">
                    本报告对2023年第三季度公司整体风险状况进行了全面分析，识别出高风险项5个，中风险项12个，低风险项23个。主要风险集中在数据安全、财务合规和人力资源领域。与上季度相比，整体风险水平下降8%，但数据安全风险上升15%，需重点关注。
                  </p>

                  <div class="grid grid-cols-4 mb-8 gap-4">
                    <ElCard shadow="hover" class="text-center">
                      <div class="mb-1 text-sm text-gray-500">
                        风险总数
                      </div>
                      <div class="flex items-baseline justify-center">
                        <span class="mr-2 text-2xl text-gray-800 font-bold">40</span>
                        <span class="flex items-center text-sm text-green-500">
                          <el-icon><i class="fas fa-arrow-down" /></el-icon>8%
                        </span>
                      </div>
                    </ElCard>
                    <ElCard shadow="hover" class="text-center">
                      <div class="mb-1 text-sm text-gray-500">
                        高风险项
                      </div>
                      <div class="flex items-baseline justify-center">
                        <span class="mr-2 text-2xl text-gray-800 font-bold">5</span>
                        <span class="flex items-center text-sm text-red-500">
                          <el-icon><i class="fas fa-arrow-up" /></el-icon>2%
                        </span>
                      </div>
                    </ElCard>
                    <ElCard shadow="hover" class="text-center">
                      <div class="mb-1 text-sm text-gray-500">
                        中风险项
                      </div>
                      <div class="flex items-baseline justify-center">
                        <span class="mr-2 text-2xl text-gray-800 font-bold">12</span>
                        <span class="flex items-center text-sm text-green-500">
                          <el-icon><i class="fas fa-arrow-down" /></el-icon>15%
                        </span>
                      </div>
                    </ElCard>
                    <ElCard shadow="hover" class="text-center">
                      <div class="mb-1 text-sm text-gray-500">
                        低风险项
                      </div>
                      <div class="flex items-baseline justify-center">
                        <span class="mr-2 text-2xl text-gray-800 font-bold">23</span>
                        <span class="flex items-center text-sm text-green-500">
                          <el-icon><i class="fas fa-arrow-down" /></el-icon>5%
                        </span>
                      </div>
                    </ElCard>
                  </div>

                  <h3 class="mb-4 text-lg text-gray-800 font-bold">
                    主要发现
                  </h3>
                  <ul class="mb-8 list-disc pl-5 text-sm text-gray-600 space-y-2">
                    <li><span class="font-medium">数据安全风险上升:</span> 发现3个新的数据安全漏洞，可能导致客户信息泄露</li>
                    <li><span class="font-medium">财务合规问题:</span> 2个业务部门存在报销流程不规范现象</li>
                    <li><span class="font-medium">人力资源风险:</span> 关键岗位人才流失率较上季度增加3个百分点</li>
                    <li><span class="font-medium">供应链风险:</span> 主要供应商交付延迟率上升至8%</li>
                    <li><span class="font-medium">法律合规:</span> 新颁布的行业法规可能影响现有业务模式</li>
                  </ul>

                  <ElAlert title="报告结论" type="info" :closable="false" class="mb-4">
                    公司整体风险状况处于可控范围，但数据安全风险需立即采取措施。建议成立专项工作组，在下一季度前解决已识别的数据安全漏洞。同时加强财务合规培训和人力资源保留计划。
                  </ElAlert>
                </div>
              </div>
            </ElCard>
          </el-col>
          <el-col :span="6">
            <!-- 右侧区域 -->
            <ElCard class="">
              <template #header>
                <div class="f-16 fw-600">
                  历史报告
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="report in historyReports" :key="report.id">
                  <a href="#" class="block text-sm text-blue-600 hover:text-blue-800">{{ report.name }}</a>
                  <span class="text-xs text-gray-500">{{ report.date }}</span>
                </li>
              </ul>
            </ElCard>
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关资源
                </div>
              </template>
              <ul class="space-y-3">
                <li v-for="resource in resources" :key="resource.id">
                  <a href="#" class="block flex items-center text-sm text-blue-600 hover:text-blue-800">
                    <el-icon class="mr-2"><i :class="resource.icon" /></el-icon>
                    {{ resource.name }}
                  </a>
                </li>
              </ul>
            </ElCard>
            <ElCard class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  报告反馈
                </div>
              </template>

              <div class="mb-2 flex items-center">
                <span class="mr-2 text-sm text-gray-600">评分:</span>
                <ElRate v-model="rating" :colors="['#F59E0B', '#F59E0B', '#F59E0B']" />
              </div>
              <ElInput v-model="feedback" type="textarea" :rows="3" placeholder="请输入您的反馈意见..." class="mb-3" />
              <ElButton v-auth="['analysisReport/index/submit']" type="primary" class="rounded-button w-full whitespace-nowrap">
                提交反馈
              </ElButton>
            </ElCard>
          </el-col>
        </el-row>
      </PageMain>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-tabs__item) {
    height: 40px;
    padding: 0 16px;
    line-height: 40px;
  }

  :deep(.el-tabs__active-bar) {
    height: 2px;
  }

  :deep(.el-table .cell) {
    padding-right: 12px;
    padding-left: 12px;
  }

  :deep(.el-card) {
    border: 1px solid #e5e7eb;
  }

  :deep(.el-card__body) {
    padding: 16px;
  }

  :deep(.el-rate) {
    display: inline-flex;
    align-items: center;
  }

  :deep(.el-rate__icon) {
    margin-right: 2px;
    font-size: 16px;
  }

  :deep(.el-alert) {
    padding: 12px 16px;
  }

  :deep(.el-alert__title) {
    margin-bottom: 4px;
    font-size: 14px;
    font-weight: 500;
  }

  :deep(.el-alert__content) {
    font-size: 13px;
    line-height: 1.5;
  }
</style>
