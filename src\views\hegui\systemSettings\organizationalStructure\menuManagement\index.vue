<!-- 菜单权限管理 - 使用 TailwindCSS 和 ElementPlus -->
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox, ElTree } from 'element-plus'
import {
  Delete as ElIconDelete,
  Document as ElIconDocument,
  Edit as ElIconEdit,
  FolderOpened as ElIconFolderOpened,
  Plus as ElIconPlus,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
  Setting as ElIconSetting,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import roleApi from '@/api/permissions/role'
import menuApi from '@/api/permissions/menu'

const router = useRouter()

// 菜单数据
const menuData = ref([])
const loading = ref(false)
const searchText = ref('')
const expandedKeys = ref([])
const selectedNode = ref(null)

// 树形组件引用
const treeRef = ref()

// 过滤后的菜单数据
const filteredMenuData = computed(() => {
  if (!searchText.value) { return menuData.value }

  const filterNode = (nodes) => {
    return nodes.filter((node) => {
      const matchesSearch = getMenuTitle(node)?.includes(searchText.value)
        || node.name?.includes(searchText.value)
        || node.path?.includes(searchText.value)

      if (node.children && node.children.length > 0) {
        node.children = filterNode(node.children)
        return matchesSearch || node.children.length > 0
      }

      return matchesSearch
    })
  }

  return filterNode(JSON.parse(JSON.stringify(menuData.value)))
})

// 获取菜单数据
async function getMenuData() {
  loading.value = true
  try {
    // 调用权限接口获取菜单数据
    const response = await roleApi.rolePermissionsTree()
    menuData.value = response || []

    // 默认展开第一层
    expandedKeys.value = menuData.value.map(item => item.id)
  }
  catch (error) {
    ElMessage.error('获取菜单数据失败')
    console.error(error)
    menuData.value = []
  }
  finally {
    loading.value = false
  }
}

// 节点点击事件
function handleNodeClick(data) {
  selectedNode.value = data
}

// 新增菜单
function handleAdd(node = null) {
  const parentId = node ? node.id : 0
  router.push({
    path: '/systemSettings/organizationalStructure/menuManagement/addEdit',
    query: { type: 'add', parentId },
  })
}

// 编辑菜单
function handleEdit(node) {
  // 将节点数据序列化后传递
  console.log(node, 'pppppp')
  const nodeData = encodeURIComponent(JSON.stringify(node))
  router.push({
    path: '/systemSettings/organizationalStructure/menuManagement/addEdit',
    query: { type: 'edit', id: node.id, data: nodeData },
  })
}

// 删除菜单
async function handleDelete(node) {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 "${getMenuTitle(node)}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 这里应该调用删除API
    await menuApi.BusinessPermissions({ id: node.id }, 'delete')

    ElMessage.success('删除成功')
    getMenuData()
  }
  catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 刷新数据
function handleRefresh() {
  getMenuData()
}

// 展开/收起所有节点
const expandAll = ref(false)
function handleExpandAll() {
  expandAll.value = !expandAll.value
  if (expandAll.value) {
    // 展开所有节点
    const getAllKeys = (nodes) => {
      let keys = []
      nodes.forEach((node) => {
        keys.push(node.id)
        if (node.children && node.children.length > 0) {
          keys = keys.concat(getAllKeys(node.children))
        }
      })
      return keys
    }
    expandedKeys.value = getAllKeys(menuData.value)
  }
  else {
    // 收起所有节点
    expandedKeys.value = []
  }
}

// 获取节点图标
function getNodeIcon(node) {
  if (node.children && node.children.length > 0) {
    return 'folder'
  }
  const meta = parseMeta(node)
  return meta.icon || 'document'
}

// 获取节点状态标签
function getNodeStatus(node) {
  if (node.mark === 1) { return { text: '启用', type: 'success' } }
  return { text: '禁用', type: 'danger' }
}

// 获取菜单类型
function getMenuType(node) {
  if (node.children && node.children.length > 0) { return '目录' }

  const meta = parseMeta(node)
  return meta.menu ? '菜单' : '按钮'
}

// 解析meta字段
function parseMeta(node) {
  if (typeof node.meta === 'string') {
    try {
      return JSON.parse(node.meta)
    } catch (error) {
      console.warn('解析meta JSON失败:', error)
      return {}
    }
  }
  return node.meta || {}
}

// 获取菜单标题
function getMenuTitle(node) {
  const meta = parseMeta(node)
  return meta.title || node.name || '未命名菜单'
}

onMounted(() => {
  getMenuData()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="菜单权限管理" content="管理系统菜单结构和权限配置">
      <template #content>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl text-gray-900 font-bold">
              菜单权限管理
            </h1>
            <el-tag type="info" size="small">
              共 {{ menuData.length }} 个根菜单
            </el-tag>
          </div>
          <div class="flex items-center space-x-2">
            <el-button v-auth="'menuManagement/index/addrootMenu'" type="primary" @click="handleAdd()">
              <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon>
              新增根菜单
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon class="mr-1">
                <ElIconRefresh />
              </el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
    </page-header>

    <PageMain style="background-color: transparent;">
      <div class="rounded-lg bg-white shadow-sm">
        <!-- 搜索和操作栏 -->
        <div class="border-b border-gray-200 p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <el-input
                v-model="searchText"
                placeholder="搜索菜单名称、路径或标识"
                class="w-80"
                clearable
              >
                <template #prefix>
                  <el-icon>
                    <ElIconSearch />
                  </el-icon>
                </template>
              </el-input>
              <el-button @click="handleExpandAll">
                <el-icon class="mr-1">
                  <ElIconFolderOpened />
                </el-icon>
                {{ expandAll ? '收起全部' : '展开全部' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 菜单树形结构 -->
        <div class="p-6">
          <ElTree
            ref="treeRef"
            :data="filteredMenuData"
            :props="{
              children: 'children',
              label: 'name',
            }"
            node-key="id"
            :expanded-keys="expandedKeys"
            :highlight-current="true"
            :expand-on-click-node="false"
            class="menu-tree"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="w-full flex items-center justify-between py-2 pr-4">
                <div class="flex flex-1 items-center space-x-3">
                  <!-- 图标 -->
                  <div
                    class="h-8 w-8 flex items-center justify-center rounded-lg"
                    :class="data.children && data.children.length > 0 ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'"
                  >
                    <el-icon size="16">
                      <ElIconFolderOpened v-if="data.children && data.children.length > 0" />
                      <ElIconDocument v-else />
                    </el-icon>
                  </div>

                  <!-- 菜单信息 -->
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <span class="text-gray-900 font-medium">{{ getMenuTitle(data) }}</span>
                      <el-tag :type="getNodeStatus(data).type" size="small">
                        {{ getNodeStatus(data).text }}
                      </el-tag>
                      <el-tag size="small" effect="plain">
                        {{ getMenuType(data) }}
                      </el-tag>
                    </div>
                    <div class="mt-1 text-sm text-gray-500">
                      <span v-if="data.path">路径: {{ data.path }}</span>
                      <span v-if="data.component" class="ml-4">组件: {{ data.component }}</span>
                      <span v-if="data.auths && data.auths.length > 0" class="ml-4">权限: {{ data.auths.length }}个</span>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center transition-opacity space-x-1">
                  <el-button size="small" type="primary" text @click.stop="handleAdd(data)">
                    <el-icon><ElIconPlus /></el-icon>
                  </el-button>
                  <el-button size="small" type="warning" text @click.stop="handleEdit(data)">
                    <el-icon><ElIconEdit /></el-icon>
                  </el-button>
                  <el-button size="small" type="danger" text @click.stop="handleDelete(data)">
                    <el-icon><ElIconDelete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </ElTree>

          <!-- 空状态 -->
          <div v-if="!loading && filteredMenuData.length === 0" class="py-12 text-center">
            <el-icon size="48" class="mb-4 text-gray-400">
              <ElIconDocument />
            </el-icon>
            <p class="mb-4 text-gray-500">
              {{ searchText ? '未找到匹配的菜单' : '暂无菜单数据' }}
            </p>
            <el-button v-if="!searchText" type="primary" @click="handleAdd()">
              <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon>
              创建第一个菜单
            </el-button>
          </div>
        </div>
      </div>

      <!-- 选中节点详情面板 -->
      <div v-if="selectedNode" class="mt-6 rounded-lg bg-white shadow-sm">
        <div class="p-6">
          <div class="mb-4 flex items-center justify-between">
            <h3 class="text-lg text-gray-900 font-semibold">
              菜单详情
            </h3>
            <div class="flex items-center space-x-2">
              <el-button v-auth="'menuManagement/index/edit'" size="small" type="primary" @click="handleEdit(selectedNode)">
                <el-icon class="mr-1">
                  <ElIconEdit />
                </el-icon>
                编辑
              </el-button>
              <el-button v-auth="'menuManagement/index/addsubmenus'" size="small" @click="handleAdd(selectedNode)">
                <el-icon class="mr-1">
                  <ElIconPlus />
                </el-icon>
                添加子菜单
              </el-button>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 md:grid-cols-2">
            <!-- 基本信息 -->
            <div class="space-y-3">
              <h4 class="border-b pb-2 text-gray-900 font-medium">
                基本信息
              </h4>
              <div class="text-sm space-y-2">
                <div><span class="text-gray-500">ID:</span> {{ selectedNode.id }}</div>
                <div><span class="text-gray-500">名称:</span> {{ getMenuTitle(selectedNode) }}</div>
                <div><span class="text-gray-500">路径:</span> {{ selectedNode.path || '-' }}</div>
                <div><span class="text-gray-500">组件:</span> {{ selectedNode.component || '-' }}</div>
                <div><span class="text-gray-500">排序:</span> {{ selectedNode.sort }}</div>
                <div>
                  <span class="text-gray-500">状态:</span>
                  <el-tag :type="getNodeStatus(selectedNode).type" size="small">
                    {{ getNodeStatus(selectedNode).text }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 元数据信息 -->
            <div class="space-y-3">
              <h4 class="border-b pb-2 text-gray-900 font-medium">
                元数据配置
              </h4>
              <div class="text-sm space-y-2">
                <div><span class="text-gray-500">图标:</span> {{ parseMeta(selectedNode).icon || '-' }}</div>
                <div>
                  <span class="text-gray-500">是否菜单:</span>
                  <el-tag :type="parseMeta(selectedNode).menu ? 'success' : 'info'" size="small">
                    {{ parseMeta(selectedNode).menu ? '是' : '否' }}
                  </el-tag>
                </div>
                <div>
                  <span class="text-gray-500">面包屑:</span>
                  <el-tag :type="parseMeta(selectedNode).breadcrumb ? 'success' : 'info'" size="small">
                    {{ parseMeta(selectedNode).breadcrumb ? '显示' : '隐藏' }}
                  </el-tag>
                </div>
                <div>
                  <span class="text-gray-500">侧边栏:</span>
                  <el-tag :type="parseMeta(selectedNode).sidebar ? 'success' : 'info'" size="small">
                    {{ parseMeta(selectedNode).sidebar ? '显示' : '隐藏' }}
                  </el-tag>
                </div>
                <div>
                  <span class="text-gray-500">默认展开:</span>
                  <el-tag :type="parseMeta(selectedNode).defaultOpened ? 'success' : 'info'" size="small">
                    {{ parseMeta(selectedNode).defaultOpened ? '是' : '否' }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 权限信息 -->
            <div class="space-y-3">
              <h4 class="border-b pb-2 text-gray-900 font-medium">
                权限配置
              </h4>
              <div class="text-sm space-y-2">
                <div><span class="text-gray-500">权限数量:</span> {{ selectedNode.auths?.length || 0 }}</div>
                <div v-if="selectedNode.auths && selectedNode.auths.length > 0" class="space-y-1">
                  <div class="mb-2 text-gray-500">
                    权限列表:
                  </div>
                  <div v-for="auth in selectedNode.auths" :key="auth.id" class="flex items-center space-x-2">
                    <el-tag size="small" type="primary">
                      {{ auth.name }}
                    </el-tag>
                    <span class="text-xs text-gray-400">{{ auth.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.menu-tree {
  :deep(.el-tree-node) {
    &:hover {
      .group-hover\:opacity-100 {
        opacity: 1;
      }
    }
  }

  :deep(.el-tree-node__content) {
    height: auto;
    padding: 4px 0;

    &:hover {
      background-color: #f8fafc;
    }
  }

  :deep(.el-tree-node__expand-icon) {
    color: #6b7280;
  }
}

.absolute-container {
  @apply h-full flex flex-col;
}
</style>
