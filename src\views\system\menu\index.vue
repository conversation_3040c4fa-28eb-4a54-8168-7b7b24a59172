<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import Api from '@/api/modules/system/menu'
import useSettingsStore from '@/store/modules/settings'

defineOptions({
  name: 'PagesExampleMenuList',
})
const router = useRouter()
const tabbar = useTabbar()
const settingsStore = useSettingsStore()

const formInline = reactive({
  name: '',
})

const data: any = ref({
  loading: false,
  // 表格是否自适应高度
  tableAutoHeight: true,
  // 列表数据
  dataList: [],
  openinput: false,
  sort: 0,
  collapsed: true,
  dataKeys: [],
  flatDataList: [],
})

// onMounted(() => {
//   getDataList()
// })
function recursiveTreeNode(nodeTree: any[], Fn: Function) {
  if (nodeTree) {
    for (const node of nodeTree) {
      Fn(node)
      node.children && recursiveTreeNode(node.children, Fn)
    }
  }
}
function flatTreeNodeListInit(treeNodes: any[]) {
  const flatTreeNodeList: any[] = []
  recursiveTreeNode(treeNodes, (node: any) => {
    flatTreeNodeList.push(node)
  })
  return flatTreeNodeList
}
function getDataList() {
  data.value.loading = true
  Api.list().then((res: any) => {
    data.value.loading = false
    data.value.dataList = res.data
    data.value.flatDataList = flatTreeNodeListInit(res.data)
    data.value.dataKeys = data.value.flatDataList.map((item: any) => item.id)
  })
}

function onCreate(row?: any) {
  if (settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.mergeTabsBy !== 'activeMenu') {
    tabbar.open({
      name: 'menuadd',
      query: {
        parentId: row.id,
      },
    })
  }
  else {
    router.push({
      name: 'menuadd',
      query: {
        parentId: row.id,
      },
    })
  }
}

function onEdit(row: any) {
  if (settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.mergeTabsBy !== 'activeMenu') {
    // console.log(111, row.id)
    tabbar.open({
      name: 'menuadd',
      // name: 'menudetail',
      query: {
        id: row.id,
      },
    })
  }
  else {
    // console.log(222)
    router.push({
      name: 'menuadd',
      query: {
        id: row.id,
      },
    })
  }
}

function onDel(row: any) {
  ElMessageBox.confirm(`确认删除「${row.meta.title}」吗？`, '确认信息').then(() => {
    Api.AdminMenudelete({ id: row.id }).then((res: any) => {
      getDataList()
      ElMessage.success({
        message: res.msg,
        center: true,
      })
    })
  }).catch(() => { })
}
function onSubmit(row: any) {
  getDataList()
}
function reset(row: any) {

}

/* 展开全部 */
function expandAll(e: any) {
  data.value.collapsed = e
  data.value.dataKeys = e ? [] : data.value.flatDataList.map((item: any) => item.id)
}
onActivated(() => {
  // 更新内容
  getDataList()
})
</script>

<template>
  <div :class="{ 'absolute-container': data.tableAutoHeight }">
    <!-- <page-header title="导航管理" content="页面数据为 Mock 示例数据，非真实数据。" /> -->
    <page-main class="pr">
      <!-- <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="菜单名称:">
            <el-input v-model="formInline.name" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">
              <template #icon>
                <svg-icon name="ep:search" />
              </template>
              查询
            </el-button>
            <el-button @click="reset">
              重置
            </el-button>
          </el-form-item>
        </el-form> -->
      <div class="btnbox">
        <el-button type="primary" size="default" @click="onCreate">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          新增主导航
        </el-button>
        <el-button @click="expandAll(true)">
          展开全部
        </el-button>
        <el-button @click="expandAll(false)">
          折叠全部
        </el-button>
      </div>
      <ElTable
        v-if="!data.loading" v-loading="data.loading" class="list-table" :data="data.dataList" row-key="id"
        :expand-row-keys="data.dataKeys" :default-expand-all="data.collapsed" stripe highlight-current-row border
        height="100%"
      >
        <el-table-column type="index" width="65" align="center" fixed="left" />
        <el-table-column prop="meta.title" label="菜单名称" min-width="140" fixed="left" />
        <el-table-column label="路由" width="200">
          <template #default="{ row }">
            <span :title="row.path">{{ row.path }}</span>
          </template>
        </el-table-column>
        <el-table-column label="页面组件" width="260">
          <template #default="{ row }">
            <el-tag v-if="row.component === 'Layout'">
              Layout
            </el-tag>
            <span v-else :title="row.component">{{ row.component }}</span>
          </template>
        </el-table-column>
        <el-table-column label="图标" width="90" align="center">
          <template #default="{ row }">
            <div style="display: flex; justify-content: center;">
              <svg-icon v-if="row.meta.icon" :name="row.meta.icon" :size="24" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="激活图标" width="90" align="center">
          <template #default="{ row }">
            <svg-icon v-if="row.meta.activeIcon" :name="row.meta.activeIcon" :size="24" />
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <!-- <el-tag v-if="typeof row.meta.sidebar === 'boolean'" :type="row.meta.sidebar ? 'success' : 'danger'">
                {{ row.meta.sidebar ? '显示' : '隐藏' }}
              </el-tag> -->
            <div v-if="row.meta.sidebar === true" class="d-c-c">
              <span class="status-dot" />
              <span style="margin-left: 8px;">显示</span>
            </div>
            <div v-else class="d-c-c">
              <span class="status-dot gray" />
              <span style="margin-left: 8px;">隐藏</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="面包屑" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="typeof row.meta.breadcrumb === 'boolean'" :type="row.meta.breadcrumb ? 'success' : 'danger'">
              {{ row.meta.breadcrumb ? '显示' : '隐藏' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" width="80" align="center">
          <template #default="{ row }">
            <div> {{ row.sort }}</div>
            <!-- <div v-if="!data.openinput" @click="checkinput(row)">
                {{ row.sort }}
              </div>
              <el-input v-else v-model="data.sort" type="number" /> -->
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200" align="center">
          <template #default="scope">
            <el-button type="primary" text @click="onCreate(scope.row)">
              <template #icon>
                <svg-icon name="ep:plus" />
              </template>添加
            </el-button>
            <el-button type="primary" text @click="onEdit(scope.row)">
              <template #icon>
                <svg-icon name="ep:edit" />
              </template>修改
            </el-button>
            <el-button type="danger" text @click="onDel(scope.row)">
              <template #icon>
                <svg-icon name="ep:delete" />
              </template>删除
            </el-button>
            <!-- <el-button type="info" size="small" plain @click="onCreate(scope.row)">
                新增导航
              </el-button> -->
            <!-- <el-button type="primary" size="small" plain @click="onEdit(scope.row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" plain @click="onDel(scope.row)">
                删除
              </el-button> -->
          </template>
        </el-table-column>
      </ElTable>
    </page-main>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    margin-bottom: 15px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .absolute-container {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .page-header {
      margin-bottom: 0;
    }

    .page-main {
      display: flex;
      // 让 page-main 的高度自适应
      flex: 1;
      flex-direction: column;
      overflow: auto;
    }
  }

  :deep(.el-table.list-table) {
    margin: 0;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }

  :deep(.el-table td.el-table__cell div) {
    @include text-overflow;
  }

  .status-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background: #52c41a;
    border-radius: 50%;
  }

  .status-dot.gray {
    background: #ccc;
  }
</style>
