import api from '@/api/index'

export default {
  // 列表
  list: (data: any) => api.get('role/index', {
    params: data,
    // baseURL: '/mock/',
  }),
  // 分配权限
  savePermission: (data: any) => api.post('role/savePermission', { ...data }, {
    // baseURL: '/mock/',
  }),
  // 新增/修改
  edit: (data: any) => api.post('role/edit', {
    ...data,
  }, {
    // baseURL: '/mock/',
  }),
  // 删除
  userdelete: (data: any) => api.post('role/delete', { ...data }, {
    // baseURL: '/mock/',
  }),
  getPermissionLis: (data: any) => api.get('role/getPermissionList', {
    params: data,
    // baseURL: '/mock/',
  }),
}
