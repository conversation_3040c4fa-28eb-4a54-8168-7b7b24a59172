<script lang="ts" setup>
import { ref } from 'vue'
import { Back, Bottom, Check, Close, Delete, Plus, Refresh,
  Right, Setting, Top, Upload, Warning,
} from '@element-plus/icons-vue'

// 矩阵数据
const items = ref([
  { id: 1, code: 'IT-001', name: '信息系统安全管理', domain: '信息技术', importance: '高', description: '确保信息系统安全运行' },
  { id: 2, code: 'HR-002', name: '员工合规培训', domain: '人力资源', importance: '中', description: '组织员工合规培训' },
  { id: 3, code: 'FN-003', name: '财务报告审核', domain: '财务', importance: '高', description: '审核财务报告合规性' },
  { id: 4, code: 'LG-004', name: '合同法律审查', domain: '法务', importance: '高', description: '审查合同法律条款' },
  { id: 5, code: 'OP-005', name: '运营风险评估', domain: '运营', importance: '中', description: '评估运营风险' },
])

const roles = ref([
  { id: 1, code: 'R001', name: '合规总监', type: '管理层', level: '总部', description: '负责整体合规管理' },
  { id: 2, code: 'R002', name: 'IT经理', type: '职能部门', level: '总部', description: '负责IT系统管理' },
  { id: 3, code: 'R003', name: 'HR主管', type: '职能部门', level: '总部', description: '负责人力资源管理' },
  { id: 4, code: 'R004', name: '财务总监', type: '管理层', level: '总部', description: '负责财务管理' },
  { id: 5, code: 'R005', name: '法务专员', type: '职能部门', level: '总部', description: '负责法律事务' },
])

const matrix = ref({})
items.value.forEach((item) => {
  matrix.value[item.id] = {}
  roles.value.forEach((role) => {
    matrix.value[item.id][role.id] = ''
  })
})

// 矩阵设置
const matrixSettings = ref({
  name: '2023年度合规权责分配矩阵',
  description: '本矩阵定义了各合规事项的责任分配',
  version: 'V1.0',
  effectiveDate: new Date(),
  approvalProcess: 'compliance',
  publishScope: ['management', 'department'],
})

// 权责规则
const rules = ref({
  responsible: '具体执行工作的角色，负责完成任务',
  accountable: '对任务负最终责任的角色，通常有审批权',
  consulted: '需要咨询意见的角色，双向沟通',
  informed: '需要知情的角色，单向通知',
  ruleChecks: ['requireA', 'requireR', 'singleA', 'noSameRA'],
})

// 问题检查
const issues = ref([
  {
    description: '信息系统安全管理缺少A角色',
    type: '责任缺失',
    suggestion: '请为信息系统安全管理指定一个A角色',
  },
  {
    description: '财务报告审核同时指定了R和A为财务总监',
    type: '角色冲突',
    suggestion: '请将R角色分配给其他人员',
  },
])

// 最佳实践
const bestPractices = ref([
  {
    title: '明确A角色',
    description: '每个合规事项应明确一个且仅一个A角色，避免责任不清',
  },
  {
    title: '合理分配R角色',
    description: 'R角色应分配给具体执行层面的人员，确保可操作性',
  },
])

// 矩阵模板
const templates = ref([
  {
    name: '基础合规矩阵',
    scenario: '适用于初创企业的基本合规要求',
  },
  {
    name: '金融行业合规矩阵',
    scenario: '适用于金融行业的特殊合规要求',
  },
])

// 获取重要程度标签类型
function getImportanceTagType(importance: string) {
  switch (importance) {
    case '高': return 'danger'
    case '中': return 'warning'
    default: return 'success'
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              编辑权责分配矩阵
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Check />
              </el-icon>
              保存
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Close />
              </el-icon>
              取消
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Upload />
              </el-icon>
              导入模板
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <div class="flex items-center justify-between">
                <div class="flex space-x-3">
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Plus />
                    </el-icon>
                    添加事项
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Plus />
                    </el-icon>
                    添加角色
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Setting />
                    </el-icon>
                    批量设置
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Refresh />
                    </el-icon>
                    重置
                  </el-button>
                </div>
                <div class="flex space-x-3">
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Warning />
                    </el-icon>
                    检查问题
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                      <view />
                    </el-icon>
                    预览
                  </el-button>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  编辑合规权责分配矩阵
                </div>
              </template>
              <div class="overflow-x-auto">
                <table class="min-w-full border-collapse">
                  <thead>
                    <tr>
                      <th class="border bg-gray-50 p-2">
                        合规事项/职责
                      </th>
                      <th v-for="role in roles" :key="role.id" class="border bg-gray-50 p-2">
                        {{ role.name }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in items" :key="item.id">
                      <td class="border p-2">
                        {{ item.name }}
                      </td>
                      <td v-for="role in roles" :key="role.id" class="border p-2">
                        <el-select v-model="matrix[item.id][role.id]" size="small" class="w-full">
                          <el-option value="">
                            无
                          </el-option>
                          <el-option value="R">
                            R
                          </el-option>
                          <el-option value="A">
                            A
                          </el-option>
                          <el-option value="C">
                            C
                          </el-option>
                          <el-option value="I">
                            I
                          </el-option>
                          <el-option value="R*">
                            R*
                          </el-option>
                          <el-option value="A*">
                            A*
                          </el-option>
                        </el-select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!-- <template #header>
                <div class="f-16 fw-600">编辑合规权责分配矩阵</div>
              </template> -->
              <!-- 配置管理区域 -->
              <el-tabs type="border-card">
                <el-tab-pane label="合规事项管理">
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <h2 class="text-lg text-gray-800 font-bold">
                        合规事项管理
                      </h2>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon class="mr-1">
                          <Plus />
                        </el-icon>
                        添加事项
                      </el-button>
                    </div>
                    <el-table :data="items" border style="width: 100%;">
                      <el-table-column prop="code" label="事项编号" width="120" />
                      <el-table-column prop="name" label="事项名称" />
                      <el-table-column prop="domain" label="业务领域" width="150" />
                      <el-table-column prop="importance" label="重要程度" width="100">
                        <template #default="{ row }">
                          <el-tag :type="getImportanceTagType(row.importance)" size="small">
                            {{ row.importance }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="description" label="说明" />
                      <el-table-column label="操作" width="150">
                        <template #default="{ row }">
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Top />
                            </el-icon>
                          </el-button>
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Bottom />
                            </el-icon>
                          </el-button>
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Delete />
                            </el-icon>
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="角色管理">
                  <div class="space-y-4">
                    <div class="flex items-center justify-between">
                      <h2 class="text-lg text-gray-800 font-bold">
                        角色管理
                      </h2>
                      <el-button size="small" class="!rounded-button whitespace-nowrap">
                        <el-icon class="mr-1">
                          <Plus />
                        </el-icon>
                        添加角色
                      </el-button>
                    </div>
                    <el-table :data="roles" border style="width: 100%;">
                      <el-table-column prop="code" label="角色编号" width="120" />
                      <el-table-column prop="name" label="角色名称" />
                      <el-table-column prop="type" label="类型" width="150" />
                      <el-table-column prop="level" label="组织层级" width="150" />
                      <el-table-column prop="description" label="说明" />
                      <el-table-column label="操作" width="150">
                        <template #default="{ row }">
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Back />
                            </el-icon>
                          </el-button>
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Right />
                            </el-icon>
                          </el-button>
                          <el-button size="small" class="!rounded-button whitespace-nowrap">
                            <el-icon>
                              <Delete />
                            </el-icon>
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="矩阵设置">
                  <div class="space-y-4">
                    <h2 class="text-lg text-gray-800 font-bold">
                      矩阵设置
                    </h2>
                    <el-form label-width="120px">
                      <el-form-item label="矩阵名称">
                        <el-input v-model="matrixSettings.name" />
                      </el-form-item>
                      <el-form-item label="矩阵说明">
                        <el-input v-model="matrixSettings.description" type="textarea" :rows="3" />
                      </el-form-item>
                      <el-form-item label="版本号">
                        <el-input v-model="matrixSettings.version" />
                      </el-form-item>
                      <el-form-item label="生效日期">
                        <el-date-picker v-model="matrixSettings.effectiveDate" type="date" />
                      </el-form-item>
                      <el-form-item label="审批流程">
                        <el-select v-model="matrixSettings.approvalProcess" class="w-full">
                          <el-option label="直接生效" value="direct" />
                          <el-option label="主管审批" value="manager" />
                          <el-option label="合规总监审批" value="compliance" />
                          <el-option label="总经理审批" value="ceo" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="发布范围">
                        <el-select v-model="matrixSettings.publishScope" multiple class="w-full">
                          <el-option label="全体员工" value="all" />
                          <el-option label="管理层" value="management" />
                          <el-option label="各部门负责人" value="department" />
                          <el-option label="合规团队" value="compliance" />
                        </el-select>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="权责规则设置">
                  <div class="space-y-4">
                    <h2 class="text-lg text-gray-800 font-bold">
                      权责规则设置
                    </h2>
                    <div class="space-y-6">
                      <div>
                        <h3 class="mb-2 text-gray-700 font-medium">
                          RACI规则定义
                        </h3>
                        <el-form label-width="120px">
                          <el-form-item label="R-负责">
                            <el-input v-model="rules.responsible" type="textarea" :rows="2" />
                          </el-form-item>
                          <el-form-item label="A-批准">
                            <el-input v-model="rules.accountable" type="textarea" :rows="2" />
                          </el-form-item>
                          <el-form-item label="C-咨询">
                            <el-input v-model="rules.consulted" type="textarea" :rows="2" />
                          </el-form-item>
                          <el-form-item label="I-知情">
                            <el-input v-model="rules.informed" type="textarea" :rows="2" />
                          </el-form-item>
                        </el-form>
                      </div>
                      <div>
                        <h3 class="mb-2 text-gray-700 font-medium">
                          权责分配规则
                        </h3>
                        <el-checkbox-group v-model="rules.ruleChecks" class="space-y-2">
                          <el-checkbox label="每个事项必须有至少一个A角色" value="requireA" />
                          <el-checkbox label="每个事项必须有至少一个R角色" value="requireR" />
                          <el-checkbox label="每个事项A角色不超过一个" value="singleA" />
                          <el-checkbox label="禁止R与A为同一角色" value="noSameRA" />
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  问题检查
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(issue, index) in issues" :key="index" class="rounded bg-red-50 p-3">
                  <div class="text-red-600 font-medium">
                    {{ issue.description }}
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    {{ issue.type }}
                  </div>
                  <div class="mt-2 text-sm text-gray-700">
                    {{ issue.suggestion }}
                  </div>
                  <el-button size="small" class="!rounded-button mt-2 whitespace-nowrap">
                    修复建议
                  </el-button>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最佳实践
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(practice, index) in bestPractices" :key="index" class="rounded bg-blue-50 p-3">
                  <div class="text-blue-600 font-medium">
                    {{ practice.title }}
                  </div>
                  <div class="mt-2 text-sm text-gray-700">
                    {{ practice.description }}
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  矩阵模板
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(template, index) in templates" :key="index" class="rounded bg-green-50 p-3">
                  <div class="text-green-600 font-medium">
                    {{ template.name }}
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    {{ template.scenario }}
                  </div>
                  <el-button size="small" class="!rounded-button mt-2 whitespace-nowrap">
                    应用模板
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
  }

  .bg-gray-50 {
    background-color: #f9fafb;
  }

  .bg-red-50 {
    background-color: #fef2f2;
  }

  .bg-blue-50 {
    background-color: #eff6ff;
  }

  .bg-green-50 {
    background-color: #f0fdf4;
  }

  .text-red-600 {
    color: #dc2626;
  }

  .text-blue-600 {
    color: #2563eb;
  }

  .text-green-600 {
    color: #16a34a;
  }

  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
  }

  .rounded-lg {
    border-radius: 0.5rem;
  }
</style>
