import api from '@/api/index'

export default {
  // 经验教训列表
  experienceList(paging: any, params: any) {
    const page = paging?.page
    const size = paging?.size
    return api.post(`/whiskerguardviolationservice/api/continuous/improvement/experiences/search?page=${page - 1}&size=${size}`, params)
  },

  // 创建新的经验教训
  createExperience(data: any) {
    return api.post('/whiskerguardviolationservice/api/continuous/improvement/experiences', data)
  },

  // 获取经验教训详情
  getExperienceDetail(id: string) {
    return api.get(`/whiskerguardviolationservice/api/continuous/improvement/experiences/${id}`)
  },

  // 更新经验教训
  updateExperience(id: string, data: any) {
    return api.patch(`/whiskerguardviolationservice/api/continuous/improvement/experiences/${id}`, data)
  },

  // 删除经验教训
  deleteExperience(id: string) {
    return api.delete(`/whiskerguardviolationservice/api/continuous/improvement/experiences/${id}`)
  },

  // 自动生成编号
  generateExperienceCode() {
    return api.get('/whiskerguardviolationservice/api/continuous/improvement/experiences/generate-code')
  },

  // 获取类型选项
  getExperienceTypes() {
    return api.get('/whiskerguardviolationservice/api/continuous/improvement/experiences/types')
  },

  // 获取来源选项
  getExperienceSources() {
    return api.get('/whiskerguardviolationservice/api/continuous/improvement/experiences/sources')
  },

  // 搜索关联来源
  searchRelatedSources(keyword: string) {
    return api.get(`/whiskerguardviolationservice/api/continuous/improvement/experiences/search-sources?keyword=${keyword}`)
  },

}
