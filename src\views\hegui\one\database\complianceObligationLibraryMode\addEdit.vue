<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'
import dictApi from '@/api/modules/system/dict'

const route = useRoute()
const router = useRouter()

// 选项类型定义
interface OptionItem {
  value: string | number
  label: string
}

const formRef: any = ref(null)
const loading = ref(false)
const isEdit = ref(false)
const obligationId = ref('')

const form: any = ref({
  title: '',
  obligationType: '',
  sourceType: '',
  obligationCode: '',
  level: '',
  effectiveDate: '',
  department: '',
  content: '',
  corePoints: '',
  applicability: '',
  relatedLaw: '',
  lawInterpretation: '',
  riskDescription: '',
  status: 'DRAFT',
})

const formRules: any = ref({
  title: [{
    required: true,
    message: '请输入义务名称',
    trigger: 'blur',
  }],
  obligationType: [{
    required: true,
    message: '请选择义务类型',
    trigger: 'change',
  }],
  sourceType: [{
    required: true,
    message: '请选择来源类型',
    trigger: 'change',
  }],
  level: [{
    required: true,
    message: '请选择风险等级',
    trigger: 'change',
  }],
  department: [{
    required: true,
    message: '请选择责任部门',
    trigger: 'change',
  }],
})

// 义务类型选项
const obligationTypeOptions = ref<OptionItem[]>([])

// 来源类型选项
const sourceTypeOptions = ref<OptionItem[]>([])

// 风险等级选项
const riskLevelOptions = ref<OptionItem[]>([])

// 责任部门选项
const departmentOptions: OptionItem[] = [
  { value: '合规部', label: '合规部' },
  { value: '信息安全部', label: '信息安全部' },
  { value: '法务部', label: '法务部' },
  { value: '技术部', label: '技术部' },
]

// 关联法规选项
const relatedLawOptions: OptionItem[] = [
  { value: 1, label: '数据安全法' },
  { value: 2, label: '个人信息保护法' },
  { value: 3, label: '网络安全法' },
]

// 获取义务类型选项
async function getObligationTypeOptions() {
  try {
    const res = await dictApi.dictAll(1)
    if (res && res.length > 0) {
      obligationTypeOptions.value = res.map((item: any) => ({
        value: item.value,
        label: item.name,
      }))
    }
  }
  catch (error) {
    console.error('获取义务类型选项失败:', error)
    ElMessage.error('获取义务类型选项失败')
  }
}

// 获取来源类型选项
async function getSourceTypeOptions() {
  try {
    const res = await dictApi.dictAll(3)
    if (res && res.length > 0) {
      sourceTypeOptions.value = res.map((item: any) => ({
        value: item.value,
        label: item.name,
      }))
    }
  }
  catch (error) {
    console.error('获取来源类型选项失败:', error)
    ElMessage.error('获取来源类型选项失败')
  }
}

// 获取风险等级选项
async function getRiskLevelOptions() {
  try {
    const res = await dictApi.dictAll(2)
    if (res && res.length > 0) {
      riskLevelOptions.value = res.map((item: any) => ({
        value: item.value,
        label: item.name,
      }))
    }
  }
  catch (error) {
    console.error('获取风险等级选项失败:', error)
    ElMessage.error('获取风险等级选项失败')
  }
}

// 获取义务编号
async function getObligationCode() {
  try {
    const res = await dictApi.getCode('COMPLIANCE_OBLIGATION')
    if (res) {
      form.value.obligationCode = res
    }
  }
  catch (error) {
    console.error('获取义务编号失败:', error)
    ElMessage.error('获取义务编号失败')
  }
}

// 获取义务详情
async function getDetail() {
  if (!obligationId.value) {
    return
  }

  try {
    loading.value = true
    const res = await systemApi.complianceSystem({
      id: obligationId.value,
    }, 'info')

    if (res) {
      Object.assign(form.value, res)
    }
  }
  catch (error) {
    console.error('获取义务详情失败:', error)
    ElMessage.error('获取义务详情失败')
  }
  finally {
    loading.value = false
  }
}

// 保存义务
async function saveObligation(isDraft = false) {
  try {
    await formRef.value?.validate()

    loading.value = true
    const params = {
      ...form.value,
      status: isDraft ? 'DRAFT' : 'EFFECTIVE', // DRAFT: 草稿, EFFECTIVE: 生效
    }

    let res
    if (isEdit.value) {
      params.id = obligationId.value
      res = await systemApi.complianceSystem(params, 'update')
    }
    else {
      res = await systemApi.complianceSystem(params, 'create')
    }

    if (res) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.back()
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('操作失败')
  }
  finally {
    loading.value = false
  }
}

// 取消操作
function cancel() {
  router.back()
}

// 保存草稿
function saveDraft() {
  saveObligation(true)
}

// 提交审核
function submitForReview() {
  saveObligation(false)
}

// 预览
function preview() {
  ElMessage.info('预览功能待开发')
}

onMounted(() => {
  // 获取义务类型选项
  getObligationTypeOptions()
  // 获取来源类型选项
  getSourceTypeOptions()
  // 获取风险等级选项
  getRiskLevelOptions()

  // 检查是否为编辑模式
  if (route.query.id) {
    isEdit.value = true
    obligationId.value = route.query.id as string
    getDetail()
  }
  else {
    // 新增模式下获取义务编号
    getObligationCode()
  }
})
</script>

<template>
  <div class="" style="height: 100%;">
    <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px" label-position="top">
      <PageMain style="background-color: transparent;">
        <div class="flex">
          <div class="flex-1" style="flex: 1.5;overflow-y: auto;">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>基本信息</span>
                </div>
              </template>
              <el-form-item label="义务名称:" prop="title">
                <el-input v-model="form.title" placeholder="请输入义务名称" />
              </el-form-item>
              <el-row>
                <el-col :span="12" class="pr-10">
                  <el-form-item label="义务类型:" prop="obligationType">
                    <el-select v-model="form.obligationType" class="m-2" placeholder="请选择义务类型" style="width: 100%;">
                      <el-option v-for="item in obligationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="pl-10">
                  <el-form-item label="来源类型:" prop="sourceType">
                    <el-select v-model="form.sourceType" class="m-2" placeholder="请选择来源类型" style="width: 100%;">
                      <el-option v-for="item in sourceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="pr-10">
                  <el-form-item label="义务编号:" prop="obligationCode">
                    <el-input v-model="form.obligationCode" placeholder="系统自动生成" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="pl-10">
                  <el-form-item label="风险等级:" prop="level">
                    <el-select v-model="form.level" class="m-2" placeholder="请选择风险等级" style="width: 100%;">
                      <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="pr-10">
                  <el-form-item label="生效日期：" prop="effectiveDate" label-width="100">
                    <el-date-picker
                      v-model="form.effectiveDate" value-format="YYYY-MM-DD" style="width: 100%;" type="date"
                      placeholder="请选择生效日期"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" class="pl-10">
                  <el-form-item label="责任部门:" prop="department">
                    <el-select v-model="form.department" class="m-2" placeholder="请选择责任部门" style="width: 100%;">
                      <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>义务内容</span>
                </div>
              </template>
              <el-form-item label="详细描述:" prop="content">
                <el-input v-model="form.content" :rows="4" type="textarea" placeholder="请输入义务详细描述" />
              </el-form-item>
              <el-form-item label="核心要点:" prop="corePoints">
                <el-input v-model="form.corePoints" :rows="4" type="textarea" placeholder="请输入义务核心要点" />
              </el-form-item>
              <el-form-item label="适用条件:" prop="applicability">
                <el-input v-model="form.applicability" :rows="4" type="textarea" placeholder="请输入适用条件" />
              </el-form-item>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>法律依据</span>
                </div>
              </template>
              <el-form-item label="关联法规:" prop="relatedLaw">
                <el-select v-model="form.relatedLaw" class="m-2" placeholder="请选择关联法规" style="width: 100%;">
                  <el-option v-for="item in relatedLawOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="法规解读:" prop="lawInterpretation">
                <el-input v-model="form.lawInterpretation" :rows="4" type="textarea" placeholder="请输入法规解读" />
              </el-form-item>
            </el-card>
            <el-card class="mt-24">
              <template #header>
                <div class="card-header">
                  <span>风险与控制</span>
                </div>
              </template>
              <el-form-item label="风险描述:" prop="riskDescription">
                <el-input v-model="form.riskDescription" :rows="4" type="textarea" placeholder="请描述可能存在的风险" />
              </el-form-item>
            </el-card>
          </div>

          <div style="width: 1.25rem;" />
          <div class="flex-1">
            <el-card>
              <template #header>
                <div class="aic jcsb flex">
                  <span>AI 智能助手</span>
                  <img
                    style="width: 14px;height: 14px;"
                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAKdJREFUOE+t0rsNAkEQA9B3DVAIFVABATEBMQExATEVEFwZBMQEVEAFVHAVUAGy+GiFuI/QWRrtSiOPvbOuPHHE7HXvOy7YVAVxh6aHleFbrEYjRnCPE2IpmGMde5i2KU5wRmyHHCxxwALpj2s1y8nUVIk7Up3LiZVUiRqpTuLfil1f+VOxLzHpx03e+glA/ud7IW2DQry9kxNSyEOQzTdl5IaG/BqrD9k6Lw8zevfZAAAAAElFTkSuQmCC"
                    alt=""
                  >
                </div>
              </template>
              <div>
                <div>
                  <span class="f-16 fw-600">智能建议</span>
                </div>
                <div class="br-8 f-14 mt-12 bg-[#F5F7FA] p-16">
                  <div>根据已输入内容，建议补充以下要点：</div>
                  <div class="mt-12">
                    · 完善风险影响范围
                  </div>
                  <div class="mt-12">
                    · 完善风险影响范围
                  </div>
                  <div class="mt-12">
                    · 完善风险影响范围
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <span class="f-16 fw-600">合规检查</span>
                </div>
                <div class="br-8 f-14 mt-12 bg-[#F5F7FA] p-16">
                  <div class="mt-12">
                    1. 基本信息完整性检查通过
                  </div>
                  <div class="mt-12">
                    1. 基本信息完整性检查通过
                  </div>
                  <div class="mt-12">
                    1. 基本信息完整性检查通过
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <span class="f-16 fw-600">相关映射</span>
                </div>
                <div class="br-8 f-14 mt-12 bg-[#F5F7FA] p-16">
                  <div>已找到相似义务：</div>
                  <div class="flex flex-wrap">
                    <div class="css-xgys mr-8 mt-12">
                      数据安全管理要求
                    </div>
                    <div class="css-xgys mr-8 mt-12">
                      数据安全管理要求
                    </div>
                    <div class="css-xgys mr-8 mt-12">
                      数据安全管理要求
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card class="mt-24">
              <div class="aic flex" style="justify-content: flex-end;">
                <el-button class="ml-16" @click="cancel">
                  <span class="">取消</span>
                </el-button>
                <el-button class="ml-16" @click="saveDraft">
                  <span class="">保存草稿</span>
                </el-button>
                <el-button class="ml-16" @click="preview">
                  <span class="">预览</span>
                </el-button>
                <el-button type="primary" :loading="loading" @click="submitForReview">
                  <span class="">{{ isEdit ? '更新' : '提交审核' }}</span>
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </PageMain>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .css-xgys {
    display: flex;
    place-content: center center;
    width: fit-content;
    padding: 0 7px 0 8px;
    font-size: 12px;
    font-weight: 400;
    color: #0958d9;
    background: #e6f4ff;
    border: 1px solid #91caff;
    border-radius: 4px;
  }
</style>
