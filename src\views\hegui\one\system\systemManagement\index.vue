<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import type { TabsPaneContext } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/modules/user'
import ImportComponent from '@/components/import/index.vue'
import dictApi from '@/api/modules/system/dict'

import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const _userStore = useUserStore()

// 路由
const router = useRouter()

// 加载状态
const tableLoading = ref(false)
const treeLoading = ref(false)

// 导入弹窗状态
const importDialogVisible = ref(false)

// 关联法规弹窗状态
const relatedLawsDialogVisible = ref(false)
const relatedLawsLoading = ref(false)
const relatedLawsData = ref<any[]>([])

// 制度分类管理弹窗状态
const categoryManageDialogVisible = ref(false)
const categoryDialogVisible = ref(false)
const categoryLoading = ref(false)
const categoryList = ref<any[]>([])
const isEditingCategory = ref(false)
const categoryForm = ref({
  id: null,
  categoryName: '',
  description: '',
  sortNum: 1,
  parentId: null,
})
const categoryFormRef = ref()
const categoryFormRules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
}

// 当前激活的标签页
const activeName = ref(1)

// 标签页列表
const tabsList = ref([
  { id: 1, name: '全部制度 (0)', count: 0 },
  { id: 2, name: '我创建的 (0)', count: 0 },
  { id: 3, name: '待我审批 (0)', count: 0 },
  { id: 4, name: '近期更新 (0)', count: 0 },
])

// 分页参数
const paging = reactive({
  page: 1,
  limit: 10,
  total: 0,
  title: '', // 制度名称搜索
  status: '', // 状态筛选
  categoryId: '', // 分类筛选
  regulationType: '', // 制度类型筛选
  tabType: 1, // 当前标签页类型
})

// 表格数据
const dataList = ref([])

// 表格选中项
const multipleSelection = ref([])

// 状态选项
const statusOptions = ref([] as { value: string, name: string, description?: string }[])

// 制度类型选项
const regulationTypeOptions = [
  { label: '规章制度', value: 'REGULATION' },
  { label: '管理办法', value: 'MEASURES' },
  { label: '行为准则', value: 'CONDUCT' },
]

// 格式化状态显示
function _formatStatus(status: string) {
  const statusItem = statusOptions.value.find(item => item.value === status)
  return statusItem?.name || status
}

// 格式化制度类型显示
function formatRegulationType(type: string) {
  const typeMap: Record<string, string> = {
    REGULATION: '规章制度',
    MEASURES: '管理办法',
    CONDUCT: '行为准则',
  }
  return typeMap[type] || type
}

// 左侧树结构接口
interface TreeItem {
  id: number | string
  name: string
  children?: TreeItem[]
}

// 树形数据
const categoryTree = ref<TreeItem[]>([])

// 树形配置
const _defaultProps = {
  children: 'children',
  label: 'categoryName',
}

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取状态数据
    const response = await dictApi.dictAll(4)
    statusOptions.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取状态数据失败')
  }
}

// 初始化
onMounted(() => {
  loadBasicData()
  // getCategoryTree()
  // getTabCounts()
  getList()
})

// 监听标签页变化
watch(activeName, (newVal) => {
  paging.tabType = newVal
  paging.page = 1
  getList()
})

// 获取分类树
async function getCategoryTree() {
  treeLoading.value = true
  try {
    const res = await systemApi.enterpriseCategories({
      tenantId: _userStore.tenantId,
    }, 'tree')
    categoryTree.value = res
  }
  catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
  finally {
    treeLoading.value = false
  }
}

// 获取各标签页数量
async function getTabCounts() {
  try {
    // 全部制度
    const allRes = await systemApi.system({ countOnly: true }, 'count')
    if (allRes && allRes.data) {
      tabsList.value[0].count = allRes.data
      tabsList.value[0].name = `全部制度 (${allRes.data})`
    }

    // 我创建的
    const creatorRes = await systemApi.system({ countOnly: true, creator: true }, 'count')
    if (creatorRes && creatorRes.data) {
      tabsList.value[1].count = creatorRes.data
      tabsList.value[1].name = `我创建的 (${creatorRes.data})`
    }

    // 待我审批
    const pendingRes = await systemApi.system({ countOnly: true, pendingApproval: true }, 'count')
    if (pendingRes && pendingRes.data) {
      tabsList.value[2].count = pendingRes.data
      tabsList.value[2].name = `待我审批 (${pendingRes.data})`
    }

    // 近期更新
    const recentRes = await systemApi.system({ countOnly: true, recentlyUpdated: true }, 'count')
    if (recentRes && recentRes.data) {
      tabsList.value[3].count = recentRes.data
      tabsList.value[3].name = `近期更新 (${recentRes.data})`
    }
  }
  catch (error) {
    console.error('获取标签页数量失败:', error)
  }
}

// 获取列表数据
async function getList() {
  tableLoading.value = true

  try {
    // 构建查询参数
    const params: any = {
      page: paging.page,
      limit: paging.limit,
    }

    // 添加筛选条件
    if (paging.title) { params.title = paging.title }
    if (paging.status) { params.status = paging.status }
    if (paging.categoryId) { params.categoryId = paging.categoryId }
    if (paging.regulationType) { params.regulationType = paging.regulationType }

    // 根据标签页类型添加不同参数
    // switch (paging.tabType) {
    //   case 2: // 我创建的
    //     params.creator = true
    //     break
    //   case 3: // 待我审批
    //     params.pendingApproval = true
    //     break
    //   case 4: // 近期更新
    //     params.recentlyUpdated = true
    //     break
    // }

    const res = await systemApi.system(params)
    if (res) {
      if (res.content) {
        dataList.value = res.content
        paging.total = res.totalElements || 0
      }
      else {
        dataList.value = res.data
        paging.total = res.length || 0
      }
    }
    else {
      dataList.value = []
      paging.total = 0
    }
  }
  catch (error) {
    console.error('获取制度列表失败:', error)
    ElMessage.error('获取制度列表失败')
    dataList.value = []
  }
  finally {
    tableLoading.value = false
  }
}

// 处理分页变化
function pagChange(val: any) {
  paging.page = val.page
  paging.limit = val.limit
  getList()
}

// 处理树节点点击
function _handleNodeClick(data: TreeItem) {
  paging.categoryId = data.id
  paging.page = 1
  getList()
}

// 处理标签页点击
function handleClick(_tab: TabsPaneContext) {
  // 标签页切换逻辑在watch中处理
}

// 处理表格选择变化
function handleSelectionChange(selection: any[]) {
  multipleSelection.value = selection
}

// 新增制度
function goAdd() {
  router.push({
    path: '/one/systemManagement/addEdit',
  })
}

// 编辑制度
function goEdit(item: any) {
  router.push({
    path: '/one/systemManagement/addEdit',
    query: { id: item.id },
  })
}

// 查看详情
function goDetail(item: any) {
  router.push({
    path: '/one/systemManagement/detail',
    query: { id: item.id },
  })
}

// 删除制度
function deleteRegulation(item: any) {
  ElMessageBox.confirm('确定要删除该制度吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await systemApi.system({ id: item.id }, 'delete')
      ElMessage.success('删除成功')
      getList()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 批量发布
function batchPublish() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要发布的制度')
    return
  }

  // 检查是否有已发布的制度
  const publishedItems = multipleSelection.value.filter((item: any) => item.status === 'PUBLISHED')
  if (publishedItems.length > 0) {
    ElMessage.warning('已发布的制度不能重复发布')
    return
  }

  const ids = multipleSelection.value.map((item: any) => item.id)
  ElMessageBox.confirm(`确定要发布选中的 ${ids.length} 个制度吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await systemApi.system(ids, 'publish')
      ElMessage.success('批量发布成功')
      getList()
    }
    catch (error) {
      console.error('批量发布失败:', error)
      ElMessage.error('批量发布失败')
    }
  }).catch(() => {})
}

// 发布制度
function publishRegulation(row: any) {
  if (row.status !== 'REVIEWING') {
    ElMessage.warning('只有待审批的制度才能发布')
    return
  }
  ElMessageBox.confirm('确定要发布该制度吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const id = [row.id]
      await systemApi.system(id, 'publish')
      ElMessage.success('发布成功')
      getList()
    }
    catch (error) {
      console.error('发布失败:', error)
      ElMessage.error('发布失败')
    }
  }).catch(() => {})
}

// 重置筛选条件
function resetFilter() {
  paging.title = ''
  paging.status = ''
  paging.categoryId = ''
  paging.regulationType = ''
  paging.page = 1
  getList()
}

// 打开导入弹窗
function openImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess() {
  importDialogVisible.value = false
  getList()
  ElMessage.success('导入成功')
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入失败:', error)
  ElMessage.error(`导入失败: ${error.message || '未知错误'}`)
}

// 查看关联法规
async function viewRelatedLaws(row: any) {
  if (!row || !row.id) {
    ElMessage.warning('无法获取制度信息')
    return
  }

  relatedLawsLoading.value = true
  relatedLawsData.value = []
  relatedLawsDialogVisible.value = true

  try {
    const res = await systemApi.system({ id: row.id }, 'relatedLaws')
    if (res) {
      relatedLawsData.value = res
    }
    else {
      ElMessage.info('该制度暂无关联法规')
    }
  }
  catch (error) {
    console.error('获取关联法规失败:', error)
    ElMessage.error('获取关联法规失败')
  }
  finally {
    relatedLawsLoading.value = false
  }
}

// 打开分类管理弹窗
function openCategoryManageDialog() {
  categoryManageDialogVisible.value = true
  getCategoryList()
}

// 关闭分类管理弹窗
function closeCategoryManageDialog() {
  categoryManageDialogVisible.value = false
}

// 获取分类列表
async function getCategoryList() {
  categoryLoading.value = true
  try {
    const res = await systemApi.enterpriseCategories({
    }, 'tree')
    categoryList.value = res || []
  }
  catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  }
  finally {
    categoryLoading.value = false
  }
}

// 打开分类弹窗
function openCategoryDialog() {
  isEditingCategory.value = false
  categoryForm.value = {
    id: null,
    categoryName: '',
    description: '',
    sortNum: 1,
    parentId: null,
  }
  categoryDialogVisible.value = true
}

// 编辑分类
function editCategory(row: any) {
  isEditingCategory.value = true
  categoryForm.value = {
    id: row.id,
    categoryName: row.categoryName,
    description: row.description || '',
    sortNum: row.sortNum || 1,
    parentId: row.parentId,
  }
  categoryDialogVisible.value = true
}

// 关闭分类弹窗
function closeCategoryDialog() {
  categoryDialogVisible.value = false
  if (categoryFormRef.value) {
    categoryFormRef.value.resetFields()
  }
}

// 提交分类表单
async function submitCategoryForm() {
  if (!categoryFormRef.value) { return }

  try {
    await categoryFormRef.value.validate()

    const params = {
      ...categoryForm.value,
      tenantId: _userStore.tenantId,
    }

    if (isEditingCategory.value) {
      await systemApi.enterpriseCategories(params, 'update')
      ElMessage.success('分类更新成功')
    }
    else {
      await systemApi.enterpriseCategories(params, 'create')
      ElMessage.success('分类创建成功')
    }

    closeCategoryDialog()
    getCategoryList()
  }
  catch (error) {
    console.error('提交分类失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除分类
async function deleteCategory(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${row.categoryName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.enterpriseCategories({ id: row.id }, 'delete')
    ElMessage.success('删除成功')
    getCategoryList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              制度库管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <!-- 制度分类管理按钮 -->
            <el-button v-auth="['systemManagement/index/categories']" type="success" plain @click="openCategoryManageDialog">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              制度分类管理
            </el-button>
            <!-- <FaAuth v-auth="['/one/systemManagement/add']"> -->
            <el-button v-auth="['systemManagement/index/add']" type="primary" @click="goAdd">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              新建制度
            </el-button>
            <!-- </FaAuth> -->
            <el-button v-auth="['systemManagement/index/import']" type="primary" plain @click="openImportDialog">
              <el-icon class="mr-1">
                <Download />
              </el-icon>
              批量导入
            </el-button>
            <!-- <el-button type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">导出</span>
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <!-- <div class="card p-16"> -->
      <div class="card flex p-16">
        <div class="ml-32">
          <el-form :inline="true">
            <el-form-item label="名称:">
              <el-input v-model="paging.title" clearable placeholder="搜索制度名称..." @clear="paging.title = ''" />
            </el-form-item>
            <el-form-item label="状态:">
              <el-select
                v-model="paging.status" style="width: 192px;" clearable placeholder="请选择状态"
                @clear="paging.status = ''"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="制度类型:">
              <el-select
                v-model="paging.regulationType" style="width: 192px;" clearable placeholder="请选择制度类型"
                @clear="paging.regulationType = ''"
              >
                <el-option
                  v-for="item in regulationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-debounce="1000" v-auth="['systemManagement/index/search']" type="primary" @click="getList()">
                查询
              </el-button>
              <el-button v-auth="['systemManagement/index/reset']" @click="resetFilter">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-if="false">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i, j in tabsList" :key="j" :label="i.name" :name="i.id" />
        </el-tabs>
      </div>
      <div class="mt-10">
        <div>
          <!-- <div class="mb-4">
            <el-button v-auth="['systemManagement/index/batchPublish']" type="primary" @click="batchPublish">
              批量发布
            </el-button>
          </div> -->
          <el-table
            v-loading="tableLoading"
            :data="dataList"
            highlight-current-row
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="title" label="制度名称" min-width="150" align="center">
              <!-- v-auth="['systemManagement/index/detail']" -->
              <template #default="{ row }">
                <el-link type="primary" @click="goDetail(row)">
                  {{ row.title }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="regulationCode" label="制度编号" width="100" align="center" />
            <el-table-column label="制度类型" width="100" align="center">
              <template #default="{ row }">
                {{ formatRegulationType(row.regulationType) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <div v-if="row.status === 'PUBLISHED'" class="relative flex items-center justify-center">
                  <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                    <span
                      class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                    >true</span>
                  </div>
                  {{ _formatStatus(row.status) }}
                </div>
                <div v-else class="relative flex items-center justify-center">
                  <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                    <span
                      class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                    >true</span>
                  </div>
                  {{ _formatStatus(row.status) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="发布日期" width="120" align="center" />
            <el-table-column prop="updatedAt" label="最后更新" width="120" align="center" />
            <el-table-column prop="relatedRegulations" label="关联法规" width="100" align="center">
              <template #default="{ row }">
                <el-button v-auth="['systemManagement/index/view']" type="primary" link @click="viewRelatedLaws(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="240">
              <template #default="{ row }">
                <div class="action-buttons">
                  <div class="button-row">
                    <el-button
                      v-auth="['systemManagement/index/edit']"
                      type="primary"
                      text
                      size="small"
                      class="action-btn"
                      @click="goEdit(row)"
                    >
                      修改
                    </el-button>
                    <el-button
                      v-auth="['systemManagement/index/delete']"
                      type="danger"
                      text
                      size="small"
                      class="action-btn"
                      @click="deleteRegulation(row)"
                    >
                      删除
                    </el-button>
                  </div>
                  <div class="button-row">
                    <el-button
                      v-auth="['systemManagement/index/viewDetail']"
                      type="success"
                      text
                      size="small"
                      class="action-btn"
                      @click="goDetail(row)"
                    >
                      查看详情
                    </el-button>
                    <el-button
                      v-if="row.status !== 'PUBLISHED'"
                      v-auth="['systemManagement/index/publish']"
                      type="warning"
                      text
                      size="small"
                      class="action-btn"
                      @click="publishRegulation(row)"
                    >
                      发布
                    </el-button>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <page-compon
            :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
            @pag-change="pagChange"
          />
        </div>
        <!-- <LayoutContainer v-if="false" style="padding: 0;" :enable-left-side="false" :enable-right-side="true" :left-side-width="280">
          <template v-if="false" #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                制度分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree
                v-loading="treeLoading"
                :data="categoryTree"
                :props="defaultProps"
                :default-expand-all="true"
                @node-click="handleNodeClick"
              />
            </div>
          </template>
          <div>
            <div class="mb-4">
              <el-button type="primary" @click="batchPublish">
                批量发布
              </el-button>
            </div>
            <el-table
              v-loading="tableLoading"
              :data="dataList"
              highlight-current-row
              border
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column prop="title" label="制度名称" min-width="150" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="goDetail(row)">
                    {{ row.title }}
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column prop="regulationCode" label="制度编号" width="100" align="center" />
              <el-table-column label="制度类型" width="100" align="center">
                <template #default="{ row }">
                  {{ formatRegulationType(row.regulationType) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="{ row }">
                  <div v-if="row.status === 'PUBLISHED'" class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    {{ _formatStatus(row.status) }}
                  </div>
                  <div v-else class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
                      >true</span>
                    </div>
                    {{ _formatStatus(row.status) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="发布日期" width="120" align="center" />
              <el-table-column prop="updatedAt" label="最后更新" width="120" align="center" />
              <el-table-column prop="relatedRegulations" label="关联法规" width="100" align="center" />
              <el-table-column label="操作" width="180">
                <template #default="{ row }">
                  <div class="flex">
                    <el-button type="text" size="small" @click="goEdit(row)">
                      修改
                    </el-button>
                    <el-button type="text" size="small" @click="deleteRegulation(row)">
                      删除
                    </el-button>
                    <el-dropdown>
                      <el-button size="small" type="text">
                        <span>更多</span>
                        <svg-icon name="ep:arrow-down" />
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item @click="goDetail(row)">
                            查看详情
                          </el-dropdown-item>
                          <el-dropdown-item v-if="row.status !== 'PUBLISHED'">
                            发布
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon
              :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange"
            />
          </div>
        </LayoutContainer> -->
      </div>
    </PageMain>

    <!-- 导入组件 -->
    <ImportComponent
      v-model:visible="importDialogVisible"
      title="制度库导入"
      :download-template-api="() => systemApi.system({}, 'template')"
      :import-data-api="(formData: FormData) => systemApi.system({ formData }, 'import')"
      template-file-name="制度库导入模板.xlsx"
      accept-file-types=".xlsx,.xls"
      :max-file-size="10"
      :show-download-template="true"
      @success="handleImportSuccess"
      @error="handleImportError"
    />

    <!-- 关联法规弹窗 -->
    <el-dialog
      v-model="relatedLawsDialogVisible"
      title="关联法规详情"
      width="800px"
      destroy-on-close
    >
      <div v-loading="relatedLawsLoading">
        <el-empty v-if="!relatedLawsData.length" description="暂无关联法规" />
        <el-table v-else :data="relatedLawsData" style="width: 100%" border>
          <el-table-column prop="title" label="法规名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="code" label="文件号" width="120" show-overflow-tooltip />
          <el-table-column prop="department" label="发布部门" width="120" show-overflow-tooltip />
          <el-table-column prop="pubDate" label="发布日期" width="100" show-overflow-tooltip />
          <el-table-column prop="isTime" label="状态" width="100" show-overflow-tooltip />
        </el-table>
      </div>
    </el-dialog>

    <!-- 制度分类管理弹窗 -->
    <el-dialog v-model="categoryManageDialogVisible" title="制度分类管理" width="800px" @close="closeCategoryManageDialog">
      <div class="mb-4">
        <el-button type="primary" @click="openCategoryDialog">
          <el-icon class="mr-1">
            <Plus />
          </el-icon>
          新增分类
        </el-button>
      </div>
      <el-table v-loading="categoryLoading" :data="categoryList" style="width: 100%" height="400">
        <el-table-column prop="categoryName" label="分类名称" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-link type="primary" :underline="false" class="mr-3" @click="editCategory(row)">
              编辑
            </el-link>
            <el-link type="danger" :underline="false" @click="deleteCategory(row)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 新增/编辑分类弹窗 -->
    <el-dialog v-model="categoryDialogVisible" :title="isEditingCategory ? '编辑分类' : '新增分类'" width="500px" @close="closeCategoryDialog">
      <el-form ref="categoryFormRef" :model="categoryForm" :rules="categoryFormRules" label-width="100px">
        <el-form-item label="排序序号">
          <el-input-number v-model="categoryForm.sortNum" :min="1" placeholder="请输入排序序号" />
        </el-form-item>
        <el-form-item label="分类名称" prop="categoryName">
          <el-input v-model="categoryForm.categoryName" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" placeholder="请输入分类描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCategoryDialog">
            取消
          </el-button>
          <el-button type="primary" @click="submitCategoryForm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 4px 0;
  }

  .button-row {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }

  .action-btn {
    min-width: 70px;
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .el-icon {
      font-size: 12px;
    }
  }
</style>
