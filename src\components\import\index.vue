<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'

interface ImportProps {
  // 弹窗显示状态
  visible?: boolean
  // 弹窗标题
  title?: string
  // 下载模板的API函数
  downloadTemplateApi?: () => Promise<any>
  // 导入数据的API函数
  importDataApi?: (file: File) => Promise<any>
  // 模板文件名
  templateFileName?: string
  // 支持的文件类型
  acceptFileTypes?: string
  // 最大文件大小(MB)
  maxFileSize?: number
  // 是否显示下载模板按钮
  showDownloadTemplate?: boolean
}

const props = withDefaults(defineProps<ImportProps>(), {
  visible: false,
  title: '数据导入',
  templateFileName: '导入模板.xlsx',
  acceptFileTypes: '.xlsx,.xls',
  maxFileSize: 10,
  showDownloadTemplate: true,
})

const emit = defineEmits<{
  'update:visible': [visible: boolean]
  success: [result: any]
  error: [error: any]
}>()

const uploading = ref(false)
const downloading = ref(false)
const uploadRef = ref()

// 下载模板
async function handleDownloadTemplate() {
  if (!props.downloadTemplateApi) {
    ElMessage.warning('未配置下载模板接口')
    return
  }

  try {
    downloading.value = true
    const response = await props.downloadTemplateApi()

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = props.templateFileName

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  }
  catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败，请重试')
  }
  finally {
    downloading.value = false
  }
}

// 文件上传前的校验
function beforeUpload(file: File) {
  // 检查文件类型
  const fileExtension = file.name.substring(file.name.lastIndexOf('.'))
  const allowedTypes = props.acceptFileTypes.split(',')
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error(`只支持 ${props.acceptFileTypes} 格式的文件`)
    return false
  }

  // 检查文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > props.maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxFileSize}MB`)
    return false
  }

  return true
}

// 处理文件上传
async function handleFileUpload(file: File) {
  if (!props.importDataApi) {
    ElMessage.warning('未配置导入接口')
    return
  }

  try {
    uploading.value = true
    const result = await props.importDataApi(file)

    // 处理导入结果
    if (result.success) {
      ElMessage.success(`导入成功！共处理 ${result.totalRows} 行，成功 ${result.successRows} 行`)
      emit('success', result)
      // 导入成功后关闭弹窗
      handleClose()
    }
    else {
      // 显示详细错误信息
      let errorMessage = result.message || '导入失败'
      if (result.errorDetails && result.errorDetails.length > 0) {
        const errorCount = result.errorDetails.length
        errorMessage += `，共 ${errorCount} 个错误`
      }

      ElMessageBox.alert(
        errorMessage,
        '导入结果',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true,
        },
      )
      emit('error', result)
    }
  }
  catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败，请重试')
    emit('error', error)
  }
  finally {
    uploading.value = false
    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
  // 清空文件选择
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 自定义上传处理
function customUpload(options: any): Promise<void> {
  const file = options.file
  if (beforeUpload(file)) {
    return handleFileUpload(file)
  }
  return Promise.resolve()
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="500px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <div class="import-dialog-content">
      <div class="flex items-center justify-center space-x-3 mb-4">
        <!-- 下载模板按钮 -->
        <el-button
          v-if="showDownloadTemplate"
          :loading="downloading"
          @click="handleDownloadTemplate"
        >
          <el-icon class="mr-1">
            <Download />
          </el-icon>
          下载模板
        </el-button>

        <!-- 导入按钮 -->
        <el-upload
          ref="uploadRef"
          :show-file-list="false"
          :http-request="customUpload"
          :before-upload="beforeUpload"
          :accept="acceptFileTypes"
        >
          <el-button :loading="uploading" type="primary">
            <el-icon class="mr-1">
              <Upload />
            </el-icon>
            {{ uploading ? '导入中...' : '导入数据' }}
          </el-button>
        </el-upload>
      </div>

      <!-- 提示信息 -->
      <div class="text-sm text-gray-500 text-center">
        <p>支持文件格式：{{ acceptFileTypes }}，文件大小不超过 {{ maxFileSize }}MB</p>
        <p v-if="showDownloadTemplate" class="mt-1">请先下载模板，按照模板格式填写数据后再导入</p>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.import-dialog-content {
  @apply py-4;
}
</style>