import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/personalCenter/toDoList',
  component: Layout,
  name: '/personalCenter/toDoList',
  meta: {
    title: '待办事项',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/personalCenter/toDoList/toDoTasks',
      name: '/personalCenter/toDoList/toDoTasks',
      component: () => import('@/views/hegui/personalCenter/toDoList/toDoTasks/index.vue'),
      meta: {
        title: '待办任务',
        // sidebar: false,
      },
      children: [
        {
          path: '/personalCenter/toDoList/toDoTasks/detail',
          name: '/personalCenter/toDoList/toDoTasks/detail',
          component: () => import('@/views/hegui/personalCenter/toDoList/toDoTasks/detail.vue'),
          meta: {
            title: '实时监控详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/personalCenter/toDoList/completedTasks',
      name: '/personalCenter/toDoList/completedTasks',
      component: () => import('@/views/hegui/personalCenter/toDoList/completedTasks/index.vue'),
      meta: {
        title: '已办任务',
      },
    },

  ],
}

export default routes
