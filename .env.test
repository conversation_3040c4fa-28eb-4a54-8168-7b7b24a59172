# 应用配置面板--测试环境
VITE_APP_SETTING = false
# 页面标题
VITE_APP_TITLE = 忆佰智能名片系统

# 版本号
VITE_APP_VERSION = V1.0.1

# 接口请求地址，会设置到 axios 的 baseURL 参数上 -测试环境

VITE_APP_API_BASEURL = http://slyynew-api.029-360.com/
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =

# 图片回显域名-测试环境
VITE_APP_API_BASEURLIMG = 'http://7slbyy-pc.029-360.com'

