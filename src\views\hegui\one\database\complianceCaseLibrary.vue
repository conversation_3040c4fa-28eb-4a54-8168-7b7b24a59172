<script setup lang="ts">
import { onMounted, ref } from 'vue'

import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

// 搜索参数
const searchParams = ref({
  caseName: null,
  areaType: null,
  caseSource: null,
  level: null,
  occurDateStart: null,
  occurDateEnd: null,
})

const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 表格数据
const tableData = ref([])

// 风险等级映射
const riskLevelMap: any = {
  GENERAL: '一般风险',
  MAJOR: '重大风险',
  TYPICAL: '典型风险',
  SAFE: '安全',
}

// 案例来源映射
const caseSourceMap: any = {
  REGULATORY_PENALTIES: '监管处罚',
  JUDICIAL_PRECEDENTS: '司法判例',
  INDUSTRY: '行业案例',
  INTERNAL: '内部案例',
}

// 领域类型映射
const areaTypeMap: any = {
  INDUSTRY_REGULATION: '行业监管',
  CORPORATE_GOVERNANCE: '公司治理',
  BUSINESS_OPERATIONS: '业务运营',
  FINANCE_TAXATION: '财务税务',
}

const router = useRouter()

// 获取案例列表
async function getList() {
  try {
    const params = {
      ...searchParams.value,
      page: paging.value.page,
      limit: paging.value.limit,
    }
    const response = await systemApi.caseSystem(params, 'list')
    if (response && response.content) {
      tableData.value = response.content.map((item: any) => ({
        ...item,
        riskLevel: riskLevelMap[item.level] || item.level,
        category: areaTypeMap[item.areaType] || item.areaType,
        createTime: item.occurDate || item.createdAt?.seconds ? new Date(item.createdAt.seconds * 1000).toLocaleDateString() : '',
        isStarred: false,
      }))
      paging.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    ElMessage.error('获取案例列表失败')
  }
}

// 分页变化
function pagChange(params: { page: number, limit: number }) {
  paging.value.page = params.page
  paging.value.limit = params.limit
  getList()
}

// 搜索
function handleSearch() {
  paging.value.page = 1
  getList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    caseName: null,
    areaType: null,
    caseSource: null,
    level: null,
    occurDateStart: null,
    occurDateEnd: null,
  }
  paging.value.page = 1
  getList()
}

function goDetail(item: any) {
  router.push({
    name: '/database/cases/detail',
    query: { id: item.id },
  })
}

function goAddEdit(item: any) {
  router.push({
    name: '/database/cases/addEdit',
    query: item ? { id: item.id } : {},
  })
}

// 分享案例
function shareCase(_item: any) {
  ElMessage.success('分享功能开发中')
}

// 切换收藏状态
function toggleStar(item: any) {
  item.isStarred = !item.isStarred
  ElMessage.success(item.isStarred ? '已收藏' : '已取消收藏')
}

// 页面初始化
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader>
      <template #content>
        <div class="card flex justify-between p-16">
          <!-- <div class="ml-14">
            <el-button v-auth="'complianceCaseLibrary/index/batchExport'" type="primary" plain>
              <svg-icon name="ep:download" />
              <span class="ml-4">批量导出</span>
            </el-button>
          </div> -->
          <div class="ml-32">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="案例名称">
                <el-input v-model="searchParams.caseName" style="width: 192px;" placeholder="请输入案例名称" clearable />
              </el-form-item>
              <el-form-item label="领域类型">
                <el-select v-model="searchParams.areaType" style="width: 192px;" placeholder="请选择领域类型" clearable>
                  <el-option label="行业监管" value="INDUSTRY_REGULATION" />
                  <el-option label="公司治理" value="CORPORATE_GOVERNANCE" />
                  <el-option label="业务运营" value="BUSINESS_OPERATIONS" />
                  <el-option label="财务税务" value="FINANCE_TAXATION" />
                </el-select>
              </el-form-item>
              <el-form-item label="案例来源">
                <el-select v-model="searchParams.caseSource" style="width: 192px;" placeholder="请选择案例来源" clearable>
                  <el-option label="监管处罚" value="REGULATORY_PENALTIES" />
                  <el-option label="司法判例" value="JUDICIAL_PRECEDENTS" />
                  <el-option label="行业案例" value="INDUSTRY" />
                  <el-option label="内部案例" value="INTERNAL" />
                </el-select>
              </el-form-item>
              <el-form-item label="风险等级">
                <el-select v-model="searchParams.level" style="width: 192px;" placeholder="请选择风险等级" clearable>
                  <el-option label="一般风险" value="GENERAL" />
                  <el-option label="重大风险" value="MAJOR" />
                  <el-option label="典型风险" value="TYPICAL" />
                  <el-option label="安全" value="SAFE" />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="发生日期">
                <el-date-picker
                  v-model="searchParams.occurDateStart"
                  type="date"
                  placeholder="开始日期"
                  style="width: 192px;"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>
              <el-form-item label="至">
                <el-date-picker
                  v-model="searchParams.occurDateEnd"
                  type="date"
                  placeholder="结束日期"
                  style="width: 192px;"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item> -->
              <el-form-item>
                <el-button v-auth="'complianceCaseLibrary/index/search'" type="primary" @click="handleSearch">
                  <template #icon>
                    <svg-icon name="ep:search" />
                  </template>
                  搜索
                </el-button>
                <el-button v-auth="'complianceCaseLibrary/index/reset'" @click="handleReset">
                  重置
                </el-button>
                <!-- <el-button v-auth="'complianceCaseLibrary/index/advancedFilter'">
                  <svg-icon name="ep:filter" />
                  <span class="ml-4">高级筛选</span>
                </el-button> -->
              </el-form-item>
            </el-form>
          </div>
          <div>
            <el-button v-auth="'complianceCaseLibrary/index/add'" type="primary" @click="goAddEdit(null)">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增案例</span>
            </el-button>
          </div>
        </div>
      </template>
    </PageHeader>
    <!-- 合规案例库 -->
    <PageMain style="background-color: transparent;">
      <div class="mt-20 pr-20">
        <LayoutContainer
          style="padding: 0;" :enable-left-side="false" :enable-right-side="true"
          :right-side-width="476"
        >
          <div>
            <!-- 列表视图 -->
            <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="caseName" label="案例名称" min-width="200">
                <template #default="{ row }">
                  <el-link type="primary" @click="goDetail(row)">
                    {{ row.caseName }}
                  </el-link>
                </template>
              </el-table-column>

              <el-table-column prop="summary" label="案例描述" min-width="300">
                <template #default="{ row }">
                  <div class="f-14 line-clamp-2 c-[#666]">
                    {{ row.summary }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="riskLevel" label="风险等级" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="row.riskLevel === '重大风险' ? 'danger' : row.riskLevel === '典型风险' ? 'warning' : 'info'"
                    size="small"
                  >
                    {{ row.riskLevel }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="category" label="业务领域" width="120">
                <template #default="{ row }">
                  <el-tag type="primary" size="small" plain>
                    {{ row.category }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="createTime" label="创建时间" width="120">
                <template #default="{ row }">
                  <div class="f-14 c-[#666]">
                    {{ row.createdAt }}
                  </div>
                </template>
              </el-table-column>

              <!-- <el-table-column label="统计" width="120">
                <template #default="{ row }">
                  <div class="flex items-center c-[#999] space-x-4">
                    <div class="flex items-center">
                      <svg-icon name="ep:view" class="text-sm" />
                      <span class="f-12 ml-1">{{ row.viewCount }}</span>
                    </div>
                    <div class="flex items-center">
                      <svg-icon name="ep:comment" class="text-sm" />
                      <span class="f-12 ml-1">{{ row.commentCount }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column> -->

              <el-table-column label="操作" width="120" fixed="right">
                <template #default="{ row }">
                  <div class="flex items-center space-x-2">
                    <el-button
                      v-auth="'complianceCaseLibrary/index/edit'"
                      type="primary"
                      link
                      size="small"
                      @click.stop="goAddEdit(row)"
                    >
                      编辑
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;" @pag-change="pagChange" />
            <!-- <centerContent></centerContent> -->
          </div>
        </LayoutContainer>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  // .card {
  //   background: #fff;
  //   border-radius: 4px;
  //   box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  // }

  // .conBox {
  //   :deep(.main) {
  //     background-color: transparent;

  //     .main-container {
  //       padding: 0 !important;
  //     }

  //     .el-slider__button-wrapper {
  //       display: none;
  //     }

  //     .el-slider__runway.is-disabled .el-slider__bar {
  //       height: 8px;
  //       background: #4caf50;
  //       border-radius: 9999px;
  //     }
  //   }

  //   :deep(.flex-container) {
  //     padding-right: 40px !important;
  //   }
  // }
</style>
