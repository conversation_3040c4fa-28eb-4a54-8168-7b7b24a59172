<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  Bottom,
  CircleCheck,
  Clock,
  Document,
  Loading,
  MagicStick,
  Plus,
  Search,
  Star,
  Top,
  Upload,
  Warning,
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'
import measureApi from '@/api/problemTask/measure'
import dictApi from '@/api/modules/system/dict'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

const router = useRouter()

// 字典选项
interface DictOption {
  label: string
  value: string
}
const _improveTypeOptions = ref<DictOption[]>([])

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取措施类型选项 (31)
    const typeResponse = await dictApi.dictAll(31)
    if (typeResponse) {
      _improveTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 根据字典值获取显示文本
function getImproveTypeLabel(value: string) {
  const option = _improveTypeOptions.value.find(item => item.value === value)
  return option ? option.label : value
}

// 表格数据
const tableData = ref<any[]>([])

// 分页参数
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 筛选字段
const filterForm = ref({
  improveName: null,
  improveCode: null,
  improveType: null,
  improveSource: null,
  level: null,
  dutyEmployeeId: null,
  dutyEmployeeOrgId: null,
  status: null,
  createdAtStart: null,
  createdAtEnd: null,
})

// 日期范围绑定变量
const dateRange = ref<[Date, Date] | null>(null)

// 获取列表数据
async function fetchTableData() {
  try {
    const params = {
      page: pagination.value.page - 1, // 后端从0开始
      size: pagination.value.size,
      improveName: filterForm.value.improveName,
      improveCode: filterForm.value.improveCode,
      improveType: filterForm.value.improveType,
      improveSource: filterForm.value.improveSource,
      level: filterForm.value.level,
      dutyEmployeeId: filterForm.value.dutyEmployeeId,
      dutyEmployeeOrgId: filterForm.value.dutyEmployeeOrgId,
      status: filterForm.value.status,
      createdAtStart: filterForm.value.createdAtStart,
      createdAtEnd: filterForm.value.createdAtEnd,
    }
    const response = await measureApi.getResponsibilityMeasuresList(params)
    if (response && response.content) {
      tableData.value = response.content
      pagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取改进措施列表失败:', error)
  }
}

// 新增改进措施
function handleAddMeasure() {
  router.push({
    path: '/respond/improveAndOptimize/improvementMeasures/addEdit',
  })
}

function editPolicy(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/improvementMeasures/addEdit',
    query: {
      id: row.id,
    },
  })
}
function viewPolicy(row: any) {
  router.push({
    path: '/respond/improveAndOptimize/improvementMeasures/detail',
    query: {
      id: row.id,
    },
  })
}
function deletePolicy(row: any) {
  ElMessageBox.confirm(
    '确定要删除这条持续改进措施吗？删除后将无法恢复。',
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    },
  ).then(async () => {
    try {
      await measureApi.deleteImprove(row.id)
      ElMessage.success('删除成功')
      // 重新加载列表
      fetchTableData()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 页码变化
function handlePageChange(page: number) {
  pagination.value.page = page
  fetchTableData()
}

// 页大小变化
function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.page = 1
  fetchTableData()
}

// 查询
function queryList() {
  pagination.value.page = 1
  fetchTableData()
}

// 重置搜索
function resetList() {
  filterForm.value = {
    improveName: null,
    improveCode: null,
    improveType: null,
    improveSource: null,
    level: null,
    dutyEmployeeId: null,
    dutyEmployeeOrgId: null,
    status: null,
    createdAtStart: null,
    createdAtEnd: null,
  }
  dateRange.value = null
  pagination.value.page = 1
  fetchTableData()
}

// 日期范围处理
function handleDateRangeChange(dateRangeValue: any) {
  dateRange.value = dateRangeValue
  if (dateRangeValue && dateRangeValue.length === 2) {
    // 格式化日期为 YYYY-MM-DD 格式
    const startDate = new Date(dateRangeValue[0])
    const endDate = new Date(dateRangeValue[1])
    filterForm.value.createdAtStart = startDate.toISOString().split('T')[0]
    filterForm.value.createdAtEnd = endDate.toISOString().split('T')[0]
  }
  else {
    filterForm.value.createdAtStart = null
    filterForm.value.createdAtEnd = null
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDictOptions()
  fetchTableData()
})
// 视图类型
const viewType = ref<'table' | 'card' | 'gantt'>('table')
const _ganttDays = computed(() => {
  const days = []
  const today = new Date()
  const startDate = new Date(today)
  startDate.setDate(today.getDate() - 15)
  const endDate = new Date(today)
  endDate.setDate(today.getDate() + 45)
  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    days.push({
      date: `${currentDate.getMonth() + 1}/${currentDate.getDate()}`,
      day: ['日', '一', '二', '三', '四', '五', '六'][currentDate.getDay()],
      isToday: currentDate.toDateString() === today.toDateString(),
    })
    currentDate.setDate(currentDate.getDate() + 1)
  }
  return days
})
const draggingItem = ref<any>(null)

function getDaysLeft(endDate: string) {
  const end = new Date(endDate)
  const now = new Date()
  const diffTime = end.getTime() - now.getTime()
  return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)))
}

const updateDialogVisible = ref(false)
const currentItem = ref<any>(null)
const updateForm = ref({
  progress: 0,
  progressNote: '',
  problems: '',
  solutions: '',
  attachments: [],
})
function showUpdateDialog(item: any) {
  currentItem.value = item
  updateForm.value = {
    progress: item.progress,
    progressNote: '',
    problems: '',
    solutions: '',
    attachments: [],
  }
  updateDialogVisible.value = true
}
function handleUpdateSubmit() {
  const item = currentItem.value
  if (item) {
    item.progress = updateForm.value.progress
    if (item.progress === 100) {
      item.status = '已完成'
    }
    updateDialogVisible.value = false
  }
}
const filterTypes = ref({
  process: true,
  system: true,
  position: true,
  tech: true,
  training: true,
  other: true,
})

function toggleAllTypes() {
  const allSelected = Object.values(filterTypes.value).every(v => v)
  Object.keys(filterTypes.value).forEach((key) => {
    filterTypes.value[key as keyof typeof filterTypes.value] = !allSelected
  })
}

const templates = ref([
  {
    name: '数据安全漏洞修复',
    scenario: '适用于系统安全漏洞修复类改进措施',
    content: '...',
  },
  {
    name: '合规培训计划更新',
    scenario: '适用于年度合规培训计划更新',
    content: '...',
  },
  {
    name: '流程优化标准模板',
    scenario: '适用于各类业务流程优化措施',
    content: '...',
  },
])

function applyTemplate(_template: any) {
  // 应用模板逻辑
}

const departmentStats = ref([
  {
    department: '合规部',
    total: 57,
    completed: 15,
    completionRate: 26,
    effectivenessRate: 82,
  },
  {
    department: '技术部',
    total: 32,
    completed: 8,
    completionRate: 25,
    effectivenessRate: 75,
  },
  {
    department: '人力资源',
    total: 19,
    completed: 6,
    completionRate: 32,
    effectivenessRate: 85,
  },
])

const efficiencyRanking = ref([
  {
    rank: 1,
    owner: '李华',
    count: 12,
    completionRate: '92%',
    avgTime: '18天',
    isCurrentUser: false,
  },
  {
    rank: 2,
    owner: '张明',
    count: 8,
    completionRate: '88%',
    avgTime: '22天',
    isCurrentUser: true,
  },
  {
    rank: 3,
    owner: '王芳',
    count: 10,
    completionRate: '85%',
    avgTime: '25天',
    isCurrentUser: false,
  },
])

const aiSuggestion = ref('')

function analyzeDelayRisk() {
  aiSuggestion.value = 'AI分析：当前有3项措施存在延期风险，建议优先处理"数据安全漏洞修复"和"反洗钱系统升级"两项高优先级措施。'
}

function generateProgressReport() {
  aiSuggestion.value = 'AI已生成进度报告：本月共完成15项改进措施，总体完成率65%，较上月提升5%。'
}

function suggestPriorityMeasures() {
  aiSuggestion.value = 'AI建议：根据当前进度和优先级，建议优先处理"数据安全漏洞修复"(技术部)和"客户投诉处理流程优化"(市场部)。'
}

// 其他数据变量
const activeAnalysisTab = ref('source')
const trendViewType = ref('count')
const analysisData = ref([
  {
    source: '内部审计',
    count: 56,
    percentage: '43.8%',
  },
  {
    source: '年度计划',
    count: 32,
    percentage: '25.0%',
  },
  {
    source: '客户反馈',
    count: 18,
    percentage: '14.1%',
  },
  {
    source: '法规变更',
    count: 12,
    percentage: '9.4%',
  },
  {
    source: '管理层要求',
    count: 10,
    percentage: '7.8%',
  },
])
const departmentAnalysisData = ref<any[]>([])
const trendAnalysisData = ref<any[]>([])
const effectivenessAnalysisData = ref<any[]>([])
const priorityTasks = ref<any[]>([])

// 图表相关
const sourceChart = ref<HTMLElement>()
const departmentChart = ref<HTMLElement>()
const trendChart = ref<HTMLElement>()
const effectivenessChart = ref<HTMLElement>()

function _initTrendChart() {
  if (!trendChart.value) { return }
  const chart = echarts.init(trendChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['完成数量', '总数量'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendAnalysisData.value.map(item => item.period),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '完成数量',
        type: 'line',
        stack: 'total',
        data: trendAnalysisData.value.map(item => item.completedCount),
      },
      {
        name: '总数量',
        type: 'line',
        stack: 'total',
        data: trendAnalysisData.value.map(item => item.totalCount),
      },
    ],
  }
  chart.setOption(option)
}
function _initEffectivenessChart() {
  if (!effectivenessChart.value) { return }
  const chart = echarts.init(effectivenessChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['有效', '部分有效', '无效'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: effectivenessAnalysisData.value.map(item => item.type),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '有效',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          position: 'inside',
        },
        emphasis: {
          focus: 'series',
        },
        data: effectivenessAnalysisData.value.map(item => item.effective),
      },
      {
        name: '部分有效',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          position: 'inside',
        },
        emphasis: {
          focus: 'series',
        },
        data: effectivenessAnalysisData.value.map(item => item.partiallyEffective),
      },
      {
        name: '无效',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
          position: 'inside',
        },
        emphasis: {
          focus: 'series',
        },
        data: effectivenessAnalysisData.value.map(item => item.ineffective),
      },
    ],
  }
  chart.setOption(option)
}
// onMounted(() => {
//   nextTick(() => {
//     initSourceChart()
//     initDepartmentChart()
//     initTrendChart()
//     initEffectivenessChart()
//   })
// })

// watch(activeAnalysisTab, (newVal) => {
//   nextTick(() => {
//     if (newVal === 'source') { initSourceChart() }
//     if (newVal === 'department') { initDepartmentChart() }
//     if (newVal === 'trend') { initTrendChart() }
//     if (newVal === 'effectiveness') { initEffectivenessChart() }
//   })
// })

watch(trendViewType, () => {
  if (activeAnalysisTab.value === 'trend') {
    _initTrendChart()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              改进措施管理
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['improvementMeasures/index/addMeasure']" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddMeasure">
              新增
            </el-button>
          </div>
          <div v-if="false" class="flex space-x-3">
            <button
              v-auth="['improvementMeasures/index/addMeasure']"
              class="!rounded-button whitespace-nowrap bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700"
            >
              <el-icon class="mr-1">
                <Plus />
              </el-icon>新增改进措施
            </button>
            <button
              v-auth="['improvementMeasures/index/batchImport']"
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
            >
              批量导入
            </button>
            <button
              v-auth="['improvementMeasures/index/export']"
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
            >
              导出
            </button>
            <button
              v-auth="['improvementMeasures/index/statisticalAnalysis']"
              class="!rounded-button whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
            >
              统计分析
            </button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="!mx-0">
          <el-col :span="24">
            <!-- 统计卡片区 -->
            <div class="grid grid-cols-5 mb-6 gap-4">
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-500">
                      总措施数
                    </p>
                    <p class="mt-1 text-2xl font-bold">
                      128
                    </p>
                  </div>
                  <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                    <el-icon class="text-blue-500">
                      <Document />
                    </el-icon>
                  </div>
                </div>
                <div class="mt-2 flex items-center text-xs text-green-500">
                  <el-icon class="mr-1">
                    <Top />
                  </el-icon>12% 同比上升
                </div>
              </div>
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-500">
                      未开始数
                    </p>
                    <p class="mt-1 text-2xl font-bold">
                      24
                    </p>
                  </div>
                  <div class="h-10 w-10 flex items-center justify-center rounded-full bg-gray-100">
                    <el-icon class="text-gray-500">
                      <Clock />
                    </el-icon>
                  </div>
                </div>
                <div class="mt-2 flex items-center text-xs text-green-500">
                  <el-icon class="mr-1">
                    <Bottom />
                  </el-icon>8% 同比下降
                </div>
              </div>
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-500">
                      进行中数
                    </p>
                    <p class="mt-1 text-2xl font-bold">
                      68
                    </p>
                  </div>
                  <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                    <el-icon class="text-blue-500">
                      <Loading />
                    </el-icon>
                  </div>
                </div>
                <div class="mt-2 flex items-center text-xs text-green-500">
                  <el-icon class="mr-1">
                    <Top />
                  </el-icon>15% 同比上升
                </div>
              </div>
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-500">
                      已完成数
                    </p>
                    <p class="mt-1 text-2xl font-bold">
                      32
                    </p>
                  </div>
                  <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
                    <el-icon class="text-green-500">
                      <CircleCheck />
                    </el-icon>
                  </div>
                </div>
                <div class="mt-2 flex items-center text-xs text-green-500">
                  <el-icon class="mr-1">
                    <Top />
                  </el-icon>5% 同比上升
                </div>
              </div>
              <div class="rounded-lg bg-white p-4 shadow-sm">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-500">
                      已逾期数
                    </p>
                    <p class="mt-1 text-2xl font-bold">
                      4
                    </p>
                  </div>
                  <div class="h-10 w-10 flex items-center justify-center rounded-full bg-red-100">
                    <el-icon class="text-red-500">
                      <Warning />
                    </el-icon>
                  </div>
                </div>
                <div class="mt-2 flex items-center text-xs text-red-500">
                  <el-icon class="mr-1">
                    <Bottom />
                  </el-icon>20% 同比下降
                </div>
              </div>
            </div>
            <!-- 筛选区 -->
            <el-card shadow="hover" class="mb-6 !rounded-lg">
              <div class="mb-4 flex items-center space-x-4">
                <el-select v-model="filterForm.improveType" placeholder="措施类型" class="w-48">
                  <el-option label="全部" value="" />
                  <el-option v-for="(item, index) in _improveTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <el-select v-model="filterForm.level" placeholder="优先级" class="w-48">
                  <el-option label="全部" value="" />
                  <el-option label="低" value="LOW" />
                  <el-option label="中" value="MIDDLE" />
                  <el-option label="高" value="HIGH" />
                </el-select>
                <DepartmentTreeSelect
                  v-model="filterForm.dutyEmployeeOrgId"
                  placeholder="责任部门"
                  width="192px"
                  clearable
                />
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="w-64"
                  @change="handleDateRangeChange"
                />
                <el-input v-model="filterForm.improveName" placeholder="关键字搜索" class="w-48">
                  <template #prefix>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </div>
              <div class="flex justify-end">
                <!-- v-auth="['improvementMeasures/index/query']" -->
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="queryList">
                  查询
                </el-button>
                <!-- v-auth="['improvementMeasures/index/reset']" -->
                <el-button class="!rounded-button whitespace-nowrap" @click="resetList">
                  重置
                </el-button>
              </div>
            </el-card>
            <!-- 改进措施列表区 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <div class="mb-4 flex items-center justify-between">
                <div class="flex space-x-2">
                  <button
                    v-auth="['improvementMeasures/index/tableView']"
                    :class="{ 'bg-blue-500 text-white': viewType === 'table', 'border': viewType !== 'table' }"
                    class="rounded px-3 py-1 text-sm"
                    @click="viewType = 'table'"
                  >
                    表格视图
                  </button>
                  <button
                    v-auth="['improvementMeasures/index/cardView']"
                    :class="{ 'bg-blue-500 text-white': viewType === 'card', 'border': viewType !== 'card' }"
                    class="rounded px-3 py-1 text-sm"
                    @click="viewType = 'card'"
                  >
                    卡片视图
                  </button>
                  <button
                    v-auth="['improvementMeasures/index/ganttChart']"
                    :class="{ 'bg-blue-500 text-white': viewType === 'gantt', 'border': viewType !== 'gantt' }"
                    class="rounded px-3 py-1 text-sm"
                    @click="viewType = 'gantt'"
                  >
                    甘特图
                  </button>
                </div>
              </div>
              <!-- 表格视图 -->
              <el-table v-if="viewType === 'table'" :data="tableData" style="width: 100%;">
                <!-- 卡片视图 -->
                <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
                  <div
                    v-for="item in tableData" :key="item.id"
                    class="flex flex-col border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
                  >
                    <div class="mb-3 flex items-start justify-between">
                      <h4 class="text-gray-800 font-medium">
                        {{ item.improveName }}
                      </h4>
                      <span
                        :class="{
                          'bg-red-100 text-red-800': item.level === 'HIGH',
                          'bg-orange-100 text-orange-800': item.level === 'MIDDLE',
                          'bg-green-100 text-green-800': item.level === 'LOW',
                        }" class="rounded px-2 py-1 text-xs"
                      >
                        {{ item.level === 'HIGH' ? '高' : item.level === 'MIDDLE' ? '中' : '低' }}
                      </span>
                    </div>
                    <div class="mb-2 flex items-center text-sm text-gray-600">
                      <span class="mr-2 rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">{{ item.improveType }}</span>
                      <span>{{ item.dutyEmployeeOrgId }} · {{ item.dutyEmployeeId }}</span>
                    </div>
                    <div class="mb-3">
                      <div class="mb-1 flex items-center justify-between text-xs text-gray-500">
                        <span>进度</span>
                        <span>{{ item.progress }}%</span>
                      </div>
                      <div class="h-1 rounded-full bg-gray-200">
                        <div
                          :class="{
                            'bg-blue-500': item.status === '进行中',
                            'bg-green-500': item.status === '已完成',
                            'bg-gray-500': item.status === '未开始',
                            'bg-red-500': item.status === '已逾期',
                          }" class="h-1 rounded-full" :style="`width: ${item.progress}%`"
                        />
                      </div>
                    </div>
                    <div class="mt-auto">
                      <div class="mb-3 flex items-center justify-between text-sm">
                        <div>
                          <span class="text-gray-500">截止日期:</span>
                          <span class="ml-1">{{ item.finishDate }}</span>
                          <span
                            :class="{
                              'text-red-500': getDaysLeft(item.finishDate) <= 7,
                              'text-orange-500': getDaysLeft(item.finishDate) > 7 && getDaysLeft(item.finishDate) <= 14,
                              'text-gray-500': getDaysLeft(item.finishDate) > 14,
                            }" class="ml-2"
                          >
                            (剩余{{ getDaysLeft(item.finishDate) }}天)
                          </span>
                        </div>
                        <span
                          :class="{
                            'bg-gray-100 text-gray-800': item.status === '未开始',
                            'bg-blue-100 text-blue-800': item.status === '进行中',
                            'bg-green-100 text-green-800': item.status === '已完成',
                            'bg-red-100 text-red-800': item.status === '已逾期',
                          }" class="rounded px-2 py-1 text-xs"
                        >
                          {{ item.status }}
                        </span>
                      </div>
                      <div class="flex items-center justify-between border-t pt-3">
                        <el-button v-auth="['improvementMeasures/index/view']" size="small" class="text-blue-500">
                          <el-icon class="mr-1">
                            <view />
                          </el-icon>查看
                        </el-button>
                        <el-button v-auth="['improvementMeasures/index/edit']" size="small" class="text-green-500">
                          <el-icon class="mr-1">
                            <edit />
                          </el-icon>编辑
                        </el-button>
                        <el-button v-auth="['improvementMeasures/index/updateProgress']" size="small" class="text-orange-500" @click="showUpdateDialog(item)">
                          <el-icon class="mr-1">
                            <Upload />
                          </el-icon>更新
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>

                <el-table-column type="selection" width="40" />
                <el-table-column prop="improveName" label="措施名称" width="180" />
                <el-table-column prop="improveType" label="措施类型" width="120">
                  <template #default="{ row }">
                    <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">
                      {{ getImproveTypeLabel(row.improveType) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="level" label="优先级" width="100">
                  <template #default="{ row }">
                    <span v-if="row.level === 'HIGH'" class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">高</span>
                    <span
                      v-else-if="row.level === 'MIDDLE'"
                      class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
                    >中</span>
                    <span v-else class="rounded bg-green-100 px-2 py-1 text-xs text-green-800">低</span>
                  </template>
                </el-table-column>
                <el-table-column prop="improveSource" label="来源" width="120">
                  <template #default="{ row }">
                    {{ row.improveSource === 'INTERNAL_AUDIT' ? '内部审计'
                      : row.improveSource === 'ANNUAL_PLAN' ? '年度计划'
                        : row.improveSource === 'CUSTOMER_FEEDBACK' ? '客户反馈'
                          : row.improveSource === 'REGULATORY_CHANGE' ? '法规变更'
                            : row.improveSource === 'MANAGEMENT_REQUIREMENT' ? '管理层要求' : row.improveSource }}
                  </template>
                </el-table-column>
                <el-table-column prop="dutyEmployeeOrgId" label="责任部门" width="120" />
                <el-table-column prop="dutyEmployeeId" label="负责人" width="120" />
                <el-table-column prop="startDate" label="开始日期" width="120" />
                <el-table-column prop="finishDate" label="计划完成日期" width="120" />
                <!-- <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <span
                      v-if="row.status === 'NO_START'"
                      class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800"
                    >未开始</span>
                    <span
                      v-else-if="row.status === 'PROGRESSING'"
                      class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
                    >进行中</span>
                    <span
                      v-else-if="row.status === 'FINISHED'"
                      class="rounded bg-green-100 px-2 py-1 text-xs text-green-800"
                    >已完成</span>
                    <span
                      v-else-if="row.status === 'PAUSED'"
                      class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
                    >已暂停</span>
                    <span
                      v-else-if="row.status === 'CANCELED'"
                      class="rounded bg-red-100 px-2 py-1 text-xs text-red-800"
                    >已取消</span>
                    <span v-else class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">{{ row.status }}</span>
                  </template>
                </el-table-column> -->
                <!-- <el-table-column prop="result" label="评估结果" width="120">
                  <template #default="{ row }">
                    <span
                      v-if="row.result === '有效'"
                      class="rounded bg-green-100 px-2 py-1 text-xs text-green-800"
                    >有效</span>
                    <span
                      v-else-if="row.result === '部分有效'"
                      class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800"
                    >部分有效</span>
                    <span
                      v-else-if="row.result === '无效'"
                      class="rounded bg-red-100 px-2 py-1 text-xs text-red-800"
                    >无效</span>
                    <span v-else class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-800">待评估</span>
                  </template>
                </el-table-column> -->
                <el-table-column label="操作" width="210" align="right">
                  <template #default="{ row }">
                    <el-button v-auth="['improvementMeasures/index/view']" type="primary" size="small" @click="viewPolicy(row)">
                      查看
                    </el-button>
                    <el-button v-auth="['improvementMeasures/index/edit']" type="warning" size="small" @click="editPolicy(row)">
                      编辑
                    </el-button>
                    <el-button v-auth="['improvementMeasures/index/delete']" type="danger" size="small" @click="deletePolicy(row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-500">
                  显示 {{ (pagination.page - 1) * pagination.size + 1 }} 到 {{ Math.min(pagination.page * pagination.size, pagination.total) }} 条，共 {{ pagination.total }} 条
                </div>
                <el-pagination
                  :current-page="pagination.page"
                  :page-size="pagination.size"
                  :total="pagination.total"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @current-change="handlePageChange"
                  @size-change="handleSizeChange"
                />
              </div>
              <!-- 更新进度弹窗 -->
              <el-dialog v-model="updateDialogVisible" title="改进进度更新" width="600px">
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <p class="text-sm text-gray-500">
                        措施名称
                      </p>
                      <p class="font-medium">
                        {{ currentItem?.name }}
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        责任部门
                      </p>
                      <p class="font-medium">
                        {{ currentItem?.department }}
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        负责人
                      </p>
                      <p class="font-medium">
                        {{ currentItem?.owner }}
                      </p>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">
                        当前状态
                      </p>
                      <p class="font-medium">
                        {{ currentItem?.status }}
                      </p>
                    </div>
                  </div>
                  <div>
                    <p class="mb-2 text-sm text-gray-500">
                      当前进度
                    </p>
                    <div class="flex items-center space-x-4">
                      <el-slider v-model="updateForm.progress" :step="5" show-stops />
                      <span class="w-12 text-center">{{ updateForm.progress }}%</span>
                    </div>
                  </div>
                  <div>
                    <p class="mb-2 text-sm text-gray-500">
                      进度说明
                    </p>
                    <el-input
                      v-model="updateForm.progressNote" type="textarea" :rows="3"
                      placeholder="请简要描述当前完成的工作内容"
                    />
                  </div>
                  <div>
                    <p class="mb-2 text-sm text-gray-500">
                      遇到的问题
                    </p>
                    <el-input v-model="updateForm.problems" type="textarea" :rows="3" placeholder="请描述在实施过程中遇到的问题" />
                  </div>
                  <div>
                    <p class="mb-2 text-sm text-gray-500">
                      解决方案
                    </p>
                    <el-input v-model="updateForm.solutions" type="textarea" :rows="3" placeholder="请描述针对问题的解决方案或计划" />
                  </div>
                  <div>
                    <p class="mb-2 text-sm text-gray-500">
                      上传附件
                    </p>
                    <el-upload
                      v-model:file-list="updateForm.attachments" action="#" multiple :limit="3"
                      :on-exceed="handleExceed" :auto-upload="false"
                    >
                      <el-button v-auth="['improvementMeasures/index/selectFile']" type="primary" size="small">
                        <el-icon class="mr-1">
                          <Upload />
                        </el-icon>选择文件
                      </el-button>
                      <template #tip>
                        <div class="el-upload__tip text-xs text-gray-500">
                          支持上传文档、图片等附件，单个文件不超过10MB
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </div>
                <template #footer>
                  <div class="flex justify-end space-x-3">
                    <el-button v-auth="['improvementMeasures/index/cancel']" @click="updateDialogVisible = false">
                      取消
                    </el-button>
                    <el-button v-auth="['improvementMeasures/index/submit']" type="primary" @click="handleUpdateSubmit">
                      提交
                    </el-button>
                  </div>
                </template>
              </el-dialog>
            </div>
            <!-- 改进措施分析区 -->
            <div v-if="false" class="mt-6 rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 font-bold">
                改进措施分析
              </h3>
              <div class="flex border-b">
                <div
                  v-auth="['improvementMeasures/index/sourceDistribution']"
                  class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'source' }"
                  @click="activeAnalysisTab = 'source'"
                >
                  来源分布
                </div>
                <div
                  v-auth="['improvementMeasures/index/departmentDistribution']"
                  class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'department' }"
                  @click="activeAnalysisTab = 'department'"
                >
                  部门分布
                </div>
                <div
                  v-auth="['improvementMeasures/index/completionTrend']"
                  class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'trend' }"
                  @click="activeAnalysisTab = 'trend'"
                >
                  完成趋势
                </div>
                <div
                  v-auth="['improvementMeasures/index/effectivenessAnalysis']"
                  class="cursor-pointer px-4 py-2" :class="{ 'border-b-2 border-blue-500 text-blue-500': activeAnalysisTab === 'effectiveness' }"
                  @click="activeAnalysisTab = 'effectiveness'"
                >
                  有效性分析
                </div>
              </div>
              <!-- 来源分布 -->
              <div v-if="activeAnalysisTab === 'source'" class="grid grid-cols-3 mt-4 gap-4">
                <div class="col-span-2">
                  <div ref="sourceChart" class="h-64 w-full" />
                </div>
                <div>
                  <el-table :data="analysisData" style="width: 100%;">
                    <el-table-column prop="source" label="来源" />
                    <el-table-column prop="count" label="数量" />
                    <el-table-column prop="percentage" label="占比" />
                  </el-table>
                </div>
              </div>
              <!-- 部门分布 -->
              <div v-if="activeAnalysisTab === 'department'" class="grid grid-cols-3 mt-4 gap-4">
                <div class="col-span-2">
                  <div ref="departmentChart" class="h-64 w-full" />
                </div>
                <div>
                  <el-table :data="departmentAnalysisData" style="width: 100%;">
                    <el-table-column prop="department" label="部门" />
                    <el-table-column prop="notStarted" label="未开始" />
                    <el-table-column prop="inProgress" label="进行中" />
                    <el-table-column prop="completed" label="已完成" />
                    <el-table-column prop="overdue" label="已逾期" />
                    <el-table-column prop="total" label="总计" />
                  </el-table>
                </div>
              </div>
              <!-- 完成趋势 -->
              <div v-if="activeAnalysisTab === 'trend'" class="grid grid-cols-3 mt-4 gap-4">
                <div class="col-span-2">
                  <div class="mb-2 flex justify-end">
                    <el-radio-group v-model="trendViewType" size="small">
                      <el-radio-button v-auth="['improvementMeasures/index/quantityView']" label="count">
                        数量
                      </el-radio-button>
                      <el-radio-button v-auth="['improvementMeasures/index/completionRateView']" label="rate">
                        完成率
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                  <div ref="trendChart" class="h-64 w-full" />
                </div>
                <div>
                  <el-table :data="trendAnalysisData" style="width: 100%;">
                    <el-table-column prop="period" label="时间段" />
                    <el-table-column prop="completedCount" label="完成数量" />
                    <el-table-column prop="totalCount" label="总数量" />
                    <el-table-column prop="completionRate" label="完成率" />
                  </el-table>
                </div>
              </div>
              <!-- 有效性分析 -->
              <div v-if="activeAnalysisTab === 'effectiveness'" class="grid grid-cols-3 mt-4 gap-4">
                <div class="col-span-2">
                  <div ref="effectivenessChart" class="h-64 w-full" />
                </div>
                <div>
                  <el-table :data="effectivenessAnalysisData" style="width: 100%;">
                    <el-table-column prop="type" label="改进类型" />
                    <el-table-column prop="effective" label="有效" />
                    <el-table-column prop="partiallyEffective" label="部分有效" />
                    <el-table-column prop="ineffective" label="无效" />
                    <el-table-column prop="total" label="总计" />
                  </el-table>
                </div>
              </div>
            </div>
            <!-- 优先任务区 -->
            <div v-if="false" class="mt-6 rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 font-bold">
                优先处理任务
              </h3>
              <el-table :data="priorityTasks" style="width: 100%;">
                <el-table-column prop="urgency" label="紧急程度" width="120">
                  <template #default="{ row }">
                    <span v-if="row.urgency === '高'" class="rounded bg-red-100 px-2 py-1 text-xs text-red-800">高</span>
                    <span
                      v-else-if="row.urgency === '中'"
                      class="rounded bg-orange-100 px-2 py-1 text-xs text-orange-800"
                    >中</span>
                    <span v-else class="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800">低</span>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="措施名称" width="180" />
                <el-table-column prop="type" label="类型" width="120" />
                <el-table-column prop="department" label="责任部门" width="120" />
                <el-table-column prop="owner" label="负责人" width="120" />
                <el-table-column prop="endDate" label="截止日期" width="120" />
                <el-table-column prop="daysLeft" label="剩余天数" width="120">
                  <template #default="{ row }">
                    <span
                      :class="{ 'text-red-500': row.daysLeft <= 7, 'text-orange-500': row.daysLeft > 7 && row.daysLeft <= 14 }"
                    >
                      {{ row.daysLeft }}天
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="progress" label="进度" width="120">
                  <template #default="{ row }">
                    <div class="h-1 rounded-full bg-gray-200">
                      <div class="h-1 rounded-full bg-blue-500" :style="`width: ${row.progress}%`" />
                    </div>
                    <p class="mt-1 text-right text-xs">
                      {{ row.progress }}%
                    </p>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="120">
                  <template #default>
                    <el-button v-auth="['improvementMeasures/index/immediateProcess']" type="text" size="small" class="text-blue-500">
                      立即处理
                    </el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
          </el-col>
          <el-col v-if="false" :span="6">
            <!-- 措施类型筛选 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                措施类型
              </h3>
              <div class="space-y-3">
                <el-checkbox v-model="filterTypes.process">
                  流程改进
                </el-checkbox>
                <el-checkbox v-model="filterTypes.system">
                  制度改进
                </el-checkbox>
                <el-checkbox v-model="filterTypes.position">
                  岗位改进
                </el-checkbox>
                <el-checkbox v-model="filterTypes.tech">
                  系统改进
                </el-checkbox>
                <el-checkbox v-model="filterTypes.training">
                  培训改进
                </el-checkbox>
                <el-checkbox v-model="filterTypes.other">
                  其他
                </el-checkbox>
                <div class="pt-2">
                  <a href="#" class="text-sm text-blue-500" @click="toggleAllTypes">全选/取消全选</a>
                </div>
              </div>
            </div>

            <!-- 改进措施模板 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                常用模板
              </h3>
              <div class="space-y-3">
                <div v-for="(template, index) in templates" :key="index" class="border-b pb-3">
                  <h4 class="text-sm font-medium">
                    {{ template.name }}
                  </h4>
                  <p class="mt-1 text-xs text-gray-500">
                    {{ template.scenario }}
                  </p>
                  <el-button
                    v-auth="['improvementMeasures/index/applyTemplate']"
                    size="small" class="!rounded-button mt-2 whitespace-nowrap"
                    @click="applyTemplate(template)"
                  >
                    应用模板
                  </el-button>
                </div>
                <div class="pt-2">
                  <a href="#" class="text-sm text-blue-500">管理模板</a>
                </div>
              </div>
            </div>

            <!-- 部门完成情况 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                部门完成情况
              </h3>
              <el-table :data="departmentStats" style="width: 100%;" size="small">
                <el-table-column prop="department" label="部门" width="80" />
                <el-table-column prop="total" label="总数" width="60" />
                <el-table-column prop="completed" label="完成数" width="60" />
                <el-table-column label="完成率" width="100">
                  <template #default="{ row }">
                    <div class="h-1 rounded-full bg-gray-200">
                      <div class="h-1 rounded-full bg-blue-500" :style="`width: ${row.completionRate}%`" />
                    </div>
                    <p class="mt-1 text-right text-xs">
                      {{ row.completionRate }}%
                    </p>
                  </template>
                </el-table-column>
                <el-table-column label="有效率" width="100">
                  <template #default="{ row }">
                    <div class="h-1 rounded-full bg-gray-200">
                      <div class="h-1 rounded-full bg-green-500" :style="`width: ${row.effectivenessRate}%`" />
                    </div>
                    <p class="mt-1 text-right text-xs">
                      {{ row.effectivenessRate }}%
                    </p>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pt-2">
                <a href="#" class="text-sm text-blue-500">完整统计</a>
              </div>
            </div>

            <!-- 人员效率排名 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                人员效率排名
              </h3>
              <el-table :data="efficiencyRanking" style="width: 100%;" size="small">
                <el-table-column prop="rank" label="排名" width="50" />
                <el-table-column prop="owner" label="负责人" width="80">
                  <template #default="{ row }">
                    <span :class="{ 'text-blue-500 font-medium': row.isCurrentUser }">{{ row.owner }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="措施数" width="60" />
                <el-table-column prop="completionRate" label="完成率" width="80" />
                <el-table-column prop="avgTime" label="平均时间" width="80" />
              </el-table>
              <div class="pt-2">
                <a href="#" class="text-sm text-blue-500">完整排名</a>
              </div>
            </div>

            <!-- AI助手 -->
            <div class="rounded-lg bg-white p-4 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                AI助手
              </h3>
              <div class="space-y-3">
                <el-button v-auth="['improvementMeasures/index/analyzeDelayRisk']" class="!rounded-button w-full whitespace-nowrap" @click="analyzeDelayRisk">
                  <el-icon class="mr-1">
                    <MagicStick />
                  </el-icon>分析延期风险
                </el-button>
                <el-button v-auth="['improvementMeasures/index/generateProgressReport']" class="!rounded-button w-full whitespace-nowrap" @click="generateProgressReport">
                  <el-icon class="mr-1">
                    <Document />
                  </el-icon>生成进度报告
                </el-button>
                <el-button v-auth="['improvementMeasures/index/suggestPriorityMeasures']" class="!rounded-button w-full whitespace-nowrap" @click="suggestPriorityMeasures">
                  <el-icon class="mr-1">
                    <Star />
                  </el-icon>建议优先措施
                </el-button>
                <div v-if="aiSuggestion" class="mt-3 rounded bg-blue-50 p-3 text-sm">
                  {{ aiSuggestion }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 甘特图拖动样式 */
  .gantt-bar {
    cursor: move;
    user-select: none;
  }

  .gantt-bar:hover {
    opacity: 0.8;
  }
</style>
