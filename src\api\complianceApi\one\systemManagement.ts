import api from '@/api/index'

export default {
  // ai审查
  // 制度ai智能审查
  aiReview(id: any) {
    return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/ai/review/${id}`,
      {},
    )
  },
  // audit ai
  auditReview(params: any) {
    return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/review`,
      params,
    )
  },
  // 制度管理
  system(params: any, key: any) {
    switch (key) {
      case 'info': // 获取制度详情
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulations/get/${params.id}`, {
        })
      case 'create': // 创建制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/create`, params)
      case 'update': // 更新制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/update/${params.id}`, params)
      case 'publish': // 单个发布制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/publish/batch`, params)
      case 'publishBatch': // 批量发布制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/publish/batch`, params)
      case 'delete': // 删除制度
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulations/delete/${params.id}`)
      case 'import': // 导入制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/import${params.categoryId ? `?categoryId=${params.categoryId}` : ''}`, params.formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      case 'template': // 下载导入模板
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulations/template`, {
          responseType: 'blob',
        })
      case 'relatedLaws': // 获取关联法规详情
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulations/findAllLaws/${params.id}`, {
        })
      default: // 分页查询制度
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulations/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
    }
  },
  // 制度分类管理
  systemCategories(params: any = {}) {
    return api.get(`/whiskerguardregulatoryservice/api/enterprise/categories/tree`, { params })
  },

  // 法律法规
  lawsSystem(params: any, key: any) {
    switch (key) {
      case 'info': // 获取制度详情
        return api.get(`/whiskerguardregulatoryservice/api/laws/regulations/get/${params.id}`, {
        })
      case 'create': // 创建制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulations/create`, params)
      case 'update': // 更新制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulations/partialUpdate/${params.id}`, params)
      case 'publish': // 批量发布制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulations/publish/batch`, params)
      case 'delete': // 删除制度
        return api.delete(`/whiskerguardregulatoryservice/api/laws/regulations/${params.id}`)
      default: // 分页查询制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulations/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
    }
  },
  // 法规转化
  regulatoryConversion(params: any, key: any) {
    switch (key) {
      case 'info': // 获取制度详情
        return api.get(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/${params.id}`, {
        })
      case 'create': // 创建制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulation/transforms`, params)
      case 'update': // 更新制度
        return api.patch(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/${params.id}`, params)
      case 'delete': // 删除制度
        return api.delete(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/${params.id}`)
      default: // 分页查询制度
        return api.post(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
    }
  },
  // 审核法规转化
  auditRegulatoryConversion(params: any) {
    return api.get(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/audit/${params.id}/${params.isAudited}`, {
    })
  },

  // 法律法规分类管理
  categories(params: any, key: any) {
    switch (key) {
      case 'tree':
        return api.get(`/whiskerguardregulatoryservice/api/laws/categories/tree`, params)
      // default:
      //   return api.post(`/whiskerguardregulatoryservice/api/laws/regulation/transforms/page?page=${params.page ? params.page : 0}&size=${params.size ? params.size : 10}`, params)
    }
  },

  // 企业内部法规分类管理
  enterpriseCategories(params: any, key: any) {
    switch (key) {
      case 'tree':
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/categories/tree`, { })
      case 'create': // 创建分类
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/categories/create`, params)
      case 'update': // 更新分类
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/categories/partialUpdate/${params.id}`, params)
      case 'delete': // 删除分类
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/categories/delete/${params.id}`)
      case 'info': // 获取分类详情
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/categories/get/${params.id}`)
      default:
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/categories`, {
          params,
        })
    }
  },

  // 合规管理
  complianceSystem(params: any, key: any) {
    switch (key) {
      case 'info': // 合规管理详情
        return api.get(`/whiskerguardregulatoryservice/api/compliance/obligations/${params.id}`, {
        })
      case 'create': // 创建合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/obligations/create`, params)
      case 'update': // 更新合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/obligations/update/${params.id}`, params)
      case 'delete': // 删除合规管理
        return api.delete(`/whiskerguardregulatoryservice/api/compliance/obligations/${params.id}`)
      default: // 分页查询合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/obligations/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
    }
  },
  // 合规管理树形结构
  complianceTree(params: any) {
    return api.get(`/whiskerguardregulatoryservice/api/laws/categories/tree`, params)
  },
  // 案例树
  caseTree(params: any) {
    return api.get(`/whiskerguardregulatoryservice/api/laws/categories/tree`, params)
  },
  // 案例接口集合包含增删改查
  caseSystem(params: any, key: any) {
    switch (key) {
      case 'info': // 合规管理详情
        return api.get(`/whiskerguardregulatoryservice/api/compliance/cases/${params.id}`, {
        })
      case 'create': // 创建合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/cases/create`, params)
      case 'update': // 更新合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/cases/update/${params.id}`, params)
      case 'delete': // 删除合规管理
        return api.delete(`/whiskerguardregulatoryservice/api/compliance/cases/${params.id}`)
      default: // 分页查询合规管理
        return api.post(`/whiskerguardregulatoryservice/api/compliance/cases/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
    }
  },
  // 企业监管审核
  reviewSystem(params: any, key: any, paging: any) {
    switch (key) {
      case 'info': // 企业监管审核详情
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/${params.id}`, {
        })
      case 'create': // 创建企业监管审核
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/create`, params)
      case 'update': // 更新企业监管审核
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/process/${params.id}`, params)
      case 'delete': // 删除企业监管审核
        return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/delete/${params.id}`)
      default: // 分页查询企业监管审核
        return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/page?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },
  // 立即转化
  transform(params: any) {
    return api.post(`/whiskerguardregulatoryservice/api/laws/regulation/transforms`, params)
  },
  // 审查记录详情
  reviewDetail(id: any) {
    return api.get(`/whiskerguardregulatoryservice/api/enterprise/regulation/audit/contents/review/${id}`, {
    })
  },
  // 是否加入法规库
  isSubscribe(params: any) {
    return api.post(`/whiskerguardregulatoryservice/api/tenant/law/regulations`,
      params,
    )
  },
  // 删除订阅
  deleteSubscribe(regulationId: any) {
    return api.delete(`/whiskerguardregulatoryservice/api/tenant/law/regulations/${regulationId}`)
  },
  // 租户法规列表
  tenantRegulations(params: any) {
    return api.post(`/whiskerguardregulatoryservice/api/tenant/law/regulations/page?page=${params.page ? params.page - 1 : 0}&size=${params.limit ? params.limit : 10}`, params)
  },
  // 智能推荐法规列表
  intelligentRecommendations() {
    return api.get(`/whiskerguardregulatoryservice/api/laws/regulations/tenant`)
  },

  // ai智能分析
  aiAnalysis(params: any) {
    return api.post(`/whiskerguardregulatoryservice/api/enterprise/regulation/audits/ai/review`, params)
  },
}
