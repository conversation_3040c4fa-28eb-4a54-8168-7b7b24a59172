<script lang="ts" setup>
import {
  Edit,
  Lock,
  Setting,
} from '@element-plus/icons-vue'
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              个人资料
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['notificationsSettings/index/edit']" type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />编辑资料
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  个人资料
                </div>
              </template>
              <!-- 个人信息卡片 -->
              <!-- <div class="bg-white rounded-lg shadow-sm p-6"> -->
              <div class="flex space-x-6">
                <!-- 头像区域 -->
                <div class="flex flex-col items-center space-y-4">
                  <div class="h-24 w-24 overflow-hidden rounded-full bg-gray-200">
                    <img
                      src="https://ai-public.mastergo.com/ai/img_res/73e2c1061cbac3cebdf92730326f6566.jpg" alt="用户头像"
                      class="h-full w-full object-cover"
                    >
                  </div>
                  <button v-auth="['notificationsSettings/index/changeAvatar']" class="text-sm text-blue-500 hover:text-blue-600">
                    更换头像
                  </button>
                </div>

                <!-- 基本信息 -->
                <div class="grid grid-cols-2 flex-1 gap-4">
                  <div class="space-y-4">
                    <div>
                      <div class="text-sm text-gray-500">
                        账号
                      </div>
                      <div class="text-gray-800">
                        zhangsan
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        姓名
                      </div>
                      <div class="text-gray-800">
                        张三
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        性别
                      </div>
                      <div class="text-gray-800">
                        男
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        手机号码
                      </div>
                      <div class="text-gray-800">
                        138****5678
                      </div>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <div class="text-sm text-gray-500">
                        电子邮箱
                      </div>
                      <div class="text-gray-800">
                        <EMAIL>
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        所属部门
                      </div>
                      <div class="text-gray-800">
                        法务部
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        岗位
                      </div>
                      <div class="text-gray-800">
                        合规专员
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        入职日期
                      </div>
                      <div class="text-gray-800">
                        2023-01-15
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- </div> -->
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  账号信息
                </div>
              </template>
              <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      账号创建时间
                    </div>
                    <div class="text-gray-800">
                      2023-01-10
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      账号状态
                    </div>
                    <div class="text-gray-800">
                      正常
                    </div>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      最后登录时间
                    </div>
                    <div class="text-gray-800">
                      2024-04-28 10:30:15
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      最后登录IP
                    </div>
                    <div class="text-gray-800">
                      *************
                    </div>
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    最后登录设备
                  </div>
                  <div class="text-gray-800">
                    Chrome / Windows
                  </div>
                </div>
              </div>
              <div class="mt-4">
                <a href="#" class="text-sm text-blue-500 hover:text-blue-600">查看登录记录</a>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  补充信息
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    个人简介
                  </div>
                  <div class="text-gray-800">
                    张三，法务部合规专员，拥有3年合规管理经验，熟悉企业合规体系建设，擅长风险评估与合规培训。
                  </div>
                </div>
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    专业技能
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">合规管理</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">风险评估</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">法律文书</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">合规培训</span>
                  </div>
                </div>
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    兴趣爱好
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">阅读</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">羽毛球</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">旅行</span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      联系地址
                    </div>
                    <div class="text-gray-800">
                      上海市浦东新区张江高科技园区
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      紧急联系人
                    </div>
                    <div class="text-gray-800">
                      李四
                    </div>
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    紧急联系电话
                  </div>
                  <div class="text-gray-800">
                    138****1234
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  系统角色
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <div class="font-medium">
                    合规专员
                  </div>
                  <div class="mt-1 text-sm text-gray-500">
                    负责企业日常合规事务管理
                  </div>
                </div>
                <div>
                  <div class="font-medium">
                    培训讲师
                  </div>
                  <div class="mt-1 text-sm text-gray-500">
                    负责内部合规培训工作
                  </div>
                </div>
                <div>
                  <div class="font-medium">
                    风险评估员
                  </div>
                  <div class="mt-1 text-sm text-gray-500">
                    负责企业合规风险评估
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>修改密码</span>
                </a>
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Lock />
                  </el-icon>
                  <span>安全设置</span>
                </a>
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Setting />
                  </el-icon>
                  <span>通知设置</span>
                </a>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
</style>
