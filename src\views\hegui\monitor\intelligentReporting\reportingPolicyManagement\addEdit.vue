<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 表单数据
const policyForm = ref({
  name: '',
  code: '',
  type: '',
  scope: [],
  departments: [],
  positions: [],
  level: '1',
  startDate: '',
  endDate: '',
  isLongTerm: false,
  description: '',
  creator: '张合规',
  createTime: new Date().toLocaleString(),
  status: '草稿',
})
const isEditMode = ref(false)
const activeMenu = ref('3-4')
const statusTagType = computed(() => {
  switch (policyForm.value.status) {
    case '已发布': return 'success'
    case '已过期': return 'danger'
    default: return ''
  }
})
// 政策内容相关
const sections = ref([
  { title: '第一章 总则', content: '' },
  { title: '第二章 举报范围', content: '' },
  { title: '第三章 举报方式', content: '' },
])
const activeSection = ref('0')
const wordCount = computed(() => {
  return sections.value.reduce((total, section) => {
    return total + (section.content ? section.content.length : 0)
  }, 0)
})
// 关联法规相关
const lawCategories = ref([
  {
    id: '1',
    label: '国家法律法规',
    children: [
      { id: '1-1', label: '中华人民共和国公司法' },
      { id: '1-2', label: '中华人民共和国劳动法' },
      { id: '1-3', label: '中华人民共和国合同法' },
    ],
  },
  {
    id: '2',
    label: '行业规范',
    children: [
      { id: '2-1', label: '金融行业合规指引' },
      { id: '2-2', label: '互联网行业自律公约' },
    ],
  },
])
const defaultProps = {
  children: 'children',
  label: 'label',
}
const selectedLaws = ref([
  { name: '中华人民共和国公司法', org: '全国人大', date: '2018-10-26', desc: '' },
  { name: '金融行业合规指引', org: '银保监会', date: '2020-05-01', desc: '' },
])
// 附件相关
const attachments = ref([
  { name: '举报政策模板.docx', type: 'Word', size: '2.5MB', time: '2023-05-10 14:30', desc: '' },
  { name: '相关法规.pdf', type: 'PDF', size: '1.8MB', time: '2023-05-11 09:15', desc: '' },
])

// 发布设置
const publishSettings = ref({
  scope: ['1'],
  departments: [],
  positions: [],
  roles: [],
  visibility: '1',
  notification: ['1', '2'],
  importance: 3,
  publishTimeType: '1',
  scheduledTime: '',
})

// 审核流程
const reviewProcess = ref({
  requireReview: true,
  reviewers: ['1'],
  ccPersons: [],
  reviewDays: 3,
  reviewUnit: 'days',
  notes: '',
})

// 版本控制
const versionControl = ref({
  version: '1.0.1',
  changeLog: '',
})

const versionHistory = ref([
  { version: '1.0.0', editor: '张合规', time: '2023-05-10 14:30', status: '已发布', changes: '初始版本' },
  { version: '1.0.1', editor: '张合规', time: '2023-05-12 09:15', status: '草稿', changes: '更新举报范围和处理流程' },
])

const showVersionDiff = ref(false)
const compareVersion1 = ref('')
const compareVersion2 = ref('')
const versionDiff = ref('')

function viewVersion(version: any) {
  ElMessage.info(`查看版本: ${version.version}`)
}

function compareVersion(version: any) {
  compareVersion1.value = version.version
  showVersionDiff.value = true
  versionDiff.value = '版本差异内容将在这里显示...'
}

function restoreVersion(version: any) {
  ElMessageBox.confirm(`确定要恢复到此版本 ${version.version} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    ElMessage.success(`已恢复版本: ${version.version}`)
  })
}
// 右侧辅助区数据
const templates = ref([
  { id: '1', name: '举报管理制度模板', desc: '包含举报管理的基本框架和条款' },
  { id: '2', name: '举报保密制度模板', desc: '详细规定举报信息的保密措施' },
  { id: '3', name: '举报奖励制度模板', desc: '规定举报奖励的标准和发放流程' },
])
const aiSuggestions = ref('')
const relatedPolicies = ref([
  { id: '1', name: '公司合规管理制度', type: '管理制度', date: '2023-01-15', relevance: 85 },
  { id: '2', name: '员工行为规范', type: '行为规范', date: '2023-03-20', relevance: 72 },
  { id: '3', name: '信息安全管理办法', type: '管理办法', date: '2023-04-05', relevance: 65 },
])
// 方法
function addSection() {
  const newIndex = sections.value.length + 1
  sections.value.push({ title: `第${newIndex}章 新增章节`, content: '' })
  activeSection.value = (sections.value.length - 1).toString()
}
function handleSectionEdit(targetName: string, action: string) {
  if (action === 'remove') {
    if (sections.value.length <= 1) {
      ElMessage.warning('至少保留一个章节')
      return
    }
    ElMessageBox.confirm('确定删除该章节吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      const index = Number.parseInt(targetName)
      sections.value.splice(index, 1)
      if (activeSection.value === targetName) {
        activeSection.value = '0'
      }
    })
  }
}
function handleNodeClick(data: any) {
  if (!data.children) {
    const exists = selectedLaws.value.some(law => law.name === data.label)
    if (!exists) {
      selectedLaws.value.push({
        name: data.label,
        org: '未知',
        date: '未知',
        desc: '',
      })
      ElMessage.success(`已添加法规: ${data.label}`)
    }
  }
}
function removeLaw(index: number) {
  selectedLaws.value.splice(index, 1)
}
function handleFileChange(file: any) {
  attachments.value.push({
    name: file.name,
    type: file.name.split('.').pop().toUpperCase(),
    size: `${(file.size / 1024 / 1024).toFixed(1)}MB`,
    time: new Date().toLocaleString(),
    desc: '',
  })
}
function previewFile(file: any) {
  ElMessage.info(`预览文件: ${file.name}`)
}
function downloadFile(file: any) {
  ElMessage.success(`下载文件: ${file.name}`)
}
function removeFile(file: any) {
  const index = attachments.value.indexOf(file)
  if (index !== -1) {
    attachments.value.splice(index, 1)
    ElMessage.success(`已删除文件: ${file.name}`)
  }
}
function applyTemplate(template: any) {
  ElMessage.success(`已应用模板: ${template.name}`)
}
function generateContent() {
  aiSuggestions.value = '根据您选择的政策类型和描述，建议包含以下内容：\n1. 明确举报的范围和对象\n2. 详细说明举报渠道和方式\n3. 规定举报处理流程和时限\n4. 强调保密措施和保护机制'
}
function checkContent() {
  aiSuggestions.value = '审核结果：\n1. 政策目标明确 ✔\n2. 举报流程完整 ✔\n3. 缺少举报奖励说明 ✖\n4. 保密条款需要加强 ✖'
}
function optimizeContent() {
  aiSuggestions.value = '优化建议：\n1. 使用更正式的政策语言\n2. 增加具体案例说明\n3. 优化章节结构\n4. 补充法律依据引用'
}
function savePolicy() {
  ElMessage.success('政策保存成功')
}
function cancel() {
  ElMessageBox.confirm('确定取消编辑吗? 所有未保存的更改将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    ElMessage.info('已取消编辑')
  })
}
// 初始化
onMounted(() => {
  // 模拟编辑模式
  if (window.location.search.includes('edit')) {
    isEditMode.value = true
    policyForm.value = {
      name: '公司举报管理制度',
      code: 'POL-2023-001',
      type: '1',
      scope: ['1', '2'],
      departments: ['1', '3'],
      positions: [],
      level: '2',
      startDate: '2023-06-01',
      endDate: '2025-05-31',
      isLongTerm: false,
      description: '本制度规定了公司内部举报的范围、方式、处理流程及保密措施',
      creator: '张合规',
      createTime: '2023-05-10 14:30:22',
      status: '已发布',
    }
    sections.value = [
      { title: '第一章 总则', content: '第一条 为规范公司内部举报行为，保护举报人合法权益，根据相关法律法规，制定本制度。\n第二条 本制度适用于公司全体员工及相关方。' },
      { title: '第二章 举报范围', content: '第三条 举报范围包括但不限于：\n1. 违法违规行为\n2. 违反公司制度行为\n3. 损害公司利益行为\n4. 其他不当行为' },
      { title: '第三章 举报方式', content: '第四条 举报可通过以下渠道进行：\n1. 公司举报邮箱\n2. 举报热线\n3. 直接向合规部门举报\n4. 匿名举报箱' },
    ]
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-2xl c-[#000] font-bold">
              新增举报政策
            </h1>
            <!-- <el-tag type="success">启用</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="savePolicy">
              <el-icon class="mr-1">
                <i class="fas fa-save" />
              </el-icon>
              保存
            </el-button>
            <el-button v-if="isEditMode" type="success" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <i class="fas fa-paper-plane" />
              </el-icon>
              发布
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <i class="fas fa-eye" />
              </el-icon>
              预览
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap" @click="cancel">
              <el-icon class="mr-1">
                <i class="fas fa-times" />
              </el-icon>
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <!-- 基本信息区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  基本信息
                </div>
              </template>
              <el-form :model="policyForm" label-width="120px" label-position="right">
                <el-form-item label="政策名称" prop="name" required>
                  <el-input v-model="policyForm.name" placeholder="请输入政策名称" />
                </el-form-item>
                <el-form-item label="政策编号" prop="code">
                  <div class="flex space-x-2">
                    <el-input v-model="policyForm.code" placeholder="请输入政策编号" />
                    <el-button>自动生成</el-button>
                  </div>
                </el-form-item>
                <el-form-item label="政策类型" prop="type" required>
                  <el-select v-model="policyForm.type" placeholder="请选择政策类型" class="w-full">
                    <el-option label="举报管理制度" value="1" />
                    <el-option label="举报保密制度" value="2" />
                    <el-option label="举报人保护制度" value="3" />
                    <el-option label="举报处理流程" value="4" />
                    <el-option label="举报奖励制度" value="5" />
                    <el-option label="其他" value="6" />
                  </el-select>
                </el-form-item>
                <el-form-item label="适用范围" prop="scope" required>
                  <el-checkbox-group v-model="policyForm.scope">
                    <el-checkbox label="全公司" value="1" />
                    <el-checkbox label="特定部门" value="2" />
                    <el-checkbox label="特定岗位" value="3" />
                    <el-checkbox label="外部相关方" value="4" />
                  </el-checkbox-group>
                  <div v-if="policyForm.scope.includes('2')" class="mt-2">
                    <el-select v-model="policyForm.departments" multiple placeholder="请选择部门" class="w-full">
                      <el-option label="财务部" value="1" />
                      <el-option label="人力资源部" value="2" />
                      <el-option label="技术研发部" value="3" />
                      <el-option label="市场部" value="4" />
                    </el-select>
                  </div>
                  <div v-if="policyForm.scope.includes('3')" class="mt-2">
                    <el-select v-model="policyForm.positions" multiple placeholder="请选择岗位" class="w-full">
                      <el-option label="部门经理" value="1" />
                      <el-option label="项目经理" value="2" />
                      <el-option label="财务专员" value="3" />
                      <el-option label="合规专员" value="4" />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="政策级别" prop="level" required>
                  <el-radio-group v-model="policyForm.level">
                    <el-radio-button label="1">
                      一般
                    </el-radio-button>
                    <el-radio-button label="2">
                      重要
                    </el-radio-button>
                    <el-radio-button label="3">
                      核心
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="有效期" prop="validDate">
                  <div class="flex items-center space-x-4">
                    <el-date-picker v-model="policyForm.startDate" type="date" placeholder="开始日期" class="w-40" />
                    <span v-if="!policyForm.isLongTerm">至</span>
                    <el-date-picker
                      v-if="!policyForm.isLongTerm" v-model="policyForm.endDate" type="date" placeholder="结束日期"
                      class="w-40"
                    />
                    <el-checkbox v-model="policyForm.isLongTerm">
                      长期有效
                    </el-checkbox>
                  </div>
                </el-form-item>
                <el-form-item label="政策描述" prop="description">
                  <el-input v-model="policyForm.description" type="textarea" :rows="3" placeholder="请输入政策描述" />
                </el-form-item>
                <el-form-item label="创建人" prop="creator">
                  <el-input v-model="policyForm.creator" disabled />
                </el-form-item>
                <el-form-item label="创建时间" prop="createTime">
                  <el-input v-model="policyForm.createTime" disabled />
                </el-form-item>
                <el-form-item v-if="isEditMode" label="状态" prop="status">
                  <el-tag :type="statusTagType">
                    {{ policyForm.status }}
                  </el-tag>
                </el-form-item>
              </el-form>
            </el-card>
            <!-- 政策内容区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  政策内容
                </div>
              </template>
              <div class="mb-4 flex justify-between">
                <el-button type="primary" size="small" @click="addSection">
                  <el-icon class="mr-1">
                    <i class="fas fa-plus" />
                  </el-icon>
                  添加章节
                </el-button>
                <div class="text-sm text-gray-500">
                  <span>字数: {{ wordCount }}</span>
                </div>
              </div>
              <div class="border rounded">
                <el-tabs v-model="activeSection" type="card" editable @edit="handleSectionEdit">
                  <el-tab-pane
                    v-for="(section, index) in sections" :key="index" :label="section.title"
                    :name="index.toString()"
                  >
                    <div class="p-4">
                      <el-input v-model="section.title" placeholder="章节标题" class="mb-4" />
                      <el-input v-model="section.content" type="textarea" :rows="10" placeholder="请输入章节内容" />
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </el-card>
            <!-- 关联法规区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  关联法规
                </div>
              </template>
              <div class="flex space-x-4">
                <div class="w-1/3 border rounded p-2">
                  <el-input placeholder="搜索法规..." size="small" class="mb-2">
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <i class="fas fa-search" />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-tree
                    :data="lawCategories" :props="defaultProps" node-key="id" default-expand-all
                    @node-click="handleNodeClick"
                  />
                </div>
                <div class="flex-1">
                  <el-table :data="selectedLaws" border>
                    <el-table-column prop="name" label="法规名称" width="180" />
                    <el-table-column prop="org" label="颁布机构" width="120" />
                    <el-table-column prop="date" label="生效日期" width="120" />
                    <el-table-column prop="desc" label="关联说明">
                      <template #default="{ row }">
                        <el-input v-model="row.desc" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button type="danger" size="small" @click="removeLaw($index)">
                          <el-icon><i class="fas fa-trash" /></el-icon>
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-card>
            <!-- 附件管理区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  附件管理
                </div>
              </template>
              <el-upload class="upload-demo" drag multiple action="" :on-change="handleFileChange" :auto-upload="false">
                <el-icon class="el-icon--upload">
                  <i class="fas fa-cloud-upload-alt" />
                </el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传PDF、Word、Excel等格式文件，单个文件不超过50MB
                  </div>
                </template>
              </el-upload>
              <el-table :data="attachments" class="mt-4">
                <el-table-column prop="name" label="附件名称" />
                <el-table-column prop="type" label="类型" width="80" />
                <el-table-column prop="size" label="大小" width="100" />
                <el-table-column prop="time" label="上传时间" width="180" />
                <el-table-column prop="desc" label="描述">
                  <template #default="{ row }">
                    <el-input v-model="row.desc" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button size="small" @click="previewFile(row)">
                      <el-icon><i class="fas fa-eye" /></el-icon>
                    </el-button>
                    <el-button size="small" @click="downloadFile(row)">
                      <el-icon><i class="fas fa-download" /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="removeFile(row)">
                      <el-icon><i class="fas fa-trash" /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <!-- 发布设置区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  发布设置
                </div>
              </template>
              <el-form :model="publishSettings" label-width="120px" label-position="right">
                <el-form-item label="发布范围">
                  <el-checkbox-group v-model="publishSettings.scope">
                    <el-checkbox label="全体员工" value="1" />
                    <el-checkbox label="指定部门" value="2" />
                    <el-checkbox label="指定岗位" value="3" />
                    <el-checkbox label="指定角色" value="4" />
                  </el-checkbox-group>
                  <div v-if="publishSettings.scope.includes('2')" class="mt-2">
                    <el-select v-model="publishSettings.departments" multiple placeholder="请选择部门" class="w-full">
                      <el-option label="财务部" value="1" />
                      <el-option label="人力资源部" value="2" />
                      <el-option label="技术研发部" value="3" />
                      <el-option label="市场部" value="4" />
                    </el-select>
                  </div>
                  <div v-if="publishSettings.scope.includes('3')" class="mt-2">
                    <el-select v-model="publishSettings.positions" multiple placeholder="请选择岗位" class="w-full">
                      <el-option label="部门经理" value="1" />
                      <el-option label="项目经理" value="2" />
                      <el-option label="财务专员" value="3" />
                      <el-option label="合规专员" value="4" />
                    </el-select>
                  </div>
                  <div v-if="publishSettings.scope.includes('4')" class="mt-2">
                    <el-select v-model="publishSettings.roles" multiple placeholder="请选择角色" class="w-full">
                      <el-option label="管理员" value="1" />
                      <el-option label="审核员" value="2" />
                      <el-option label="普通员工" value="3" />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="可见性设置">
                  <el-radio-group v-model="publishSettings.visibility">
                    <el-radio label="1">
                      公开
                    </el-radio>
                    <el-radio label="2">
                      部分可见
                    </el-radio>
                    <el-radio label="3">
                      保密
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="通知方式">
                  <el-checkbox-group v-model="publishSettings.notification">
                    <el-checkbox label="系统消息" value="1" />
                    <el-checkbox label="邮件通知" value="2" />
                    <el-checkbox label="短信通知" value="3" />
                    <el-checkbox label="弹窗提醒" value="4" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="重要程度">
                  <el-rate v-model="publishSettings.importance" :max="5" />
                </el-form-item>
                <el-form-item label="发布时间">
                  <el-radio-group v-model="publishSettings.publishTimeType">
                    <el-radio label="1">
                      立即发布
                    </el-radio>
                    <el-radio label="2">
                      定时发布
                    </el-radio>
                  </el-radio-group>
                  <el-date-picker
                    v-if="publishSettings.publishTimeType === '2'" v-model="publishSettings.scheduledTime" type="datetime"
                    placeholder="选择发布时间" class="mt-2 w-full"
                  />
                </el-form-item>
              </el-form>
            </el-card>

            <!-- 审核流程区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  审核流程
                </div>
              </template>
              <el-form :model="reviewProcess" label-width="120px" label-position="right">
                <el-form-item label="是否需要审核">
                  <el-switch v-model="reviewProcess.requireReview" />
                </el-form-item>
                <template v-if="reviewProcess.requireReview">
                  <el-form-item label="审核人">
                    <el-select v-model="reviewProcess.reviewers" multiple placeholder="请选择审核人" class="w-full">
                      <el-option label="李合规 (合规部)" value="1" />
                      <el-option label="王法务 (法务部)" value="2" />
                      <el-option label="张经理 (管理层)" value="3" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="抄送人">
                    <el-select v-model="reviewProcess.ccPersons" multiple placeholder="请选择抄送人" class="w-full">
                      <el-option label="赵总监 (人力资源)" value="1" />
                      <el-option label="钱主管 (财务部)" value="2" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="审核期限">
                    <div class="flex space-x-2">
                      <el-input-number v-model="reviewProcess.reviewDays" :min="1" :max="30" />
                      <el-select v-model="reviewProcess.reviewUnit" class="w-24">
                        <el-option label="小时" value="hours" />
                        <el-option label="天" value="days" />
                      </el-select>
                    </div>
                  </el-form-item>
                  <el-form-item label="审核备注">
                    <el-input v-model="reviewProcess.notes" type="textarea" :rows="3" placeholder="请输入审核要求说明" />
                  </el-form-item>
                </template>
              </el-form>
            </el-card>

            <!-- 版本控制区 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  版本控制
                </div>
              </template>
              <el-form :model="versionControl" label-width="120px" label-position="right">
                <el-form-item label="版本号">
                  <el-input v-model="versionControl.version" placeholder="自动生成" disabled />
                </el-form-item>
                <el-form-item label="变更说明">
                  <el-input v-model="versionControl.changeLog" type="textarea" :rows="3" placeholder="请输入本次修改的主要内容" />
                </el-form-item>
              </el-form>
              <div class="mt-4">
                <el-table :data="versionHistory" border>
                  <el-table-column prop="version" label="版本号" width="120" />
                  <el-table-column prop="editor" label="修改人" width="120" />
                  <el-table-column prop="time" label="修改时间" width="180" />
                  <el-table-column prop="status" label="状态" width="100" />
                  <el-table-column prop="changes" label="变更说明" />
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button size="small" @click="viewVersion(row)">
                        <el-icon><i class="fas fa-eye" /></el-icon>
                      </el-button>
                      <el-button size="small" @click="compareVersion(row)">
                        <el-icon><i class="fas fa-code-compare" /></el-icon>
                      </el-button>
                      <el-button size="small" type="success" @click="restoreVersion(row)">
                        <el-icon><i class="fas fa-rotate-left" /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-if="showVersionDiff" class="mt-4">
                <h4 class="mb-2">
                  版本对比
                </h4>
                <el-select v-model="compareVersion1" placeholder="选择版本1" class="mr-2">
                  <el-option v-for="v in versionHistory" :key="v.version" :label="v.version" :value="v.version" />
                </el-select>
                <el-select v-model="compareVersion2" placeholder="选择版本2">
                  <el-option v-for="v in versionHistory" :key="v.version" :label="v.version" :value="v.version" />
                </el-select>
                <div class="mt-4 rounded bg-gray-50 p-4">
                  <pre>{{ versionDiff }}</pre>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <!-- 政策模板 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  政策模板
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="template in templates" :key="template.id"
                  class="cursor-pointer border rounded p-3 hover:bg-gray-50" @click="applyTemplate(template)"
                >
                  <div class="font-medium">
                    {{ template.name }}
                  </div>
                  <div class="mt-1 text-sm text-gray-500">
                    {{ template.desc }}
                  </div>
                </div>
              </div>
            </el-card>
            <!-- AI辅助 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  AI辅助
                </div>
              </template>
              <div class="space-y-3">
                <el-button class="!rounded-button w-full whitespace-nowrap" @click="generateContent">
                  <el-icon class="mr-1">
                    <i class="fas fa-robot" />
                  </el-icon>
                  AI生成内容
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap" @click="checkContent">
                  <el-icon class="mr-1">
                    <i class="fas fa-check-circle" />
                  </el-icon>
                  AI审核
                </el-button>
                <el-button class="!rounded-button w-full whitespace-nowrap" @click="optimizeContent">
                  <el-icon class="mr-1">
                    <i class="fas fa-magic" />
                  </el-icon>
                  AI优化
                </el-button>
                <div v-if="aiSuggestions" class="mt-2 rounded bg-gray-50 p-3">
                  <div class="mb-2 font-medium">
                    AI建议
                  </div>
                  <div class="text-sm">
                    {{ aiSuggestions }}
                  </div>
                </div>
              </div>
            </el-card>
            <!-- 编辑提示 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  编辑提示
                </div>
              </template>
              <div class="text-sm space-y-2">
                <div class="rounded bg-blue-50 p-2">
                  <div class="text-blue-600 font-medium">
                    关键点提示
                  </div>
                  <ul class="mt-1 list-disc pl-5">
                    <li>确保政策目标清晰明确</li>
                    <li>详细说明举报流程和渠道</li>
                    <li>明确保密措施和保护机制</li>
                    <li>规定处理时限和反馈机制</li>
                  </ul>
                </div>
                <div class="mt-2 rounded bg-yellow-50 p-2">
                  <div class="text-yellow-600 font-medium">
                    常见问题
                  </div>
                  <ul class="mt-1 list-disc pl-5">
                    <li>如何确保举报人信息安全？</li>
                    <li>举报处理的标准流程是什么？</li>
                    <li>举报奖励如何发放？</li>
                    <li>匿名举报如何处理？</li>
                  </ul>
                </div>
              </div>
            </el-card>
            <!-- 相关政策 -->
            <el-card shadow="hover" class="!border-none">
              <template #header>
                <div class="text-base font-bold">
                  相关政策
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="policy in relatedPolicies" :key="policy.id"
                  class="cursor-pointer border-b p-2 hover:bg-gray-50"
                >
                  <div class="flex justify-between">
                    <span class="font-medium">{{ policy.name }}</span>
                    <el-tag size="small">
                      {{ policy.relevance }}%
                    </el-tag>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    {{ policy.type }} · {{ policy.date }}
                  </div>
                </div>
                <el-button type="text" class="mt-2 w-full">
                  查看全部
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .active-submenu :deep(.el-sub-menu__title) {
    color: #fff !important;
    background-color: rgb(30 136 229 / 10%) !important;
    border-left: 4px solid #1e88e5;
  }

  .active-menu-item {
    color: #fff !important;
    background-color: rgb(30 136 229 / 10%) !important;
  }

  .el-menu-item.is-active {
    color: #fff !important;
    background-color: rgb(30 136 229 / 10%) !important;
    border-left: 4px solid #1e88e5;
  }

  .el-menu-item:hover,
  .el-sub-menu__title:hover {
    background-color: rgb(255 255 255 / 10%) !important;
  }

  .el-tree {
    color: #333;
    background-color: transparent;
  }

  .el-tree :deep(.el-tree-node__content:hover) {
    background-color: #f5f7fa;
  }

  .el-tree :deep(.el-tree-node:focus > .el-tree-node__content) {
    background-color: #f5f7fa;
  }

  .upload-demo :deep(.el-upload-dragger) {
    width: 100%;
    padding: 20px;
  }

  .el-card {
    margin-bottom: 20px;
  }

  .el-card:last-child {
    margin-bottom: 0;
  }
</style>s
