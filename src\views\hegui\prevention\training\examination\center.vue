<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  Document,
  Grid,
  Upload,
} from '@element-plus/icons-vue'
import assessApi from '@/api/complianceApi/prevention/assess'

// 路由相关
const route = useRoute()
const router = useRouter()

// 考试数据
interface Question {
  id: string
  type: 'single' | 'multi' | 'judge'
  text: string
  image?: string
  options: Array<{ text: string; image?: string }>
  answer: number[]
  marked: boolean
}

const questions = ref<Question[]>([])
const currentIndex = ref(0)
const showQuestionCard = ref(false)
const countdown = ref(3600) // 默认60分钟
const timer = ref<NodeJS.Timeout | null>(null)
const loading = ref(false)
const examRecordId = ref<string>('')
const isExamEnded = ref(false)
const isSubmit = ref(false)

// 计算属性
const currentQuestion = computed(() => questions.value[currentIndex.value])
const answeredCount = computed(() => questions.value.filter(q => q.answer.length > 0).length)
const totalQuestions = computed(() => questions.value.length)

// 格式化时间显示
function formatTime(seconds: number): string {
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds % 3600) / 60)
  const s = seconds % 60
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
}

// 获取题型文字
function getQuestionType(type: string): string {
  switch (type) {
    case 'single': return '单选题'
    case 'multi': return '多选题'
    case 'judge': return '判断题'
    default: return ''
  }
}

// 判断选项是否被选中
function isSelected(index: number): boolean {
  return currentQuestion.value?.answer.includes(index) || false
}

// 处理选项选择
function handleSelectOption(index: number) {
  if (isExamEnded.value) {
    ElMessage.warning('考试时间已结束，无法答题')
    return
  }

  if (!currentQuestion.value) return

  if (currentQuestion.value.type === 'single' || currentQuestion.value.type === 'judge') {
    questions.value[currentIndex.value].answer = [index]
  }
  else if (currentQuestion.value.type === 'multi') {
    const answerIndex = currentQuestion.value.answer.indexOf(index)
    if (answerIndex === -1) {
      questions.value[currentIndex.value].answer.push(index)
    }
    else {
      questions.value[currentIndex.value].answer.splice(answerIndex, 1)
    }
  }
}

// 上一题
function handlePrevQuestion() {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
  else {
    ElMessage.info('已经是第一题了')
  }
}

// 下一题
function handleNextQuestion() {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++
  }
  else {
    ElMessage.info('已经是最后一题了')
  }
}

// 跳转到指定题目
function handleJumpQuestion(index: number) {
  currentIndex.value = index
  showQuestionCard.value = false
}

// 返回处理
function handleBack() {
  if (isSubmit.value) {
    router.back()
    return
  }

  ElMessageBox.confirm(
    '您还没有提交考试，确定要离开吗？离开将自动提交当前答案。',
    '提示',
    {
      confirmButtonText: '确定离开',
      cancelButtonText: '继续考试',
      type: 'warning',
    },
  ).then(async () => {
    if (timer.value) {
      clearInterval(timer.value)
    }
    await submitExam(true)
  }).catch(() => {
    // 用户取消
  })
}

// 提交考试
function handleSubmit() {
  if (isExamEnded.value) {
    ElMessage.warning('考试时间已结束')
    return
  }

  const totalQuestions = questions.value.length
  const answeredQuestions = answeredCount.value

  if (answeredQuestions < totalQuestions) {
    ElMessageBox.confirm(
      `您还有 ${totalQuestions - answeredQuestions} 道题未作答，是否确认提交？`,
      '提示',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '继续答题',
        type: 'warning',
      },
    ).then(() => {
      submitExam()
    }).catch(() => {
      // 用户取消
    })
  }
  else {
    ElMessageBox.confirm(
      '您已完成所有题目，确认提交考试吗？',
      '提示',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '继续答题',
        type: 'warning',
      },
    ).then(() => {
      submitExam()
    }).catch(() => {
      // 用户取消
    })
  }
}

// 执行提交考试
async function submitExam(isDefaultSubmit = false) {
  isSubmit.value = true

  if (!examRecordId.value) {
    ElMessage.error('缺少考试记录ID')
    return
  }

  try {
    const loading = ElMessage({
      message: '提交中...',
      type: 'info',
      duration: 0,
    })

    // 构造提交数据
    const submitData: Record<string, string> = {}
    questions.value.forEach((question) => {
      const answerLetters = question.answer.map(index => String.fromCharCode(65 + index))
      const userAnswer = question.type === 'multi' ? answerLetters.join(',') : (answerLetters[0] || '')
      submitData[question.id] = userAnswer
    })

    const result = await assessApi.submitExam(examRecordId.value, submitData)
    loading.close()

    if (result) {
      ElMessage.success('提交成功')

      if (timer.value) {
        clearInterval(timer.value)
      }

      if (isDefaultSubmit) {
        router.back()
      }
      else {
        setTimeout(() => {
          router.back()
        }, 1500)
      }
    }
    else {
      ElMessage.error('提交失败，请重试')
    }
  }
  catch (error) {
    console.error('提交考试失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}

// 倒计时
function startCountdown() {
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    }
    else {
      if (timer.value) {
        clearInterval(timer.value)
      }
      isExamEnded.value = true
      ElMessageBox.alert(
        '考试时间已结束，将自动提交考试',
        '提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
        },
      ).then(async () => {
        await submitExam(true)
      })
    }
  }, 1000)
}

// 预览图片
function previewImage(url: string) {
  // 这里可以使用Element Plus的图片预览组件
  window.open(url, '_blank')
}

// 页面初始化
onMounted(async () => {
  try {
    loading.value = true

    const examId = route.query.examId as string
    const examDuration = route.query.examDuration as string

    if (!examId) {
      ElMessage.error('缺少考试ID参数')
      return
    }

    if (examDuration && !Number.isNaN(examDuration)) {
      countdown.value = Number.parseInt(examDuration) * 60
    }

    // 调用开始考试接口
    const startExamResult = await assessApi.startExam(examId)
    if (startExamResult?.id) {
      examRecordId.value = startExamResult.id

      // 获取题目
      const questionsResult = await assessApi.getExamQuestion(startExamResult.id)
      if (questionsResult && Array.isArray(questionsResult)) {
        questions.value = questionsResult.map((item: any) => {
          const options: Array<{ text: string; image?: string }> = []
          if (item.optionA) options.push({ text: item.optionA })
          if (item.optionB) options.push({ text: item.optionB })
          if (item.optionC) options.push({ text: item.optionC })
          if (item.optionD) options.push({ text: item.optionD })

          let type: 'single' | 'multi' | 'judge' = 'single'
          if (item.questionType === '多选题') {
            type = 'multi'
          }
          else if (item.questionType === '判断题') {
            type = 'judge'
          }

          let userAnswerArray: number[] = []
          if (item.userAnswer && item.isAnswered) {
            if (type === 'multi') {
              userAnswerArray = item.userAnswer.split(',').map((ans: string) => {
                return ans.charCodeAt(0) - 65
              }).filter((index: number) => index >= 0 && index < options.length)
            }
            else {
              const answerIndex = item.userAnswer.charCodeAt(0) - 65
              if (answerIndex >= 0 && answerIndex < options.length) {
                userAnswerArray = [answerIndex]
              }
            }
          }

          return {
            id: item.questionId,
            type,
            text: item.questionContent,
            image: item.questionImage,
            options,
            answer: userAnswerArray,
            marked: false,
          }
        })

        if (!examDuration && startExamResult.examInfo?.examDuration) {
          countdown.value = startExamResult.examInfo.examDuration * 60
        }
      }
    }
    else {
      ElMessage.error('开始考试失败')
    }
  }
  catch (error) {
    console.error('加载考试数据失败:', error)
    setTimeout(() => {
      isSubmit.value = true
      handleBack()
    }, 1000)
  }
  finally {
    loading.value = false
  }

  startCountdown()
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<template>
  <div class="exam-page">
    <!-- 顶部导航栏 -->
    <div class="exam-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" circle @click="handleBack" />
      </div>
      <div class="header-center">
        <span class="question-progress">第 {{ currentIndex + 1 }} / {{ totalQuestions }} 题</span>
      </div>
      <div class="header-right">
        <div class="timer">
          <el-icon><Clock /></el-icon>
          <span>{{ formatTime(countdown) }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="exam-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-loading-spinner />
        <span class="loading-text">正在加载考试题目...</span>
      </div>

      <!-- 题目内容 -->
      <div v-else-if="questions.length > 0" class="question-container">
        <el-card class="question-card" shadow="never">
          <!-- 题型标签 -->
          <div class="question-type">
            <el-tag type="primary" size="large">
              {{ getQuestionType(currentQuestion?.type || '') }}
            </el-tag>
          </div>

          <!-- 题目内容 -->
          <div class="question-content">
            <div class="question-text">
              {{ currentQuestion?.text }}
            </div>
            <div v-if="currentQuestion?.image" class="question-image">
              <el-image
                :src="currentQuestion.image"
                fit="contain"
                style="max-width: 100%; max-height: 300px;"
                @click="previewImage(currentQuestion.image!)"
              />
            </div>
          </div>

          <!-- 选项区域 -->
          <div class="options-container">
            <div
              v-for="(option, index) in currentQuestion?.options || []"
              :key="index"
              class="option-item"
              :class="{ 'selected': isSelected(index) }"
              @click="handleSelectOption(index)"
            >
              <div class="option-prefix">
                {{ String.fromCharCode(65 + index) }}.
              </div>
              <div class="option-content">
                <span>{{ option.text }}</span>
                <div v-if="option.image" class="option-image">
                  <el-image
                    :src="option.image"
                    fit="contain"
                    style="max-width: 200px; max-height: 150px;"
                    @click.stop="previewImage(option.image!)"
                  />
                </div>
              </div>
              <div class="option-selector">
                <el-radio
                  v-if="currentQuestion?.type === 'single' || currentQuestion?.type === 'judge'"
                  :model-value="isSelected(index)"
                  :value="true"
                  @click.stop
                />
                <el-checkbox
                  v-else
                  :model-value="isSelected(index)"
                  @click.stop
                />
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 无题目状态 -->
      <div v-else class="empty-container">
        <el-empty description="暂无考试题目" />
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="exam-footer">
      <div class="footer-actions">
        <el-button
          :icon="ArrowLeft"
          :disabled="currentIndex === 0"
          @click="handlePrevQuestion"
        >
          上一题
        </el-button>

        <el-button
          :icon="Grid"
          @click="showQuestionCard = true"
        >
          题卡 ({{ answeredCount }}/{{ totalQuestions }})
        </el-button>

        <el-button
          v-auth="['examination/center/submit']"
          type="primary"
          :icon="Upload"
          @click="handleSubmit"
        >
          提交
        </el-button>

        <el-button
          :icon="ArrowRight"
          :disabled="currentIndex === totalQuestions - 1"
          @click="handleNextQuestion"
        >
          下一题
        </el-button>
      </div>
    </div>

    <!-- 题卡弹窗 -->
    <el-dialog
      v-model="showQuestionCard"
      title="答题进度"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="question-card-content">
        <div class="progress-info">
          <span>已答题: {{ answeredCount }} / {{ totalQuestions }}</span>
          <span>完成率: {{ Math.round((answeredCount / totalQuestions) * 100) }}%</span>
        </div>

        <div class="question-grid">
          <div
            v-for="(item, index) in questions"
            :key="index"
            class="question-number"
            :class="{
              'answered': item.answer.length > 0,
              'marked': item.marked,
              'current': index === currentIndex,
            }"
            @click="handleJumpQuestion(index)"
          >
            {{ index + 1 }}
          </div>
        </div>

        <div class="legend">
          <div class="legend-item">
            <div class="legend-color answered"></div>
            <span>已答题</span>
          </div>
          <div class="legend-item">
            <div class="legend-color current"></div>
            <span>当前题</span>
          </div>
          <div class="legend-item">
            <div class="legend-color unanswered"></div>
            <span>未答题</span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showQuestionCard = false">
          关闭
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.exam-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--el-bg-color-page);
}

/* 头部样式 */
.exam-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left,
.header-right {
  flex: 0 0 auto;
}

.header-center {
  flex: 1;
  text-align: center;
}

.question-progress {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.timer {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-danger);
}

/* 内容区域样式 */
.exam-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 16px;
}

.loading-text {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.question-container {
  max-width: 1000px;
  margin: 0 auto;
}

.question-card {
  margin-bottom: 24px;
}

.question-type {
  margin-bottom: 24px;
}

.question-content {
  margin-bottom: 32px;
}

.question-text {
  font-size: 18px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
}

.question-image {
  text-align: center;
  cursor: pointer;
}

/* 选项样式 */
.options-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background-color: var(--el-fill-color-lighter);
  border: 2px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.option-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-color-primary-light-7);
}

.option-item.selected {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.option-prefix {
  flex: 0 0 auto;
  margin-right: 16px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.option-content {
  flex: 1;
  font-size: 16px;
  line-height: 1.5;
  color: var(--el-text-color-regular);
}

.option-image {
  margin-top: 12px;
  cursor: pointer;
}

.option-selector {
  flex: 0 0 auto;
  margin-left: 16px;
}

/* 底部操作栏样式 */
.exam-footer {
  padding: 16px 24px;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  max-width: 1000px;
  margin: 0 auto;
}

/* 题卡弹窗样式 */
.question-card-content {
  padding: 16px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
}

.question-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.question-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 2px solid var(--el-border-color);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);
}

.question-number:hover {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.question-number.answered {
  background-color: var(--el-color-success);
  border-color: var(--el-color-success);
  color: white;
}

.question-number.current {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: white;
}

.question-number.marked {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
  color: white;
}

.legend {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.legend-color.answered {
  background-color: var(--el-color-success);
}

.legend-color.current {
  background-color: var(--el-color-primary);
}

.legend-color.unanswered {
  background-color: var(--el-bg-color);
  border: 2px solid var(--el-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-header {
    padding: 12px 16px;
  }

  .exam-content {
    padding: 16px;
  }

  .exam-footer {
    padding: 12px 16px;
  }

  .footer-actions {
    flex-wrap: wrap;
    gap: 12px;
  }

  .question-grid {
    grid-template-columns: repeat(8, 1fr);
  }

  .legend {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
