---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/责任追究整改

## POST 创建新的责任追究整改

POST /whiskerguardviolationservice/api/responsibility/investigate/corrections

描述：创建新的责任追究整改。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "correctionBackground": "string",
  "correctionRequire": "string",
  "correctionRange": "string",
  "correctionScheme": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ResponsibilityInvestigateCorrectionDTO](#schemaresponsibilityinvestigatecorrectiondto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "",
  "correctionCode": "",
  "correctionType": "",
  "level": "",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "",
  "finishDate": "",
  "status": "",
  "correctionBackground": "",
  "correctionRequire": "",
  "correctionRange": "",
  "correctionScheme": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityResponsibilityInvestigateCorrectionDTO](#schemaresponseentityresponsibilityinvestigatecorrectiondto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ResponseEntityResponsibilityInvestigateCorrectionDTO">ResponseEntityResponsibilityInvestigateCorrectionDTO</h2>

<a id="schemaresponseentityresponsibilityinvestigatecorrectiondto"></a>
<a id="schema_ResponseEntityResponsibilityInvestigateCorrectionDTO"></a>
<a id="tocSresponseentityresponsibilityinvestigatecorrectiondto"></a>
<a id="tocsresponseentityresponsibilityinvestigatecorrectiondto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "correctionBackground": "string",
  "correctionRequire": "string",
  "correctionRange": "string",
  "correctionScheme": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|name|string|false|none||整改项目名称|
|correctionCode|string|true|none||整改编号|
|correctionType|string|false|none||整改类型：合规风险、操作风险、系统风险|
|level|string|false|none||优先级：高、中、低|
|investigateId|integer(int64)|true|none||违规调查id|
|dutyEmployeeId|integer(int64)|true|none||责任人id|
|dutyEmployeeOrgId|integer(int64)|true|none||责任部门id|
|collaborationEmployeeId|integer(int64)|true|none||协作人id|
|supervisionEmployeeId|integer(int64)|true|none||监督人id|
|startDate|string|false|none||开始日期|
|finishDate|string|false|none||完成日期|
|status|string|false|none||状态：未开始、进行中、已完成、已暂停、已取消|
|correctionBackground|string|false|none||整改背景|
|correctionRequire|string|false|none||整改要求|
|correctionRange|string|false|none||整改范围|
|correctionScheme|string|false|none||整改方案|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|correctionType|COMPLIANCE_RISK|
|correctionType|OPERATIONAL_RISK|
|correctionType|SYSTEM_RISK|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|NO_START|
|status|PROGRESSING|
|status|FINISHED|
|status|PAUSED|
|status|CANCELED|

<h2 id="tocS_ResponsibilityInvestigateCorrectionDTO">ResponsibilityInvestigateCorrectionDTO</h2>

<a id="schemaresponsibilityinvestigatecorrectiondto"></a>
<a id="schema_ResponsibilityInvestigateCorrectionDTO"></a>
<a id="tocSresponsibilityinvestigatecorrectiondto"></a>
<a id="tocsresponsibilityinvestigatecorrectiondto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "name": "string",
  "correctionCode": "string",
  "correctionType": "COMPLIANCE_RISK",
  "level": "LOW",
  "investigateId": 0,
  "dutyEmployeeId": 0,
  "dutyEmployeeOrgId": 0,
  "collaborationEmployeeId": 0,
  "supervisionEmployeeId": 0,
  "startDate": "string",
  "finishDate": "string",
  "status": "NO_START",
  "correctionBackground": "string",
  "correctionRequire": "string",
  "correctionRange": "string",
  "correctionScheme": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|name|string|false|none||整改项目名称|
|correctionCode|string|true|none||整改编号|
|correctionType|string|false|none||整改类型：合规风险、操作风险、系统风险|
|level|string|false|none||优先级：高、中、低|
|investigateId|integer(int64)|true|none||违规调查id|
|dutyEmployeeId|integer(int64)|true|none||责任人id|
|dutyEmployeeOrgId|integer(int64)|true|none||责任部门id|
|collaborationEmployeeId|integer(int64)|true|none||协作人id|
|supervisionEmployeeId|integer(int64)|true|none||监督人id|
|startDate|string|false|none||开始日期|
|finishDate|string|false|none||完成日期|
|status|string|false|none||状态：未开始、进行中、已完成、已暂停、已取消|
|correctionBackground|string|false|none||整改背景|
|correctionRequire|string|false|none||整改要求|
|correctionRange|string|false|none||整改范围|
|correctionScheme|string|false|none||整改方案|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

#### 枚举值

|属性|值|
|---|---|
|correctionType|COMPLIANCE_RISK|
|correctionType|OPERATIONAL_RISK|
|correctionType|SYSTEM_RISK|
|level|LOW|
|level|MIDDLE|
|level|HIGH|
|status|NO_START|
|status|PROGRESSING|
|status|FINISHED|
|status|PAUSED|
|status|CANCELED|

