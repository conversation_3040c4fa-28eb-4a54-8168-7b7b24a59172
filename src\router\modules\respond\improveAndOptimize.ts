import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/respond/improveAndOptimize',
  component: Layout,
  // redirect: '/one/1',
  name: '/respond/improveAndOptimize',
  meta: {
    title: '持续改进优化',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/respond/improveAndOptimize/experienceAndLesson',
      name: '/respond/improveAndOptimize/experienceAndLesson',
      component: () => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/index.vue'),
      meta: {
        title: '经验教训库',
      },
      // redirect: '/one/1',
      children: [
        {
          path: '/respond/improveAndOptimize/experienceAndLesson/addEdit',
          name: '/respond/improveAndOptimize/experienceAndLesson/addEdit',
          component: () => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/addEdit.vue'),
          meta: {
            title: '经验教训库',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/improveAndOptimize/experienceAndLesson/detail',
          name: '/respond/improveAndOptimize/experienceAndLesson/detail',
          component: () => import('@/views/hegui/respond/improveAndOptimize/experienceAndLesson/detail.vue'),
          meta: {
            title: '经验教训库-详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/respond/improveAndOptimize/improvementMeasures',
      name: '/respond/improveAndOptimize/improvementMeasures',
      component: () => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/index.vue'),
      meta: {
        title: '改进措施管理',
      },
      children: [
        {
          path: '/respond/improveAndOptimize/improvementMeasures/addEdit',
          name: '/respond/improveAndOptimize/improvementMeasures/addEdit',
          component: () => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/addEdit.vue'),
          meta: {
            title: '调查报告管理',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/improveAndOptimize/improvementMeasures/detail',
          name: '/respond/improveAndOptimize/improvementMeasures/detail',
          component: () => import('@/views/hegui/respond/improveAndOptimize/improvementMeasures/detail.vue'),
          meta: {
            title: '调查报告管理-详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/respond/improveAndOptimize/effectEvaluation',
      name: '/respond/improveAndOptimize/effectEvaluation',
      component: () => import('@/views/hegui/respond/improveAndOptimize/effectEvaluation/index.vue'),
      meta: {
        title: '效果评估分析',
      },
    },
    {
      path: '/respond/improveAndOptimize/optimizationReport',
      name: '/respond/improveAndOptimize/optimizationReport',
      component: () => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/index.vue'),
      meta: {
        title: '优化报告',
      },
      children: [
        {
          path: '/respond/improveAndOptimize/optimizationReport/detail',
          name: '/respond/improveAndOptimize/optimizationReport/detail',
          component: () => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/detail.vue'),
          meta: {
            title: '调查报告管理-新增',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/respond/improveAndOptimize/optimizationReport/addEdit',
          name: '/respond/improveAndOptimize/optimizationReport/addEdit',
          component: () => import('@/views/hegui/respond/improveAndOptimize/optimizationReport/addEdit.vue'),
          meta: {
            title: '调查报告管理-新增',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
