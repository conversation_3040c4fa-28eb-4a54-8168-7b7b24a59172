<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { ref } from 'vue'
import {
  CircleCheck,
  Clock,
  Close,
  Delete,
  Document,
  DocumentAdd,
  Download,
  Edit,
  Notebook,
  Plus,
  Promotion,
  QuestionFilled,
  Search,
  View,
  Warning,
} from '@element-plus/icons-vue'

const activeTab = ref('policy')
const searchQuery = ref('')
const policyType = ref('')
const policyStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(23)

const policyList = ref([
  {
    name: '反腐败举报政策 V3.2',
    type: '反腐败政策',
    status: '已发布',
    createTime: '2023-05-12',
    updateTime: '2023-06-15',
    publishTime: '2023-06-15',
  },
  {
    name: '信息安全举报指南 V2.1',
    type: '信息安全政策',
    status: '草稿',
    createTime: '2023-07-20',
    updateTime: '2023-08-05',
    publishTime: '-',
  },
  {
    name: '合规举报政策 2022',
    type: '合规政策',
    status: '已过期',
    createTime: '2022-03-10',
    updateTime: '2022-12-31',
    publishTime: '2022-04-15',
  },
  {
    name: '商业道德举报规范 V1.0',
    type: '商业道德政策',
    status: '已发布',
    createTime: '2023-01-15',
    updateTime: '2023-02-28',
    publishTime: '2023-02-20',
  },
  {
    name: '数据保护举报流程 V2.3',
    type: '信息安全政策',
    status: '已发布',
    createTime: '2023-04-05',
    updateTime: '2023-06-18',
    publishTime: '2023-05-10',
  },
])

const policyStats = ref([
  {
    label: '政策总数',
    value: '28',
    icon: Document,
    bgColor: 'bg-blue-50',
    textColor: 'text-primary',
  },
  {
    label: '已发布政策',
    value: '15',
    icon: CircleCheck,
    bgColor: 'bg-green-50',
    textColor: 'text-green-500',
  },
  {
    label: '待发布政策',
    value: '5',
    icon: Clock,
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-500',
  },
  {
    label: '已过期政策',
    value: '8',
    icon: Warning,
    bgColor: 'bg-red-50',
    textColor: 'text-red-500',
  },
])

const channelUsage = ref([
  { name: '网页举报', percentage: '45%', color: 'bg-primary' },
  { name: '邮箱举报', percentage: '25%', color: 'bg-green-500' },
  { name: '电话举报', percentage: '15%', color: 'bg-yellow-500' },
  { name: '微信举报', percentage: '10%', color: 'bg-purple-500' },
  { name: '其他渠道', percentage: '5%', color: 'bg-red-500' },
])

const processEfficiency = ref([
  { name: '反腐败流程', days: '3.2' },
  { name: '信息安全流程', days: '5.1' },
  { name: '合规流程', days: '2.8' },
  { name: '商业道德流程', days: '4.5' },
])

const quickLinks = ref([
  { text: '举报处理指南', icon: Notebook },
  { text: '举报政策模板', icon: DocumentAdd },
  { text: '系统帮助文档', icon: QuestionFilled },
])

function viewPolicy(row: any) {
  console.log('查看政策:', row)
}

function editPolicy(row: any) {
  console.log('编辑政策:', row)
}

function publishPolicy(row: any) {
  console.log('发布政策:', row)
}

function disablePolicy(row: any) {
  console.log('禁用政策:', row)
}

function deletePolicy(row: any) {
  console.log('删除政策:', row)
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              举报政策管理
            </h1>
            <!-- <el-tag type="info" class="ml-4" size="small">受理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['reportingPolicyManagement/index/newpolicy']" type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Plus />
              </el-icon>新增政策
            </el-button>
            <el-button v-auth="['reportingPolicyManagement/index/policyrelease']" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>政策发布
            </el-button>
            <el-button v-auth="['reportingPolicyManagement/index/policyexport']" plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <Download />
              </el-icon>政策导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <el-row :gutter="20" class="">
        <el-col :span="18">
          <el-card class="">
            <!-- 标签页导航 -->
            <el-tabs v-model="activeTab">
              <el-tab-pane label="举报政策" name="policy">
                <!-- 政策筛选 -->
                <div class="mb-6 flex items-center space-x-4">
                  <div class="flex-1">
                    <el-input v-model="searchQuery" placeholder="搜索政策名称或关键词" class="w-full">
                      <template #prefix>
                        <el-icon>
                          <Search />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                  <div class="w-48">
                    <el-select v-model="policyType" placeholder="所有政策类型" class="w-full">
                      <el-option label="所有政策类型" value="" />
                      <el-option label="反腐败政策" value="1" />
                      <el-option label="信息安全政策" value="2" />
                      <el-option label="合规政策" value="3" />
                      <el-option label="商业道德政策" value="4" />
                    </el-select>
                  </div>
                  <div class="w-40">
                    <el-select v-model="policyStatus" placeholder="所有状态" class="w-full">
                      <el-option label="所有状态" value="" />
                      <el-option label="草稿" value="1" />
                      <el-option label="已发布" value="2" />
                      <el-option label="已过期" value="3" />
                    </el-select>
                  </div>
                </div>

                <!-- 政策列表 -->
                <el-table :data="policyList" style="width: 100%;">
                  <el-table-column prop="name" label="政策名称" />
                  <el-table-column prop="type" label="政策类型" />
                  <el-table-column prop="status" label="状态">
                    <template #default="{ row }">
                      <el-tag
                        :type="
                          row.status === '已发布'
                            ? 'success'
                            : row.status === '草稿'
                              ? 'info'
                              : 'danger'
                        " size="small"
                      >
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" />
                  <el-table-column prop="updateTime" label="更新时间" />
                  <el-table-column prop="publishTime" label="发布时间" />
                  <el-table-column label="操作" width="180" align="right">
                    <template #default="{ row }">
                      <el-button v-auth="['reportingPolicyManagement/index/view']" type="primary" size="small" @click="viewPolicy(row)">
                        <el-icon>
                          查看
                        </el-icon>
                      </el-button>
                      <el-button v-auth="['reportingPolicyManagement/index/edit']" type="warning" size="small" @click="editPolicy(row)">
                        <el-icon>
                          编辑
                        </el-icon>
                      </el-button>
                      <el-button v-auth="['reportingPolicyManagement/index/delete']" type="danger" size="small" @click="deletePolicy(row)">
                        <el-icon>
                          删除
                        </el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="mt-6 flex items-center justify-between">
                  <div class="text-sm text-gray-500">
                    显示 1 到 5 条，共 23 条记录
                  </div>
                  <el-pagination
                    :current-page="currentPage" :page-size="pageSize" :total="total"
                    layout="prev, pager, next"
                  />
                </div>
              </el-tab-pane>
              <el-tab-pane label="处理流程配置" name="process" />
              <el-tab-pane label="角色权限配置" name="role" />
              <el-tab-pane label="举报渠道配置" name="channel" />
            </el-tabs>
          </el-card>
        </el-col>
        <el-col :span="6">
          <!-- 政策统计 -->
          <el-card class="">
            <template #header>
              <div class="f-16 fw-600">
                政策统计
              </div>
            </template>
            <div class="space-y-3">
              <div
                v-for="stat in policyStats" :key="stat.label"
                class="flex items-center justify-between rounded-md p-3" :class="stat.bgColor"
              >
                <div>
                  <div class="text-sm text-gray-600">
                    {{ stat.label }}
                  </div>
                  <div class="text-2xl font-bold">
                    {{ stat.value }}
                  </div>
                </div>
                <div :class="stat.textColor">
                  <el-icon :size="24">
                    <component :is="stat.icon" />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 渠道使用情况 -->
          <el-card class="mt-20">
            <template #header>
              <div class="f-16 fw-600">
                渠道使用情况
              </div>
            </template>
            <div class="mb-4 h-48 flex items-center justify-center rounded-md bg-gray-100">
              <div class="text-gray-500">
                饼图展示
              </div>
            </div>
            <div class="space-y-2">
              <div v-for="channel in channelUsage" :key="channel.name" class="flex items-center">
                <div class="mr-2 h-3 w-3 rounded-full" :class="channel.color" />
                <div class="flex-1 text-sm">
                  {{ channel.name }}
                </div>
                <div class="text-sm font-medium">
                  {{ channel.percentage }}
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="mt-20">
            <template #header>
              <div class="f-16 fw-600">
                流程效率
              </div>
            </template>
            <div class="mb-4 h-48 flex items-center justify-center rounded-md bg-gray-100">
              <div class="text-gray-500">
                条形图展示
              </div>
            </div>
            <div class="space-y-2">
              <div v-for="process in processEfficiency" :key="process.name" class="flex items-center justify-between">
                <div class="text-sm">
                  {{ process.name }}
                </div>
                <div class="text-sm font-medium">
                  {{ process.days }} 天
                </div>
              </div>
            </div>
          </el-card>

          <el-card class="mt-20">
            <template #header>
              <div class="f-16 fw-600">
                快捷链接
              </div>
            </template>
            <div class="space-y-3">
              <el-link
                v-for="link in quickLinks" :key="link.text" type="primary" :underline="false"
                class="flex items-center"
              >
                <el-icon class="mr-3">
                  <component :is="link.icon" />
                </el-icon>
                <span>{{ link.text }}</span>
              </el-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </PageMain>
  </div>
</template>

<style scoped>
  .container {
    max-width: 1440px;
    min-height: 1024px;
  }

  .el-tabs__item {
    height: 48px;
    padding: 0 24px;
  }

  .el-table {
    margin-top: 16px;
  }

  .el-pagination {
    justify-content: flex-end;
  }

  .bg-blue-50 {
    background-color: #e3f2fd;
  }

  .bg-green-50 {
    background-color: #e8f5e9;
  }

  .bg-yellow-50 {
    background-color: #fff8e1;
  }

  .bg-red-50 {
    background-color: #ffebee;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
