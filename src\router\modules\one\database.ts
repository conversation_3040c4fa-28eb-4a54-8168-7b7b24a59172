import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/database',
  component: Layout,
  redirect: '/database/laws',
  name: 'database',
  meta: {
    title: '合规数据库',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/database/laws',
      name: 'database/laws',
      component: () => import('@/views/hegui/one/database/regulatoryDatabase.vue'),
      meta: {
        title: '法规库',
      },
      children: [
        {
          path: '/database/laws/detail',
          name: '/database/laws/detail',
          component: () => import('@/views/hegui/one/database/regulatoryDatabaseMode/detail.vue'),
          meta: {
            title: '法规详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/database/laws/addEdit',
          name: '/database/laws/addEdit',
          component: () => import('@/views/hegui/one/database/regulatoryDatabaseMode/addEdit.vue'),
          meta: {
            title: '新增法规',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    // {
    //   path: '/database/duty',
    //   name: 'database/duty',
    //   // redirect: '/multilevel_menu_example/level2/page',
    //   component: () => import('@/views/hegui/one/database/complianceObligationLibrary.vue'),
    //   meta: {
    //     title: '合规义务库',
    //   },
    //   children: [
    //     {
    //       path: '/database/duty/detail',
    //       name: '/database/duty/detail',
    //       component: () => import('@/views/hegui/one/database/complianceObligationLibraryMode/detail.vue'),
    //       meta: {
    //         title: '义务详情',
    //         sidebar: false,
    //         breadcrumb: false,
    //       },
    //     },
    //     {
    //       path: '/database/duty/addEdit',
    //       name: '/database/duty/addEdit',
    //       component: () => import('@/views/hegui/one/database/complianceObligationLibraryMode/addEdit.vue'),
    //       meta: {
    //         title: '新增合规义务',
    //         sidebar: false,
    //         breadcrumb: false,
    //       },
    //     },
    //   ],
    // },
    {
      path: '/database/cases',
      name: 'database/cases',
      component: () => import('@/views/hegui/one/database/complianceCaseLibrary.vue'),
      meta: {
        title: '合规案例库',
      },
      children: [
        {
          path: '/database/cases/detail',
          name: '/database/cases/detail',
          component: () => import('@/views/hegui/one/database/complianceCaseLibraryMode/detail.vue'),
          meta: {
            title: '案例详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
        {
          path: '/database/cases/addEdit',
          name: '/database/cases/addEdit',
          component: () => import('@/views/hegui/one/database/complianceCaseLibraryMode/addEdit.vue'),
          meta: {
            title: '新增案例',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
    {
      path: '/database/tenantRegulations',
      name: '/database/tenantRegulations',
      component: () => import('@/views/hegui/one/database/tenantRegulations.vue'),
      meta: {
        title: '专属法规库',
      },
    },
    // {
    //   path: '/system/role',
    //   name: '/system/role',
    //   component: () => import('@/views/system/role/index.vue'),
    //   meta: {
    //     title: '权限树',
    //   },
    //   // children: [
    //   //   {
    //   //     path: '/database/cases/detail',
    //   //     name: '/database/cases/detail',
    //   //     component: () => import('@/views/hegui/one/database/complianceCaseLibraryMode/detail.vue'),
    //   //     meta: {
    //   //       title: '案例详情',
    //   //       sidebar: false,
    //   //       breadcrumb: false,
    //   //     },
    //   //   },
    //   // ],
    // },
  ],
}

export default routes
