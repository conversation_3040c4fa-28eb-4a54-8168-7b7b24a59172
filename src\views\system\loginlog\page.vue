<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// import type Edit from './edit.vue'
// import Getuser from './getuser.vue'
// import Handover from './handover.vue'

// import systemApi from '@/api/modules/system/systemApi'

const tableData: any = ref([
  {
    id: 1,
    name: '张三',
    type: '新增',
    pc: 'HKMJKJ<_152sss_mklk',
    create_time: '2024.2.20 18:50:32',
    system: 'windows',
    status: 1,
    webtype: '谷歌',
  },
  {
    id: 2,
    name: '李四',
    type: '修改',
    pc: '555HK_J<_152sss_mklk',
    create_time: '2024.2.20 18:50:32',
    system: 'windows',
    status: 1,
    webtype: '谷歌',
  },
  {
    id: 3,
    name: '王五',
    type: '删除',
    pc: '122kkkK_MJKJ_152sss_mklk',
    create_time: '2024.2.20 18:50:32',
    system: 'windows',
    status: 1,
    webtype: 'IE',
  },
  {
    id: 4,
    name: '赵六',
    type: '查询',
    pc: 'JNUJ_JKJ<_152sss_mklk',
    create_time: '2024.2.20 18:50:32',
    system: 'windows',
    status: 2,
    webtype: '谷歌',
  },

])
const loadingRef: any = ref(null)
const form: any = ref({})
const data = ref({
  page: 1,
  limit: 10,
})
const total: any = ref(0)
const paging: any = ref({
  page: data.value.page,
  limit: data.value.limit,
})

const currentPage4 = ref(1)
const pageSize4 = ref(10)
const background = ref(true)
const headerCellStyle = ref({
  backgroundColor: '#e5e4e4',
  color: '#333',
  padding: '8px 0',
  fontSize: '16px',
})
const dialogVisible = ref(false)
onMounted(() => {
  // loadingRef.value.open(1500)
  // getList()// 请求数据
})
function getList() {
  loadingRef.value.open(1500)

  // systemApi.Ranklist(paging.value).then((res: any) => {
  //   tableData.value = res.data
  //   total.value = res.count
  // })
}
// 修改
const isedit: any = ref(null)
function modify(val: any) {
  isedit.value = true
  if (!val) {
    form.value = {}
    dialogVisible.value = !dialogVisible.value
  }
  else {
    form.value = JSON.parse(JSON.stringify(val))
    dialogVisible.value = !dialogVisible.value
  }
}

function openEdit() {
  isedit.value = false
  dialogVisible.value = !dialogVisible.value
}
// 分页
function sizeChange(val: any) {
  // console.log(val, '55555')
}
// 分页
function handleCurrentChange(val: any) {
  paging.value.page = val
  getList()
}
function submitForm() {
  console.log(form.value, ' form.value')

  if (!form.value.name) {
    ElMessage({ message: '请输入角色名称', type: 'error' })
    return false
  }
  // systemApi.Rank_edit(form.value).then((res: any) => {
  //   ElMessage({ message: res.msg, type: 'success' })
  //   dialogVisible.value = false
  //   getList()
  // })
  if (form.value.id) {
    tableData.value.forEach((element: any, index: any) => {
      if (element.id === form.value.id) {
        tableData.value[index] = form.value
      }
    })
  }
  else {
    form.value.is_edit = true
    form.value.id = tableData.value.length + 1
    tableData.value.push(form.value)
  }
  dialogVisible.value = false
}
</script>

<template>
  <div>
    <page-main style="position: relative;">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="用户:">
          <el-input v-model="paging.name" placeholder="请输入用户" clearable @clear="paging.name = null" />
        </el-form-item>
        <el-form-item label="时间:">
          <el-date-picker
            v-model="paging.create_time" type="datetimerange" range-separator="至" start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="getList()">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button
            @click="paging = {
              page: 1,
              limit: 10,
              name: null,
              create_time: null,
            }, getList()"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- <div class="btnbox">
        <el-button type="primary" size="default" @click="openEdit">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          添加
        </el-button>
      </div> -->
      <div style="position: relative;">
        <Loading ref="loadingRef" />
        <el-table
          :header-cell-style="headerCellStyle" :data="tableData" highlight-current-row :border="false"
          height="calc(100vh - 300px)"
        >
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column prop="id" label="ID" width="60" align="center" />

          <el-table-column prop="name" label="成员" width="120" align="center" />
          <el-table-column prop="pc" label="IP地址" align="center" />

          <el-table-column prop="system" label="操作系统" width="120" align="center" />
          <el-table-column prop="webtype" label="浏览器" width="120" align="center" />

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row: i }">
              <el-tag v-if="i.status === 1" type="success">
                登录
              </el-tag>
              <el-tag v-if="i.status === 2" type="danger">
                退出
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="登录时间" width="180" align="center" />

          <!-- <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row: i }">
              <el-switch
                v-model="i.status" :active-value="1" :inactive-value="0"
                @change="onChangeStatus(i)"
              />
            </template>
          </el-table-column> -->

          <!-- <el-table-column prop="roles" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.create_time }}
              </template>
            </el-table-column> -->
          <!-- <el-table-column fixed="right" label="操作" width="160" align="center">
            <template #default="scope">
              <el-button type="primary" :disabled="!scope.row.is_edit" text @click="modify(scope.row)">
                <template #icon>
                  <svg-icon name="ep:edit" />
                </template>修改
              </el-button>
              <el-popconfirm title="确定删除吗？" @confirm="removeBatch(scope.row.id)">
                <template #reference>
                  <el-button type="danger" text>
                    <template #icon>
                      <svg-icon name="ep:delete" />
                    </template>移除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column> -->
        </el-table>

        <el-pagination
          v-model:current-page="currentPage4" v-model:page-size="pageSize4" :page-sizes="[10, 20, 50, 100]"
          :background="background" layout="total, sizes, prev, pager, next, jumper" :total="total"
          @size-change="sizeChange" @current-change="handleCurrentChange"
        />
      </div>
    </page-main>

    <el-dialog v-model="dialogVisible" :title="form.id ? '修改角色' : '添加角色'" width="480px">
      <el-form :model="form" label-width="86px">
        <el-form-item label="角色名称：" prop="name" class="mt24">
          <el-input v-model="form.name" placeholder="请输入职位" />
        </el-form-item>
        <el-form-item label="角色描述：" prop="remark" class="mt24">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入角色描述" />
        </el-form-item>
        <el-form-item label="成员数量：" prop="num">
          <el-input-number
            v-model="form.num" style="width: 100%;" placeholder="请输入成员数量" controls-position="right"
            class="ele-fluid ele-text-left"
          />
        </el-form-item>
        <!-- <el-form-item label="排序：" prop="sort">
          <el-input-number
            v-model="form.sort"
            style="width: 100%;"
            :min="0"
            placeholder="请输入排序号"
            controls-position="right"
            class="ele-fluid ele-text-left"
          />
        </el-form-item> -->
        <!-- <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              启用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  :deep(.main-container) {
    padding: 15px 32px;
  }

  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }
</style>
