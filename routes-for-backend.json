[{"id": 1, "tenantId": 1, "serviceName": "admin-service", "code": "ONE_SYSTEM", "name": "一体", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": "/one", "icon": "i-ri:database-2-line", "sortOrder": 1, "description": "一体化管理模块", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": null, "children": [{"id": 2, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_MANAGEMENT", "name": "制度管理", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": "/one", "icon": "i-heroicons-solid:menu-alt-3", "sortOrder": 1, "description": "制度管理功能", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 1, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": [{"id": 3, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_LIBRARY_MANAGE", "name": "制度库管理", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": "/one/systemManagement/index", "icon": null, "sortOrder": 1, "description": "制度库管理页面", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 2, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": [{"id": 4, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_DETAIL_VIEW", "name": "制度详情", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/one/systemManagement/detail", "icon": null, "sortOrder": 1, "description": "查看制度详情", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 3, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}, {"id": 5, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_ADD_EDIT", "name": "新增制度", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/one/systemManagement/addEdit", "icon": null, "sortOrder": 2, "description": "新增或编辑制度", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 3, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 6, "tenantId": 1, "serviceName": "admin-service", "code": "REGULATORY_CONVERSION", "name": "法规转化", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": "/one/regulatoryConversion/index", "icon": null, "sortOrder": 2, "description": "法规转化功能", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 2, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": [{"id": 7, "tenantId": 1, "serviceName": "admin-service", "code": "REGULATORY_DETAIL_VIEW", "name": "制度详情", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/one/regulatoryConversion/detail", "icon": null, "sortOrder": 1, "description": "查看法规转化详情", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 6, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}, {"id": 8, "tenantId": 1, "serviceName": "admin-service", "code": "REGULATORY_ADD_EDIT", "name": "新增制度", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/one/regulatoryConversion/addEdit", "icon": null, "sortOrder": 2, "description": "新增或编辑法规转化", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 6, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}, {"id": 9, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_REVIEW", "name": "制度新增与审查", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": "/system/review", "icon": null, "sortOrder": 3, "description": "制度新增与审查功能", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 2, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": [{"id": 10, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_REVIEW_DETAIL", "name": "制度审查详情", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/system/review/detail", "icon": null, "sortOrder": 1, "description": "查看制度审查详情", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 9, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}, {"id": 11, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_REVIEW_REPORT", "name": "制度审查报告", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/system/review/report", "icon": null, "sortOrder": 2, "description": "查看制度审查报告", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 9, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}, {"id": 12, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_REVIEW_ADD_EDIT", "name": "新增审核记录", "resourceType": "BUTTON", "urlPattern": null, "method": null, "frontendRoute": "/system/review/addEdit", "icon": null, "sortOrder": 3, "description": "新增或编辑审核记录", "metadata": "{\"sidebar\": false, \"breadcrumb\": false}", "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": {"id": 9, "tenantId": null, "serviceName": null, "code": null, "name": null, "resourceType": null, "urlPattern": null, "method": null, "frontendRoute": null, "icon": null, "sortOrder": null, "description": null, "metadata": null, "version": null, "createdBy": null, "createdAt": null, "updatedBy": null, "updatedAt": null, "isDeleted": null, "parent": null, "children": []}, "children": []}]}]}]}, {"id": 13, "tenantId": 1, "serviceName": "admin-service", "code": "PREVENTION_WING", "name": "预防之翼", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": "i-ri:database-2-line", "sortOrder": 2, "description": "预防之翼模块", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": null, "children": []}, {"id": 14, "tenantId": 1, "serviceName": "admin-service", "code": "MONITOR_WING", "name": "监控之翼", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": "i-ri:database-2-line", "sortOrder": 3, "description": "监控之翼模块", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": null, "children": []}, {"id": 15, "tenantId": 1, "serviceName": "admin-service", "code": "PERSONAL_CENTER", "name": "个人中心", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": "i-ri:database-2-line", "sortOrder": 4, "description": "个人中心模块", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": null, "children": []}, {"id": 16, "tenantId": 1, "serviceName": "admin-service", "code": "SYSTEM_SETTINGS", "name": "系统设置", "resourceType": "MENU", "urlPattern": null, "method": null, "frontendRoute": null, "icon": "i-ri:database-2-line", "sortOrder": 5, "description": "系统设置模块", "metadata": null, "version": 1, "createdBy": "system", "createdAt": "2025-01-27T00:00:00Z", "updatedBy": "system", "updatedAt": "2025-01-27T00:00:00Z", "isDeleted": false, "parent": null, "children": []}]