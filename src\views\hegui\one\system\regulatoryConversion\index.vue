<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const router = useRouter()
const tableLoading = ref(false)
// 左侧树
interface Tree {
  label: string
  categoryName: string
  id: number
  children?: Tree[]
}

const _data: Tree[] = [
  {
    id: 1,
    label: '发布机构',
    categoryName: '发布机构',
    children: [
      {
        id: 2,
        label: '商务部',
        categoryName: '商务部',
        children: [],
      },
    ],
  },
  {
    id: 3,
    label: '行业分类',
    categoryName: '行业分类',
    children: [
      {
        id: 4,
        label: '金融业务',
        categoryName: '金融业务',
        children: [],
      },
    ],
  },
  {
    id: 6,
    label: '新分类1',
    categoryName: '新分类1',
    children: [],
  },
  {
    id: 7,
    label: '测试法律法规分类',
    categoryName: '测试法律法规分类',
    children: [],
  },
]

const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
const dataList: any = ref([{
  regulationName: '国家卫生健康委关于发布推荐性卫生行业标准《输血医学术语》的通告',
  taskName: '第一个转化任务',
  enterpriseRegulation: '卫生行业标准',
  createdBy: 'xuk',
  status: 'PROGRESS',
  level: 'MIDDLE',
  startDateStart: '2025-04-29',
  startDateEnd: '2025-05-20',
  finishDateStart: '2025-04-29',
  finishDateEnd: '2025-05-20',
}])

const classificationList: any = ref([])
const props1 = {
  children: 'children',
  label: 'categoryName',
  value: 'id',
}
const groupList: any = ref([])

onMounted(() => {
  getList()
  getClassification()
})
function pagChange(val: any) {
  console.log(val)
  paging.value.page = val.page
  paging.value.limit = val.limit
  getList()
}
function getList() {
  tableLoading.value = true
  const params = {
    page: Number(paging.value.page),
    limit: Number(paging.value.limit),
  }

  // 添加筛选条件
  if (paging.value.taskName) { params.taskName = paging.value.taskName }
  if (paging.value.enterpriseRegulation) { params.enterpriseRegulation = paging.value.enterpriseRegulation }
  if (paging.value.status) { params.status = paging.value.status }

  systemApi.regulatoryConversion(params).then((res: any) => {
    tableLoading.value = false
    paging.value.total = res.totalElements ? res.totalElements : 0
    dataList.value = res.content ? res.content : []
  })
}

function getClassification() {
  systemApi.categories({}, 'tree').then((res: any) => {
    classificationList.value = res
    groupList.value = res
  })
}

function changegroup(_value: any) {
  // 处理分组变化
}

// 查看详情
function goDetail(id: any) {
  router.push({
    name: '/one/regulatoryConversion/detail',
    query: { id },
  })
}

// 处理表格选择变化
function handleSelectionChange(_selection: any) {
  // 处理选择变化
}

// 删除转化任务
function deleteTask(id: any) {
  ElMessageBox.confirm(
    '确定要删除这个转化任务吗？',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    // 调用删除接口
    systemApi.regulatoryConversion({ id }, 'delete').then(() => {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    }).catch(() => {
      ElMessage.error('删除失败')
    })
  }).catch(() => {
    // 用户取消删除
  })
}

// 审核转化任务
function auditTask(id: any, isAudited: boolean) {
  const actionText = isAudited ? '通过' : '不通过'
  ElMessageBox.confirm(
    `确定要${actionText}这个转化任务吗？`,
    '审核确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    // 显示loading
    const loading = ElLoading.service({
      lock: true,
      text: `正在${actionText}审核...`,
      background: 'rgba(0, 0, 0, 0.7)',
    })

    // 调用审核接口
    systemApi.auditRegulatoryConversion({ id, isAudited }).then(() => {
      ElMessage.success(`审核${actionText}成功`)
      getList() // 重新加载列表
    }).catch(() => {
      ElMessage.error(`审核${actionText}失败`)
    }).finally(() => {
      // 隐藏loading
      loading.close()
    })
  }).catch(() => {
    // 用户取消审核
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              法规转化
            </h1>
          </div>
          <!-- <div class="flex space-x-3">
            <el-button v-auth="['/regulatoryConversion/add']" type="primary" @click="goEdit(null)">
              <svg-icon name="ep:plus" />
              <span class="ml-4">新增转化任务</span>
            </el-button>
          </div> -->
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div class="card flex p-16">
        <div class="ml-32">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="任务名称:">
              <el-input v-model="paging.taskName" clearable placeholder="搜索任务名称..." @clear="paging.taskName = null" />
            </el-form-item>
            <el-form-item label="产出制度:">
              <el-input v-model="paging.enterpriseRegulation" clearable placeholder="搜索产出制度..." @clear="paging.regulationName = null" />
            </el-form-item>
            <!-- <el-form-item label="发布机构:">
              <el-cascader
                v-model="paging.group_id" :props="props1" clearable class="w-full" :options="groupList"
                @change="changegroup" @clear="paging.group_id = null"
              />
            </el-form-item> -->
            <el-form-item label="全部状态" style="width: 200px;">
              <el-select v-model="paging.status" clearable placeholder="请选择状态" @clear="paging.status = null">
                <el-option label="进行中" value="PROGRESS" />
                <el-option label="已完成" value="FINISHED" />
                <el-option label="已超时" value="OVERTIME" />
                <el-option label="已取消" value="CANCEL" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-debounce="2000" v-auth="['regulatoryConversion/index/search']" type="primary" @click="getList()">
                查询
              </el-button>
              <el-button
                v-auth="['regulatoryConversion/index/reset']"
                @click="paging = {
                  page: 1,
                  limit: 10,
                  total: 0,
                  taskName: null,
                  enterpriseRegulation: null,
                  status: null,
                }, getList()"
              >
                重置
              </el-button>
              <!-- <el-button>
                <svg-icon name="ep:filter" />
                <span class="ml-4">高级筛选</span>
              </el-button> -->
              <!-- <el-button type="danger" @click="removealldata()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                批量删除
              </el-button> -->
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!--      <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="(_i, j) in tabsList" :key="j" :label="_i.name" :name="_i.id"></el-tab-pane>
        </el-tabs>
      </div> -->
      <div class="conBox mt-20">
        <!-- <LayoutContainer style="padding: 0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="280"> -->
        <!-- <template #leftSide>
            <div class="aic jcsb flex">
              <div class="f-16 f-500">
                法规分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree
                :data="classificationList" :props="defaultProps" :default-expand-all="true"
                @node-click="handleNodeClick"
              />
            </div>
          </template> -->
        <div>
          <el-card class="">
            <!-- <image-preview :src="i.image" :width="60" :height="60" /> -->
            <el-table v-loading="tableLoading" :data="dataList" min-height="200" border @selection-change="handleSelectionChange">
              <el-table-column prop="taskName" label="任务名称" min-width="150" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="goDetail(row.id)">
                    {{ row.taskName }}
                  </el-link>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="id" label="发布机构" width="100" align="center" /> -->
              <!-- <el-table-column prop="id" label="制度类型" width="100" align="center" /> -->
              <el-table-column prop="startDate" label="发布日期" width="180" align="center" />
              <el-table-column prop="finishDate" label="完成日期" width="180" align="center">
                <template #default="{ row: i }">
                  <!-- {{ i.send_time>0?dayjs(i.send_time * 1000).format('YYYY-MM-DD'):'' }} -->
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row: i }">
                  <el-tag v-if="i.status === 'PROGRESS'" type="primary">
                    进行中
                  </el-tag>
                  <el-tag v-else-if="i.status === 'FINISHED'" type="success">
                    已完成
                  </el-tag>
                  <el-tag v-else-if="i.status === 'OVERTIME'" type="warning">
                    已超时
                  </el-tag>
                  <el-tag v-else-if="i.status === 'CANCEL'" type="info">
                    已取消
                  </el-tag>
                  <el-tag v-else>
                    未知
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="240">
                <template #default="{ row }">
                  <div class="action-buttons">
                    <div class="button-row">
                      <el-button
                        v-auth="['regulatoryConversion/index/detail']"
                        type="primary"
                        text
                        size="small"
                        class="action-btn"
                        @click="goDetail(row.id)"
                      >
                        详情
                      </el-button>
                      <el-button
                        v-auth="['regulatoryConversion/index/delete']"
                        type="danger"
                        text
                        size="small"
                        class="action-btn"
                        @click="deleteTask(row.id)"
                      >
                        删除
                      </el-button>
                    </div>
                    <div v-if="row.status === 'PROGRESS'" class="button-row">
                      <el-button
                        v-auth="['regulatoryConversion/index/approve']"
                        type="success"
                        text
                        size="small"
                        class="action-btn"
                        @click="auditTask(row.id, true)"
                      >
                        通过
                      </el-button>
                      <el-button
                        v-auth="['regulatoryConversion/index/approve']"
                        type="warning"
                        text
                        size="small"
                        class="action-btn"
                        @click="auditTask(row.id, false)"
                      >
                        不通过
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon
              :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange"
            />
          </el-card>
        </div>
        <!-- </LayoutContainer> -->
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/toolsCss";

.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
}

.conBox {
  :deep(.main) {
    background-color: transparent;

    .main-container {
      padding: 0 !important;
    }

    .el-slider__button-wrapper {
      display: none;
    }

    .el-slider__runway.is-disabled .el-slider__bar {
      height: 8px;
      background: #4caf50;
      border-radius: 9999px;
    }
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
}

.button-row {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.action-btn {
  min-width: 70px;
  height: 28px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .el-icon {
    font-size: 12px;
  }
}
</style>
