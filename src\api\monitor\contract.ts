import api from '@/api/index'

export default {
  // 合同审查列表
  contractlist(paging: any, params: any) {
    const page = paging?.page
    const size = paging?.size
    return api.post(`/whiskerguardcontractservice/api/contract/reviews/page?page=${page - 1}&size=${size}`, params)
  },

  // 合同审查详情查询
  contractDetail(id: string) {
    return api.get(`/whiskerguardcontractservice/api/contract/reviews/${id}`)
  },

  // 合同审查搜索
  contractSearch(params: any) {
    return api.post('/whiskerguardcontractservice/api/contract/reviews/search', params)
  },
}
