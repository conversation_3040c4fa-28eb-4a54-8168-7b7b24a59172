# AI智能问答功能

## 功能概述

本项目成功将小程序AI智能问答功能同步到PC端，提供了完整的AI对话体验，包括文件上传、Markdown渲染、代码高亮等功能。

## 主要特性

### ✨ 核心功能
- **AI智能对话**：支持多轮对话，上下文理解
- **文档上传**：支持PDF、DOC、PPT、XLS、TXT及代码文件
- **Markdown渲染**：完整支持Markdown语法，包括表格、代码块、引用等
- **代码高亮**：使用highlight.js提供语法高亮
- **流式输出**：模拟打字机效果的实时响应
- **文件预览**：支持上传文档的在线预览
- **模型切换**：支持多种AI模型选择

### 🎨 用户界面
- **现代化设计**：采用Element Plus组件库
- **响应式布局**：适配不同屏幕尺寸
- **直观操作**：拖拽上传、一键发送
- **美观样式**：渐变背景、圆角设计、阴影效果

## 技术栈

### 前端技术
- **Vue 3** + **TypeScript**：现代化前端框架
- **Element Plus**：UI组件库
- **Vite**：构建工具
- **Pinia**：状态管理
- **Vue Router**：路由管理

### 核心依赖
- **marked**：Markdown解析和渲染
- **highlight.js**：代码语法高亮
- **axios**：HTTP请求库

## 文件结构

```
src/
├── views/hegui/one/qa/
│   └── index.vue                 # 主页面
├── api/complianceApi/one/
│   └── qa.ts                     # API接口定义
├── components/
│   ├── MarkdownRenderer/
│   │   └── index.vue             # Markdown渲染组件
│   └── DemoChat/
│       └── index.vue             # 演示聊天组件
```

## API接口

### 主要接口
- `getSessionId()` - 获取会话ID
- `getModelList()` - 获取可用AI模型列表
- `queryChatStream()` - 流式对话接口
- `uploadFile()` - 文件上传
- `deleteFile()` - 文件删除
- `previewFile()` - 文件预览
- `getChatHistory()` - 获取聊天历史

## 使用说明

### 1. 基本对话
1. 在输入框中输入问题
2. 点击发送按钮或按Ctrl+Enter
3. AI将实时回复，支持Markdown格式

### 2. 文档上传
1. 点击"上传文档"按钮
2. 选择或拖拽文件到上传区域
3. 支持的格式：PDF、DOC、PPT、XLS、TXT、代码文件
4. 上传后可以选择文档作为对话上下文

### 3. 文件管理
- **预览**：点击文件的"预览"按钮查看内容
- **删除**：点击"删除"按钮移除文件
- **选择**：点击文件项将其设为对话上下文

### 4. 模型切换
- 在页面顶部选择不同的AI模型
- 支持根据需求选择最适合的模型

## 演示功能

页面包含一个完整的演示组件，无需后端API即可体验：
- 预设的智能回答
- 完整的Markdown渲染
- 代码高亮显示
- 表格和列表支持

## 开发指南

### 环境要求
- Node.js 18+
- pnpm 包管理器

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

## 扩展功能

### 已实现
- ✅ Markdown渲染
- ✅ 代码高亮
- ✅ 文件上传
- ✅ 文件预览
- ✅ 演示模式

### 可扩展
- 🔄 数学公式渲染（LaTeX）
- 🔄 图片上传和预览
- 🔄 语音输入
- 🔄 对话导出
- 🔄 主题切换

## 注意事项

1. **依赖版本**：marked使用v12版本以兼容Node.js 18
2. **文件大小**：单个文件限制10MB
3. **API集成**：需要后端API支持完整功能
4. **浏览器兼容**：建议使用现代浏览器

## 参考项目

本实现参考了以下开源项目的设计理念：
- [chatdocs](https://github.com/marella/chatdocs) - 文档聊天功能
- [Chat_On_PDFs](https://github.com/DorGetter/Chat_On_PDFs) - PDF聊天
- [chat-with-your-doc](https://github.com/linjungz/chat-with-your-doc) - 多格式文档支持

## 更新日志

### v1.0.0 (2024-07-19)
- ✨ 初始版本发布
- ✨ 完整的AI问答界面
- ✨ 文件上传和管理
- ✨ Markdown渲染和代码高亮
- ✨ 演示功能
- ✨ API接口定义

---

**开发团队**：AI助手 & 用户协作开发
**技术支持**：基于Vue 3 + TypeScript + Element Plus