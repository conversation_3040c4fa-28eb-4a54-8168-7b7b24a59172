<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCloseFilled,
  Clock,
  Document,
  InfoFilled,
  Star,
  SuccessFilled,
  Trophy,
  VideoPlay,
  Warning,
} from '@element-plus/icons-vue'
import assessApi from '@/api/complianceApi/prevention/assess'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面参数
const examId = ref<string>('')
const examRecordId = ref<string>('')

// 考试详情数据
interface ExamDetail {
  examId?: string
  examName?: string
  examDescription?: string
  examDuration?: number
  questionCount?: number
  totalScore?: number
  passScore?: number
  scorePerQuestion?: number
  examNotice?: string
  hasParticipated?: boolean
  latestExamStatus?: string
  latestExamRecordId?: string
  latestScore?: number
  latestIsPassed?: boolean
}

const examDetail = ref<ExamDetail>({})
const loading = ref(false)

// 考试状态映射
function getExamStatus(latestExamStatus?: string, hasParticipated?: boolean): number {
  if (!hasParticipated) {
    return 0 // 未开始
  }
  if (latestExamStatus === 'IN_PROGRESS') {
    return 1 // 进行中
  }
  if (latestExamStatus === 'COMPLETED') {
    return 2 // 已完成
  }
  return 0
}

// 动态计算的状态
const examStatus = computed(() => {
  return getExamStatus(examDetail.value.latestExamStatus, examDetail.value.hasParticipated)
})

const examStatusText = computed(() => {
  switch (examStatus.value) {
    case 0: return '未开始'
    case 1: return '进行中'
    case 2: return '已完成'
    default: return ''
  }
})

// 获取状态标签类型
function getStatusTagType(status: number) {
  switch (status) {
    case 0: return 'info'
    case 1: return 'warning'
    case 2: return 'success'
    default: return 'info'
  }
}

// 返回上一页
function handleBack() {
  router.back()
}

// 处理开始考试
async function handleStartExam() {
  if (!examId.value) {
    ElMessage.error('缺少考试ID')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要开始考试吗？开始后将计时，请确保网络稳定。',
      '开始考试',
      {
        confirmButtonText: '开始考试',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 跳转到考试页面
    ElMessage.success('正在进入考试页面...')
    router.push({
      path: '/training/examination/center',
      query: {
        examId: examId.value,
        examDuration: examDetail.value.examDuration,
      },
    })
  }
  catch {
    // 用户取消
  }
}

// 处理错题解析
function handleWrongAnalysis() {
  if (!examDetail.value.latestExamRecordId) {
    ElMessage.error('缺少考试记录ID')
    return
  }

  // 跳转到错题解析页面
  router.push({
    path: '/training/examination/error',
    query: {
      examRecordId: examDetail.value.latestExamRecordId,
    },
  })
}

// 获取考试详情
async function loadExamDetail() {
  if (!examId.value) {
    ElMessage.error('缺少考试ID')
    return
  }

  loading.value = true

  try {
    const response = await assessApi.getExamDetail(examId.value)
    if (response) {
      examDetail.value = response
    }
  }
  catch (error) {
    console.error('获取考试详情失败:', error)
    ElMessage.error('获取考试详情失败')
  }
  finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  // 从路由参数获取考试ID
  if (route.query.examId) {
    examId.value = route.query.examId as string
  }

  if (route.query.examRecordId) {
    examRecordId.value = route.query.examRecordId as string
  }

  // 加载考试详情
  if (examId.value) {
    loadExamDetail()
  }
})
</script>

<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <PageHeader>
      <template #content>
        <div class="flex items-center justify-between">
          <h1 class="font-bold text-black">
            考试详情
          </h1>
          <el-button v-if="examStatus === 0" type="primary" @click="handleBack">
            返回
          </el-button>
        </div>
      </template>
    </PageHeader>

    <!-- 主要内容区域 -->
    <PageMain>
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-loading-spinner />
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!examDetail.examId" class="empty-container">
        <el-empty description="暂无考试详情">
          <el-button type="primary" @click="loadExamDetail">
            重新加载
          </el-button>
        </el-empty>
      </div>

      <!-- 考试详情内容 -->
      <div v-else class="detail-content">
        <!-- 考试基本信息卡片 -->
        <el-card class="exam-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2 class="exam-title">
                {{ examDetail.examName }}
              </h2>
              <el-tag :type="getStatusTagType(examStatus) as any" size="large">
                {{ examStatusText }}
              </el-tag>
            </div>
          </template>

          <!-- 考试描述 -->
          <div v-if="examDetail.examDescription" class="exam-desc">
            <div v-html="examDetail.examDescription" />
          </div>

          <!-- 考试信息网格 -->
          <el-row :gutter="24" class="exam-info-grid">
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">
                  考试时长
                </div>
                <div class="info-value">
                  {{ examDetail.examDuration }}分钟
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">
                  题目数量
                </div>
                <div class="info-value">
                  {{ examDetail.questionCount }}题
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">
                  总分
                </div>
                <div class="info-value">
                  {{ examDetail.totalScore }}分
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <div class="info-label">
                  及格分
                </div>
                <div class="info-value">
                  {{ examDetail.passScore }}分
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 考试须知卡片 -->
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <h3 class="notice-title">
              <el-icon><InfoFilled /></el-icon>
              考试须知
            </h3>
          </template>

          <!-- 显示接口返回的考试须知 -->
          <div v-if="examDetail.examNotice" class="notice-content">
            <div v-html="examDetail.examNotice" />
          </div>

          <!-- 默认考试信息列表 -->
          <div class="notice-list">
            <div class="notice-item">
              <el-icon color="#409eff">
                <Clock />
              </el-icon>
              <span class="notice-text">考试时长：{{ examDetail.examDuration }}分钟</span>
            </div>

            <div class="notice-item">
              <el-icon color="#409eff">
                <Document />
              </el-icon>
              <span class="notice-text">题目数量：{{ examDetail.questionCount }}题</span>
            </div>

            <div class="notice-item">
              <el-icon color="#409eff">
                <Star />
              </el-icon>
              <span class="notice-text">每题分值：{{ examDetail.scorePerQuestion }}分</span>
            </div>

            <div class="notice-item">
              <el-icon color="#409eff">
                <Trophy />
              </el-icon>
              <span class="notice-text">及格分数：{{ examDetail.passScore }}分</span>
            </div>

            <div v-if="examDetail.hasParticipated && examDetail.latestScore !== null" class="notice-item">
              <el-icon :color="examDetail.latestIsPassed ? '#67c23a' : '#f56c6c'">
                <component :is="examDetail.latestIsPassed ? 'SuccessFilled' : 'CircleCloseFilled'" />
              </el-icon>
              <span class="notice-text">
                最近成绩：{{ examDetail.latestScore }}分
                <el-tag :type="examDetail.latestIsPassed ? 'success' : 'danger'" size="small">
                  {{ examDetail.latestIsPassed ? '合格' : '不合格' }}
                </el-tag>
              </span>
            </div>
          </div>
        </el-card>

        <!-- 操作按钮区域 -->
        <div v-if="examDetail.examId" class="mt-6 flex justify-center gap-4">
          <!-- 开始考试按钮 -->
          <el-button
            v-auth="['examination/detail/startExam']"
            v-if="!examDetail.latestExamRecordId"
            type="primary"
            size="large"
            :icon="VideoPlay"
            class="action-button"
            @click="handleStartExam"
          >
            开始考试
          </el-button>

          <!-- 错题解析按钮 -->
          <el-button
            v-if="examDetail.latestExamRecordId"
            type="warning"
            size="large"
            :icon="Warning"
            class="action-button"
            @click="handleWrongAnalysis"
          >
            错题解析
          </el-button>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
  gap: 16px;
}

.loading-text {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.empty-container {
  padding: 100px 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 考试卡片样式 */
.exam-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.exam-desc {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.exam-info-grid {
  margin-top: 24px;
}

.info-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
}

.info-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.info-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-color-primary);
}

/* 考试须知样式 */
.notice-card {
  margin-bottom: 24px;
}

.notice-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.notice-content {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notice-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 8px;
  transition: all 0.3s;
}

.notice-item:hover {
  background-color: var(--el-fill-color-light);
}

.notice-text {
  font-size: 14px;
  color: var(--el-text-color-regular);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 操作按钮样式 */
.action-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exam-info-grid .el-col {
    margin-bottom: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .exam-title {
    font-size: 20px;
  }
}
</style>
