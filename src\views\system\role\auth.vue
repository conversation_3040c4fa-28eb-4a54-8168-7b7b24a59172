<script setup lang="ts">
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import Api from '@/api/modules/system/menu'
  import Apirole from '@/api/modules/system/role'

  import { menuList } from './menuTest'
  // console.log('menuList', menuList)

  const props = defineProps<{
    id : any[]
  }>()

  const emits = defineEmits(['back'])
  const data : any = ref([])
  const allData : any = ref([])
  const arr : any = ref([])
  const defaultProps = {
    children: 'children',
    label: 'metaTitle',
  }
  const allids : any = ref([])
  onMounted(() => {
    getDataList()
    setTimeout(() => {
      PermissionLis()
    }, 300)
  })
  // 处理数据
  function recicleData(origin_data : any) {
    origin_data.forEach((ele : any) => {
      if (ele.meta && ele.meta.title) {
        ele.metaTitle = ele.meta.title
      }
      if (ele.meta && ele.meta.auth) {
        ele.auth = ele.meta.auth
      }
      if (ele.children && ele.children.length > 0) {
        recicleData(ele.children)
      }
    })
  }
  // 请求数据

  function getDataList() {
    console.log('酷酷酷')
    let res = menuList
    recicleData(res)
    allids.value = []
    res.forEach((i : any) => {
      console.log(i, '酷酷酷')
      allids.value.push(i.id)
      if (i && i.children) {
        i.children.forEach((it : any) => {
          allids.value.push(it.id)
          it.children.forEach((item : any) => {
            allids.value.push(item.id)
          })
        })
      }
    })
    data.value = res
    allData.value = res

    // console.log(props.id, 'id')
    // Api.list().then((res : any) => {
    //   recicleData(res.data)
    //   allids.value = []
    //   res.data.forEach((i : any) => {
    //     console.log(i, '酷酷酷')
    //     allids.value.push(i.id)
    //     if (i && i.children) {
    //       i.children.forEach((it : any) => {
    //         allids.value.push(it.id)
    //         it.children.forEach((item : any) => {
    //           allids.value.push(item.id)
    //         })
    //       })
    //     }
    //   })
    //   data.value = res.data
    //   allData.value = res.data
    //   console.log(res.data, allids.value, '菜单打印--列表1')
    // })
  }

  // 提交
  const tree = ref<any>()

  function onSubmit() {
    const data : any = ref(tree.value.getCheckedNodes())

    allData.value.forEach((item : any) => {
      data.value.forEach((i : any) => {
        if (item.id === i.parentId) {
          data.value.push(item)
        }
      })
    })
    allData.value.forEach((item : any) => {
      item.children.forEach((it : any) => {
        it.children.forEach((it1 : any) => {
          data.value.forEach((i : any) => {
            if (it.id === i.parentId) {
              data.value.push(it)
            }
          })
        })
      })
    })
    data.value = data.value.map((i : any) => {
      return i.id
    })
    const uniqueArr = [...new Set(data.value)]
    Apirole.savePermission({
      menu_id: uniqueArr,
      role_id: props.id,
    }).then((res : any) => {
      ElMessage({ message: res.msg, type: 'success' })
      console.log(res, '分配权限')
    })
    emits('back', true)
    // data.value // 后端上传数据id
    // 分配权限
  }
  function onCancel() {
    emits('back', true)
  }
  const defaultChecked = ref<any>([])
  function PermissionLis() {
    Apirole.getPermissionLis({
      role_id: props.id,
    }).then((res : any) => {
      console.log(res, 'res.data.includes(arr.value[i])')

      allData.value.forEach((item : any, index : any) => {
        arr.value = []
        item.children.forEach((it : any, ind : any) => {
          it.children.forEach((ite : any, indd : any) => {
            arr.value.push(ite.id)
            for (let i = 0; i < arr.value.length; i++) {
              if (!res.data.includes(arr.value[i])) {
                it.children.forEach((o : any) => {
                  if (arr.value[i] === o.id) {
                    const index = res.data.indexOf(o.parentId)
                    if (index !== -1) {
                      res.data.splice(index, 1)
                    }
                  }
                })
              }
            }
          })
        })
      })

      allData.value.forEach((item : any, index : any) => {
        arr.value = []
        item.children.forEach((it : any, ind : any) => {
          arr.value.push(it.id)
          for (let i = 0; i < arr.value.length; i++) {
            if (!res.data.includes(arr.value[i])) {
              if (item.children[i].id === arr.value[i]) {
                const index = res.data.indexOf(item.children[i].parentId)
                if (index !== -1) {
                  res.data.splice(index, 1)
                }
              }
            }
          }
        })
      })
      setTimeout(() => {
        defaultChecked.value = res.data
      }, 200)
    })
  }
</script>

<template>
  <div>
    <div v-if="defaultChecked" style="max-height: 600px;overflow: auto;">
      <el-tree ref="tree" :data="data" default-expand-all show-checkbox node-key="id"
        :default-checked-keys="defaultChecked" :props="defaultProps" />
    </div>
    <div class="el-dialog__footer">
      <span class="dialog-footer">
        <el-button @click="onCancel">
          取消
        </el-button>
        <el-button type="primary" @click="onSubmit">
          确定
        </el-button>
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .el-dialog__footer {
    // margin: 10px -20px -30px;
  }
</style>
