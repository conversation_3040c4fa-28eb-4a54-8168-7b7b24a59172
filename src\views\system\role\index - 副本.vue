<script setup lang="ts">
import { reactive, ref } from 'vue'
import moment from 'moment'

import type {
  FormInstance,
  FormRules,
} from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import Auths from './auth.vue'
import Api from '@/api/modules/system/role'
import useUserStore from '@/store/modules/user'

const userStore: any = useUserStore()

const showEdit = ref(false)
const id: any = ref('')
const dialogVisible: any = ref(false)// 权限弹框
function roleAdd(e: any) {
  showEdit.value = true
}

function authEdit(e: any) {
  id.value = e.id
  dialogVisible.value = true
}

const formInline = reactive({
  name: '',
})

function onSubmit() {
  getList()
}

function reset() {
  formInline.name = ''
  getList()
}
// 请求参数
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  sizes: [10, 20, 50, 100],
  layout: 'total, sizes, ->, prev, pager, next, jumper',
})
const tableData: any = ref([]) // 表格数据
// 请求数据
function getList() {
  Api.list({
    page: pagination.value.page,
    limit: pagination.value.size,
    name: formInline.name || '',
  }).then((res: any) => {
    console.log(res.data, '列表打印')
    tableData.value = res.data
    pagination.value.total = res.count
  }).catch(() => {
    // console.log(error, '列表打印')
  })
}
getList()
onActivated(() => {
  getList()
})
// 分配权限
function back() {
  dialogVisible.value = !dialogVisible.value
}

// 新增用户
const form: any = ref({})
const isAdd: any = ref(false)
// 定义提交按钮函数
const formRef = ref<FormInstance>()
const formRules = ref<FormRules>({
  name: [{
    required: true,
    message: '请输入角色名称',
    trigger: 'blur',
  }],
  sort: [{
    required: true,
    message: '请输入排序',
    trigger: 'blur',
  }],
  code: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择角色状态', trigger: 'blur' },
  ],
})
function handleAdd(e: any) {
  // console.log(e, '新增修改')
  if (e.id) {
    form.value = JSON.parse(JSON.stringify(e))
  }
  else {
    form.value = {}
    form.value.status = 1
  }
  isAdd.value = true
}
// 提交
function submitedit() {
  formRef.value && formRef.value.validate((valid) => {
    if (valid) {
      Api.edit(form.value).then((res: any) => {
        ElMessage({ message: res.msg, type: 'success' })
        isAdd.value = false
        getList()
      })
    }
  })
}
// 删除角色
function removeBatch(e: any) {
  Api.userdelete({
    id: e,
  }).then((res: any) => {
    ElMessage({ message: res.msg, type: 'success' })
    getList()
  })
}
function onChangeStatus(row: any) {
  if (!userStore.permissions.includes('roleedit')) {
    ElMessage({
      type: 'info',
      message: '您没有修改权限',
    })
    row.status = row.status === 1 ? 2 : 1
    return
  }
  ElMessageBox.confirm(
      `确认${row.status === 1 ? '启用' : '禁用'}当前项?`,
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
  )
    .then(() => {
      Api.edit(row).then(() => {
        getList()
      })
      ElMessage({
        type: 'success',
        message: '修改成功',
      })
    })
    .catch(() => {
      row.status = row.status === 1 ? 2 : 1
    })
}
</script>

<template>
  <div>
    <page-main class="pr">
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="角色名称:">
          <el-input v-model="formInline.name" placeholder="请输入角色名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button @click="reset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <div class="btnbox">
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>添加
        </el-button>
      </div>
      <el-table :data="tableData" highlight-current-row border height="calc(100vh - 315px)">
        <el-table-column prop="id" label="ID" width="60" align="center" fixed="left" />
        <el-table-column prop="name" label="角色名称" width="200" align="center" />
        <el-table-column prop="code" label="角色标识" width="140" align="center" />
        <el-table-column prop="status" label="角色状态" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status" :active-value="1" :inactive-value="2"
              @change="onChangeStatus(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" sortable width="100" align="center" />
        <el-table-column prop="note" label="备注" align="center" />
        <el-table-column prop="create_time" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ moment(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="update_time" label="更新时间" width="180" align="center">
          <template #default="scope">
            {{ scope.row.update_time > 0 ? moment(scope.row.update_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="230" align="center">
          <template #default="scope">
            <el-button type="primary" text @click="handleAdd(scope.row)">
              <template #icon>
                <svg-icon name="ep:edit" />
              </template>修改
            </el-button>
            <el-button type="primary" text @click="authEdit(scope.row)">
              <template #icon>
                <svg-icon name="ep:finished" />
              </template>分配权限
            </el-button>
            <el-popconfirm title="确定要删除吗？" @confirm="removeBatch(scope.row.id)">
              <template #reference>
                <el-button type="danger" text>
                  <template #icon>
                    <svg-icon name="ep:delete" />
                  </template>删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </page-main>

    <!-- 修改添加组件 -->
    <!-- <Edit v-model="showEdit" /> -->
    <!-- 分配权限 -->
    <el-dialog v-model="dialogVisible" title="分配权限" width="440px">
      <Auths v-if="dialogVisible" :id="id" @back="back" />
    </el-dialog>
    <!-- 添加修改 -->
    <el-dialog v-model="isAdd" :title="form.id ? '修改角色' : '新增角色' " width="460px">
      <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="角色标识" prop="code">
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item label="角色状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              在用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number
            v-model="form.sort" :min="1" style="width: 100%;" controls-position="right"
            class="ele-fluid ele-text-left"
          />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input v-model="form.note" type="textarea" />
        </el-form-item>

        <!-- <div style="display: flex;justify-content: center;">
            <el-button type="primary" @click="submitedit()">
              确定
            </el-button>
            <el-button @click="isAdd = false">
              取消
            </el-button>
          </div> -->
      </el-form>
      <template #footer>
        <el-button @click="isAdd = false">
          取消
        </el-button>
        <el-button type="primary" @click="submitedit()">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }

  .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
