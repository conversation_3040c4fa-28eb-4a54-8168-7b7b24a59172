import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/organizationalStructure',
  component: Layout,
  name: '/systemSettings/organizationalStructure',
  meta: {
    title: '组织架构',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/organizationalStructure/organizationalStructureSetting',
      name: '/systemSettings/organizationalStructure/organizationalStructureSetting',
      component: () => import('@/views/hegui/systemSettings/organizationalStructure/organizationalStructureSetting/index.vue'),
      meta: {
        title: '组织结构设置',
        // sidebar: false,
      },
      // children: [
      //   {
      //     path: '/systemSettings/organizationalStructure/toDoTasks/detail',
      //     name: '/systemSettings/organizationalStructure/toDoTasks/detail',
      //     component: () => import('@/views/hegui/systemSettings/organizationalStructure/toDoTasks/detail.vue'),
      //     meta: {
      //       title: '实时监控详情',
      //       sidebar: false,
      //       breadcrumb: false,
      //     },
      //   },
      // ]
    },
    {
      path: '/systemSettings/organizationalStructure/departmentManagement',
      name: '/systemSettings/organizationalStructure/departmentManagement',
      component: () => import('@/views/hegui/systemSettings/organizationalStructure/departmentManagement/index.vue'),
      meta: {
        title: '部门管理',
      },
    },
    {
      path: '/systemSettings/organizationalStructure/postManagement',
      name: '/systemSettings/organizationalStructure/postManagement',
      component: () => import('@/views/hegui/systemSettings/organizationalStructure/postManagement/index.vue'),
      meta: {
        title: '岗位管理',
      },
    },
    {
      path: '/systemSettings/organizationalStructure/menuManagement',
      name: '/systemSettings/organizationalStructure/menuManagement',
      component: () => import('@/views/hegui/systemSettings/organizationalStructure/menuManagement/index.vue'),
      meta: {
        title: '菜单管理',
      },
      children: [
        {
          path: '/systemSettings/organizationalStructure/menuManagement/addEdit',
          name: '/systemSettings/organizationalStructure/menuManagement/addEdit',
          component: () => import('@/views/hegui/systemSettings/organizationalStructure/menuManagement/addEdit.vue'),
          meta: {
            title: '新增菜单',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
