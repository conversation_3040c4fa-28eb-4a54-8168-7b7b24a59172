import api from '@/api/index'

export default {
  // 责任追究措施列表
  getResponsibilityMeasuresList(params: any): Promise<any> {
    return api.post(`/whiskerguardviolationservice/api/continuous/improvement/improves/search?page=${params.page}&size=${params.size}`, params)
  },

  // 根据ID查询指定的持续改进措施信息
  getImproveDetail(id: string): Promise<any> {
    return api.get(`/whiskerguardviolationservice/api/continuous/improvement/improves/${id}`)
  },

  // 创建新的持续改进措施
  createImprove(data: any): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/continuous/improvement/improves', data)
  },

  // 更新持续改进措施
  updateImprove(id: string, data: any): Promise<any> {
    return api.patch(`/whiskerguardviolationservice/api/continuous/improvement/improves/${id}`, data)
  },
  // 删除持续改进措施
  deleteImprove(id: string): Promise<any> {
    return api.delete(`/whiskerguardviolationservice/api/continuous/improvement/improves/${id}`)
  },
}
