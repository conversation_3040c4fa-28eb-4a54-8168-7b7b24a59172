import api from '@/api/index'

export default {
  // 菜单--新增/修改
  edit: (data: any) =>
    api.post(
      'dict/edit',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),
  // 列表
  list: (data: any) =>
    api.post('dict/index', {
      ...data,
    }),

  // 删除
  delete: (data: any) =>
    api.post(
      'dict/delete',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),

  // 详情
  detailDict: (data: any) =>
    api.post(
      'dictdata/info',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),

  // 菜单--新增/修改
  editDict: (data: any) =>
    api.post(
      'dictdata/edit',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),
  // 列表
  listDict: (data: any) =>
    api.post('dictdata/index', {
      ...data,
    }),

  // 删除
  deleteDict: (data: any) =>
    api.post(
      'dictdata/delete',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),

  // 详情
  detail: (data: any) =>
    api.post(
      'dict/info',
      {
        ...data,
      },
      {
        // baseURL: '/mock/',
      },
    ),

  // edit
  // edit: (data: any) => api.get('/dict/edit', {
  //   params: data,
  // }),
  // // 删除
  // delete: (data: any) => api.post('dict/delete', data, {
  // }),
  // // 字典名称
  // list: (data: any) => api.post('dict/index', data, {
  // }),
  // 内容列表
  index: (data: any) => api.post('dictdata/index', data, {}),
  dictdatadelete: (data: any) => api.post('dictdata/delete', data, {}),
  dictdataedit: (data: any) => api.post('dictdata/edit', data, {}),
  // 获取字典
  dictAll(types: any) {
    return api.get(`/whiskerguardregulatoryservice/api/dictionary/all?types=${types}`, { })
  },
  // 获取编号
  getCode(type: any) {
    return api.get(`/whiskerguardcontractservice/api/contract/reviews/code/${type}`, { })
  },
}
