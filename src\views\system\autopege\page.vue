<route lang="yaml">
  meta:
    title: 权限配置
  </route>

<script setup lang="ts">
import { ref } from 'vue'

const checkList = ref(['selected and disabled', 'Option A'])
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          权限配置
        </div>
      </template>
      <!-- <template #content>
        <div class="text-sm/6">
          <div>
            这是一款<b class="text-emphasis">开箱即用</b>的中后台框架，同时它也经历过数十个真实项目的技术沉淀，确保框架在开发中可落地、可使用、可维护
          </div>
        </div>
      </template> -->
      <!-- <HButton outline @click="open('https://fantastic-admin.gitee.io')">
        <SvgIcon name="i-ri:file-text-line" />
        开发文档
      </HButton> -->
      <!-- <HDropdownMenu
        :items="[
          [
            { label: 'Gitee', handle: () => open('https://gitee.com/fantastic-admin/basic') },
            { label: 'Github', handle: () => open('https://github.com/fantastic-admin/basic') },
          ],
        ]"
      >
        <HButton class="ml-2">
          <SvgIcon name="i-ri:code-s-slash-line" />
          代码仓库
          <SvgIcon name="i-ep:arrow-down" />
        </HButton>
      </HDropdownMenu> -->
    </PageHeader>
    <PageMain>
      <div class="border-2-999">
        <div class="flex">
          <div class="bor-b bor-r fontW-600 bg-ccc h-10 w-50 flex items-center justify-center">
            模块
          </div>
          <div class="bg-ccc bor-b fontW-600 h-10 w-full flex items-center justify-center">
            功能
          </div>
        </div>
        <div class="flex">
          <div class="bor-r bor-b w-50 flex items-center justify-center">
            客人管理
          </div>
          <div class="d-c w-full flex">
            <div class="bor-b h-10 w-full flex items-center p2">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="客人管理总权限" />
              </el-checkbox-group>
            </div>
            <div class="bor-b w-full flex">
              <div class="w-full flex">
                <div class="bor-r w-30 flex items-center p2">
                  添加客人
                </div>
                <div class="d-c w-full flex">
                  <div class="h-10 w-full flex items-center p2">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox label="增加客人" />
                      <el-checkbox label="智能录入" />
                    </el-checkbox-group>
                  </div>
                  <div class="h-10 w-full flex items-center p2">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox label="修改" />
                      <el-checkbox label="删除" />
                      <el-checkbox label="日志" />
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
            </div>
            <div class="bor-b w-full flex">
              <div class="w-full flex">
                <div class="bor-r w-30 flex items-center p2">
                  搜索客人
                </div>
                <div class="d-c w-full flex">
                  <div class="h-10 w-full flex items-center p2">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox label="搜索" />
                      <el-checkbox label="搜索设置" />
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
            </div>
            <div class="bor-b w-full flex">
              <div class="w-full flex">
                <div class="bor-r w-30 flex items-center p2">
                  回收站
                </div>
                <div class="d-c w-full flex">
                  <div class="h-10 w-full flex items-center p2">
                    <el-checkbox-group v-model="checkList">
                      <el-checkbox label="查看回收站" />
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="bor-r w-50 flex items-center justify-center">
            计调成团
          </div>
          <div class="d-c w-full flex">
            <div class="bor-b h-10 w-full flex items-center p2">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="计调成团总权限" />
              </el-checkbox-group>
            </div>
            <div class="w-full flex">
              <div class="w-full flex">
                <div class="bor-r w-30 flex items-center p2">
                  成团
                </div>
                <div class="d-c w-full flex">
                  <div class="w-full flex">
                    <div class="bor-r bor-b w-30 flex items-center p2">
                      成团功能
                    </div>
                    <div class="d-c w-full flex">
                      <div class="h-10 w-full flex items-center p2">
                        <el-checkbox-group v-model="checkList">
                          <el-checkbox label="发起成团" />
                          <el-checkbox label="导出数据" />
                        </el-checkbox-group>
                      </div>
                      <div class="h-10 w-full flex items-center p2">
                        <el-checkbox-group v-model="checkList">
                          <el-checkbox label="修改" />
                          <el-checkbox label="删除" />
                        </el-checkbox-group>
                      </div>
                      <div class="h-10 w-full flex items-center p2">
                        <el-checkbox-group v-model="checkList">
                          <el-checkbox label="立即审核" />
                          <el-checkbox label="取消审核" />
                          <el-checkbox label="设置团接" />
                          <el-checkbox label="整车转移" />
                          <el-checkbox label="操作日志" />
                        </el-checkbox-group>
                      </div>
                      <div class="bor-b h-10 w-full flex items-center p2">
                        <el-checkbox-group v-model="checkList">
                          <el-checkbox label="分享" />
                          <el-checkbox label="打印" />
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                  <div class="w-full flex">
                    <div class="bor-r w-30 flex items-center p2">
                      客人功能
                    </div>
                    <div class="d-c w-full flex">
                      <div class="h-10 w-full flex items-center p2">
                        <el-checkbox-group v-model="checkList">
                          <el-checkbox label="修改" />
                          <el-checkbox label="删除" />
                          <el-checkbox label="调车" />
                          <el-checkbox label="日志" />
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-checkbox__label) {
  font-size: 16px;
}

.border-2-999 {
  border: 2px solid #999;
}

.bg-ccc {
  background: #ccc;
}

.fontW-500 {
  font-weight: 500;
}

.fontW-600 {
  font-weight: 600;
}

.d-c {
  flex-direction: column;
}

.bor-r {
  border-right: 1px solid #999;
}

.bor-t {
  border-top: 1px solid #999;
}

.bor-l {
  border-left: 1px solid #999;
}

.bor-b {
  border-bottom: 1px solid #999;
}
</style>
