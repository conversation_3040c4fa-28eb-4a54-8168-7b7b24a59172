<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  ArrowDown,
  Delete,
  Document,
  Download,
  Edit,
  Help,
  Plus,
  Search,
  TrendCharts,
  Upload,
  View,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import dictApi from '@/api/modules/system/dict'
import ImportComponent from '@/components/import/index.vue'

const router = useRouter()
// 筛选条件
const departmentFilter = ref('')
const businessTypeFilter = ref('')
const riskLevelFilter = ref('')
const statusFilter = ref('')
const searchTerm = ref('')
// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
// 加载状态
const loading = ref(false)
// 导入弹窗状态
const importDialogVisible = ref(false)
// 表格数据
const riskList = ref([])

// 业务类型数据
const businessTypes = ref([] as { value: number, name: string, description?: string }[])

// 审批状态映射
const approvalStatusMap = {
  DRAFT: '草稿',
  APPROVED: '审核通过',
  REJECTED: '审核未通过',
  PENDING: '审批中',
}

// 风险等级映射
const riskLevelMap = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
}

// 获取状态名称
function getStatusName(status: string) {
  return approvalStatusMap[status as keyof typeof approvalStatusMap] || status
}

// 获取风险等级显示名称
function getRiskLevelName(level: string) {
  const levelMap: Record<string, string> = {
    HIGH: '高风险',
    MEDIUM: '中风险',
    LOW: '低风险',
  }
  return levelMap[level] || level
}

// 获取业务类型名称
function getBusinessTypeName(businessType: number | string) {
  if (businessType) {
    const businessTypeItem = businessTypes.value.find(item => item.value === businessType)
    return businessTypeItem?.name || '未知'
  }
}

// 获取部门名称
function getDepartmentName(departmentId: string) {
  const departmentMap: Record<string, string> = {
    LEGAL: '法务部',
    FINANCE: '财务部',
    HR: '人力资源部',
    PROCUREMENT: '采购部',
    SALES: '销售部',
    OTHER: '其他',
  }
  return departmentMap[departmentId] || departmentId
}
// 选中项
const selectedRisks = ref([])
function handleSelectionChange(val: any) {
  selectedRisks.value = val
}

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取业务类型数据
    const response = await dictApi.dictAll(88)
    businessTypes.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取业务类型数据失败')
  }
}

// 获取风险清单数据
async function getRiskList() {
  try {
    loading.value = true
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      riskLevelModel: riskLevelFilter.value || undefined,
      searchTerm: searchTerm.value || undefined,
      businessType: businessTypeFilter.value || undefined,
      approvalStatus: statusFilter.value || undefined,
    }

    const response = await threeListApi.getRiskIdentificationList(params)

    if (response && response.content) {
      riskList.value = response.content.map((item: any) => ({
        ...item,
        id: item.complianceRiskMainId || item.id,
        businessType: item.businessType, // 保持原始数字值
        riskDescription: item.riskDescription || '暂无描述',
        riskConsequence: item.riskConsequence || '暂无后果描述',
        mainDepartment: getDepartmentName(item.orgUnitId),
        riskLevelModel: riskLevelMap[item.riskLevelModel as keyof typeof riskLevelMap] || 'low',
        status: item.approvalStatus,
        createTime: item.createdAt,
      }))
      total.value = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取风险清单失败:', error)
    ElMessage.error('获取风险清单失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索功能
function handleSearch() {
  currentPage.value = 1
  getRiskList()
}

// 重置筛选条件
function handleReset() {
  departmentFilter.value = ''
  businessTypeFilter.value = ''
  riskLevelFilter.value = ''
  statusFilter.value = ''
  searchTerm.value = ''
  currentPage.value = 1
  getRiskList()
}

// 分页变化
function handlePageChange() {
  getRiskList()
}

function goAddEdit(item: any) {
  if (item?.id) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/complianceRisk/edit',
      query: { id: item.id },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/complianceRisk/edit',
    })
  }
}
// 查看风险清单详情
function goDetail(row: any) {
  router.push({
    name: '/threeListManagement/complianceRisk/detail',
    query: { id: row.id },
  })
}

// 导出Excel
async function handleExport() {
  try {
    loading.value = true
    const params = {
      page: 0,
      size: 999999, // 导出所有数据
      sort: 'createdAt',
      direction: 'desc',
      riskLevel: riskLevelFilter.value || undefined,
      riskDescriptionKeyword: searchTerm.value || undefined,
      businessType: businessTypeFilter.value || undefined,
      approvalStatus: statusFilter.value || undefined,
    }

    const response = await threeListApi.exportComplianceRiskList(params)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    link.download = `合规风险识别清单_${timestamp}.xlsx`

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  }
  catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 显示导入弹窗
function showImportDialog() {
  importDialogVisible.value = true
}

// 导入成功回调
function handleImportSuccess(result: any) {
  ElMessage.success(`导入成功！共处理 ${result.totalRows} 行，成功 ${result.successRows} 行`)
  // 刷新列表
  getRiskList()
}

// 导入失败回调
function handleImportError(error: any) {
  console.error('导入失败:', error)
}

// 图表
const departmentChart = ref<HTMLElement>()
const businessTypeChart = ref<HTMLElement>()
onMounted(() => {
  // 获取基础数据和风险清单数据
  loadBasicData()
  getRiskList()

  nextTick(() => {
  // 部门风险分布饼图
    const departmentChartInstance = echarts.init(departmentChart.value)
    departmentChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: ['法务部', '财务部', '人力资源部', '采购部', '销售部', '其他'],
      },
      series: [
        {
          name: '部门风险分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 18, name: '法务部', itemStyle: { color: '#5470C6' } },
            { value: 22, name: '财务部', itemStyle: { color: '#91CC75' } },
            { value: 15, name: '人力资源部', itemStyle: { color: '#FAC858' } },
            { value: 12, name: '采购部', itemStyle: { color: '#EE6666' } },
            { value: 10, name: '销售部', itemStyle: { color: '#73C0DE' } },
            { value: 9, name: '其他', itemStyle: { color: '#3BA272' } },
          ],
        },
      ],
    })
    // 业务类型分布条形图
    const businessTypeChartInstance = echarts.init(businessTypeChart.value)
    businessTypeChartInstance.setOption({
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        data: ['合同管理', '财务管理', '人事管理', '采购管理', '销售管理', '其他'],
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: '风险数量',
          type: 'bar',
          data: [
            {
              value: 18,
              itemStyle: {
                color: '#5470C6',
              },
            },
            {
              value: 22,
              itemStyle: {
                color: '#91CC75',
              },
            },
            {
              value: 15,
              itemStyle: {
                color: '#FAC858',
              },
            },
            {
              value: 12,
              itemStyle: {
                color: '#EE6666',
              },
            },
            {
              value: 10,
              itemStyle: {
                color: '#73C0DE',
              },
            },
            {
              value: 9,
              itemStyle: {
                color: '#3BA272',
              },
            },
          ],
          label: {
            show: true,
            position: 'right',
          },
        },
      ],
    })
    // 窗口大小变化时重绘图表
    window.addEventListener('resize', () => {
      departmentChartInstance.resize()
      businessTypeChartInstance.resize()
    })
  })
})
</script>

<template>
  <div>
    <!-- 主内容区 -->
    <div class="w-full px-4 py-4">
      <div class="flex space-x-6">
        <!-- 左侧内容区 -->
        <div class="flex-1 overflow-x-auto">
          <!-- 标题和操作按钮 -->
          <div class="mb-6 flex flex-col justify-between md:flex-row md:items-center">
            <h1 class="mb-4 text-xl text-gray-800 font-bold md:mb-0">
              合规风险识别清单
            </h1>
            <div class="flex items-center space-x-3">
              <el-button v-auth="'threeListManagement/complianceRisk/add'" @click="goAddEdit(null)">
                <el-icon class="mr-1">
                  <Plus />
                </el-icon>新增清单
              </el-button>
              <el-button v-auth="'threeListManagement/complianceRisk/export'" :loading="loading" @click="handleExport">
                <el-icon class="mr-1">
                  <Download />
                </el-icon>导出Excel
              </el-button>
              <el-button v-auth="'threeListManagement/complianceRisk/import'" type="success" @click="showImportDialog">
                <el-icon class="mr-1">
                  <Upload />
                </el-icon>导入
              </el-button>
            </div>
          </div>
          <!-- 筛选条件 -->
          <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div class="flex flex-wrap items-center gap-3">
              <div class="relative w-64">
                <el-input
                  v-model="searchTerm"
                  type="text"
                  placeholder="搜索风险描述、业务类型..."
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon class="text-gray-400">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </div>
              <el-button v-auth="'threeListManagement/complianceRisk/search'" type="primary" @click="handleSearch">
                搜索
              </el-button>
              <el-select v-model="departmentFilter" placeholder="部门" clearable class="w-40" @change="handleSearch">
                <el-option label="法务部" value="legal" />
                <el-option label="财务部" value="finance" />
                <el-option label="人力资源部" value="hr" />
                <el-option label="业务部门" value="business" />
                <el-option label="审计部" value="audit" />
              </el-select>
              <el-select v-model="businessTypeFilter" placeholder="业务类型" clearable class="w-40" @change="handleSearch">
                <el-option label="合同管理" value="contract" />
                <el-option label="财务管理" value="finance" />
                <el-option label="人事管理" value="hr" />
                <el-option label="采购管理" value="purchase" />
                <el-option label="销售管理" value="sales" />
                <el-option label="其他" value="other" />
              </el-select>
              <el-select v-model="riskLevelFilter" placeholder="风险等级" clearable class="w-40" @change="handleSearch">
                <el-option label="高风险" value="HIGH" />
                <el-option label="中风险" value="MEDIUM" />
                <el-option label="低风险" value="LOW" />
              </el-select>
              <el-select v-model="statusFilter" placeholder="状态" clearable class="w-40" @change="handleSearch">
                <el-option label="草稿" value="DRAFT" />
                <el-option label="审批中" value="PENDING" />
                <el-option label="审核通过" value="APPROVED" />
                <el-option label="审核未通过" value="REJECTED" />
              </el-select>
              <el-button v-auth="'threeListManagement/complianceRisk/reset'" @click="handleReset">
                重置
              </el-button>
            </div>
          </div>

          <!-- 导入弹窗 -->
          <ImportComponent
            v-model:visible="importDialogVisible"
            title="合规风险识别清单导入"
            :download-template-api="threeListApi.downloadComplianceRiskTemplate"
            :import-data-api="threeListApi.importComplianceRiskList"
            template-file-name="合规风险识别清单导入模板.xlsx"
            @success="handleImportSuccess"
            @error="handleImportError"
          />

          <!-- 风险列表表格 -->
          <div class="overflow-hidden rounded-lg bg-white shadow-sm">
            <el-table
              :data="riskList"
              :loading="loading"
              style="width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="id" label="序号" width="80" />
              <el-table-column prop="businessType" label="业务类型" width="120">
                <template #default="{ row }">
                  {{ getBusinessTypeName(row.businessType) }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="riskDescription" label="合规风险描述点" min-width="180">
                <template #default="{ row }">
                  <span>{{ row.riskDescription }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="riskConsequence" label="风险发生的责任或后果" min-width="200" />
              <el-table-column prop="mainDepartment" label="归口部门" width="120" /> -->
              <el-table-column prop="riskLevelModel" label="风险等级" width="100">
                <template #default="{ row }">
                  <span
                    :class="{
                      'bg-red-100 text-red-800': row.riskLevelModel === 'high',
                      'bg-orange-100 text-orange-800': row.riskLevelModel === 'medium',
                      'bg-green-100 text-green-800': row.riskLevelModel === 'low',
                    }"
                    class="rounded-full px-2 py-1 text-xs"
                  >
                    {{ getRiskLevelName(row.riskLevelModel.toUpperCase()) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="120" />
              <el-table-column prop="status" label="状态" width="120">
                <template #default="{ row }">
                  <span
                    :class="{
                      'bg-gray-100 text-gray-800': row.status === 'DRAFT',
                      'bg-yellow-100 text-yellow-800': row.status === 'PENDING',
                      'bg-green-100 text-green-800': row.status === 'APPROVED',
                      'bg-red-100 text-red-800': row.status === 'REJECTED',
                    }"
                    class="rounded-full px-2 py-1 text-xs"
                  >
                    {{ getStatusName(row.status) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right">
                <template #default="{ row }">
                  <div class="flex items-center gap-0.02">
                    <el-button v-auth="'threeListManagement/complianceRisk/view'" size="small" type="primary" @click="goDetail(row)">
                      查看
                    </el-button>
                    <el-button v-auth="'threeListManagement/complianceRisk/edit'" size="small" type="warning" @click="goAddEdit(row)">
                      编辑
                    </el-button>
                    <el-button v-auth="'threeListManagement/complianceRisk/confirm'" size="small" type="success">
                      确认
                    </el-button>
                    <el-button v-auth="'threeListManagement/complianceRisk/delete'" size="small" type="danger">
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="flex items-center justify-between border-t p-4">
              <div>
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @current-change="handlePageChange"
                  @size-change="handlePageChange"
                />
              </div>
            </div>
          </div>
          <!-- 批量操作区 -->
          <div v-if="selectedRisks.length > 0" class="mt-4 rounded-lg bg-white p-4 shadow-sm">
            <div class="flex items-center space-x-3">
              <span class="text-sm text-gray-600">已选择 {{ selectedRisks.length }} 项</span>
              <button v-auth="'threeListManagement/complianceRisk/batchDelete'" class="!rounded-button whitespace-nowrap border border-red-500 px-4 py-2 text-red-500 hover:bg-red-50">
                批量删除
              </button>
              <button v-auth="'threeListManagement/complianceRisk/batchExport'" class="!rounded-button whitespace-nowrap border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50">
                批量导出
              </button>
              <button class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600 hover:bg-gray-50">
                取消选择
              </button>
            </div>
          </div>
        </div>
        <!-- 右侧统计区 -->
        <div v-if="false" class="w-80 shrink-0">
          <div class="space-y-4">
            <!-- 风险概览卡片 -->
            <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                风险概览
              </h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">总风险项</span>
                  <span class="text-2xl font-bold">86</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">高风险项</span>
                  <span class="text-xl text-red-600 font-bold">12</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">中风险项</span>
                  <span class="text-xl text-orange-500 font-bold">34</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-gray-600">低风险项</span>
                  <span class="text-xl text-green-600 font-bold">40</span>
                </div>
              </div>
            </div>
            <!-- 部门风险分布 -->
            <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                部门风险分布
              </h3>
              <div ref="departmentChart" class="h-48" />
            </div>
            <!-- 业务类型分布 -->
            <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                业务类型分布
              </h3>
              <div ref="businessTypeChart" class="h-48" />
            </div>
            <!-- 快捷链接卡片 -->
            <div class="mb-4 rounded-lg bg-white p-4 pt-2 shadow-sm">
              <h3 class="mb-4 text-base font-bold">
                快捷链接
              </h3>
              <div class="flex flex-col space-y-2">
                <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                  <el-icon class="mr-2"><Document /></el-icon>合规风险评估报告
                </a>
                <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                  <el-icon class="mr-2"><TrendCharts /></el-icon>风险趋势分析
                </a>
                <a href="#" class="flex items-center text-sm text-gray-700 hover:underline">
                  <el-icon class="mr-2"><Help /></el-icon>帮助文档
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

  <style scoped>
  /* 自定义表格样式 */
  :deep(.el-table) {
  --el-table-border-color: #f0f0f0;
  --el-table-header-bg-color: #f9fafb;
  --el-table-row-hover-bg-color: #f5f7fa;
  }
  /* 自定义分页样式 */
  :deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  }
  /* 自定义按钮样式 */
  :deep(.el-button) {
  --el-button-hover-text-color: var(--el-color-primary);
  --el-button-hover-bg-color: rgba(64, 158, 255, 0.05);
  --el-button-active-bg-color: rgba(64, 158, 255, 0.1);
  }
  /* 自定义选择器样式 */
  :deep(.el-select) {
  --el-select-input-focus-border-color: var(--el-color-primary);
  }
  /* 自定义输入框样式 */
  :deep(.el-input__inner) {
  --el-input-focus-border-color: var(--el-color-primary);
  }
  </style>
