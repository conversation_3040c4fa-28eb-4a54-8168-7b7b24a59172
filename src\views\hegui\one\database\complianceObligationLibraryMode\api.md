---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/合规义务管理

## POST 添加合规义务

POST /whiskerguardregulatoryservice/api/compliance/obligations/create

描述：添加合规义务。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "content": "string",
  "effectiveDate": "string",
  "status": "DRAFT",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "corePoints": "string",
  "applicability": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ComplianceObligationDTO](#schemacomplianceobligationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "",
  "title": "",
  "obligationType": "",
  "sourceType": "",
  "level": "",
  "department": "",
  "content": "",
  "effectiveDate": "",
  "status": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0,
  "corePoints": "",
  "applicability": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceObligationDTO](#schemaresponseentitycomplianceobligationdto)|

## POST 部分更新合规义务记录

POST /whiskerguardregulatoryservice/api/compliance/obligations/partialUpdate/{id}

描述：部分更新合规义务记录

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "content": "string",
  "effectiveDate": "string",
  "status": "DRAFT",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "corePoints": "string",
  "applicability": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要更新的合规义务记录|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ComplianceObligationDTO](#schemacomplianceobligationdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "",
  "title": "",
  "obligationType": "",
  "sourceType": "",
  "level": "",
  "department": "",
  "content": "",
  "effectiveDate": "",
  "status": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0,
  "corePoints": "",
  "applicability": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceObligationDTO](#schemaresponseentitycomplianceobligationdto)|

## POST 获取所有合规义务记录（可分页、可筛选）

POST /whiskerguardregulatoryservice/api/compliance/obligations/page

描述：获取所有合规义务记录（可分页、可筛选）

> Body 请求参数

```json
{
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "effectiveDateStart": "string",
  "effectiveDateEnd": "string",
  "status": "DRAFT"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|string| 否 |none|
|size|query|string| 否 |none|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|
|body|body|[ComplianceObligationsReq](#schemacomplianceobligationsreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "obligationCode": "",
      "title": "",
      "obligationType": "",
      "sourceType": "",
      "level": "",
      "department": "",
      "content": "",
      "effectiveDate": "",
      "status": "",
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": 0,
      "corePoints": "",
      "applicability": ""
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageComplianceObligationDTO](#schemaresponseentitypagecomplianceobligationdto)|

## GET 根据ID获取单个合规义务记录

GET /whiskerguardregulatoryservice/api/compliance/obligations/{id}

描述：根据ID获取单个合规义务记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要查询的合规义务记录ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "",
  "title": "",
  "obligationType": "",
  "sourceType": "",
  "level": "",
  "department": "",
  "content": "",
  "effectiveDate": "",
  "status": "",
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": 0,
  "corePoints": "",
  "applicability": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityComplianceObligationDTO](#schemaresponseentitycomplianceobligationdto)|

## GET 删除指定的合规义务记录

GET /whiskerguardregulatoryservice/api/compliance/obligations/delete/{id}

描述：删除指定的合规义务记录。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |要删除的合规义务记录ID|
|Authorization|header|string| 否 |访问token|
|tenantId|header|integer| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||none|
|descending|boolean|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_ResponseEntityComplianceObligationDTO">ResponseEntityComplianceObligationDTO</h2>

<a id="schemaresponseentitycomplianceobligationdto"></a>
<a id="schema_ResponseEntityComplianceObligationDTO"></a>
<a id="tocSresponseentitycomplianceobligationdto"></a>
<a id="tocsresponseentitycomplianceobligationdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "content": "string",
  "effectiveDate": "string",
  "status": "DRAFT",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "corePoints": "string",
  "applicability": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID标识不同公司的数据隔离|
|obligationCode|string|true|none||义务编号用于唯一标识|
|title|string|true|none||合规义务标题|
|obligationType|string|false|none||义务类型 法律法规(LAR) 监管要求(SUPERVISE) 强制标准(MS) 企业标准(ES)|
|sourceType|string|false|none||来源类型 法规(LAWS)|
|level|string|false|none||风险等级 一般风险(GENERAL)、重大风险(MAJOR)、典型风险(TYPICAL)、安全(SAFE)|
|department|string|false|none||责任部门|
|content|string|false|none||合规义务详细内容支持大文本存储|
|effectiveDate|string|false|none||生效日期|
|status|string|false|none||状态 草稿(DRAFT)、生效(EFFECTIVE)、失效(EXPIRED)、待审核(REVIEWING)|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除 0表示正常，1表示已删除|
|corePoints|string|false|none||核心要点|
|applicability|string|false|none||适用条件|

#### 枚举值

|属性|值|
|---|---|
|obligationType|LAR|
|obligationType|SUPERVISE|
|obligationType|MS|
|obligationType|ES|
|sourceType|LAWS|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|

<h2 id="tocS_ComplianceObligationDTO">ComplianceObligationDTO</h2>

<a id="schemacomplianceobligationdto"></a>
<a id="schema_ComplianceObligationDTO"></a>
<a id="tocScomplianceobligationdto"></a>
<a id="tocscomplianceobligationdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "content": "string",
  "effectiveDate": "string",
  "status": "DRAFT",
  "createdBy": "string",
  "createdAt": {},
  "updatedBy": "string",
  "updatedAt": {},
  "isDeleted": true,
  "corePoints": "string",
  "applicability": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID标识不同公司的数据隔离|
|obligationCode|string|true|none||义务编号用于唯一标识|
|title|string|true|none||合规义务标题|
|obligationType|string|false|none||义务类型 法律法规(LAR) 监管要求(SUPERVISE) 强制标准(MS) 企业标准(ES)|
|sourceType|string|false|none||来源类型 法规(LAWS)|
|level|string|false|none||风险等级 一般风险(GENERAL)、重大风险(MAJOR)、典型风险(TYPICAL)、安全(SAFE)|
|department|string|false|none||责任部门|
|content|string|false|none||合规义务详细内容支持大文本存储|
|effectiveDate|string|false|none||生效日期|
|status|string|false|none||状态 草稿(DRAFT)、生效(EFFECTIVE)、失效(EXPIRED)、待审核(REVIEWING)|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|object|false|none||创建时间|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|object|false|none||创建时间|
|isDeleted|boolean|false|none||是否删除 0表示正常，1表示已删除|
|corePoints|string|false|none||核心要点|
|applicability|string|false|none||适用条件|

#### 枚举值

|属性|值|
|---|---|
|obligationType|LAR|
|obligationType|SUPERVISE|
|obligationType|MS|
|obligationType|ES|
|sourceType|LAWS|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|

<h2 id="tocS_ResponseEntityPageComplianceObligationDTO">ResponseEntityPageComplianceObligationDTO</h2>

<a id="schemaresponseentitypagecomplianceobligationdto"></a>
<a id="schema_ResponseEntityPageComplianceObligationDTO"></a>
<a id="tocSresponseentitypagecomplianceobligationdto"></a>
<a id="tocsresponseentitypagecomplianceobligationdto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "obligationCode": "string",
      "title": "string",
      "obligationType": "LAR",
      "sourceType": "LAWS",
      "level": "GENERAL",
      "department": "string",
      "content": "string",
      "effectiveDate": "string",
      "status": "DRAFT",
      "createdBy": "string",
      "createdAt": {},
      "updatedBy": "string",
      "updatedAt": {},
      "isDeleted": true,
      "corePoints": "string",
      "applicability": "string"
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[ComplianceObligationDTO](#schemacomplianceobligationdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_ComplianceObligationsReq">ComplianceObligationsReq</h2>

<a id="schemacomplianceobligationsreq"></a>
<a id="schema_ComplianceObligationsReq"></a>
<a id="tocScomplianceobligationsreq"></a>
<a id="tocscomplianceobligationsreq"></a>

```json
{
  "tenantId": 0,
  "obligationCode": "string",
  "title": "string",
  "obligationType": "LAR",
  "sourceType": "LAWS",
  "level": "GENERAL",
  "department": "string",
  "effectiveDateStart": "string",
  "effectiveDateEnd": "string",
  "status": "DRAFT"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|false|none||租户ID标识不同公司的数据隔离|
|obligationCode|string|false|none||义务编号用于唯一标识|
|title|string|false|none||合规义务标题|
|obligationType|string|false|none||义务类型 要求型(DEMAND)|
|sourceType|string|false|none||来源类型 法规(LAWS)|
|level|string|false|none||风险等级 一般风险(GENERAL)、重大风险(MAJOR)、典型风险(TYPICAL)、安全(SAFE)|
|department|string|false|none||责任部门|
|effectiveDateStart|string|false|none||生效日期开始时间|
|effectiveDateEnd|string|false|none||生效日期结束时间|
|status|string|false|none||状态 草稿(DRAFT)、生效(EFFECTIVE)、失效(EXPIRED)、待审核(REVIEWING)|

#### 枚举值

|属性|值|
|---|---|
|obligationType|LAR|
|obligationType|SUPERVISE|
|obligationType|MS|
|obligationType|ES|
|sourceType|LAWS|
|level|GENERAL|
|level|MAJOR|
|level|SAFE|
|level|TYPICAL|
|status|DRAFT|
|status|PUBLISHED|
|status|EFFECTIVE|
|status|EXPIRED|
|status|REVIEWING|

