import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/qa',
  component: Layout,
  // redirect: '/one/1',
  name: '/qa',
  meta: {
    title: '问答库管理',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/one/qa/index',
      name: '/one/qa/index',
      component: () => import('@/views/hegui/one/qa/index.vue'),
      meta: {
        title: '问答库管理',
      },
      children: [
        {
          path: '/one/qa/detail',
          name: '/one/qa/detail',
          component: () => import('@/views/hegui/one/qa/detail.vue'),
          meta: {
            title: '问答详情',
            sidebar: false,
            breadcrumb: false,
          },
        },
      ],
    },
  ],
}

export default routes
