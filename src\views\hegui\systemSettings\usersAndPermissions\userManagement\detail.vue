<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Check,
  Close,
  Delete,
  Download,
  Edit,
  OfficeBuilding,
  Plus,
  Refresh,
  Search,
  Upload,
  User,
} from '@element-plus/icons-vue'

// 左侧菜单激活项
const activeMenu = ref('1-1')

// 组织架构树数据
const orgTreeData = ref([
  {
    id: 1,
    label: '总公司',
    type: 'dept',
    children: [
      {
        id: 2,
        label: '技术部',
        type: 'dept',
        children: [
          { id: 3, label: '前端组', type: 'dept' },
          { id: 4, label: '后端组', type: 'dept' },
          { id: 5, label: '测试组', type: 'dept' },
        ],
      },
      {
        id: 6,
        label: '产品部',
        type: 'dept',
        children: [
          { id: 7, label: '产品设计组', type: 'dept' },
          { id: 8, label: '项目管理组', type: 'dept' },
        ],
      },
      {
        id: 9,
        label: '市场部',
        type: 'dept',
        children: [
          { id: 10, label: '品牌推广组', type: 'dept' },
          { id: 11, label: '数字营销组', type: 'dept' },
        ],
      },
    ],
  },
])

const treeProps = {
  children: 'children',
  label: 'label',
}

// 用户筛选条件
const filter = ref({
  status: '',
  type: '',
  keyword: '',
})

// 用户列表数据
const userList = ref([
  {
    id: 'U00001',
    username: 'admin',
    name: '管理员',
    phone: '138****1234',
    email: '<EMAIL>',
    department: 'IT部门',
    position: '系统管理员',
    role: '系统管理员',
    status: 'active',
    lastLogin: '2024/4/26 10:15',
  },
  {
    id: 'U00002',
    username: 'zhangsan',
    name: '张三',
    phone: '139****5678',
    email: '<EMAIL>',
    department: '法务部',
    position: '合规专员',
    role: '合规管理员',
    status: 'active',
    lastLogin: '2024/4/26 9:30',
  },
  {
    id: 'U00003',
    username: 'lisi',
    name: '李四',
    phone: '137****9012',
    email: '<EMAIL>',
    department: '财务部',
    position: '财务主管',
    role: '财务管理员',
    status: 'active',
    lastLogin: '2024/4/25 16:45',
  },
  {
    id: 'U00004',
    username: 'wangwu',
    name: '王五',
    phone: '136****3456',
    email: '<EMAIL>',
    department: '人力资源部',
    position: 'HRBP',
    role: '人力资源管理员',
    status: 'inactive',
    lastLogin: '2024/4/20 14:20',
  },
  {
    id: 'U00005',
    username: 'zhaoliu',
    name: '赵六',
    phone: '135****7890',
    email: '<EMAIL>',
    department: '市场部',
    position: '市场专员',
    role: '市场管理员',
    status: 'active',
    lastLogin: '2024/4/26 11:05',
  },
  {
    id: 'U00006',
    username: 'qianqi',
    name: '钱七',
    phone: '134****1234',
    email: '<EMAIL>',
    department: '销售部',
    position: '销售经理',
    role: '销售管理员',
    status: 'active',
    lastLogin: '2024/4/26 8:45',
  },
  {
    id: 'U00007',
    username: 'sunba',
    name: '孙八',
    phone: '133****5678',
    email: '<EMAIL>',
    department: '运营部',
    position: '运营主管',
    role: '运营管理员',
    status: 'inactive',
    lastLogin: '2024/4/18 10:30',
  },
  {
    id: 'U00008',
    username: 'zhoujiu',
    name: '周九',
    phone: '132****9012',
    email: '<EMAIL>',
    department: '客服部',
    position: '客服专员',
    role: '客服管理员',
    status: 'active',
    lastLogin: '2024/4/26 13:15',
  },
])

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 100,
})

// 选中的用户
const selectedUsers = ref([])

// 最近活动数据
const recentActivities = ref([
  {
    username: 'admin',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    action: '登录系统',
    time: '10分钟前',
  },
  {
    username: 'zhangsan',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    action: '修改了个人信息',
    time: '25分钟前',
  },
  {
    username: 'lisi',
    avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    action: '创建了新用户',
    time: '1小时前',
  },
  {
    username: 'wangwu',
    avatar: 'https://cube.elemecdn.com/d/e9/c1d93af380ef5d0366245e2b31c13png.png',
    action: '更新了合规文档',
    time: '2小时前',
  },
  {
    username: 'zhaoliu',
    avatar: 'https://cube.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549png.png',
    action: '提交了审批',
    time: '3小时前',
  },
])

// 饼图实例
const pieChart = ref()
let pieChartInstance: echarts.ECharts | null = null

// 初始化饼图
function initPieChart() {
  if (!pieChart.value) { return }

  pieChartInstance = echarts.init(pieChart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '部门分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 320, name: '技术部' },
          { value: 240, name: '产品部' },
          { value: 149, name: '市场部' },
          { value: 100, name: '财务部' },
          { value: 59, name: '人力资源部' },
        ],
      },
    ],
  }

  pieChartInstance.setOption(option)
}

// 处理组织架构树节点点击
function handleNodeClick(data: any) {
  console.log('点击节点:', data)
}

// 处理表格选择变化
function handleSelectionChange(val: any[]) {
  selectedUsers.value = val
}

// 查看用户详情
function handleView(row: any) {
  console.log('查看用户:', row)
}

// 编辑用户
function handleEdit(row: any) {
  console.log('编辑用户:', row)
}

// 切换用户状态
function handleToggleStatus(row: any) {
  row.status = row.status === 'active' ? 'inactive' : 'active'
}

// 重置密码
function handleResetPwd(row: any) {
  console.log('重置密码:', row)
}

// 页面加载完成后初始化图表
onMounted(() => {
  nextTick(() => {
    initPieChart()
  })
})

// 窗口大小变化时重新调整图表大小
window.addEventListener('resize', () => {
  if (pieChartInstance) {
    pieChartInstance.resize()
  }
})
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              用户管理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              新增用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Upload />
              </el-icon>
              导入用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Download />
              </el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="4">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="text-gray-800 font-bold">
                    组织架构
                  </div>
                  <el-button text>
                    <el-icon>
                      <Refresh />
                    </el-icon>
                  </el-button>
                </div>
              </template>
              <el-scrollbar class="h-[calc(100vh-280px)]">
                <el-tree
                  :data="orgTreeData" node-key="id" :props="treeProps" :expand-on-click-node="false"
                  :highlight-current="true" default-expand-all @node-click="handleNodeClick"
                >
                  <template #default="{ node, data }">
                    <span class="flex items-center">
                      <el-icon v-if="data.type === 'dept'" class="mr-1"><OfficeBuilding /></el-icon>
                      <el-icon v-else class="mr-1">
                        <User />
                      </el-icon>
                      <span>{{ node.label }}</span>
                    </span>
                  </template>
                </el-tree>
              </el-scrollbar>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="hover" class="">
              <!-- 中间用户列表 -->
              <div class="flex-1 overflow-hidden rounded-md bg-white shadow-sm">
                <!-- 筛选条件 -->
                <div class="border-b border-gray-100 p-4">
                  <div class="flex items-center space-x-4">
                    <el-select v-model="filter.status" placeholder="用户状态" size="small" class="w-32">
                      <el-option label="全部" value="" />
                      <el-option label="启用" value="active" />
                      <el-option label="禁用" value="inactive" />
                    </el-select>
                    <el-select v-model="filter.type" placeholder="用户类型" size="small" class="w-32">
                      <el-option label="全部" value="" />
                      <el-option label="普通用户" value="normal" />
                      <el-option label="管理员" value="admin" />
                    </el-select>
                    <el-input
                      v-model="filter.keyword" placeholder="搜索用户名、姓名、部门..." size="small"
                      class="max-w-xs flex-1"
                    >
                      <template #append>
                        <el-button>
                          <el-icon>
                            <Search />
                          </el-icon>
                        </el-button>
                      </template>
                    </el-input>
                    <el-button text size="small">
                      <el-icon class="mr-1">
                        <filter />
                      </el-icon>
                      高级筛选
                    </el-button>
                  </div>
                </div>

                <!-- 用户表格 -->
                <el-table :data="userList" style="width: 100%;" @selection-change="handleSelectionChange">
                  <el-table-column type="selection" width="50" />
                  <el-table-column prop="id" label="用户ID" width="120" />
                  <el-table-column prop="username" label="用户名" width="150" />
                  <el-table-column prop="name" label="姓名" width="120" />
                  <el-table-column prop="phone" label="手机号码" width="150" />
                  <el-table-column prop="email" label="邮箱" width="200" />
                  <el-table-column prop="department" label="所属部门" width="150" />
                  <el-table-column prop="position" label="岗位" width="150" />
                  <el-table-column prop="role" label="角色" width="150" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="row.status === 'active' ? 'success' : 'info'" size="small">
                        {{ row.status === 'active' ? '启用' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastLogin" label="最后登录时间" width="180" />
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                      <el-button size="small" text @click="handleView(row)">
                        <el-icon>
                          <view />
                        </el-icon>
                      </el-button>
                      <el-button size="small" text @click="handleEdit(row)">
                        <el-icon>
                          <Edit />
                        </el-icon>
                      </el-button>
                      <el-button size="small" text @click="handleToggleStatus(row)">
                        <el-icon v-if="row.status === 'active'">
                          <Close />
                        </el-icon>
                        <el-icon v-else>
                          <Check />
                        </el-icon>
                      </el-button>
                      <el-button size="small" text @click="handleResetPwd(row)">
                        <el-icon>
                          <Refresh />
                        </el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="flex items-center justify-between p-4">
                  <div v-if="selectedUsers.length > 0" class="flex items-center space-x-3">
                    <span class="text-sm text-gray-500">已选择 {{ selectedUsers.length }} 项</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Check />
                      </el-icon>
                      批量启用
                    </el-button>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Close />
                      </el-icon>
                      批量禁用
                    </el-button>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Delete />
                      </el-icon>
                      批量删除
                    </el-button>
                    <el-button size="small" text @click="selectedUsers = []">
                      取消选择
                    </el-button>
                  </div>
                  <el-pagination
                    v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                    :total="pagination.total" :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper" class="ml-auto"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="text-gray-800 font-bold">
                  用户概览
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-3">
                <div class="rounded-md bg-blue-50 p-3">
                  <div class="mb-1 text-sm text-gray-500">
                    总用户数
                  </div>
                  <div class="text-xl text-blue-600 font-bold">
                    1,248
                  </div>
                </div>
                <div class="rounded-md bg-green-50 p-3">
                  <div class="mb-1 text-sm text-gray-500">
                    管理员
                  </div>
                  <div class="text-xl text-green-600 font-bold">
                    42
                  </div>
                </div>
                <div class="rounded-md bg-purple-50 p-3">
                  <div class="mb-1 text-sm text-gray-500">
                    普通用户
                  </div>
                  <div class="text-xl text-purple-600 font-bold">
                    1,158
                  </div>
                </div>
                <div class="rounded-md bg-yellow-50 p-3">
                  <div class="mb-1 text-sm text-gray-500">
                    禁用用户
                  </div>
                  <div class="text-xl text-yellow-600 font-bold">
                    48
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="text-gray-800 font-bold">
                  部门分布
                </div>
              </template>
              <div ref="pieChart" class="h-48" />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="text-gray-800 font-bold">
                  最近活动
                </div>
              </template>
              <el-scrollbar class="h-48">
                <div class="space-y-3">
                  <div v-for="(item, index) in recentActivities" :key="index" class="flex items-start">
                    <el-avatar :size="32" :src="item.avatar" class="mr-3" />
                    <div>
                      <div class="text-sm text-gray-800 font-medium">
                        {{ item.username }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ item.action }}
                      </div>
                      <div class="text-xs text-gray-400">
                        {{ item.time }}
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-tree {
    background: transparent;
  }

  .el-table {
    --el-table-header-bg-color: #f5f7fa;
  }

  .el-pagination {
    justify-content: flex-end;
  }

  :deep(.el-input-group__append) {
    color: white;
    background-color: var(--el-color-primary);
  }

  :deep(.el-input-group__append) .el-icon {
    color: white;
  }
</style>
