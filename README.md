# WhiskerGuard UI System Admin （猫伯伯合规管家企业端系统）

WhiskerGuard UI System Admin 是一个基于 Vue 3 + TypeScript + Vite 构建的现代化管理系统前端项目。

## 技术栈

- **核心框架**：Vue 3.4.x
- **构建工具**：Vite 5.x
- **开发语言**：TypeScript 5.x
- **UI 框架**：Element Plus 2.7.x
- **状态管理**：Pinia 2.x
- **路由管理**：Vue Router 4.x
- **国际化**：Vue I18n 9.x
- **CSS 预处理器**：Sass
- **代码规范**：ESLint + StyleLint
- **包管理器**：pnpm

## 项目特性

- 🚀 基于 Vue 3 + Vite 的快速开发体验
- 💪 使用 TypeScript 开发，提供完整的类型定义
- 🎨 集成 Element Plus UI 框架
- 📦 组件自动导入
- 🎯 基于文件系统的路由
- 📱 响应式布局
- 🌍 国际化支持
- 🎨 主题定制
- 📊 集成多种图表库（ECharts、G2Plot）
- 🔍 代码规范检查
- 📝 富文本编辑器支持（TinyMCE、WangEditor）
- 🔄 自动导入组件和 API
- 🛠 开发工具链完善

## 开发环境要求

- Node.js: ^18.0.0 || ^20.0.0
- pnpm: ^10.8.1

## 快速开始

1. 安装依赖
```bash
pnpm install
```

2. 启动开发服务器
```bash
pnpm dev
```

3. 构建生产版本
```bash
pnpm build
```

## 项目结构

```
├── src/                # 源代码目录
│   ├── api/           # API 接口定义
│   ├── assets/        # 静态资源
│   ├── components/    # 公共组件
│   ├── layouts/       # 布局组件
│   ├── locales/       # 国际化文件
│   ├── router/        # 路由配置
│   ├── store/         # 状态管理
│   ├── styles/        # 全局样式
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   └── App.vue        # 根组件
├── public/            # 公共资源目录
├── vite/              # Vite 配置
├── themes/            # 主题配置
└── scripts/           # 构建脚本
```

## 主要功能

- 用户认证与授权
- 多语言支持
- 主题定制
- 响应式布局
- 数据可视化
- 富文本编辑
- 文件上传
- 权限管理

## 开发规范

- 使用 ESLint 进行代码检查
- 使用 StyleLint 进行样式检查
- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 组合式 API 风格
- 使用 Git Commit 规范

## 构建与部署

- 开发环境：`pnpm dev`
- 测试环境：`pnpm build:test`
- 生产环境：`pnpm build`

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork 本仓库
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 许可证

[MIT License](LICENSE)
