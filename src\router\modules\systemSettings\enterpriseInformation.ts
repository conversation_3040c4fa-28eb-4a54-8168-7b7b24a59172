import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/systemSettings/enterpriseInformation',
  component: Layout,
  name: '/systemSettings/enterpriseInformation',
  meta: {
    title: '企业信息',
    icon: 'i-heroicons-solid:menu-alt-3',
  },
  children: [
    {
      path: '/systemSettings/enterpriseInformation/essentialInformation',
      name: '/systemSettings/enterpriseInformation/essentialInformation',
      component: () => import('@/views/hegui/systemSettings/enterpriseInformation/essentialInformation/index.vue'),
      meta: {
        title: '基本信息',
        // sidebar: false,
      },
      // children: [
      //   {
      //     path: '/systemSettings/enterpriseInformation/toDoTasks/detail',
      //     name: '/systemSettings/enterpriseInformation/toDoTasks/detail',
      //     component: () => import('@/views/hegui/systemSettings/enterpriseInformation/toDoTasks/detail.vue'),
      //     meta: {
      //       title: '实时监控详情',
      //       sidebar: false,
      //       breadcrumb: false,
      //     },
      //   },
      // ]
    },
    {
      path: '/systemSettings/enterpriseInformation/brandSetting',
      name: '/systemSettings/enterpriseInformation/brandSetting',
      component: () => import('@/views/hegui/systemSettings/enterpriseInformation/brandSetting/index.vue'),
      meta: {
        title: '品牌设置',
      },
    },
  ],
}

export default routes
