import type {
  CourseQueryParams,
  LearningProgressQueryParams,
  RecommendedCourse,
  ResponseEntityLearningProgressStatisticsDTO,
} from './types'
import api from '@/api/index'

export default {
  // 待办事件管理
  eventsApi(paging: any, params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardapprovalservice/api/todo/events/${params.id}`, {
        })
      case 'create':
        return api.post(`/whiskerguardapprovalservice/api/todo/events`, params)
      case 'update':
        return api.patch(`/whiskerguardapprovalservice/api/todo/events/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardapprovalservice/api/todo/events/${params.id}`)
      case 'today':
        // 获取员工当天的待办事件
        return api.get(`/whiskerguardapprovalservice/api/todo/events/today`)
      case 'list':
      default:
        // 使用POST方式进行分页查询
        return api.post(`/whiskerguardapprovalservice/api/todo/events/page?page=${paging.page}&size=${paging.size}`, params)
    }
  },
  // 学习培训
  learningApi(
    params: LearningProgressQueryParams | CourseQueryParams,
    key: 'overview' | 'recommended' | 'courses',
  ) {
    switch (key) {
      case 'overview':
        // 获取用户学习进度概览
        return api.get(`/whiskerguardtrainingservice/api/learning/progress/statistics/overview`, {
          params,
        })
      case 'recommended':
        // 获取推荐课程列表（假设的API，需要根据实际后端接口调整）
        return api.get(`/whiskerguardtrainingservice/api/courses/recommended`, {
          params,
        })
      case 'courses':
        // 获取课程列表
        return api.get(`/whiskerguardtrainingservice/api/courses`, {
          params,
        })
      default:
        // 默认获取用户学习进度概览
        return api.get(`/whiskerguardtrainingservice/api/learning/progress/statistics/overview`, {
          params,
        })
    }
  },
  // 法规更新列表
  regulationApi(params: any) {
    // 调用法规列表接口，获取最新的法规更新
    return api.post(`/whiskerguardregulatoryservice/api/laws/regulations/page?page=${params.page ? params.page - 1 : 0}&size=${params.size ? params.size : 10}`, {})
  },
  // 系统通知列表
  notificationApi(params: any) {
    // 调用通知中心接口，获取系统通知
    return api.post(`/whiskerguardgeneralservice/api/notification-center/records/category?page=${params.page}&size=${params.size}`, {
      categoryName: params.categoryName,
    })
  },
  // 获取我的岗位职责统计
  dutyApi() {
    // 调用岗位职责接口，获取当前用户的岗位职责信息
    return api.get(`/compliancelistservice/api/duty/mains/my/stats`)
  },
  // 获取待办事件统计
  eventsStatisticsApi() {
    // 调用待办事件统计接口，获取当前用户的待办事件统计信息
    return api.get(`/whiskerguardapprovalservice/api/todo/events/statistics`)
  },
}
