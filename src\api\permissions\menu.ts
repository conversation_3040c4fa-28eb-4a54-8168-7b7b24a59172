import api from '@/api/index'

export default {
  // 权限
  BusinessPermissions(params: any, key: any) {
    switch (key) {
      case 'detail':
        return api.get(`/whiskerguardorgservice/api/permissions/${params.id}`, {
          params,
        })
      case 'info':
        return api.get(`/whiskerguardorgservice/api/permissions/${params.id}`, {
          params,
        })
      case 'create':
        return api.post(`/whiskerguardorgservice/api/permissions`, params)
      case 'update':
        return api.patch(`/whiskerguardorgservice/api/permissions/${params.id}`, params)
      case 'delete':
        return api.delete(`/whiskerguardorgservice/api/permissions/${params.id}`)
      default:
        return api.get(`/whiskerguardorgservice/api/permissions`, params)
    }
  },
}
