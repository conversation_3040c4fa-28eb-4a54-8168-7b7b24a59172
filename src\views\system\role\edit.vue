<script setup lang="ts">
import { ElMessage } from 'element-plus'
import Api from '@/api/modules/system/role'

const props = defineProps<{
  id: any[]
}>()

const form = ref<any>({
  name: '',
  code: '',
  status: '',
  sort: 1,
  note: '',
})
const formRules = ref({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择职级状态', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
  ],
})
getPermissionLis()
function getPermissionLis() {
  Api.getPermissionLis({
    id: props.id,
  }).then((res: any) => {
    ElMessage({ message: res.msg, type: 'success' })
  })
}
function onSubmit() {

}

function onCancel() {

}
</script>

<template>
  <el-dialog title="添加角色" width="460px" :close-on-click-modal="false" append-to-body destroy-on-close>
    <el-form :model="form" :rules="formRules" label-width="84px">
      <el-form-item label="角色名称:" prop="name">
        <el-input v-model="form.name" placeholder="请输入角色名称" clearable />
      </el-form-item>
      <el-form-item label="角色标识:" prop="code">
        <el-input v-model="form.code" placeholder="请输入角色标识" clearable />
      </el-form-item>
      <el-form-item label="角色状态:">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">
            在用
          </el-radio>
          <el-radio :label="2">
            停用
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号:" prop="sort">
        <el-input-number
          v-model="form.sort" style="width: 100%;" :min="0" placeholder="请输入排序号"
          controls-position="right" class="ele-fluid ele-text-left"
        />
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="form.note" :maxlength="200" placeholder="请输入备注" :rows="4" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancel">
        取消
      </el-button>
      <el-button type="primary" @click="onSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
  .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
