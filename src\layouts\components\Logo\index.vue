<script setup lang="ts">
import useSettingsStore from '@/store/modules/settings'
import imgLogo from '@/assets/images/logo.png'

defineOptions({
  name: 'Logo',
})

withDefaults(
  defineProps<{
    showLogo?: boolean
    showTitle?: boolean
  }>(),
  {
    showLogo: true,
    showTitle: true,
  },
)

const settingsStore = useSettingsStore()

const title = ref(import.meta.env.VITE_APP_TITLE)
const version: any = ref(`${import.meta.env.VITE_APP_VERSION}`)
const logo = ref(imgLogo)

const to = computed(() => settingsStore.settings.home.enable ? settingsStore.settings.home.fullPath : '')
</script>

<template>
  <RouterLink
    :to="to"
    class="h-[var(--g-sidebar-logo-height)] w-inherit flex-center gap-2 px-3 text-inherit no-underline"
    :class="{ 'cursor-pointer': settingsStore.settings.home.enable }" :title="title"
  >
    <!--    <img v-if="showLogo" :src="logo" class="logo h-[30px] w-[30px] object-contain"
      style="animation: rotate 5s linear infinite"> -->
    <img v-if="showLogo" :src="logo" class="logo h-[30px] w-[30px] object-contain" style="border-radius: 50%;">
    <div class="flex">
      <span v-if="showTitle" class="block truncate font-bold">{{ title }}</span>
      <!-- <span class="bg-[#4280EB] px-1.5  c-[#fff] flex justify-center items-center h-4" style="border-radius: 12px; font-size:12px; ">{{version}}</span> -->
    </div>
  </RouterLink>
</template>
