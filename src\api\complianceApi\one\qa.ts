import api from '@/api/index'

export default {
  // 获取AI对话会话ID
  getSessionId: () => {
    return api.post('/whiskerguardaiservice/api/ai/conversations/generate', {})
  },

  // 获取可用模型列表
  getModelList: () => {
    return api.get('/whiskerguardaiservice/api/ai-tools/models')
  },

  // 流式AI对话接口
  queryChatStream: (params: any) => {
    return api.post('/whiskerguardaiservice/api/ai/stream', params, {
      responseType: 'stream',
      timeout: 60000, // 60秒超时
    })
  },

  // 获取聊天历史记录
  queryChatList: (params: any) => {
    return api.get('/whiskerguardaiservice/api/ai-requests/employee/query-by-status', {
      params,
    })
  },

  // 上传文件并解析
  uploadFile: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/whiskerguardgeneralservice/api/file/upload?serviceName=org', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 删除上传的文件
  deleteFile: (fileId: string) => {
    return api.delete(`/whiskerguardaiservice/api/ai/file/${fileId}`)
  },

  // 获取文件解析状态
  getFileStatus: (fileId: string) => {
    return api.get(`/whiskerguardaiservice/api/ai/file/status/${fileId}`)
  },

  // 预览文件内容
  previewFile: (fileId: string) => {
    return api.get(`/whiskerguardaiservice/api/ai/file/preview/${fileId}`)
  },
}
