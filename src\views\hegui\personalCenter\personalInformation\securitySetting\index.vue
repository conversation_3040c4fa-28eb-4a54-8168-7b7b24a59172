<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Iphone,
  Lock,
  Message,
  Monitor,
  QuestionFilled,
  SuccessFilled,
  Warning,
} from '@element-plus/icons-vue'

const securityScoreChart = ref<HTMLElement | null>(null)

const deviceList = ref([
  {
    name: 'MacBook Pro (15-inch, 2023)',
    ip: '*************',
    time: '2024-05-20 14:30:22',
    status: '当前设备',
  },
  {
    name: 'iPhone 13 Pro',
    ip: '*************',
    time: '2024-05-19 09:15:33',
    status: '其他设备',
  },
  {
    name: 'Windows PC',
    ip: '*************',
    time: '2024-05-18 18:45:12',
    status: '其他设备',
  },
  {
    name: 'iPad Air',
    ip: '*************',
    time: '2024-05-15 11:20:45',
    status: '其他设备',
  },
  {
    name: 'HUAWEI Mate 40',
    ip: '*************',
    time: '2024-05-10 16:05:18',
    status: '其他设备',
  },
])

const securityLogs = ref([
  {
    time: '今天 14:30',
    desc: '在 MacBook Pro (15-inch, 2023) 上登录系统',
    type: 'login',
  },
  {
    time: '昨天 09:15',
    desc: '在 iPhone 13 Pro 上登录系统',
    type: 'login',
  },
  {
    time: '2024-05-18 18:45',
    desc: '在 Windows PC 上登录系统',
    type: 'login',
  },
  {
    time: '2024-05-15 11:20',
    desc: '修改了登录密码',
    type: 'password',
  },
  {
    time: '2024-05-10 16:05',
    desc: '在 HUAWEI Mate 40 上登录系统',
    type: 'login',
  },
  {
    time: '2024-05-08 10:30',
    desc: '绑定了邮箱 <EMAIL>',
    type: 'email',
  },
])

function getLogDotClass(type: string) {
  switch (type) {
    case 'login':
      return 'bg-blue-500'
    case 'password':
      return 'bg-green-500'
    case 'email':
      return 'bg-purple-500'
    default:
      return 'bg-gray-500'
  }
}

onMounted(() => {
  nextTick(() => {
    if (securityScoreChart.value) {
      const chart = echarts.init(securityScoreChart.value)
      const option = {
        animation: false,
        series: [
          {
            type: 'pie',
            radius: ['70%', '90%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold',
                formatter: '{c}分',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: 85,
                name: '安全评分',
                itemStyle: {
                  color: '#36a2eb',
                },
              },
              {
                value: 15,
                name: '剩余',
                itemStyle: {
                  color: '#e6e6e6',
                },
              },
            ],
          },
        ],
      }
      chart.setOption(option)
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              安全设置
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3" />
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  安全总览
                </div>
              </template>
              <div class="flex items-center justify-between">
                <div class="w-1/4">
                  <div ref="securityScoreChart" class="h-40 w-full" />
                </div>
                <div class="w-1/3">
                  <div class="flex items-center">
                    <el-icon class="mr-2 text-green-500" :size="24">
                      <SuccessFilled />
                    </el-icon>
                    <span class="text-lg font-medium">安全状态：良好</span>
                  </div>
                  <p class="mt-2 text-sm text-gray-500">
                    您的账户安全等级较高，请继续保持
                  </p>
                </div>
                <div class="w-2/5">
                  <h3 class="mb-2 text-sm font-medium">
                    安全建议
                  </h3>
                  <ul class="text-sm space-y-2">
                    <li class="flex items-center">
                      <el-icon class="mr-2 text-blue-500">
                        <Warning />
                      </el-icon>
                      <span>定期修改密码以增强安全性</span>
                    </li>
                    <li class="flex items-center">
                      <el-icon class="mr-2 text-blue-500">
                        <Warning />
                      </el-icon>
                      <span>开启双重验证提高账户安全</span>
                    </li>
                    <li class="flex items-center">
                      <el-icon class="mr-2 text-blue-500">
                        <Warning />
                      </el-icon>
                      <span>及时清理不常用的登录设备</span>
                    </li>
                  </ul>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  安全设置
                </div>
              </template>
              <div class="space-y-6">
                <!-- 登录密码 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-4 text-blue-500" :size="20">
                      <Lock />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        登录密码
                      </p>
                      <p class="text-sm text-gray-500">
                        上次修改时间：2024-04-15
                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="mr-4 text-sm text-green-500">已设置</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      修改
                    </el-button>
                  </div>
                </div>

                <!-- 手机验证 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-4 text-blue-500" :size="20">
                      <Iphone />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        手机验证
                      </p>
                      <p class="text-sm text-gray-500">
                        上次修改时间：2023-12-10
                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="mr-4 text-sm text-green-500">已绑定 (138****5678)</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      修改
                    </el-button>
                  </div>
                </div>

                <!-- 邮箱验证 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-4 text-blue-500" :size="20">
                      <Message />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        邮箱验证
                      </p>
                      <p class="text-sm text-gray-500">
                        上次修改时间：2023-12-10
                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="mr-4 text-sm text-green-500">已绑定 (<EMAIL>)</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      修改
                    </el-button>
                  </div>
                </div>

                <!-- 安全问题 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-4 text-blue-500" :size="20">
                      <QuestionFilled />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        安全问题
                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="mr-4 text-sm text-gray-500">未设置</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      设置
                    </el-button>
                  </div>
                </div>

                <!-- 登录设备管理 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-icon class="mr-4 text-blue-500" :size="20">
                      <Monitor />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        登录设备管理
                      </p>
                    </div>
                  </div>
                  <div>
                    <span class="mr-4 text-sm text-gray-500">5 台设备</span>
                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                      管理
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
            <!-- <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">安全总览</div>
              </template>
            </el-card> -->
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  安全日志
                </div>
              </template>
              <div class="timeline">
                <div v-for="(log, index) in securityLogs" :key="index" class="timeline-item">
                  <div class="timeline-dot" :class="getLogDotClass(log.type)" />
                  <div class="timeline-content">
                    <div class="timeline-time">
                      {{ log.time }}
                    </div>
                    <div class="timeline-desc">
                      {{ log.desc }}
                    </div>
                  </div>
                </div>
              </div>
              <el-link type="primary" class="mt-4 block text-center">
                查看更多
              </el-link>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .timeline {
    position: relative;
    padding-left: 20px;
  }

  .timeline-item {
    position: relative;
    padding-bottom: 20px;
  }

  .timeline-dot {
    position: absolute;
    top: 4px;
    left: -8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .timeline-content {
    margin-left: 10px;
  }

  .timeline-time {
    font-size: 12px;
    color: #999;
  }

  .timeline-desc {
    margin-top: 4px;
    font-size: 14px;
  }

  .timeline-item:not(:last-child)::after {
    position: absolute;
    top: 16px;
    bottom: 0;
    left: -2px;
    width: 2px;
    content: "";
    background-color: #e4e7ed;
  }
</style>
