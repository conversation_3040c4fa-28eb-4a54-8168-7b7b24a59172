<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Search } from '@element-plus/icons-vue'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'

// Props
interface Props {
  modelValue: boolean
  selectedValues?: string[] // 已选中的ID数组
  selectedNames?: string[] // 已选中的名称数组
  multiple?: boolean // 是否多选，默认为true
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  selectedValues: () => [],
  selectedNames: () => [],
  multiple: true,
})

const emit = defineEmits<Emits>()

// Emits
interface Emits {
  'update:modelValue': [value: boolean]
  'confirm': [selectedIds: string[], selectedItems: any[], selectedNames: string[]]
}

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const regulationList = ref<any[]>([])
const selectedIds = ref<string[]>([])
const selectedSingleId = ref<string>('')
const searchTimer = ref<number | null>(null)

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 初始化选中状态
    if (props.multiple) {
      selectedIds.value = [...props.selectedValues]
    }
    else {
      selectedSingleId.value = props.selectedValues.length > 0 ? props.selectedValues[0] : ''
    }
    // 重置搜索和分页
    searchKeyword.value = ''
    currentPage.value = 1
    // 获取数据
    fetchRegulations()
  }
})

// 监听弹窗关闭
watch(visible, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})

// 获取制度列表
async function fetchRegulations() {
  loading.value = true

  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    title: searchKeyword.value || undefined, // 搜索关键词
    status: 'PUBLISHED', // 只获取已发布的制度
  }

  try {
    const res: any = await systemApi.lawsSystem(params, 'page')
    if (res) {
      const data = res.data || res
      if (data.content) {
        regulationList.value = data.content
        total.value = data.totalElements || 0
      }
      else if (Array.isArray(data)) {
        regulationList.value = data
        total.value = data.length
      }
      else {
        regulationList.value = []
        total.value = 0
      }
    }
  }
  catch (error: any) {
    console.error('获取制度列表失败:', error)
    ElMessage.error('获取制度列表失败')
    regulationList.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索处理（防抖）
function handleSearch() {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(() => {
    currentPage.value = 1 // 重置到第一页
    fetchRegulations()
  }, 300)
}

// 分页大小改变
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchRegulations()
}

// 当前页改变
function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchRegulations()
}

// 关闭弹窗
function handleClose() {
  visible.value = false
}

// 确认选择
function handleConfirm() {
  if (props.multiple) {
    // 多选模式
    const selectedItems = regulationList.value.filter(item =>
      selectedIds.value.includes(item.id),
    )
    const selectedNames = selectedItems.map(item => item.title)
    emit('confirm', selectedIds.value, selectedItems, selectedNames)
  }
  else {
    // 单选模式
    if (selectedSingleId.value) {
      const selectedItem = regulationList.value.find(item => item.id === selectedSingleId.value)
      if (selectedItem) {
        emit('confirm', [selectedSingleId.value], [selectedItem], [selectedItem.title])
      }
      else {
        emit('confirm', [], [], [])
      }
    }
    else {
      emit('confirm', [], [], [])
    }
  }
  handleClose()
}

// 组件挂载时的初始化
onMounted(() => {
  // 组件挂载时不自动加载数据，等待弹窗打开时再加载
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="关联法规"
    width="800px"
    :before-close="handleClose"
  >
    <!-- 搜索栏 -->
    <div class="mb-4">
      <el-input
        v-model="searchKeyword"
        placeholder="请输入制度名称进行搜索"
        clearable
        @input="handleSearch"
        @clear="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 列表 -->
    <div v-loading="loading" element-loading-text="加载中..." element-loading-background="rgba(255, 255, 255, 0.8)">
      <div class="regulation-list-container" style="position: relative; max-height: 400px;">
        <div class="regulation-list" style="max-height: 400px; overflow-y: auto;">
          <!-- 多选模式 -->
          <el-checkbox-group v-if="props.multiple" v-model="selectedIds">
            <div v-for="item in regulationList" :key="item.id" class="regulation-item">
              <el-checkbox :label="item.id" class="regulation-checkbox">
                <div class="regulation-content">
                  <div class="regulation-title text-gray-900 font-medium">
                    {{ item.title }}
                  </div>
                  <div class="regulation-meta mt-1 text-sm text-gray-500">
                    编号：{{ item.code || '暂无' }} |
                    类型：{{ item.lawType }} |
                    <el-tag type="success" size="small" class="ml-1">
                      已发布
                    </el-tag>
                  </div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>

          <!-- 单选模式 -->
          <el-radio-group v-else v-model="selectedSingleId" class="regulation-radio-group">
            <div v-for="item in regulationList" :key="item.id" class="regulation-item">
              <el-radio :label="item.id" class="regulation-radio">
                <div class="regulation-content">
                  <div class="regulation-title text-gray-900 font-medium">
                    {{ item.title }}
                  </div>
                  <div class="regulation-meta mt-1 text-sm text-gray-500">
                    编号：{{ item.code || '暂无' }} |
                    类型：{{ item.lawType }} |
                    <el-tag type="success" size="small" class="ml-1">
                      已发布
                    </el-tag>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>

          <!-- 空状态 -->
          <div v-if="regulationList.length === 0 && !loading" class="py-8 text-center text-gray-500">
            <el-empty description="暂无数据" />
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          :disabled="loading"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 底部按钮 -->
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-500">
          已选择 {{ props.multiple ? selectedIds.length : (selectedSingleId ? 1 : 0) }} 项
        </div>
        <div>
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.regulation-list-container {
  position: relative;

  :deep(.el-loading-mask) {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
  }
}

.regulation-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.regulation-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
  width: 100%;
  display: block;

  &:hover {
    border-color: #409eff;
    background-color: #f5f7fa;
  }

  .regulation-checkbox,
  .regulation-radio {
    width: 100%;
    display: flex;
     align-items: flex-start;

     :deep(.el-checkbox__input),
     :deep(.el-radio__input) {
       margin-top: 2px;
       flex-shrink: 0;
     }

     :deep(.el-checkbox__label),
    :deep(.el-radio__label) {
      width: 100%;
      padding-left: 8px;
      line-height: 1.4;
    }
  }

  .regulation-content {
    width: 100%;
    min-width: 0;
  }

  .regulation-title {
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
  }

  .regulation-meta {
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;
  }
}

.el-pagination {
  justify-content: center;
}
</style>
