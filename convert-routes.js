const fs = require('node:fs')
const path = require('node:path')

// 递归读取目录下的所有.ts文件
function getAllTsFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir, { withFileTypes: true })
    
    files.forEach(file => {
        if (file.isDirectory()) {
            getAllTsFiles(path.join(dir, file.name), fileList)
        } else if (file.name.endsWith('.ts')) {
            fileList.push(path.join(dir, file.name))
        }
    })
    
    return fileList
}

// 解析路由文件内容
function parseRouteFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 简单的正则匹配来提取路由配置
  // 这里需要根据实际的文件结构来调整
  const routeMatch = content.match(/const routes[\s\S]*?= ([\s\S]*?);\s*export default routes/);
  
  if (routeMatch) {
    try {
      // 移除注释和函数调用，简化解析
      let routeStr = routeMatch[1];
      
      // 替换函数调用为字符串
      routeStr = routeStr.replace(/Layout/g, '"Layout"');
      routeStr = routeStr.replace(/\(\) => import\([^)]+\)/g, (match) => {
        const importPath = match.match(/import\('([^']+)'\)/);
        if (importPath) {
          return `"${importPath[1].replace('@/views/', '')}"`;
        }
        return '""';
      });
      
      // 移除注释
      routeStr = routeStr.replace(/\/\/.*$/gm, '');
      routeStr = routeStr.replace(/\/\*[\s\S]*?\*\//g, '');
      
      // 尝试解析为JSON（这里可能需要更复杂的处理）
      console.log('Processing file:', filePath);
      console.log('Route string:', routeStr.substring(0, 200) + '...');
      
      return { filePath, content: routeStr };
    } catch (error) {
      console.error('Error parsing route file:', filePath, error.message);
      return null;
    }
  }
  
  return null;
}

// 简单的路由转换函数（需要根据实际情况完善）
function convertRouteToJson() {
    // 这里需要实现具体的转换逻辑
    // 由于路由文件结构复杂，这里只是一个示例
    return {
        id: Math.floor(Math.random() * 1000),
        parentId: 0,
        path: '',
        redirect: null,
        component: '',
        name: '',
        isMenu: true,
        sort: 1,
        mark: 1,
        meta: '{}',
        children: [],
        auths: []
    }
}

// 主函数
function main() {
  const routerModulesDir = '/Users/<USER>/Documents/mbb-admin/whiskerguard-ui-system-admin/src/router/modules';
  const outputFile = '/Users/<USER>/Documents/mbb-admin/whiskerguard-ui-system-admin/src/store/modules/newRoute.js';
  
  try {
    const routerModulesPath = path.join(__dirname, 'src/router/modules')
    const files = fs.readdirSync(routerModulesPath, { withFileTypes: true })
    
    const routes = []
    
    files.forEach(file => {
      if (file.name.endsWith('.ts')) {
        const filePath = path.join(routerModulesPath, file.name)
        fs.readFileSync(filePath, 'utf8')
        
        // 这里应该解析.ts文件内容并转换为route.json格式
        // 由于解析TypeScript代码比较复杂，这里只是一个占位符
        const route = convertRouteToJson()
        routes.push(route)
      }
    })
    
    // 生成 newRoute.js 文件内容
    const output = `// 自动生成的路由配置文件\nexport const routes = ${JSON.stringify(routes, null, 2)}`
    
    // 写入文件
    const outputPath = path.join(__dirname, 'src/store/modules/newRoute.js')
    fs.writeFileSync(outputPath, output, 'utf8')
    
  } catch (error) {
    // Error converting routes
  }
}

if (require.main === module) {
  main();
}