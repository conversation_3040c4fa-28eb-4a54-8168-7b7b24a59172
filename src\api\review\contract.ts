import api from '@/api/index'

export default {
  // 合同附件智能识别
  smartRecognition(key: any) {
    return api.get(`/whiskerguardcontractservice/api/contract/messages/parse/document?key=${key}`, {
    })
  },
  // 获取合同统计数据
  getContractStatistic() {
    return api.get(`/whiskerguardcontractservice/api/contract/reviews/statistics`, {
    })
  },
  // 合同审查
  contractReview(paging: any, params: any, key: any) {
    switch (key) {
      case 'info': // 获取合同审查
        return api.get(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`, {
        })
      case 'create': // 创建合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews`, params)
      case 'update': // 更新合同审查
        return api.patch(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`, params)
      case 'publish': // 批量发布合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews/publish/batch`, params)
      case 'delete': // 删除合同审查
        return api.delete(`/whiskerguardcontractservice/api/contract/reviews/${params.id}`)
      default: // 分页查询合同审查
        return api.post(`/whiskerguardcontractservice/api/contract/reviews/page?page=${paging.page ? paging.page - 1 : 0}&size=${paging.limit ? paging.limit : 10}`, params)
    }
  },
  getComplianceProcess(params: any) {
    return api.get(
      `/whiskerguardcontractservice/api/compliance/reviews/process`,
      { params },
    )
  },
  // 根据流程类型获取审批流程
  getApprovalProcess(processType: any) {
    return api.get(
            `/whiskerguardapprovalservice/api/approval/processes/processType?processType=${processType}`,
            {})
  },
  // 创建合同审查记录（就是当complianceReview不为null时进入的页面提交的接口）
  createReview(params: any) {
    return api.post(
            `whiskerguardcontractservice/api/compliance/reviews`,
            params, 'post')
  },
  // ai智能审查
  aiContract(contractId: string | number, params?: any) {
    return api.get(
      `/whiskerguardcontractservice/api/contract/message/review/ai/review/${contractId}`,
      { params },
    )
  },
  // 查询所有审查记录
  queryAllReviews(contractId: string | number, params: any = {}) {
    return api.get(
      `/whiskerguardcontractservice/api/contract/message/review/list/${contractId}`,
      { params },
    )
  },
  // 合同通过或者不通过
  contractMessageReview(params: any = {}) {
    return api.post(`/whiskerguardcontractservice/api/contract/message/review`,
      params,
    )
  },
  // 保存审批流程
  createApproval(params: any) {
    return api.post(
            `/whiskerguardapprovalservice/api/approval/processes`,
            params,
    )
  },
}
