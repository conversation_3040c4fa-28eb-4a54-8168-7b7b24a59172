<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import {
  ArrowDown as ElIconArrowDown,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Filter as ElIconFilter,
  Plus as ElIconPlus,
  Search as ElIconSearch,
  SetUp as ElIconSetUp,
  Tickets as ElIconTickets,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { set } from 'nprogress'
import contractApi from '@/api/review/contract'

const router = useRouter()

// 标签页
const activeTab = ref('contract')
// 筛选条件
const filter = ref({
  status: null as string | null,
  level: null as string | null,
  department: null as string | null,
  dateRange: null as any,
  name: null as string | null,
  reviewer: null as string | null,
  result: null as string | null,
  risk: null as string | null,
  complianceTypes: null as string[] | null,
  contractType: null as string | null,
  reviewCode: null as string | null,
  createdAtStart: null as string | null,
  createdAtEnd: null as string | null,
})
const showAdvancedFilter = ref(false)

// 加载状态
const loading = ref(false)
// 树形数据
const treeData = [
  {
    label: '合同审查',
    children: [{ label: '采购合同' }, { label: '销售合同' }, { label: '服务合同' }, { label: '租赁合同' }, { label: '其他合同' }],
  },
  {
    label: '重大决策审查',
    children: [{ label: '投资决策' }, { label: '融资决策' }, { label: '重组决策' }],
  },
]
const defaultProps = {
  children: 'children',
  label: 'label',
}
// 表格数据
const contractData = ref([] as any[])

const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
function pagChange(val: any) {
  paging.value.page = val.page
  paging.value.limit = val.limit
  loadContractData()
}
// 分页
const currentPage = ref(1)
const pageSize = 10
const totalItems = computed(() => contractData.value.length)
// 选择项
const selectedItems = ref([] as any[])
const selectAll = ref(false)
// 我的审查
const myReviews = [
  { id: 1, title: '数据隐私政策审查', status: '需修改', deadline: '剩余1天' },
  { id: 2, title: '年度环保技术升级投资决策', status: '审查中', deadline: '剩余2天' },
  { id: 3, title: '供应商环保合规评估', status: '已完成', deadline: '2024-05-01' },
  { id: 4, title: '设备采购合同', status: '审查中', deadline: '剩余3天' },
  { id: 5, title: '软件许可协议', status: '待审查', deadline: '剩余5天' },
]
const contractStatistic = ref({
  basicStatistics: {},
  decisionTypeStatistics: [],
})
const pieData = ref([])
const pieName = ref([])
async function getContractStatistic() {
  try {
    contractStatistic.value = await contractApi.getContractStatistic()
    pieData.value = contractStatistic.value.contractTypeStatistics.map((item: any) => ({
      value: item.count,
      name: item.contractTypeName,
    }))
    pieName.value = contractStatistic.value.contractTypeStatistics.map((item: any) => item.contractTypeName)

    // 数据获取完成后，重新初始化图表
    nextTick(() => {
      initCharts()
    })
  }
  catch (error) {
    console.error('获取合同统计数据失败:', error)
  }
}
// API调用函数
async function loadContractData() {
  try {
    loading.value = true
    const params: any = {}

    // 映射筛选条件到API参数
    if (filter.value.status) { params.status = filter.value.status.toUpperCase() }
    if (filter.value.level) { params.level = filter.value.level }
    if (filter.value.department) { params.department = filter.value.department }
    if (filter.value.reviewer) { params.auditBy = filter.value.reviewer }
    if (filter.value.result) { params.result = filter.value.result }
    if (filter.value.risk) { params.riskLevel = filter.value.risk }
    if (filter.value.contractType) { params.contractType = filter.value.contractType }
    if (filter.value.name) { params.name = filter.value.name }
    if (filter.value.reviewCode) { params.reviewCode = filter.value.reviewCode }

    // 处理日期范围
    if (filter.value.dateRange && filter.value.dateRange.length === 2) {
      params.createdAtStart = filter.value.dateRange[0]
      params.createdAtEnd = filter.value.dateRange[1]
    }

    // 处理合规类型
    if (filter.value.complianceTypes && filter.value.complianceTypes.length > 0) {
      params.complianceTypes = filter.value.complianceTypes
    }

    const response = await contractApi.contractReview(paging.value, params)

    if (response) {
      // 映射API返回的字段到页面显示字段
      const mappedData = (response.content || []).map((item: any) => ({
        id: item.id,
        name: item.name || item.contractTitle, // 合同名称
        type: getContractTypeText(item.contractType), // 合同类型
        amount: item.contractMessage?.money || 0, // 合同金额
        counterparty: item.contractMessage?.secondParty || '', // 交易对手
        department: item.departmentName, // 发起部门
        level: getLevelText(item.level), // 审查级别
        status: getStatusText(item.status), // 状态
        startTime: item.createdAt, // 发起时间
        reviewer: item.auditNameBy, // 审查人
        endTime: item.reviewEndTime, // 完成时间
        riskLevel: item.overallRiskLevel, // 风险等级
        riskScore: item.riskScore, // 风险分数
        originalData: item, // 保存原始数据用于详情页面
      }))
      contractData.value = mappedData
      paging.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('加载合同审查数据失败:', error)
    ElMessage.error('加载数据失败')
    // 接口失败时返回空数组
    contractData.value = []
    paging.value.total = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索函数
function handleSearch() {
  paging.value.page = 1
  loadContractData()
}

// 重置搜索
function resetSearch() {
  filter.value = {
    status: null,
    level: null,
    department: null,
    dateRange: null,
    name: null,
    reviewer: null,
    result: null,
    risk: null,
    complianceTypes: [], // 将null改为空数组
    contractType: null,
    name: null,
    reviewCode: null,
    createdAtStart: null,
    createdAtEnd: null,
  }
  handleSearch()
}

// 应用高级筛选
function applyFilter() {
  handleSearch()
  // 可以在这里添加其他逻辑，如关闭高级筛选面板等
}

// 分页处理
function handlePageChange(page: number) {
  paging.value.page = page
  loadContractData()
}

function handleSizeChange(size: number) {
  paging.value.limit = size
  paging.value.page = 1
  loadContractData()
}

// 新增合同审查
function handleAdd() {
  router.push('/monitor/examination/contractReview/addEdit')
}

// 编辑合同审查
function handleEdit(row: any) {
  router.push(`/monitor/examination/contractReview/addEdit?id=${row.id}`)
}

// 导出数据
function handleExport() {
  ElMessage.info('导出功能开发中...')
}

// 审查统计
function handleStatistics() {
  ElMessage.info('统计功能开发中...')
}

// 查看详情
function handleDetail(row: any) {
  router.push(`/monitor/examination/contractReview/detail?id=${row.id}`)
}

// 删除合同审查
async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm('确定要删除这条合同审查记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await contractApi.contractReview({}, { id: row.id }, 'delete')
    ElMessage.success('删除成功')
    loadContractData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
async function handleBatchDelete() {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 批量删除逻辑
    for (const item of selectedItems.value) {
      await contractApi.contractReview({}, { id: item.id }, 'delete')
    }

    ElMessage.success('批量删除成功')
    selectedItems.value = []
    loadContractData()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 状态映射
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    MODIFY: '需修改',
    PENDING: '待审查',
    PUBLISHED: '已发布',
    REVIEWING: '审查中',
    REVOKE: '已撤回',
  }
  return statusMap[status] || status
}

// 级别映射
function getLevelText(level: string) {
  const levelMap: Record<string, string> = {
    GENERAL: '一般',
    IMPORTANT: '重要',
    CRITICAL: '关键',
  }
  return levelMap[level] || level
}

// 合同类型映射
function getContractTypeText(type: string) {
  const typeMap: Record<string, string> = {
    TECHNICAL_SERVICES: '技术服务合同',
    MAJOR_DECISIONS: '重大决策',
    DATA: '数据合同',
    PROCUREMENT: '采购合同',
  }
  return typeMap[type] || type
}

// 获取合同类型图表数据
function getContractTypeChartData() {
  if (!contractStatistic.value?.contractTypeStatistics) {
    return []
  }
  return contractStatistic.value.contractTypeStatistics.map((item: any) => ({
    value: item.count,
    name: item.contractTypeName,
  }))
}

// 获取合同类型图例数据
function getContractTypeLegendData() {
  if (!contractStatistic.value?.contractTypeStatistics) {
    return []
  }
  return contractStatistic.value.contractTypeStatistics.map((item: any) => item.contractTypeName)
}
// 审查趋势数据
const trendData = [
  { month: '2023-12', count: 18 },
  { month: '2024-01', count: 22 },
  { month: '2024-02', count: 19 },
  { month: '2024-03', count: 25 },
  { month: '2024-04', count: 28 },
  { month: '2024-05', count: 8 },
]
// 初始化图表
const trendChart = ref(null as any)
const pieChart = ref(null as any)
function initCharts() {
  if (pieChart.value) {
    const pieChartInstance = echarts.init(pieChart.value)
    const pieOption = {
      animation: false,
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        confine: false,
        appendToBody: true,
        position(point, params, dom, rect, size) {
          // 获取容器大小
          const containerWidth = size.viewSize[0]
          const containerHeight = size.viewSize[1]
          const tooltipWidth = size.contentSize[0]
          const tooltipHeight = size.contentSize[1]

          // 计算最佳位置，确保不超出视口
          let x = point[0] + 10
          let y = point[1] - tooltipHeight / 2

          // 右边界检查
          if (x + tooltipWidth > containerWidth) {
            x = point[0] - tooltipWidth - 10
          }

          // 上边界检查
          if (y < 0) {
            y = 10
          }

          // 下边界检查
          if (y + tooltipHeight > containerHeight) {
            y = containerHeight - tooltipHeight - 10
          }

          return [x, y]
        },
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: pieName.value,
      },
      series: [
        {
          name: '分布比例',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: pieData.value,
        },
      ],
    }
    pieChartInstance.setOption(pieOption)
  }
  window.addEventListener('resize', () => {
    if (pieChart.value && pieChartInstance) {
      pieChartInstance.resize()
    }
  })
}
onMounted(() => {
  // 初始化加载数据
  initCharts()
  loadContractData()
  getContractStatistic()
  // setTimeout(() => {
  //   initCharts()
  // }, 0)
})
// 监听 contractStatistic 数据变化，重新初始化图表
// watch(activeTab, () => {
//   nextTick(() => {
//     initCharts()
//   })
// })
// 方法
function getTagType(type: any) {
  if (!type) { return 'primary' }
  const types = {
    采购合同: 'primary',
    销售合同: 'success',
    服务合同: 'info',
    租赁合同: 'warning',
    其他合同: 'danger',
  }
  return types[type] || 'primary'
}

function getStatusTagType(status: any) {
  if (!status) { return 'primary' }
  const statusTypes = {
    待审查: 'info',
    审查中: 'primary',
    已完成: 'success',
    已撤回: 'info',
    需修改: 'warning',
  }
  return statusTypes[status] || 'primary'
}

// 获取审查级别标签类型
function getLevelTagType(level: any) {
  if (!level) { return 'primary' }
  const levelTypes = {
    一般: 'info',
    重要: 'warning',
    关键: 'danger',
    高: 'danger',
    中: 'warning',
    低: 'info',
  }
  return levelTypes[level] || 'primary'
}

// 获取风险等级文本
function getRiskLevelText(level: any) {
  if (!level) { return '未知风险' }
  const levelMap = {
    1: '低风险',
    2: '中风险',
    3: '高风险',
    4: '极高风险',
  }
  return levelMap[level] || `风险等级${level}`
}

// 获取风险等级标签类型
function getRiskLevelTagType(level: any) {
  if (!level) { return 'primary' }
  const typeMap = {
    1: 'success',
    2: 'warning',
    3: 'danger',
    4: 'danger',
  }
  return typeMap[level] || 'info'
}

function formatAmount(amount: any) {
  if (!amount) { return '¥0' }
  return `¥${amount.toLocaleString()}`
}

function handleSelectionChange(val: any) {
  selectedItems.value = val
  selectAll.value = val.length === contractData.value.length
}

function resetFilter() {
  filter.value = {
    status: null,
    level: null,
    department: null,
    dateRange: null,
    name: null,
    reviewer: null,
    result: null,
    risk: null,
    complianceTypes: null,
    contractType: null,
    name: null,
    reviewCode: null,
    createdAtStart: null,
    createdAtEnd: null,
  }
}
// 其他审查数据
const otherData = [
  {
    id: 'OR20240502001',
    name: '产品安全性合规审查',
    type: '安全合规',
    object: '新款智能手表',
    department: '产品部',
    level: '高',
    status: '审查中',
    startTime: '2024-05-02',
    reviewer: '刘芳',
    endTime: '',
  },
  {
    id: 'OR20240429002',
    name: '年度IT安全审计',
    type: 'IT合规',
    object: '企业信息系统',
    department: 'IT部',
    level: '中',
    status: '待审查',
    startTime: '2024-04-29',
    reviewer: '待分配',
    endTime: '',
  },
  {
    id: 'OR20240425003',
    name: '财务季度合规审查',
    type: '财务合规',
    object: 'Q1财务报表',
    department: '财务部',
    level: '高',
    status: '已完成',
    startTime: '2024-04-25',
    reviewer: '王强',
    endTime: '2024-04-30',
  },
  {
    id: 'OR20240418004',
    name: '用户数据隐私审查',
    type: '数据合规',
    object: '用户数据库',
    department: '数据部',
    level: '中',
    status: '需修改',
    startTime: '2024-04-18',
    reviewer: '李明',
    endTime: '',
  },
  {
    id: 'OR20240410005',
    name: '新业务线合规评估',
    type: '业务合规',
    object: '新零售业务',
    department: '业务部',
    level: '高',
    status: '已撤回',
    startTime: '2024-04-10',
    reviewer: '张华',
    endTime: '2024-04-12',
  },
]
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合同审查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'contractReview/index/add'" @click="handleAdd">
              <el-icon class="mr-2">
                <ElIconPlus />
              </el-icon>
              新增审查
            </el-button>
            <!-- <el-button v-auth="'contractReview/index/export'" @click="handleExport">
              <el-icon class="mr-2">
                <ElIconDownload />
              </el-icon>
              导出数据
            </el-button> -->
            <el-button v-auth="'contractReview/index/statistics'" @click="handleStatistics">
              <el-icon class="mr-2">
                <ElIconDataAnalysis />
              </el-icon>
              审查统计
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="p-6">
                <!-- 筛选区 -->
                <div class="mb-6">
                  <!-- <div class="mr-6 w-64">
                    <div class="h-full border rounded-lg p-4">
                      <div class="mb-3 font-medium">
                        审查分类
                      </div>
                      <el-tree :data="treeData" :props="defaultProps" default-expand-all />
                    </div>
                  </div> -->
                  <div class="rounded-lg bg-gray-50 p-4">
                    <div class="flex flex-wrap items-center gap-4">
                      <el-select v-model="filter.status" placeholder="审查状态" class="w-40">
                        <el-option label="全部状态" value="" />
                        <el-option label="待审查" value="pending" />
                        <el-option label="审查中" value="reviewing" />
                        <el-option label="已完成" value="completed" />
                        <el-option label="已撤回" value="withdrawn" />
                        <el-option label="需修改" value="needModify" />
                      </el-select>
                      <el-select v-model="filter.level" placeholder="审查级别" class="w-40">
                        <el-option label="全部级别" value="" />
                        <el-option label="一般" value="GENERAL" />
                        <el-option label="重要" value="IMPORTANT" />
                        <el-option label="关键" value="CRITICAL" />
                      </el-select>
                      <el-select v-model="filter.department" placeholder="发起部门" class="w-40">
                        <el-option label="全部部门" value="" />
                        <el-option label="财务部" value="finance" />
                        <el-option label="法务部" value="legal" />
                        <el-option label="市场部" value="marketing" />
                        <el-option label="技术部" value="tech" />
                      </el-select>
                      <el-date-picker
                        v-model="filter.dateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" class="w-64"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                      />
                      <div class="relative">
                        <el-input
                          v-model="filter.name"
                          class="w-64" type="text" placeholder="关键字搜索"
                        >
                          <template #prefix>
                            <el-icon class="text-gray-400">
                              <ElIconSearch />
                            </el-icon>
                          </template>
                        </el-input>
                      </div>

                      <el-button
                        v-auth="'contractReview/index/search'"
                        class="ml-auto"
                        @click="handleSearch"
                      >
                        <el-icon class="mr-2">
                          <ElIconFilter />
                        </el-icon>
                        查询
                      </el-button>
                      <el-button
                        class="ml-2"
                        @click="resetSearch"
                      >
                        重置
                      </el-button>
                      <!-- <el-button
                        @click="showAdvancedFilter = !showAdvancedFilter"
                      >
                        {{ showAdvancedFilter ? '收起' : '高级筛选' }}
                      </el-button> -->
                    </div>
                    <!-- 高级筛选 -->
                    <div v-if="showAdvancedFilter" class="mb-6 border rounded-lg p-4">
                      <div class="grid grid-cols-3 gap-4">
                        <el-select v-model="filter.reviewer" placeholder="审查人员" class="w-full">
                          <el-option label="全部人员" value="" />
                          <el-option label="张三" value="zhangsan" />
                          <el-option label="李四" value="lisi" />
                          <el-option label="王五" value="wangwu" />
                        </el-select>
                        <el-select v-model="filter.result" placeholder="评估结果" class="w-full">
                          <el-option label="全部结果" value="" />
                          <el-option label="通过" value="pass" />
                          <el-option label="不通过" value="fail" />
                          <el-option label="有条件通过" value="conditional" />
                        </el-select>
                        <el-select v-model="filter.risk" placeholder="风险等级" class="w-full">
                          <el-option label="全部等级" value="" />
                          <el-option label="低风险" value="low" />
                          <el-option label="中风险" value="medium" />
                          <el-option label="高风险" value="high" />
                        </el-select>
                      </div>
                      <div class="mt-4">
                        <div class="mb-2 font-medium">
                          合规类型
                        </div>
                        <el-checkbox-group v-model="filter.complianceTypes" class="flex flex-wrap gap-4">
                          <el-checkbox label="财务合规" />
                          <el-checkbox label="法律合规" />
                          <el-checkbox label="数据合规" />
                          <el-checkbox label="业务合规" />
                          <el-checkbox label="其他合规" />
                        </el-checkbox-group>
                      </div>
                      <div class="mt-4 flex justify-end space-x-3">
                        <button
                          v-auth="'contractReview/index/reset'"
                          class="!rounded-button whitespace-nowrap border border-gray-300 px-4 py-2 text-gray-600"
                          @click="resetFilter"
                        >
                          重置
                        </button>
                        <button
                          v-auth="'contractReview/index/apply'"
                          class="!rounded-button whitespace-nowrap bg-blue-500 px-4 py-2 text-white"
                          @click="applyFilter"
                        >
                          应用
                        </button>
                      </div>
                    </div>
                    <!-- 表格 -->
                    <el-table v-loading="loading" :data="contractData" style="width: 100%;" @selection-change="handleSelectionChange">
                      <el-table-column type="selection" width="55" />
                      <el-table-column prop="name" label="合同名称" width="200" show-overflow-tooltip />
                      <el-table-column prop="type" label="合同类型" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getTagType(row.type)" size="small">
                            {{ row.type }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="amount" label="合同金额" width="120" align="right">
                        <template #default="{ row }">
                          {{ formatAmount(row.amount) }}
                        </template>
                      </el-table-column>
                      <!-- <el-table-column prop="counterparty" label="交易对手" width="150" show-overflow-tooltip /> -->
                      <el-table-column prop="department" label="发起部门" width="120" />
                      <el-table-column prop="level" label="审查级别" width="100">
                        <template #default="{ row }">
                          <el-tag :type="getLevelTagType(row.level)" size="small">
                            {{ row.level }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <!-- <el-table-column prop="riskLevel" label="风险等级" width="100">
                        <template #default="{ row }">
                          <el-tag v-if="row.riskLevel" :type="getRiskLevelTagType(row.riskLevel)" size="small">
                            {{ getRiskLevelText(row.riskLevel) }}
                          </el-tag>
                          <span v-else class="text-gray-400">-</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="riskScore" label="风险分数" width="100" align="center">
                        <template #default="{ row }">
                          <span v-if="row.riskScore !== undefined && row.riskScore !== null">
                            {{ row.riskScore }}
                          </span>
                          <span v-else class="text-gray-400">-</span>
                        </template>
                      </el-table-column> -->
                      <el-table-column prop="status" label="状态" width="120">
                        <template #default="{ row }">
                          <el-tag :type="getStatusTagType(row.status)" size="small">
                            {{ row.status }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="startTime" label="发起时间" width="120" />
                      <el-table-column prop="reviewer" label="审查人" width="100" />
                      <el-table-column prop="endTime" label="完成时间" width="120" />
                      <el-table-column label="操作" width="200" fixed="right">
                        <template #default="{ row }">
                          <!-- v-auth="'contractReview/index/view'" -->
                          <el-button v-auth="'contractReview/index/view'" type="primary" class="!ml-0" size="small" @click="handleDetail(row)">
                            查看
                          </el-button>
                          <el-button v-auth="'contractReview/index/edit'" type="warning" class="!ml-0" size="small" @click="handleEdit(row)">
                            编辑
                          </el-button>
                          <el-button v-auth="'contractReview/index/delete'" type="danger" class="!ml-0" size="small" @click="handleDelete(row)">
                            删除
                          </el-button>
                          <!-- 更多操作菜单已禁用 -->
                          <el-dropdown v-if="false">
                            <el-button type="text" size="small">
                              更多
                              <el-icon class="el-icon--right">
                                <ElIconArrowDown />
                              </el-icon>
                            </el-button>
                            <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item @click="handleDelete(row)">
                                  删除
                                </el-dropdown-item>
                                <el-dropdown-item @click="handleExport">
                                  导出
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </template>
                          </el-dropdown>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div class="mt-4 flex items-center justify-between">
                      <div class="text-sm text-gray-500">
                        共 {{ paging.total }} 条记录
                      </div>
                      <!-- <el-pagination
                        v-model:current-page="currentPage" :page-size="pageSize" :total="paging.total"
                        layout="prev, pager, next, jumper"
                        @change
                      /> -->
                      <page-compon
                        :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
                        @pag-change="pagChange"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  审查统计
                </div>
              </template>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    总审查数
                  </div>
                  <div class="text-2xl font-bold">
                    {{ contractStatistic.basicStatistics?.totalCount }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    待审查
                  </div>
                  <div class="text-2xl text-blue-500 font-bold">
                    {{ contractStatistic.basicStatistics?.pendingCount }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    审查中
                  </div>
                  <div class="text-2xl text-yellow-500 font-bold">
                    {{ contractStatistic.basicStatistics?.reviewingCount }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    已完成
                  </div>
                  <div class="text-2xl text-green-500 font-bold">
                    {{ contractStatistic.basicStatistics?.completedCount }}
                  </div>
                </div>
                <div class="border rounded-lg p-4">
                  <div class="mb-1 text-sm text-gray-500">
                    需修改
                  </div>
                  <div class="text-2xl text-red-500 font-bold">
                    {{ contractStatistic.basicStatistics?.modifyCount }}
                  </div>
                </div>
              </div>
              <!-- 审查分布图 -->
              <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查分布
                  </div>
                </div>
                <div class="h-40">
                  <div ref="pieChart" class="h-full w-full" />
                </div>
              </div>
              <!-- 审查趋势图 -->
              <!-- <div class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    审查趋势
                  </div>
                  <el-link type="primary" :underline="false">
                    查看更多
                  </el-link>
                </div>
              </div> -->
              <!-- 我的审查 -->
              <div v-if="false" class="mb-6 border rounded-lg p-4">
                <div class="mb-3 flex items-center justify-between">
                  <div class="font-medium">
                    我的审查
                  </div>
                  <el-link type="primary" :underline="false">
                    查看全部
                  </el-link>
                </div>
                <div class="space-y-3">
                  <div v-for="item in myReviews" :key="item.id" class="border-b pb-3 last:border-b-0 last:pb-0">
                    <div class="flex justify-between">
                      <div class="font-medium">
                        {{ item.title }}
                      </div>
                      <el-tag :type="getStatusTagType(item.status)" size="small">
                        {{ item.status }}
                      </el-tag>
                    </div>
                    <div class="text-sm text-gray-500">
                      截止: {{ item.deadline }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 快捷功能 -->
              <div v-if="false" class="border rounded-lg p-4">
                <div class="mb-3 font-medium">
                  快捷功能
                </div>
                <div class="grid grid-cols-2 gap-3">
                  <button
                    v-auth="'contractReview/index/templateManage'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconDocument />
                    </el-icon>
                    模板管理
                  </button>
                  <button
                    v-auth="'contractReview/index/processConfig'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconSetUp />
                    </el-icon>
                    流程配置
                  </button>
                  <button
                    v-auth="'contractReview/index/reportGenerate'"
                    class="!rounded-button flex items-center justify-center whitespace-nowrap border border-gray-200 px-3 py-2"
                  >
                    <el-icon class="mr-2">
                      <ElIconTickets />
                    </el-icon>
                    报告生成
                  </button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
  :deep(.el-tabs__header) {
    margin: 0;
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
    background-color: #e4e7ed;
  }

  :deep(.el-tree) {
    background: transparent;
  }

  :deep(.el-tree-node__content) {
    height: 36px;
  }

  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f8f8f8;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .cell) {
    padding-right: 16px;
    padding-left: 16px;
  }

  /* ECharts 图表容器样式 */
  .h-40 {
    position: relative;
    overflow: visible !important;
  }

  /* 确保图表容器不会裁剪tooltip */
  :deep(.echarts-tooltip) {
    z-index: 9999 !important;
  }
</style>
