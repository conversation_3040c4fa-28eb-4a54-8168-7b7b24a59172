<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Bell as ElIconBell,
  Bottom as ElIconBottom,
  Delete as ElIconDelete,
  DocumentAdd as ElIconDocumentAdd,
  Edit as ElIconEdit,
  <PERSON>u as ElIconMenu,
  Search as ElIconSearch,
  SetUp as ElIconSetUp,
  Top as ElIconTop,
  View as ElIconView,
} from '@element-plus/icons-vue'
// 路由实例
const router = useRouter()
const questions = ref([
  { id: 1, type: '单选题', difficulty: '简单', score: 5, content: '下列哪项不属于合规管理的基本原则？' },
  { id: 2, type: '多选题', difficulty: '中等', score: 10, content: '数据安全保护措施包括哪些？' },
  { id: 3, type: '判断题', difficulty: '简单', score: 5, content: '反洗钱工作只需要在发现可疑交易时进行报告。' },
  { id: 4, type: '简答题', difficulty: '困难', score: 20, content: '请简述合规风险管理的三个主要步骤。' },
  { id: 5, type: '案例分析', difficulty: '困难', score: 30, content: '请分析以下案例中的合规风险点...' },
  { id: 6, type: '填空题', difficulty: '中等', score: 10, content: '合规文化的核心是____和____。' },
  { id: 7, type: '单选题', difficulty: '中等', score: 5, content: '下列哪项不属于金融合规的监管机构？' },
  { id: 8, type: '多选题', difficulty: '困难', score: 15, content: '合规培训的主要内容包括哪些方面？' },
])
function getDifficultyTag(difficulty: string) {
  switch (difficulty) {
    case '简单': return 'success'
    case '中等': return 'warning'
    case '困难': return 'danger'
    default: return ''
  }
}
</script>

<template>
  <div>
    <FaPageMain>
      <div class="h-screen flex bg-gray-100">
        <!-- 主内容区 -->
        <div class="flex flex-1 flex-col overflow-hidden">
          <page-header>
            <template #content>
              <div class="aic jcsb flex">
                <div class="f-20 c-['#000000']">
                  <h2 class="text-xl text-gray-800 font-semibold">
                    新增考核
                  </h2>
                </div>
                <div class="space-x-3">
                  <el-button type="primary" class="!rounded-button whitespace-nowrap">
                    保存
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    保存并新增
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    取消
                  </el-button>
                  <el-button class="!rounded-button whitespace-nowrap">
                    预览
                  </el-button>
                </div>
              </div>
            </template>
          </page-header>
          <!-- 主内容 -->
          <main class="flex-1 overflow-auto bg-gray-50 p-6">
            <!-- 表单卡片 -->
            <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
                基本信息
              </h2>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="考核名称" required>
                  <el-input placeholder="请输入考核名称" />
                </el-form-item>
                <el-form-item label="考核编号">
                  <div class="flex space-x-2">
                    <el-input placeholder="系统自动生成" />
                    <el-button size="small">
                      自动生成
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="考核类型" required>
                  <el-select placeholder="请选择考核类型" class="w-full">
                    <el-option label="在线考试" value="online" />
                    <el-option label="作业提交" value="homework" />
                    <el-option label="实操考核" value="practice" />
                    <el-option label="混合类型" value="mixed" />
                    <el-option label="其他" value="other" />
                  </el-select>
                </el-form-item>
                <el-form-item label="关联课程">
                  <el-select
                    multiple
                    placeholder="请选择关联课程"
                    class="w-full"
                  >
                    <el-option label="合规基础培训" value="1" />
                    <el-option label="数据安全法规" value="2" />
                    <el-option label="反洗钱知识" value="3" />
                    <el-option label="金融合规实务" value="4" />
                  </el-select>
                </el-form-item>
                <el-form-item label="考核对象" required>
                  <el-select
                    multiple
                    placeholder="请选择考核对象"
                    class="w-full"
                  >
                    <el-option label="全体员工" value="all" />
                    <el-option label="管理层" value="management" />
                    <el-option label="风控部门" value="risk" />
                    <el-option label="合规部门" value="compliance" />
                    <el-option label="运营部门" value="operation" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <!-- 考核设置 -->
            <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
                考核设置
              </h2>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="开始时间" required>
                  <el-date-picker
                    type="datetime"
                    placeholder="选择开始时间"
                    class="w-full"
                  />
                </el-form-item>
                <el-form-item label="结束时间" required>
                  <el-date-picker
                    type="datetime"
                    placeholder="选择结束时间"
                    class="w-full"
                  />
                </el-form-item>
                <el-form-item label="考试时长" required>
                  <div class="flex space-x-2">
                    <el-input-number :min="10" :max="300" />
                    <el-select placeholder="分钟" class="w-24">
                      <el-option label="分钟" value="minute" />
                      <el-option label="小时" value="hour" />
                    </el-select>
                  </div>
                </el-form-item>
                <el-form-item label="及格分数" required>
                  <el-input-number :min="0" :max="100" :step="5" class="w-32" />
                </el-form-item>
                <el-form-item label="显示结果">
                  <el-radio-group>
                    <el-radio label="考核后立即显示" />
                    <el-radio label="所有人考核完成后显示" />
                    <el-radio label="指定时间显示">
                      <el-date-picker
                        type="datetime"
                        placeholder="选择显示时间"
                        class="ml-2"
                      />
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="防作弊设置">
                  <el-checkbox-group>
                    <el-checkbox label="随机题序" />
                    <el-checkbox label="禁止切换窗口" />
                    <el-checkbox label="限制IP范围" />
                    <el-checkbox label="拍摄面部照片" />
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </div>
            <!-- 考题管理 -->
            <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
                考题管理
              </h2>
              <div class="mb-6 flex space-x-3">
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  <el-icon><ElIconDocumentAdd /></el-icon>
                  <span>从题库导入</span>
                </el-button>
                <el-button class="!rounded-button whitespace-nowrap">
                  <el-icon><ElIconSetUp /></el-icon>
                  <span>随机抽题</span>
                </el-button>
              </div>
              <el-table :data="questions" border class="mb-6">
                <el-table-column prop="id" label="题号" width="80" />
                <el-table-column prop="type" label="题型" width="120" />
                <el-table-column prop="difficulty" label="难度" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getDifficultyTag(row.difficulty)">
                      {{ row.difficulty }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="score" label="分值" width="80" />
                <el-table-column prop="content" label="题目内容" />
                <el-table-column label="操作" width="300">
                  <template #default>
                    <el-button size="small" text>
                      查看
                    </el-button>
                    <el-button size="small" text>
                      编辑
                    </el-button>
                    <el-button size="small" text>
                      删除
                    </el-button>
                    <el-button size="small" text>
                      <el-icon><ElIconTop /></el-icon>
                    </el-button>
                    <el-button size="small" text>
                      <el-icon><ElIconBottom /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex items-center justify-between">
                <span class="text-gray-500">已选题目: {{ questions.length }} 题</span>
                <span class="font-semibold">总分: 100 分</span>
              </div>
            </div>
            <!-- 考核说明 -->
            <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
              <div class="mb-6 flex items-center justify-between border-b pb-2">
                <h2 class="text-lg font-semibold">
                  考核说明
                </h2>
                <el-button size="small" class="!rounded-button whitespace-nowrap">
                  导入模板
                </el-button>
              </div>
              <el-form label-position="top">
                <el-form-item label="考核要求" required>
                  <el-input
                    type="textarea"
                    :rows="5"
                    placeholder="请输入考核要求"
                  />
                </el-form-item>
                <el-form-item label="考核范围">
                  <el-input
                    type="textarea"
                    :rows="5"
                    placeholder="描述考核涵盖的内容范围"
                  />
                </el-form-item>
                <el-form-item label="注意事项">
                  <el-input
                    type="textarea"
                    :rows="5"
                    placeholder="描述考核过程中需要注意的事项"
                  />
                </el-form-item>
              </el-form>
            </div>
            <!-- 结果管理 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h2 class="mb-6 border-b pb-2 text-lg font-semibold">
                结果管理
              </h2>
              <el-form label-position="right" label-width="120px">
                <el-form-item label="评分方式">
                  <el-radio-group>
                    <el-radio label="系统自动评分" />
                    <el-radio label="人工评分" />
                    <el-radio label="混合评分" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="结果通知">
                  <el-checkbox-group>
                    <el-checkbox label="系统消息" />
                    <el-checkbox label="电子邮件" />
                    <el-checkbox label="短信通知" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="颁发证书" class="mb-4">
                  <el-radio-group>
                    <el-radio label="是" />
                    <el-radio label="否" />
                  </el-radio-group>
                </el-form-item>
                <div class="ml-6 space-y-4">
                  <div class="flex space-x-4">
                    <el-form-item label="证书模板" class="flex-1">
                      <el-select placeholder="请选择证书模板" class="w-full">
                        <el-option label="标准证书模板" value="standard" />
                        <el-option label="高级证书模板" value="premium" />
                        <el-option label="自定义模板" value="custom" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="证书名称" class="flex-1">
                      <el-input placeholder="请输入证书名称" />
                    </el-form-item>
                    <el-form-item label="证书有效期" class="flex-1">
                      <el-select placeholder="请选择有效期" class="w-full">
                        <el-option label="1年" value="1" />
                        <el-option label="2年" value="2" />
                        <el-option label="3年" value="3" />
                        <el-option label="5年" value="5" />
                        <el-option label="永久有效" value="permanent" />
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
                <el-form-item label="允许补考" class="mb-4">
                  <el-radio-group>
                    <el-radio label="是" />
                    <el-radio label="否" />
                  </el-radio-group>
                </el-form-item>
                <div class="ml-6 flex items-start space-x-4">
                  <el-form-item label="补考次数" class="mb-0">
                    <el-input-number :min="1" :max="5" />
                  </el-form-item>
                  <el-form-item label="补考条件" class="mb-0 flex-1">
                    <el-input
                      type="textarea"
                      :rows="3"
                      placeholder="请输入补考条件"
                    />
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </main>
        </div>
      </div>
    </fapagemain>
  </div>
</template>

<style scoped>
.bg-[#1A1F37] {
background-color: #1A1F37;
}
.bg-blue-700 {
background-color: rgba(30, 136, 229, 0.8);
}
.bg-blue-600 {
background-color: rgba(30, 136, 229, 0.9);
}
:deep(.el-form-item__label) {
color: #666666;
font-size: 14px;
}
:deep(.el-input__inner),
:deep(.el-textarea__inner),
:deep(.el-select .el-input__inner) {
height: 32px;
border-radius: 4px;
border-color: #E0E0E0;
font-size: 14px;
}
:deep(.el-textarea__inner) {
min-height: 32px;
font-size: 14px;
}
:deep(.el-form-item) {
margin-bottom: 24px;
}
:deep(.el-table) {
font-size: 14px;
}
:deep(.el-table th.el-table__cell) {
background-color: #f5f7fa;
}
:deep(.el-radio__label),
:deep(.el-checkbox__label),
:deep(.el-date-editor .el-input__inner) {
font-size: 14px;
}
</style>
