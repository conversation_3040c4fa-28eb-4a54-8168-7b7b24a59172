### 模块概述
   - 该模块实现制度库管理系统，核心功能包括：
   - 制度分类树展示（组织架构/业务领域/制度类型）
   - 多维度制度查看（全部/我创建/待审批/近期更新）
   - 制度列表展示与操作（增删改查）
   - 制度搜索与筛选功能
   - 批量导入/导出功能
### 依赖项
1. **UI框架**：
   - Element Plus (Tabs/Table/Tree/Form等组件)
   - Vue 3 Composition API

2. **自定义组件**：
   - 如果有引入的组件，分析一下插槽，不要使用未定义的具名插槽，如果想要使用，可以丰富插槽内容
   - PageHeader：页面头部组件
   - PageMain：主内容区组件
   - LayoutContainer：左右布局容器
   - SvgIcon：图标组件
   - PageCompon：分页组件

3. **API服务**：
   - `@/api/complianceApi/one/systemManagement.ts`
   - system(): 获取制度列表接口
   - 请求参数：{ page, limit, title?, status? }

4. **路由**：
   - Vue Router (跳转到编辑页)
   ### 状态管理
1. **activeName**：当前激活的标签页ID (默认值:1)
   - 类型：`Ref<number>`
   - 可选值：1(全部制度)/2(我创建)/3(待审批)/4(近期更新)

5. **systemList**：制度列表数据及熟悉结构
   - 树形结构格式参考/Users/<USER>/IdeaProjects/whiskerguard-ui/whiskerguard-ui-system-admin/src/views/hegui/one/system/systemManagement/arr.json，如果数据格式不满足，将数据修改为可以在el-tree上使用的结构，点击树形结构时，传入对应的id，筛选列表数据也就是categoryId对应树形的id，并且做请求取消的功能优化，方式多次请求造成数据错误
   - 类型：`Ref<TreeItem[]>`

### 完善步骤
1. **接口对接**：
   - 根据apifox上的字段映射，完善接口参数和返回数据结构，并做防抖处理，使用公共方法v-debounce='1000'(指令)
   - 创建 `@/api/complianceApi/one/systemManagement.ts` 文件，并定义接口方法，并完善请求参数和返回数据结构
   - 补充详情页面接口及字段映射（点击列表修改即可弹窗展示详情，详情使用组件封装，有的话直接使用，没有的话创建组件封装在components文件夹下）
   - 新增页面及保存接口补充同详情一样处理
2. **css样式方面**：
   - 由于使用的是Element Plus，ui风格及大小交互使用保持统一，输入框和下拉框样式保持大小统一，不能太小，样式书写使用Tailwind CSS风格，例如：class="c-[#4B5563]"
### 错误处理
  - 创建log文件，记录错误日志，并使用try catch捕获错误，并使用console.error打印错误日志，并使用alert提示错误信息，并使用throw new Error抛出错误信息，并使用catch捕获错误，并使用console.error打印错误日志，并使用alert提示错误信息，并使用throw new Error抛出错

### 特别注意
  - 关于/Users/<USER>/IdeaProjects/whiskerguard-ui/whiskerguard-ui-system-admin/src/views/hegui/one/system/systemManagement/apifox.json这个后台接口文件里面没有的接口，你就不要单独去虚拟接口，没有接口的就告诉我。
  - VUE文件当前已有的样式不要调整
  - 插槽的命名不要随便乱命名
  - 插槽的具名插槽不要随便乱写,要根据ui组件和components里面的组件进行匹配
  - 只关注接口的对接与交互的实现，如果页面没有的，可以帮我新增并完成样式（根据接口字段补充一份目前框架的风格样式，比如新增页，详情页或者编辑页，一般编辑页和新增页同用一个页面），如果页面有的，样式不要改动
  - 接口以apifox为标准，前端做了一层封装，比如data.data.content可以拿到数据，但是现在只需要data.content就可以，这点也要注意

