---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 03-企业组织架构服务/员工（用户）

## GET  获取用户统计信息

GET /whiskerguardorgservice/api/employees/statistics

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "totalUsers": 0,
  "roleStatistics": {
    "adminUsers": 0,
    "regularUsers": 0
  },
  "statusStatistics": {
    "activeUsers": 0,
    "inactiveUsers": 0
  },
  "departmentStatistics": [
    {
      "departmentId": 0,
      "departmentName": "",
      "departmentCode": "",
      "employeeCount": 0
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityUserStatisticsDTO](#schemaresponseentityuserstatisticsdto)|

# 数据模型

<h2 id="tocS_UserRoleStatisticsDTO">UserRoleStatisticsDTO</h2>

<a id="schemauserrolestatisticsdto"></a>
<a id="schema_UserRoleStatisticsDTO"></a>
<a id="tocSuserrolestatisticsdto"></a>
<a id="tocsuserrolestatisticsdto"></a>

```json
{
  "adminUsers": 0,
  "regularUsers": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|adminUsers|integer(int64)|false|none||管理员用户数量|
|regularUsers|integer(int64)|false|none||普通员工数量|

<h2 id="tocS_UserStatusStatisticsDTO">UserStatusStatisticsDTO</h2>

<a id="schemauserstatusstatisticsdto"></a>
<a id="schema_UserStatusStatisticsDTO"></a>
<a id="tocSuserstatusstatisticsdto"></a>
<a id="tocsuserstatusstatisticsdto"></a>

```json
{
  "activeUsers": 0,
  "inactiveUsers": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|activeUsers|integer(int64)|false|none||启用用户数量（ACTIVE状态）|
|inactiveUsers|integer(int64)|false|none||禁用用户数量（INACTIVE和FROZEN状态）|

<h2 id="tocS_DepartmentEmployeeStatisticsDTO">DepartmentEmployeeStatisticsDTO</h2>

<a id="schemadepartmentemployeestatisticsdto"></a>
<a id="schema_DepartmentEmployeeStatisticsDTO"></a>
<a id="tocSdepartmentemployeestatisticsdto"></a>
<a id="tocsdepartmentemployeestatisticsdto"></a>

```json
{
  "departmentId": 0,
  "departmentName": "string",
  "departmentCode": "string",
  "employeeCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|departmentId|integer(int64)|false|none||部门ID|
|departmentName|string|false|none||部门名称|
|departmentCode|string|false|none||部门编码|
|employeeCount|integer(int64)|false|none||员工数量|

<h2 id="tocS_ResponseEntityUserStatisticsDTO">ResponseEntityUserStatisticsDTO</h2>

<a id="schemaresponseentityuserstatisticsdto"></a>
<a id="schema_ResponseEntityUserStatisticsDTO"></a>
<a id="tocSresponseentityuserstatisticsdto"></a>
<a id="tocsresponseentityuserstatisticsdto"></a>

```json
{
  "totalUsers": 0,
  "roleStatistics": {
    "adminUsers": 0,
    "regularUsers": 0
  },
  "statusStatistics": {
    "activeUsers": 0,
    "inactiveUsers": 0
  },
  "departmentStatistics": [
    {
      "departmentId": 0,
      "departmentName": "string",
      "departmentCode": "string",
      "employeeCount": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalUsers|integer(int64)|false|none||总用户数|
|roleStatistics|[UserRoleStatisticsDTO](#schemauserrolestatisticsdto)|false|none||用户角色分布统计|
|statusStatistics|[UserStatusStatisticsDTO](#schemauserstatusstatisticsdto)|false|none||用户状态统计|
|departmentStatistics|[[DepartmentEmployeeStatisticsDTO](#schemadepartmentemployeestatisticsdto)]|false|none||部门员工分布统计|

