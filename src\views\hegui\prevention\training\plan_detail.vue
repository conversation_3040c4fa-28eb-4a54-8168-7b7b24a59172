<script setup lang="ts">
import * as Echarts from 'echarts'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import planApi from '@/api/complianceApi/prevention/plan'
import organizationalApi from '@/api/organizational/index'

// 路由实例
const router = useRouter()
const route = useRoute()

// 获取路由参数中的id
const planId = ref(route.query.id as string)

// 页面加载状态
const pageLoading = ref(false)

// 培训计划详情数据
const planDetail = ref({
  id: '',
  planName: '', // 计划名称
  planCode: '', // 计划编号
  planType: '', // 计划类型
  planStatus: '', // 计划状态
  startDate: '', // 开始日期
  endDate: '', // 结束日期
  trainingTarget: [], // 培训对象
  responsiblePerson: '', // 负责人
  trainingObjective: '', // 培训目标
  planDescription: '', // 计划描述
  implementationPlan: '', // 实施方案
  resourceRequirements: '', // 资源需求
  riskManagement: '', // 风险管理
  priority: '', // 优先级
  targetCompletionRate: 80, // 目标完成率
  trainingPlanCourses: [], // 培训计划课程列表
  createdBy: '', // 创建人
  createdAt: '', // 创建时间
  updatedBy: '', // 更新人
  updatedAt: '', // 更新时间
})

// 部门树数据（用于映射部门名称）
const departmentTree = ref([])

// 获取培训计划详情
async function getTrainingPlanDetail() {
  if (!planId.value) {
    ElMessage.error('缺少计划ID参数')
    return
  }

  try {
    pageLoading.value = true
    const response = await planApi.getTrainingPlanDetail(planId.value)

    if (response) {
      // 回填基本信息
      Object.assign(planDetail.value, response)

      // 处理日期
      if (response.startDate) {
        planDetail.value.startDate = response.startDate.seconds
          ? new Date(response.startDate.seconds * 1000)
          : new Date(response.startDate)
      }
      if (response.endDate) {
        planDetail.value.endDate = response.endDate.seconds
          ? new Date(response.endDate.seconds * 1000)
          : new Date(response.endDate)
      }

      // 处理培训对象（字符串转数组，并转换为数字类型）
      if (response.trainingTarget) {
        if (typeof response.trainingTarget === 'string') {
          // 将逗号分隔的字符串转换为数字数组
          planDetail.value.trainingTarget = response.trainingTarget
            .split(',')
            .map(id => Number.parseInt(id.trim(), 10))
            .filter(id => !isNaN(id))
        }
        else if (Array.isArray(response.trainingTarget)) {
          // 如果已经是数组，确保每个元素都是数字
          planDetail.value.trainingTarget = response.trainingTarget
            .map(id => typeof id === 'string' ? Number.parseInt(id, 10) : id)
            .filter(id => !isNaN(id))
        }
        else {
          planDetail.value.trainingTarget = []
        }
      }

      // 处理课程列表
      if (response.trainingPlanCourses && Array.isArray(response.trainingPlanCourses)) {
        planDetail.value.trainingPlanCourses = response.trainingPlanCourses.map(course => ({
          id: course.id,
          courseId: course.courseId,
          courseName: course.courseName,
          courseType: course.courseType || '未分类',
          courseDurationMinutes: course.courseDurationMinutes || 0,
          courseOrder: course.courseOrder || 1,
          isRequired: course.isRequired !== false,
          estimatedDays: course.estimatedDays || 0,
          startDate: course.startDate ? (course.startDate.seconds ? new Date(course.startDate.seconds * 1000) : new Date(course.startDate)) : null,
          endDate: course.endDate ? (course.endDate.seconds ? new Date(course.endDate.seconds * 1000) : new Date(course.endDate)) : null,
          assessmentRequirement: course.assessmentRequirement || '',
          metadata: course.metadata || '',
          version: course.version || 0,
        }))
      }

      // 处理创建和更新时间
      if (response.createdAt) {
        planDetail.value.createdAt = response.createdAt.seconds
          ? new Date(response.createdAt.seconds * 1000)
          : new Date(response.createdAt)
      }
      if (response.updatedAt) {
        planDetail.value.updatedAt = response.updatedAt.seconds
          ? new Date(response.updatedAt.seconds * 1000)
          : new Date(response.updatedAt)
      }
    }
  }
  catch (error) {
    console.error('获取培训计划详情失败:', error)
    ElMessage.error('获取培训计划详情失败')
  }
  finally {
    pageLoading.value = false
  }
}

// 获取部门树数据（用于映射部门名称）
async function fetchDepartmentTree() {
  try {
    const response = await organizationalApi.organizationalUnitTreeApi()
    departmentTree.value = response || []
  }
  catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 根据部门ID数组获取部门名称
const trainingTargetNames = computed(() => {
  if (!planDetail.value.trainingTarget || planDetail.value.trainingTarget.length === 0) {
    return '未设置'
  }

  const names = []

  function findDepartmentName(tree, targetIds) {
    for (const node of tree) {
      if (targetIds.includes(node.id)) {
        names.push(node.name)
      }
      if (node.children && node.children.length > 0) {
        findDepartmentName(node.children, targetIds)
      }
    }
  }

  findDepartmentName(departmentTree.value, planDetail.value.trainingTarget)
  return names.length > 0 ? names.join('、') : '未找到对应部门'
})

// 计划状态映射
const planStatusMap = {
  DRAFT: { text: '草稿', type: 'info' },
  PENDING: { text: '待启动', type: 'warning' },
  IN_PROGRESS: { text: '进行中', type: 'primary' },
  COMPLETED: { text: '已完成', type: 'success' },
  CANCELLED: { text: '已取消', type: 'danger' },
}

// 优先级映射
const priorityMap = {
  HIGH: { text: '高', type: 'danger' },
  MEDIUM: { text: '中', type: 'warning' },
  LOW: { text: '低', type: 'info' },
}

// 格式化日期
function formatDate(date) {
  if (!date) { return '-' }

  let dateObj
  if (date.seconds) {
    dateObj = new Date(date.seconds * 1000)
  }
  else {
    dateObj = new Date(date)
  }

  if (isNaN(dateObj.getTime())) { return '-' }

  return dateObj.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 格式化时长（分钟转小时）
function formatDuration(minutes) {
  if (!minutes || minutes === 0) { return '0分钟' }

  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60

  if (hours > 0 && mins > 0) {
    return `${hours}小时${mins}分钟`
  }
  else if (hours > 0) {
    return `${hours}小时`
  }
  else {
    return `${mins}分钟`
  }
}

// 编辑培训计划
function goEdit() {
  router.push({
    path: '/training/plan/edit',
    query: { id: planId.value },
  })
}

// 返回列表
function goBack() {
  router.back()
}

const activeName = ref(1)

// 图表相关
const chart11Ref = ref(null)
const chart22Ref = ref(null)
const chart33Ref = ref(null)

function initChart11() {
  const chart1 = Echarts.init(chart11Ref.value)
  const option = {
    title: {
      text: '各部门参与情况',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    xAxis: {
      type: 'category',
      data: ['技术部', '运营部', '市场部', '人事部', '财务部'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [120, 200, 150, 80, 70],
        type: 'bar',
        itemStyle: {
          color: '#1677FF',
        },
      },
    ],
  }
  chart1.setOption(option)
}

function initChart22() {
  const chart1 = Echarts.init(chart22Ref.value)
  const option = {
    title: {
      text: '培训进度分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      bottom: '5%',
    },
    series: [
      {
        name: '培训进度',
        type: 'pie',
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 5,
        },
        data: [
          { value: 1048, name: '已完成' },
          { value: 735, name: '进行中' },
          { value: 580, name: '未开始' },
        ],
      },
    ],
  }
  chart1.setOption(option)
}

function initChart33() {
  const chart1 = Echarts.init(chart33Ref.value)
  const option = {
    title: {
      text: '每日学习时长趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
      name: '小时',
    },
    series: [
      {
        data: [1.5, 2.2, 3.1, 2.8, 4.2, 1.8, 2.5],
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#1677FF',
        },
      },
    ],
  }
  chart1.setOption(option)
}

// 页面初始化
onMounted(async () => {
  // 获取部门树数据
  await fetchDepartmentTree()
  // 获取培训计划详情
  await getTrainingPlanDetail()
  // 初始化图表
  setTimeout(() => {
    initChart11()
    initChart22()
    initChart33()
  }, 100)
})
</script>

<template>
  <div v-loading="pageLoading" class="mb-10">
    <div>
      <div class="min-h-screen flex">
        <!-- 主内容区 -->
        <div class="flex">
          <!-- 内容区 -->
          <div class="flex-1 bg-gray-100 p-6">
            <page-header>
              <template #content>
                <div class="aic jcsb flex">
                  <div class="f-28">
                    <span class="mr-10 c-[#000]">{{ planDetail.planName || '培训计划详情' }}</span>
                    <el-tag
                      v-if="planDetail.planStatus"
                      :type="planStatusMap[planDetail.planStatus]?.type || 'info'"
                      class="ml-4"
                    >
                      {{ planStatusMap[planDetail.planStatus]?.text || planDetail.planStatus }}
                    </el-tag>
                  </div>
                  <div>
                    <div class="aic flex">
                      <div>
                        <el-button v-auth="'/training/plan_detail/edit'" type="primary" @click="goEdit">
                          <el-icon><Edit /></el-icon>
                          <span class="ml-4">编辑</span>
                        </el-button>
                      </div>
                      <div class="ml-14">
                        <el-button @click="goBack">
                          返回
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </page-header>

            <PageMain style="background-color: transparent;">
              <el-row>
                <el-col :span="16" class="pr-10">
                  <!-- 基本信息卡片 -->
                  <el-card shadow="hover">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        基本信息
                      </h2>
                    </template>
                    <div class="f-14">
                      <el-row>
                        <el-col :span="12">
                          <div class="mb-16">
                            <span class="c-[#666666]">计划名称：</span>
                            <span class="c-[#000]">{{ planDetail.planName || '-' }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">计划编号：</span>
                            <span class="c-[#000]">{{ planDetail.planCode || '-' }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">计划类型：</span>
                            <span class="c-[#000]">{{ planDetail.planType || '-' }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">培训对象：</span>
                            <span class="c-[#000]">{{ trainingTargetNames }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">负责人：</span>
                            <span class="c-[#000]">{{ planDetail.responsiblePersonName || '-' }}</span>
                          </div>
                        </el-col>
                        <el-col :span="12">
                          <div class="mb-16">
                            <span class="c-[#666666]">开始时间：</span>
                            <span class="c-[#000]">{{ formatDate(planDetail.startDate) }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">结束时间：</span>
                            <span class="c-[#000]">{{ formatDate(planDetail.endDate) }}</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">优先级：</span>
                            <el-tag
                              v-if="planDetail.priority"
                              :type="priorityMap[planDetail.priority]?.type || 'info'"
                              size="small"
                            >
                              {{ priorityMap[planDetail.priority]?.text || planDetail.priority }}
                            </el-tag>
                            <span v-else class="c-[#000]">-</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">目标完成率：</span>
                            <span class="c-[#000]">{{ planDetail.targetCompletionRate || 0 }}%</span>
                          </div>
                          <div class="mb-16">
                            <span class="c-[#666666]">包含课程数：</span>
                            <span class="c-[#000]">{{ planDetail.trainingPlanCourses?.length || 0 }}</span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>

                  <!-- 培训目标 -->
                  <el-card v-if="planDetail.trainingObjective" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        培训目标
                      </h2>
                    </template>
                    <div class="f-14 whitespace-pre-wrap c-[#000]">
                      {{ planDetail.trainingObjective }}
                    </div>
                  </el-card>

                  <!-- 计划描述 -->
                  <el-card v-if="planDetail.planDescription" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        计划描述
                      </h2>
                    </template>
                    <div class="f-14 whitespace-pre-wrap c-[#000]">
                      {{ planDetail.planDescription }}
                    </div>
                  </el-card>

                  <!-- 实施方案 -->
                  <el-card v-if="planDetail.implementationPlan" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        实施方案
                      </h2>
                    </template>
                    <div class="f-14 whitespace-pre-wrap c-[#000]">
                      {{ planDetail.implementationPlan }}
                    </div>
                  </el-card>

                  <!-- 资源需求 -->
                  <el-card v-if="planDetail.resourceRequirements" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        资源需求
                      </h2>
                    </template>
                    <div class="f-14 whitespace-pre-wrap c-[#000]">
                      {{ planDetail.resourceRequirements }}
                    </div>
                  </el-card>

                  <!-- 风险管理 -->
                  <el-card v-if="planDetail.riskManagement" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        风险管理
                      </h2>
                    </template>
                    <div class="f-14 whitespace-pre-wrap c-[#000]">
                      {{ planDetail.riskManagement }}
                    </div>
                  </el-card>

                  <!-- 统计图表 -->
                  <el-card v-if="false" shadow="hover" class="mt-20">
                    <template #header>
                      <h2 class="text-lg font-bold">
                        培训统计
                      </h2>
                    </template>
                    <el-row style="height: 160px;background-color: #f5f7fa;" class="mb-20">
                      <el-col :span="6" class="aic jcc flex">
                        <div class="fdc aic jcc flex">
                          <el-progress stroke-width="20" type="circle" :percentage="90" />
                        </div>
                      </el-col>
                      <el-col :span="6" class="aic jcc flex">
                        <div class="fdc aic jcc flex">
                          <div class="f-24">
                            120/150
                          </div>
                          <div class="f-14 c-[#666666]">
                            参与人数
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="6" class="aic jcc flex">
                        <div class="fdc aic jcc flex">
                          <div class="f-24">
                            3.5h
                          </div>
                          <div class="f-14 c-[#666666]">
                            平均学习时长
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="6" class="aic jcc flex">
                        <div class="fdc aic jcc flex">
                          <div class="f-24">
                            85分
                          </div>
                          <div class="f-14 c-[#666666]">
                            平均得分
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="8">
                        <div ref="chart11Ref" style="width: 100%;height: 240px;" />
                      </el-col>
                      <el-col :span="8">
                        <div ref="chart22Ref" style="width: 100%;height: 240px;" />
                      </el-col>
                      <el-col :span="8">
                        <div ref="chart33Ref" style="width: 100%;height: 240px;" />
                      </el-col>
                    </el-row>
                  </el-card>

                  <!-- 课程列表和其他标签页 -->
                  <el-card shadow="hover" class="mt-20">
                    <el-tabs v-model="activeName" class="demo-tabs">
                      <el-tab-pane label="课程列表" :name="1">
                        <div v-if="planDetail.trainingPlanCourses && planDetail.trainingPlanCourses.length > 0">
                          <el-table :data="planDetail.trainingPlanCourses" style="width: 100%">
                            <el-table-column prop="courseOrder" label="序号" width="80" />
                            <el-table-column prop="courseName" label="课程名称" min-width="200" />
                            <el-table-column prop="courseType" label="课程类型" width="120" />
                            <el-table-column label="时长" width="120">
                              <template #default="scope">
                                {{ formatDuration(scope.row.courseDurationMinutes) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="是否必修" width="100">
                              <template #default="scope">
                                <el-tag :type="scope.row.isRequired ? 'success' : 'info'" size="small">
                                  {{ scope.row.isRequired ? '必修' : '选修' }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column label="预计天数" width="100">
                              <template #default="scope">
                                {{ scope.row.estimatedDays || 0 }}天
                              </template>
                            </el-table-column>
                            <el-table-column label="开始时间" width="120">
                              <template #default="scope">
                                {{ formatDate(scope.row.startDate) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="结束时间" width="120">
                              <template #default="scope">
                                {{ formatDate(scope.row.endDate) }}
                              </template>
                            </el-table-column>
                            <el-table-column prop="assessmentRequirement" label="考核要求" min-width="150" />
                          </el-table>
                        </div>
                        <div v-else class="py-20 text-center text-gray-500">
                          暂无课程数据
                        </div>
                      </el-tab-pane>
                      <el-tab-pane v-if="false" label="学员情况" :name="2">
                        <div class="py-20 text-center text-gray-500">
                          学员情况功能开发中...
                        </div>
                      </el-tab-pane>
                      <el-tab-pane v-if="false" label="培训公告" :name="3">
                        <div class="py-20 text-center text-gray-500">
                          培训公告功能开发中...
                        </div>
                      </el-tab-pane>
                      <el-tab-pane v-if="false" label="培训报告" :name="4">
                        <div class="py-20 text-center text-gray-500">
                          培训报告功能开发中...
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </el-card>
                </el-col>

                <!-- 右侧信息栏 -->
                <el-col :span="8" class="pl-10">
                  <!-- 关键时间点 -->
                  <el-card shadow="hover">
                    <template #header>
                      <div class="f-16 fw-600">
                        关键时间点
                      </div>
                    </template>
                    <div class="mt-16" style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;">
                      <div class="f-16 aic flex fw-500">
                        <div style="width: 10px;height: 10px;background-color: #1677ff;border-radius: 50%;" />
                        <span class="ml-8 c-[#111827]">{{ formatDate(planDetail.startDate) }}</span>
                      </div>
                      <div class="f-16 aic mt-8 flex pl-16 fw-500">
                        <span class="c-[#666]">培训计划开始</span>
                      </div>
                    </div>
                    <div class="mt-16" style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;">
                      <div class="f-16 aic flex fw-500">
                        <div style="width: 10px;height: 10px;background-color: #67c23a;border-radius: 50%;" />
                        <span class="ml-8 c-[#111827]">{{ formatDate(planDetail.endDate) }}</span>
                      </div>
                      <div class="f-16 aic mt-8 flex pl-16 fw-500">
                        <span class="c-[#666]">培训计划结束</span>
                      </div>
                    </div>
                  </el-card>

                  <!-- 培训管理员 -->
                  <el-card shadow="hover" class="mt-20">
                    <template #header>
                      <div class="f-16 fw-600">
                        培训管理员
                      </div>
                    </template>
                    <div class="mt-16" style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;">
                      <div class="flex">
                        <div class="ml-8" style="width: 32px;height: 32px;">
                          <div style="width: 100%;height: 100%;border-radius: 50%;background-color: #1677ff;display: flex;align-items: center;justify-content: center;color: white;font-size: 14px;">
                            {{ planDetail.responsiblePerson ? planDetail.responsiblePerson.charAt(0) : '?' }}
                          </div>
                        </div>
                        <div class="ml-8">
                          <div class="f-14">
                            {{ planDetail.responsiblePerson || '未设置' }}
                          </div>
                          <div class="f-14 c-[#666666]">
                            负责人
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="mt-16" style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;">
                      <div class="flex">
                        <div class="ml-8" style="width: 32px;height: 32px;">
                          <div style="width: 100%;height: 100%;border-radius: 50%;background-color: #67c23a;display: flex;align-items: center;justify-content: center;color: white;font-size: 14px;">
                            {{ planDetail.createdBy ? planDetail.createdBy.charAt(0) : '?' }}
                          </div>
                        </div>
                        <div class="ml-8">
                          <div class="f-14">
                            {{ planDetail.createdBy || '未知' }}
                          </div>
                          <div class="f-14 c-[#666666]">
                            创建人
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>

                  <!-- 计划信息 -->
                  <el-card shadow="hover" class="mt-20">
                    <template #header>
                      <div class="f-16 fw-600">
                        计划信息
                      </div>
                    </template>
                    <div class="mt-16" style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;">
                      <div class="f-14 mb-8">
                        <span class="c-[#666666]">创建时间：</span>
                        <span class="c-[#000]">{{ formatDate(planDetail.createdAt) }}</span>
                      </div>
                      <div class="f-14 mb-8">
                        <span class="c-[#666666]">更新时间：</span>
                        <span class="c-[#000]">{{ formatDate(planDetail.updatedAt) }}</span>
                      </div>
                      <div class="f-14 mb-8">
                        <span class="c-[#666666]">更新人：</span>
                        <span class="c-[#000]">{{ planDetail.updatedBy || '-' }}</span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </PageMain>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
