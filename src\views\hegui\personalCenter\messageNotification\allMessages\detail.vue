<script lang="ts" setup>
import { ref } from 'vue'
import { ArrowDown, ArrowUp, Document } from '@element-plus/icons-vue'

const messageStatus = ref<'read' | 'unread'>('read')

function toggleReadStatus() {
  messageStatus.value = messageStatus.value === 'read' ? 'unread' : 'read'
}

function handleBack() {
  console.log('返回')
}

function handleDelete() {
  console.log('删除')
}

function handleReply() {
  console.log('回复')
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              消息详情
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex items-center space-x-4">
            <el-button class="!rounded-button whitespace-nowrap" plain @click="handleBack">
              返回
            </el-button>
            <el-button
              class="!rounded-button whitespace-nowrap" type="danger" plain
              @click="handleDelete"
            >
              删除
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <div class="mb-4 flex items-start justify-between">
                <div>
                  <h2 class="mb-2 text-lg font-bold">
                    关于系统升级维护的通知
                  </h2>
                  <div class="flex items-center text-sm text-gray-500 space-x-4">
                    <span>2023-06-15 14:30</span>
                    <span>系统通知</span>
                    <el-tag size="small" type="warning">
                      重要
                    </el-tag>
                  </div>
                </div>
                <el-tag size="small" :type="messageStatus === 'read' ? 'info' : 'success'">
                  {{ messageStatus === 'read' ? '已读' : '未读' }}
                </el-tag>
              </div>

              <div class="max-w-none prose">
                <p>尊敬的各位用户：</p>
                <p>为了提供更优质的服务，我们计划于 <strong>2023年6月20日 00:00 至 06:00</strong> 进行系统升级维护。</p>
                <p>升级期间系统将暂停服务，请您提前做好相关工作安排。如遇特殊情况，维护时间可能延长，我们将及时通知。</p>
                <p>升级内容包括：</p>
                <ul>
                  <li>系统性能优化</li>
                  <li>安全漏洞修复</li>
                  <li>新增数据分析功能</li>
                </ul>
                <p>感谢您的理解与支持！如有任何疑问，请联系技术支持团队。</p>
                <p>系统运维中心</p>
              </div>

              <!-- 附件区域 -->
              <div class="mt-8 border-t border-gray-100 pt-4">
                <h3 class="mb-3 text-sm text-gray-500 font-medium">
                  附件
                </h3>
                <div class="space-y-2">
                  <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                    <div class="flex items-center">
                      <el-icon class="mr-3 text-blue-500">
                        <Document />
                      </el-icon>
                      <div>
                        <p class="text-sm font-medium">
                          系统升级说明文档.pdf
                        </p>
                        <p class="text-xs text-gray-500">
                          PDF • 2.4 MB
                        </p>
                      </div>
                    </div>
                    <el-button
                      class="!rounded-button whitespace-nowrap" size="small" type="primary"
                      plain
                    >
                      下载
                    </el-button>
                  </div>
                  <div class="flex items-center justify-between rounded bg-gray-50 p-3">
                    <div class="flex items-center">
                      <el-icon class="mr-3 text-green-500">
                        <Document />
                      </el-icon>
                      <div>
                        <p class="text-sm font-medium">
                          升级时间安排表.xlsx
                        </p>
                        <p class="text-xs text-gray-500">
                          Excel • 1.2 MB
                        </p>
                      </div>
                    </div>
                    <el-button
                      class="!rounded-button whitespace-nowrap" size="small" type="primary"
                      plain
                    >
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关操作
                </div>
              </template>
              <div class="space-x-4">
                <el-button class="!rounded-button whitespace-nowrap" type="primary">
                  查看系统公告
                </el-button>
                <el-button class="!rounded-button whitespace-nowrap" plain>
                  联系技术支持
                </el-button>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关消息
                </div>
              </template>
              <div class="space-y-3">
                <div class="cursor-pointer rounded p-3 hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium">
                      系统维护完成通知
                    </p>
                    <el-tag size="small" type="info">
                      已读
                    </el-tag>
                  </div>
                  <p class="mt-1 text-xs text-gray-500">
                    2023-06-20 06:15
                  </p>
                </div>
                <div class="cursor-pointer rounded p-3 hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium">
                      系统升级预告
                    </p>
                    <el-tag size="small" type="success">
                      未读
                    </el-tag>
                  </div>
                  <p class="mt-1 text-xs text-gray-500">
                    2023-06-10 09:00
                  </p>
                </div>
                <div class="cursor-pointer rounded p-3 hover:bg-gray-50">
                  <div class="flex items-center justify-between">
                    <p class="text-sm font-medium">
                      系统使用情况调查
                    </p>
                    <el-tag size="small" type="info">
                      已读
                    </el-tag>
                  </div>
                  <p class="mt-1 text-xs text-gray-500">
                    2023-05-28 15:30
                  </p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  消息导航
                </div>
              </template>
              <div class="">
                <div>
                  <el-button plain style="width: 100%;margin-left: 0;">
                    <el-icon class="mr-2">
                      <ArrowUp />
                    </el-icon>上一条
                  </el-button>
                </div>
                <div class="mt-16">
                  <el-button plain style="width: 100%;margin-left: 0;">
                    <el-icon class="mr-2">
                      <ArrowDown />
                    </el-icon>下一条
                  </el-button>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button class="!rounded-button w-full whitespace-nowrap" plain @click="toggleReadStatus">
                    {{ messageStatus === 'read' ? '标为未读' : '标为已读' }}
                  </el-button>
                </div>
                <div>
                  <el-button
                    class="!rounded-button w-full whitespace-nowrap" type="danger" plain
                    @click="handleDelete"
                  >
                    删除
                  </el-button>
                </div>
                <div>
                  <el-button class="!rounded-button w-full whitespace-nowrap" plain @click="handleReply">
                    回复
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .prose {
    line-height: 1.75;
  }

  .prose p {
    margin-bottom: 1em;
  }

  .prose ul {
    padding-left: 1.5em;
    margin-bottom: 1em;
    list-style-type: disc;
  }

  .prose li {
    margin-bottom: 0.5em;
  }

  .prose strong {
    font-weight: 600;
  }
</style>
