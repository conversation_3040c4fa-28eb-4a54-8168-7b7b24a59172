<script setup lang="ts">
import { nextTick, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import systemApi from '@/api/complianceApi/one/systemManagement'

// 定义props
interface Props {
  searchParams?: Record<string, any>
  refreshTrigger?: number
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({}),
  refreshTrigger: 0,
})

// 定义emits
const emit = defineEmits<{
  'update:loading': [loading: boolean]
  'update:total': [total: number]
  'data-loaded': [data: any[]]
}>()

const router = useRouter()

const tabsList: any = ref([
  {
    id: 1,
    name: '全部',
  },
  {
    id: 2,
    name: '银行',
  },
  {
    id: 3,
    name: '保险',
  },
  {
    id: 4,
    name: '证券',
  },
])

// 表格数据
const dataList: any = ref([])

// 分页数据
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})

// 加载状态
const loading = ref(false)

// 获取列表数据
async function getList() {
  try {
    loading.value = true
    emit('update:loading', true)

    const params = {
      ...props.searchParams,
      page: paging.value.page,
      limit: paging.value.limit,
    }

    const response = await systemApi.complianceSystem(params)

    if (response && response.content) {
      dataList.value = response.content
      paging.value.total = response.totalElements || 0
      emit('update:total', paging.value.total)
      emit('data-loaded', response.content)
    }
  }
  catch (error) {
    console.error('获取合规义务列表失败:', error)
    ElMessage.error('获取合规义务列表失败')
  }
  finally {
    loading.value = false
    emit('update:loading', false)
  }
}

// 查看详情
function goDeatil(item: any) {
  router.push({
    path: '/database/duty/detail',
    query: {
      id: item.id,
    },
  })
}

// 编辑义务
function editObligation(item: any) {
  router.push({
    path: '/database/duty/addEdit',
    query: {
      id: item.id,
    },
  })
}

// 删除
async function deleteObligation(item: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除「${item.title}」吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    await systemApi.complianceSystem({ id: item.id }, 'delete')
    ElMessage.success('删除成功')
    getList() // 重新获取列表
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页改变
function pagChange(page: number) {
  paging.value.page = page
  getList()
}

// 监听搜索参数变化
let isSearchParamsChanging = false
watch(
  () => props.searchParams,
  () => {
    isSearchParamsChanging = true
    paging.value.page = 1
    getList()
    nextTick(() => {
      setTimeout(() => {
        isSearchParamsChanging = false
      }, 100)
    })
  },
  { deep: true, immediate: true },
)

// 监听刷新触发器
watch(
  () => props.refreshTrigger,
  (newVal, oldVal) => {
    if (!isSearchParamsChanging && newVal !== oldVal && oldVal !== undefined) {
      getList()
    }
  },
)

// 获取风险等级显示
function getRiskLevelInfo(level: string) {
  const levelMap = {
    GENERAL: { text: '一般风险', color: '#FDC929' },
    MAJOR: { text: '重大风险', color: '#E83335' },
    TYPICAL: { text: '典型风险', color: '#F57C00' },
    SAFE: { text: '安全', color: '#4CAF50' },
  }
  return levelMap[level] || { text: '未知', color: '#999' }
}

// 获取状态显示
function getStatusInfo(status: string) {
  const statusMap = {
    DRAFT: { text: '草稿', type: 'info' },
    EFFECTIVE: { text: '生效中', type: 'success' },
    EXPIRED: { text: '已失效', type: 'danger' },
    REVIEWING: { text: '待审核', type: 'warning' },
  }
  return statusMap[status] || { text: '未知', type: 'info' }
}
</script>

<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="aic jcsb flex">
          <div class="f-16 fw-700">
            合规义务列表
          </div>
          <div>
            <svg-icon name="ep:setting" />
          </div>
        </div>
        <!-- <div class="aic flex flex-wrap">
          <div v-for="i, j in tabsList" :class="j == 0 ? 'csstabgs_' : ''" class="csstabgs mr-8 mt-16">
            {{ i.name }}
          </div>
        </div> -->
      </template>
      <div>
        <div v-for="i, j in dataList" :key="j" class="items fdc jcc flex">
          <div class="aic jcsb flex">
            <div class="f-16 cursor-pointer fw-500" @click="goDeatil(i)">
              {{ i.title || '未命名义务' }}
            </div>
            <div class="aic flex">
              <div
                class="mr-8 c-[#fff]" style="width: fit-content;padding: 2px 8px;border-radius: 30px;" :style="{
                  'background-color': getRiskLevelInfo(i.level).color,
                }"
              >
                <el-link color="#fff">
                  {{ getRiskLevelInfo(i.level).text }}
                </el-link>
              </div>
              <el-dropdown trigger="click">
                <el-button type="text" size="small">
                  <svg-icon name="ep:more" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="goDeatil(i)">
                      <svg-icon name="ep:view" class="mr-4" />查看详情
                    </el-dropdown-item>
                    <el-dropdown-item @click="editObligation(i)">
                      <svg-icon name="ep:edit" class="mr-4" />编辑
                    </el-dropdown-item>
                    <el-dropdown-item divided @click="deleteObligation(i)">
                      <svg-icon name="ep:delete" class="mr-4" />删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div class="aic flex flex-wrap">
            <div class="f-14 mr-16 mt-12 c-[#6B7280]">
              义务类型：{{ i.obligationType || '未知' }}
            </div>
            <div class="f-14 mr-16 mt-12 c-[#6B7280]">
              责任部门：{{ i.department || '未指定' }}
            </div>
            <div class="f-14 mr-16 mt-12 c-[#6B7280]">
              生效日期：{{ i.effectiveDate || '未设置' }}
            </div>
            <div class="f-14 mr-16 mt-12">
              <el-tag :type="getStatusInfo(i.status).type" size="small">
                {{ getStatusInfo(i.status).text }}
              </el-tag>
            </div>
          </div>
        </div>
        <page-compon
          :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
          @pag-change="pagChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .csstabgs {
    width: fit-content;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 400;
    background: #f5f7fa;
    border-radius: 30px;
  }

  .csstabgs_ {
    color: #f5f7fa;
    background: #1e88e5;
  }

  .items {
    width: 100%;
    height: 86px;
    border-bottom: 1px solid #e0e0e0;
  }
</style>
