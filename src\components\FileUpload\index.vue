<script setup lang="ts">
import { ref } from 'vue'
import axios from 'axios'

import type { UploadProps, UploadUserFile } from 'element-plus'
import { ElMessage } from 'element-plus'

import type { FileData } from 'qiniu-js'
import useUserStore from '@/store/modules/user'

// const { pictureEcho, pictureType } = getCurrentInstance()!.appContext.config.globalProperties.$tools() // 引入自定义挂载公共方法
defineOptions({
  name: 'FileUpload',
})
const props = withDefaults(
  defineProps<{
    action: UploadProps['action']
    headers?: any
    data?: UploadProps['data']
    name?: UploadProps['name']
    size?: number
    max?: number
    files?: UploadUserFile[]
    notip?: boolean
    ext?: string[]
    dirname?: string
    code?: string
    typename?: string
    fileurl?: string
    filetype?: string
    urlboxwidth?: string
    serviceName?: string
  }>(),
  {
    name: 'file',
    size: 1000,
    max: 1,
    // action: `${import.meta.env.VITE_APP_API_BASEURL}Upload/QiniuUpload`,
    // action: `${import.meta.env.VITE_APP_API_BASEURL}/Upload/QiniuUpload`,
    action: `${import.meta.env.VITE_APP_API_BASEURL}whiskerguardgeneralservice/api/file/upload?serviceName=whiskerguardregulatoryservice&categoryName=duty`,
    headers: {
      Authorization: `Bearer ${useUserStore().token}`,
    },
    files: () => [],
    notip: false,
    ext: () => ['mp4', 'MP4'],
    dirname: 'file', // 上传类型
    code: '', // 上传目录code
    typename: '文件上传',
    fileurl: '',
    filetype: '',
    urlboxwidth: '100%',
  },
)
const emits = defineEmits<{
  onSuccess: [
			res: any,
  ]
  onProgress: [
			res: any,
			file: UploadUserFile,
			fileList: UploadUserFile[],
  ]
}>()
const loadicon = new URL('@/assets/images/loadicon.gif', import.meta.url).href
const fullscreenLoading = ref(false)
// const emits = defineEmits<{
//   'onSuccess': [
//     res: any,
//     file: UploadUserFile,
//     fileList: UploadUserFile[],
//   ]
//   'onProgress': [
//     res: any,
//     file: UploadUserFile,
//     fileList: UploadUserFile[],
//   ]
// }>()

const upload: any = ref('')
const files: any = ref([])
const filelist: any = ref([])
const vodeofile: any = ref('')
const filetype: any = ref('')
const dialogVisible: any = ref(false)

const fileData: FileData = { type: 'string', data: 'content' }
const percent: any = ref(false)
const percentnum: any = ref(0)
const form: any = ref({})

const baseurl: any = ref(`${import.meta.env.VITE_APP_API_BASEURLIMG}`)

const beforeUpload: UploadProps['beforeUpload'] = (file: any) => {
  const fileName = file.name.split('.')
  const fileExt = fileName.at(-1) ?? ''
  const isTypeOk = props.ext.includes(fileExt)
  const isSizeOk = file.size / 1024 / 1024 < props.size
  if (!isTypeOk) {
    ElMessage.error(`上传文件只支持 ${props.ext.join(' / ')} 格式！`)
  }
  if (!isSizeOk) {
    ElMessage.error(`上传文件大小不能超过 ${props.size}MB！`)
  }
  return isTypeOk && isSizeOk
}

const onExceed: UploadProps['onExceed'] = (files: any, fileList: any) => {
  console.log(files, fileList, 'files: any, fileList')
  ElMessage.warning('文件上传超过限制')
}
watch(() => props.fileurl, (val: any) => {
  console.log(val, props.filetype, props.fileurl, val, 'valvalval')
  filelist.value = []
  files.value = []
  if (props.filetype && props.fileurl && val && val.length > 0) {
    filelist.value.push({ ext: props.filetype, url: val[0] })
    files.value.push({ ext: props.filetype, url: val[0] })
  }
  else {
    filelist.value = []
    files.value = []
  }
}, {
  immediate: true,
  deep: true,
})

// 自定义上传
async function httpRequest(file: any) {
  // if (pictureType(props.dirname) === false) {
  // 	ElMessage.error('未配置图片上传参数项')
  // 	return false
  // }
  console.log(file, 'filefilefilefile')
  // if (Math.floor(file.file.size / 1000000) > 4) {
  // 	console.log('大于30')
  // 	const token : any = await qiniu_token(file)
  // 	qiniu_uploadFile(file, token)
  // }
  // return
  const fd = new FormData()
  // fd.append('dirname', !props.code ? pictureType(props.dirname) : `${pictureType(props.dirname)}/${props.code}`)// 传参数

  fd.append('file', file.file)// 传文件
  // const loadingInstance = ElLoading.service({
  // 	lock: true,
  // 	text: 'Loading',
  // 	background: 'rgba(0, 0, 0, 0.7)',
  // })
  fullscreenLoading.value = true
  const res = await axios({
    method: 'POST',
    url: props.action,
    data: fd,
    headers: props.headers,
  })
  if (res) {
    fullscreenLoading.value = false
    console.log(res.data, 'files')
    // if (res.data.data.url === '/') {
    // 	ElMessage.error(`上传资源名称已被占用，请修改名称后重新上传`)
    // } else {
    // 	filelist.value.push(res.data.data)
    // 	emits('onSuccess', filelist.value)
    // }
    // filelist.value.push({ ext: 'mp4', url: res.data.data.url })
    emits('onSuccess', [res.data.key])
  }
  else {
    fullscreenLoading.value = false
  }
}

function remive(j: any) {
  console.log(filelist.value, j, 'files')
  if (props.max === 1) {
    filelist.value.splice(j, 1)
    upload.value.clearFiles()
  }
  else {
    filelist.value.splice(j, 1)
  }
  console.log(filelist.value, j, 'files111')
  emits('onSuccess', filelist.value)
}
function onPreview(e: any) {
  vodeofile.value = baseurl.value + e
  console.log(vodeofile.value, 'vodeofile.value')
  dialogVisible.value = true
}
function onPreviewFile(e: any, type: any) {
  console.log(e, 'kljklkl')
  dialogVisible.value = false
  vodeofile.value = ''
  filetype.value = type
  setTimeout(() => {
    if (type === 'mp3' || type === 'mp4' || type === 'png' || type === 'jpg') {
      vodeofile.value = baseurl.value + e
      dialogVisible.value = true
    }
    else {
      vodeofile.value = baseurl.value + e
      window.open(vodeofile.value)
    }
  }, 30)

  console.log(vodeofile.value, 'vodeofile.value')
}
function beforeRemove(file: any, fileList: any) {
  console.log(file, fileList, 'fileList')
}
const onSuccess: UploadProps['onSuccess'] = (res: any, file: any, fileList: any) => {
  // emits('onSuccess', res, file, fileList)
  // emits('onSuccess', res)
}
const onProgress: UploadProps['onProgress'] = (res: any, file: any, fileList: any) => {
  console.log('；乐扣乐扣')
  emits('onProgress', res, file, fileList)
}

function trimmedPath(path: any) {
  console.log(path, 'pathpathpath')
  if (path) {
    // 从路径末尾开始查找最后一个斜杠的位置
    const lastSlashIndex = path.lastIndexOf('/')
    // 如果末尾有斜杠，则从最后一个斜杠后面的位置开始截取
    // 否则，直接截取从字符串开始到末尾的部分
    return lastSlashIndex !== -1 ? path.substring(lastSlashIndex + 1) : path
  }
}
function closeFiles() {
  console.log('jkjklhk')
  vodeofile.value = ''
}
function closeLoad() {
  fullscreenLoading.value = false
}
function closepercent() {
  percent.value = false
  percentnum.value = 0
}
</script>

<template>
  <el-upload
    ref="upload" :headers="headers" :action="action" :data="data" :name="name" :before-upload="beforeUpload"
    :on-exceed="onExceed" :on-success="onSuccess" :on-progress="onProgress" :http-request="httpRequest"
    :file-list="files" :show-file-list="false" :limit="max" :on-preview="onPreview"
  >
    <div class="slot">
      <el-button type="primary">
        <svg-icon name="ep:upload-filled" class="el-icon--upload" />{{ typename }}
      </el-button>

      <!-- <div class="el-upload__text">
        将文件拖到此处，或<em>点击上传</em>
      </div> -->
    </div>

    <template #tip>
      <div v-if="!notip" class="el-upload__tip mb-2">
        <div style="display: inline-block;">
          <el-alert
            :title="`上传文件支持 ${ext.join(' / ')} 格式，单个文件大小不超过 ${size}MB，且文件数量不超过 ${max} 个`" type="info"
            show-icon :closable="false"
          />
        </div>
      </div>
    </template>
  </el-upload>
  <div v-if="filelist && filelist.length > 0">
    <div
      v-for="(item, index) in filelist" :key="index"
      style=" display: flex; justify-content: start;padding: 3px; line-height: 24px; background-color: #dce9f5;"
    >
      <div class="jcc aic flex">
        <svg-icon name="ep:edit" />
      </div>
      <div
        :style="{ width: props.urlboxwidth }" class="overflow-text" style="margin: 0 16px 0 6px;cursor: pointer;"
        @click="onPreviewFile(item.url, item.ext)"
      >
        <!-- {{ trimmedPath(item.url) }} -->
        {{ trimmedPath(item.url) }}
      </div>
      <div class="jcc aic flex" style="cursor: pointer;" @click="remive(index)">
        <svg-icon name="ep:close" />
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible" :z-index="9999"
    :title="filetype === 'mp3' ? '音频' : filetype === 'mp4' ? '视频' : (filetype === 'png' || filetype === 'jpg') ? '图片' : ''"
    width="600px" @close="closeFiles"
  >
    <div v-if="vodeofile && filetype === 'mp4'" style="width: 600px;height: 400px;">
      <video style="width: 93%;height: 100%;" autoplay controls :src="vodeofile" />
    </div>
    <div v-if="vodeofile && (filetype === 'png' || filetype === 'jpg')" style="width: 600px;height: 400px;">
      <img :src="vodeofile" style="width: 96%;height: 88%;">
    </div>
    <div
      v-if="vodeofile && filetype === 'mp3'" class="flex items-center justify-center"
      style="width: 600px;height: 200px;"
    >
      <audio ref="audioPlayer" controls>
        <source :src="vodeofile" type="audio/mpeg">
        您的浏览器不支持 audio 元素。
      </audio>
    </div>
  </el-dialog>
  <!-- -->
  <div
    v-if="percent" class="demo-progress fixed left-50% top-50% w-130 bg-[#fff]"
    style=" z-index: 99999; border-radius: 8px; box-shadow: 0 0 8px 0 #999;transform: translate(-50%, -50%);"
  >
    <!-- <div class="w-full text-end pr-4 pt-4 font-size-6 cursor-pointer" @click="closepercent">
			<svg-icon name="ep:close" />
		</div> -->
    <div class="d-c h-16 h-40 w-full flex items-center justify-center py-2" style="">
      <el-progress
        :width="500" striped striped-flow :text-inside="true" :stroke-width="24"
        :percentage="percentnum" status="success"
      />
      <div>
        <div v-if="percentnum > 0" class="my-4 c-[#67c23a]">
          上传中...
        </div>
        <div v-else class="my-4 c-[#e67c28]">
          正在解析文件流...
        </div>
      </div>
    </div>
  </div>
  <!-- 上传时的加载遮罩层 -->
  <div v-if="fullscreenLoading" class="loading-mask">
    <div class="d-c flex items-center justify-center">
      <div class="c-[#409eff]">
        <img class="h-5 w-5" :src="loadicon" alt>
      </div>
      <div class="my-4 c-[#409eff]">
        上传中...
      </div>
      <div
        style="position: fixed; top: 60px;right: 60px; z-index: 1000000; background-color: rgb(255 255 255 / 80%);  border-radius: 50%;"
        class="h-16 w-16 flex cursor-pointer items-center justify-center font-size-10 c-[#333]"
        @click="closeLoad"
      >
        <svg-icon name="ep:close" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .loading-mask {
    position: fixed;
    inset: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(0 0 0 / 70%);

    /* 确保在el-loading之上 */
  }

  .el-loading {
    position: relative;
  }

  .el-loading .el-button {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .demo-progress .el-progress--line {
    width: 450px;
    height: 30px;
    margin-bottom: 15px;
  }

  .overflow-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep(.el-upload.is-drag) {
    display: inline-block;

    .el-upload-dragger {
      padding: 0;
    }

    &.is-dragover {
      border-width: 1px;
    }

    .slot {
      width: 300px;
      padding: 40px 0;
    }
  }
</style>
