# 构建阶段
FROM node:20-alpine AS builder

WORKDIR /app

# 设置 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=8192"

# 复制依赖文件并安装依赖
COPY package.json pnpm-lock.yaml* ./
RUN npm install -g pnpm
RUN pnpm install

# 复制源代码并构建
COPY . .
RUN pnpm build

# 生产阶段
FROM node:20-alpine

WORKDIR /app

# 安装 http-server
RUN npm install -g http-server

# 复制构建产物
COPY --from=builder /app/dist /app/dist

EXPOSE 8780

# 启动 http-server
CMD ["http-server", "dist", "-p", "8780"]