import api from '@/api/index'

export default {
  // 分页查询所有持续改进报告列表
  searchReports(params: any) {
    return api.post(`/whiskerguardviolationservice/api/continuous/improvement/reports/search?page=${params.page}&size=${params.size}`, params)
  },

  // 创建新的持续改进报告
  createReport(data: any) {
    return api.post('/whiskerguardviolationservice/api/continuous/improvement/reports', data)
  },
  // 删除持续改进报告
  deleteReport(id: number) {
    return api.delete(`/whiskerguardviolationservice/api/continuous/improvement/reports/${id}`)
  },
  // 更新持续改进报告
  updateReport(data: any) {
    return api.patch(`/whiskerguardviolationservice/api/continuous/improvement/reports/${data.id}`, data)
  },
  // 获取持续改进报告详情
  getReportDetail(id: number) {
    return api.get(`/whiskerguardviolationservice/api/continuous/improvement/reports/${id}`)
  },
}
