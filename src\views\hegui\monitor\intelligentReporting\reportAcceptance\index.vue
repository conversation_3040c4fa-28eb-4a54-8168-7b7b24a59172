<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import {
  Bottom as ElIconBottom,
  Refresh as ElIconRefresh,
  Search as ElIconSearch,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import intelligentReportingApi from '@/api/report/intelligentReporting'

const router = useRouter()
const multipleSelection = ref([])
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 分页参数
const pagination = ref({
  page: 1,
  limit: 10,
})

// 搜索参数
const searchParams = ref({
  tenantId: 1, // 默认租户ID
  violationCode: '',
  violationType: '',
  title: '',
  createdBy: '',
  status: '',
  createdStart: '',
  createdEnd: '',
})

// 违规类型映射
const violationTypeMap = {
  FINANCIAL_VIOLATION: '财务违规',
  BUSINESS_ETHIC: '商业道德',
  INFORMATION_SECURITY: '信息安全',
  HUMAN_RESOURCE: '人事违规',
}

// 状态映射
const statusMap = {
  PENDING: '待处理',
  REPLIED: '已回复',
  CLOSED: '已关闭',
}

// 获取列表数据
async function getTableData() {
  loading.value = true
  try {
    const response = await intelligentReportingApi.violation(
      pagination.value,
      // searchParams.value,
      {},
      'list',
    )

    if (response && response.content) {
      tableData.value = response.content
      total.value = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 处理选择变化
function handleSelectionChange(val) {
  multipleSelection.value = val
}

// 处理排序变化
function handleSortChange({ column, prop, order }) {
  console.log('Sort changed:', prop, order)
  // 这里可以根据需要实现排序逻辑
  getTableData()
}

// 处理分页变化
function handleCurrentChange(page: number) {
  pagination.value.page = page
  getTableData()
}

function handleSizeChange(size: number) {
  pagination.value.limit = size
  pagination.value.page = 1
  getTableData()
}

// 搜索
function handleSearch() {
  pagination.value.page = 1
  getTableData()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    tenantId: 1,
    violationCode: '',
    violationType: '',
    title: '',
    createdBy: '',
    status: '',
    createdStart: '',
    createdEnd: '',
  }
  pagination.value.page = 1
  getTableData()
}

// 格式化时间
function formatTime(instant: any) {
  if (!instant || !instant.seconds) { return '-' }
  const date = new Date(instant.seconds * 1000)
  return date.toLocaleString('zh-CN')
}

// 获取违规类型显示文本
function getViolationTypeText(type: string) {
  return violationTypeMap[type] || type
}

// 获取状态显示文本
function getStatusText(status: string) {
  return statusMap[status] || status
}

// 获取状态标签类型
function getStatusType(status: string) {
  switch (status) {
    case 'PENDING':
      return 'warning'
    case 'REPLIED':
      return 'success'
    case 'CLOSED':
      return 'info'
    default:
      return 'info'
  }
}

// 查看详情
function handleView(row: any) {
  console.log('查看详情:', row)
  // 跳转到详情页面，传递ID参数
  router.push({
    path: '/monitor/intelligentReporting/reportAcceptance/detail',
    query: {
      id: row.id || row.violationCode,
    },
  })
}

// 处理举报
function handleEdit(row: any) {
  console.log('处理举报:', row)
  // 这里可以跳转到处理页面或打开处理弹窗
}

// 组件挂载时获取数据
onMounted(() => {
  getTableData()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              举报受理工作台
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['reportAcceptance/index/entrance']" type="primary" @click="handleView">
              举报入口配置
            </el-button>
            <el-button v-auth="['reportAcceptance/index/policyManagement']" type="primary" plain>
              举报政策管理
            </el-button>
            <el-button v-auth="['reportAcceptance/index/dataExport']" type="primary" plain>
              数据导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <!-- <el-row :gutter="20" class="">
          <el-col :span="4">
            <el-card shadow="hover" class="cursor-pointer" style="min-height: 152px;">
              <div class="flex flex-col">
                <span class="text-3xl text-orange-600 font-bold">7</span>
                <span class="mt-1 text-gray-600">今日新增</span>
                <div class="mt-2 flex items-center">
                  <el-icon class="text-red-500">
                    <ElIconBottom />
                  </el-icon>
                  <span class="ml-1 text-xs text-gray-500">较昨日 -3</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row> -->
        <!-- 搜索表单 -->
        <el-row :gutter="20" class="mt-6">
          <el-col :span="24">
            <el-card shadow="never" class="search-card">
              <el-form :model="searchParams" inline class="search-form">
                <el-form-item label="举报编号">
                  <el-input
                    v-model="searchParams.violationCode"
                    placeholder="请输入举报编号"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="举报类型">
                  <el-select
                    v-model="searchParams.violationType"
                    placeholder="请选择举报类型"
                    clearable
                    style="width: 200px"
                  >
                    <el-option label="财务违规" value="FINANCIAL_VIOLATION" />
                    <el-option label="商业道德" value="BUSINESS_ETHIC" />
                    <el-option label="信息安全" value="INFORMATION_SECURITY" />
                    <el-option label="人事违规" value="HUMAN_RESOURCE" />
                  </el-select>
                </el-form-item>
                <el-form-item label="举报标题">
                  <el-input
                    v-model="searchParams.title"
                    placeholder="请输入举报标题"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="状态">
                  <el-select
                    v-model="searchParams.status"
                    placeholder="请选择状态"
                    clearable
                    style="width: 150px"
                  >
                    <el-option label="待处理" value="PENDING" />
                    <el-option label="已回复" value="REPLIED" />
                    <el-option label="已关闭" value="CLOSED" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">
                    <el-icon class="mr-1">
                      <ElIconSearch />
                    </el-icon>
                    搜索
                  </el-button>
                  <el-button @click="handleReset">
                    <el-icon class="mr-1">
                      <ElIconRefresh />
                    </el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
        </el-row>

        <!-- 数据表格 -->
        <el-row :gutter="20" class="mt-4">
          <el-col :span="24">
            <el-card shadow="never">
              <el-table
                v-loading="loading"
                :data="tableData"
                class="cssTable"

                stripe border
                @selection-change="handleSelectionChange"
                @sort-change="handleSortChange"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column
                  prop="violationCode"
                  label="举报编号"
                  width="150"
                  sortable
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="title"
                  label="举报标题"
                  min-width="200"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="violationType"
                  label="举报类型"
                  width="120"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag type="info" size="small">
                      {{ getViolationTypeText(row.violationType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="状态"
                  width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="isAnonymous"
                  label="匿名举报"
                  width="100"
                  align="center"
                >
                  <template #default="{ row }">
                    <el-tag :type="row.isAnonymous ? 'warning' : 'success'" size="small">
                      {{ row.isAnonymous ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="createdBy"
                  label="举报人"
                  width="120"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ row.isAnonymous ? '匿名用户' : row.createdBy }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="createdAt"
                  label="创建时间"
                  width="180"
                  sortable
                />
                <el-table-column
                  prop="updatedAt"
                  label="更新时间"
                  width="180"
                  sortable
                />
                <el-table-column label="操作" width="150" align="center" fixed="right">
                  <template #default="{ row }">
                    <el-button v-auth="['reportAcceptance/index/view']" type="primary" size="small" @click="handleView(row)">
                      查看
                    </el-button>
                    <el-button v-auth="['reportAcceptance/index/dispose']" type="warning" size="small" @click="handleEdit(row)">
                      处理
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页组件 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="pagination.page"
                  v-model:page-size="pagination.limit"
                  :total="total"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-card {
    border-radius: 8px;
  }

  .el-card__body {
    padding: 16px;
  }

  /* 搜索表单样式 */
  .search-card {
    margin-bottom: 16px;
  }

  .search-form {
    margin-bottom: 0;
  }

  .search-form .el-form-item {
    margin-bottom: 16px;
  }

  /* 分页样式 */
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 16px 0;
  }

  /* Table styles */
  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
    --el-table-header-bg-color: #f8fafc;
    --el-table-row-hover-bg-color: #f5f7fa;
  }

  :deep(.el-table th.el-table__cell) {
    font-weight: 600;
    color: #4b5563;
    background-color: #f8fafc !important;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .el-table__cell .cell) {
    padding: 0 12px;
    line-height: 1.5;
  }

  :deep(.el-table--border .el-table__cell) {
    border-right: 1px solid #f0f0f0;
  }

  :deep(.el-table--border) {
    border: 1px solid #f0f0f0;
    border-bottom: none;
  }

  :deep(.el-table--border::after) {
    background-color: #f0f0f0;
  }

  /* 标签样式优化 */
  :deep(.el-tag) {
    border-radius: 4px;
  }

  /* 按钮样式优化 */
  :deep(.el-button--small) {
    padding: 4px 8px;
    font-size: 12px;
  }
</style>
