<script setup lang="ts">
import {
  onMounted, ref,
} from 'vue'
import type {
  FormInstance,
  FormRules,
  TabsPaneContext,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import Api from '@/api/modules/system/user'
import useUserStore from '@/store/modules/user'
// import shopApi from '@/api/modules/shop/shop'

const props = defineProps<{
  modifyID: any[]
}>()
const emits = defineEmits(['submit'])
const baseurl: any = ref(`${import.meta.env.VITE_APP_API_BASEURLIMG}`)
const id: any = ref(props.modifyID)

// 使用ref定义dom对象

const form: any = ref({
  // city: [],
  status: '1',
  sort: 1,
  shopid: '',
})
const init: any = ref({})

const data: any = ref({
  fileList: [],
  options: [],
})

function getList() {
  Api.role().then((res: any) => {
    init.value.role = res.data
    // console.log(res, 'role列表')
  })
  Api.level().then((res: any) => {
    init.value.level = res.data
    // console.log(res, '岗位')
  })
  // 岗位
  Api.position().then((res: any) => {
    init.value.position = res.data
    // console.log(res, 'role列表')
  })
  // 部门
  Api.dept().then((res: any) => {
    init.value.dept = handleTree(res.data, 'id', 'pid', 'children')
  })
}

const props1 = {
  id: null,
  checkStrictly: true,
  label: 'name',
  value: 'id',
}
const paging01: any = ref({
  page: 1,
  limit: 50,
  name: '',
})
onMounted(() => {
  getList() // 请求数据
  if (id.value) {
    Api.info({ id: props.modifyID }).then((res: any) => {
      // res.data.city.forEach((item : any) => {
      // 	if (!item) {
      // 		res.data.city = []
      // 	}
      // })
      console.log(res, form.value, 'form.value')
      form.value = res
      form.value.status = `${res.status}`
      if (form.value.id) {
        form.value.role_ids = form.value.roles.map((d: any) => d.id)
      }
      if (form.value.shop_id.id) {
        form.value.shopid = form.value.shop_id.id
      }
    })
  }
  // shopApi.list({ ...paging01.value }).then((res: any) => {
  //   data.value.options = res.data
  // })
})
// 定义提交按钮函数
const formRef = ref<FormInstance>()
const ruleForms = ref(null)
const formRules = ref<FormRules>({
  realname: [{
    required: true,
    message: '请输入路由地址',
    trigger: 'blur',
  }],
  avatar: [{
    required: true,
    message: '请上传头像',
    trigger: 'blur',
  }],
  nickname: [{
    required: true,
    message: '请输入昵称',
    trigger: 'blur',
  }],
  birthday: [{
    required: true,
    message: '请输入出生日期',
    trigger: 'blur',
  }],
  email: [{
    required: true,
    message: '请输入电子邮箱',
    trigger: 'blur',
  }],
  mobile: [{
    required: true,
    message: '请输入手机号',
    trigger: 'blur',
  }],
  level_id: [{
    required: true,
    message: '请输入职级',
    trigger: 'blur',
  }],
  position_id: [{
    required: true,
    message: '请输入岗位',
    trigger: 'blur',
  }],

  dept_id: [{
    required: true,
    message: '请输入部门',
    trigger: 'blur',
  }],
  city: [{
    required: true,
    message: '请选择城市',
    trigger: 'blur',
  }],
  status: [{
    required: true,
    message: '请选择状态',
    trigger: 'blur',
  }],
  address: [{
    required: true,
    message: '请输入详细地址',
    trigger: 'blur',
  }],
  // sort
  role_ids: [{
    required: true,
    message: '请选择角色',
    trigger: 'blur',
  }],
  username: [{
    required: true,
    message: '请输入账号',
    trigger: 'blur',
  }],
  shopid: [{
    required: true,
    message: '请选择门店',
    trigger: 'blur',
  }],
  password: [{
    required: true,
    message: '请输入密码',
    trigger: 'blur',
  }],
  intro: [{
    required: true,
    message: '请输入简介',
    trigger: 'blur',
  }],
})
async function submitButton() {
  console.log(form.value, '6666')
  formRef.value && formRef.value.validate((valid: any) => {
    if (valid) {
      // if (form.value.avatar.includes(baseurl.value)) {
      //   form.value.avatar = form.value.avatar.replace(baseurl.value, '')
      // }
      // form.value.shop_id = form.value.shopid
      if (!form.value.id) {
        form.value.password = 123456
      }
      Api.edit(form.value).then((res: any) => {
        ElMessage({ message: res.msg, type: 'success' })
      })
      emits('submit', true)
    }
  })
}
function cancelForm() {
  emits('submit', false)
}
// 请求门店列表
function remoteMethod(e: any) {
  if (e !== false) {
    // shopApi.list({ ...paging01.value, name: e }).then((res: any) => {
    //   console.log(res.data, '请求列表')
    //   data.value.options = res.data
    // })
  }
}
/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
function handleTree(data: any, id: string, parentId: string, children: any) {
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children',
  }

  const childrenListMap: any = {}
  const nodeIds: any = {}
  const tree: any = []

  for (const d of data) {
    const parentId = d[config.parentId]
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = []
    }
    nodeIds[d[config.id]] = d
    childrenListMap[parentId].push(d)
  }

  for (const d of data) {
    const parentId = d[config.parentId]
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (const t of tree) {
    adaptToChildrenList(t)
  }

  function adaptToChildrenList(o: any) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    if (o[config.childrenList]) {
      for (const c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}

const action: any = ref(`${import.meta.env.VITE_APP_API_BASEURL}Upload/uploadImage/`)
const headers: any = ref({
  Authorization: `Bearer ${useUserStore().token}`,
})

const activeName = ref('first')
// 单图上传回调事件
function handleSuccess(row: any) {
  console.log(row, 'rrowrowrowowrrowrowrowow')
  form.value.avatar = row
}
function handleClick(tab: TabsPaneContext, event: Event) {
  console.log(tab, event)
}

defineExpose({ submitButton, cancelForm })
</script>

<template>
  <el-form ref="formRef" :rules="formRules" :model="form" label-width="90px">
    <!-- <el-tabs v-model="activeName" class="demo-tabs pt-4" @tab-click="handleClick">
			<el-tab-pane label="基础信息" name="first"> -->
    <!-- <el-form-item label="头像：" class="avatar">
					<ImageUpload v-model:url="form.avatar" :width="250" :height="150"
						@on-success="handleSuccess($event)" />

				</el-form-item> -->
    <el-row :gutter="15">
      <el-col :sm="12">
        <el-form-item label="用户账号:" prop="username">
          <el-input v-model="form.username" />
        </el-form-item>
      </el-col>
      <!-- <el-col :sm="12">
            <el-form-item label="登录密码:" prop="password">
              <el-input v-model="form.password" />
            </el-form-item>
          </el-col> -->
      <el-col :sm="12">
        <el-form-item label="用户姓名:">
          <el-input v-model="form.realname" />
        </el-form-item>
      </el-col>
      <el-col :sm="12">
        <el-form-item label="手机号:">
          <el-input v-model="form.mobile" />
        </el-form-item>
      </el-col>
      <el-col :sm="12">
        <el-form-item label="性别:">
          <el-select v-model="form.gender" placeholder="请选择">
            <el-option label="男" :value="1" />
            <el-option label="女" :value="2" />
            <el-option label="保密" :value="3" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :sm="12">
        <el-form-item label="出生日期:">
          <el-date-picker v-model="form.birthday" type="date" placeholder="选择日期" style="width: 100%;" />
        </el-form-item>
      </el-col>
      <!-- <el-col :sm="12">
            <el-form-item label="所属门店:" prop="shopid">
              <el-select
                v-model="form.shopid"
                style="width: 100%;"
                filterable clearable :remote-method="remoteMethod" remote
                placeholder="请选择所属门店" @change="getList"
              >
                <el-option
                  v-for="item in data.options"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
      <el-col :sm="12">
        <el-form-item label="状态:" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">
              在用
            </el-radio>
            <el-radio label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>

      <el-col :sm="12">
        <el-form-item label="角色:" prop="role_ids">
          <el-select v-model="form.role_ids" multiple class="" placeholder="选择角色">
            <el-option v-for="item in init.role" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :sm="12">
        <el-form-item label="岗位:">
          <el-select v-model="form.position_id" class="" placeholder="选择岗位" size="large">
            <el-option v-for="item in init.position" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- </el-tab-pane> -->
    <!-- <el-tab-pane label="其他信息" name="second">
				<el-row :gutter="15">
					<el-col :sm="12">
						<el-form-item label="用户昵称:">
							<el-input v-model="form.nickname" />
						</el-form-item>
					</el-col>
					<el-col :sm="12">
						<el-form-item label="邮箱:">
							<el-input v-model="form.email" />
						</el-form-item>
					</el-col>
					<el-col :sm="12">
						<el-form-item label="职级:">
							<el-select v-model="form.level_id" class="m-2" placeholder="选择职级" size="large">
								<el-option v-for="item in init.level" :key="item.id" :label="item.name"
									:value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :sm="12">
						<el-form-item label="岗位:">
							<el-select v-model="form.position_id" class="m-2" placeholder="选择岗位" size="large">
								<el-option v-for="item in init.position" :key="item.id" :label="item.name"
									:value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :sm="12">
						<el-form-item label="所属部门:">
							<el-cascader v-model="form.dept_id" :options="init.dept" :props="props1" clearable />
						</el-form-item>
					</el-col>
					<el-col :sm="12">
            <el-form-item label="所在城市:">
              <pcas-cascader v-model="form.city" />
            </el-form-item>
          </el-col>
					<el-col :sm="12">
						<el-form-item label="地址:">
							<el-input v-model="form.address" />
						</el-form-item>
					</el-col>
					<el-col :sm="12">
						<el-form-item label="排序号:">
							<el-input v-model="form.sort" type="number" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="个人简介:">
					<el-input v-model="form.intro" type="textarea" />
				</el-form-item>
			</el-tab-pane> -->
    <!-- </el-tabs> -->
  </el-form>
  <!-- <div style="display: flex;justify-content: center;">
      <el-button type="primary" @click="submitButton()">
        确定
      </el-button>
      <el-button >取消</el-button>
    </div> -->
</template>

<style lang="scss" scoped>
  .demo-tabs {
    margin-top: -25px;
  }

  .avatar {
    :deep(.el-image) {
      width: 100px !important;
      height: 100px !important;
    }

    :deep(.image-slot) {
      width: 100px !important;
      height: 100px !important;
    }

    :deep(.el-upload__tip) {
      display: none !important;
    }
  }

  :deep(.el-textarea__inner) {
    min-height: 100px !important;
  }
</style>
