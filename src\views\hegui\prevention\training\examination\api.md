---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 18-合规培训服务/考试信息管理

## GET 获取考试信息详情

GET /whiskerguardtrainingservice/api/exam/infos/{id}

根据考试ID获取指定考试的详细信息。
返回完整的考试信息，包括所有配置参数和关联数据。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |考试信息ID，用于标识要查询的考试记录|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "courseId": "",
  "examName": "",
  "examDescription": "",
  "examDuration": 0,
  "questionCount": 0,
  "scorePerQuestion": 0,
  "totalScore": 0,
  "passScore": 0,
  "examStatus": "",
  "examNotice": "",
  "isEnabled": false,
  "courseInfo": {
    "id": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "coursePoint": 0,
    "coverImageUrl": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "coursePoint": 0,
            "coverImageUrl": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "averageRating": 0,
            "ratingCount": 0,
            "bookmarkCount": 0,
            "shareCount": 0,
            "learnerCount": 0,
            "completionCount": 0,
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityExamInfoDTO](#schemaresponseentityexaminfodto)|

## POST 创建考试信息

POST /whiskerguardtrainingservice/api/exam/infos

创建新的考试信息记录，包括考试名称、时长、分数设置等基本信息。
系统会自动设置创建时间和创建者信息。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "examName": "string",
  "examDescription": "string",
  "examDuration": 0,
  "questionCount": 0,
  "scorePerQuestion": 0,
  "totalScore": 0,
  "passScore": 0,
  "examStatus": "DRAFT",
  "examNotice": "string",
  "isEnabled": true,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "courseInfo": {
    "id": 0,
    "courseCode": "string",
    "courseName": "string",
    "courseType": "string",
    "trainingTheme": "string",
    "difficultyLevel": "string",
    "applicableRole": "string",
    "instructor": "string",
    "coursePoint": 0,
    "coverImageUrl": "string",
    "producer": "string",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "string",
    "learningObjective": "string",
    "prerequisites": "string",
    "certificationInfo": "string",
    "courseChapters": "new ArrayList<>()",
    "courseAttachments": "new ArrayList<>()",
    "playbackSetting": {
      "id": 0,
      "courseId": 0,
      "autoPlay": true,
      "rememberPlayback": true,
      "continuousPlayback": true,
      "defaultClarity": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": 0,
      "commentEnabled": true,
      "likeEnabled": true,
      "shareEnabled": true,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "relatedCourses": "new HashSet<>()"
  }
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ExamInfoDTO](#schemaexaminfodto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "",
  "examName": "",
  "examDescription": "",
  "examDuration": 0,
  "questionCount": 0,
  "scorePerQuestion": 0,
  "totalScore": 0,
  "passScore": 0,
  "examStatus": "",
  "examNotice": "",
  "isEnabled": false,
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "courseInfo": {
    "id": 0,
    "courseCode": "",
    "courseName": "",
    "courseType": "",
    "trainingTheme": "",
    "difficultyLevel": "",
    "applicableRole": "",
    "instructor": "",
    "coursePoint": 0,
    "coverImageUrl": "",
    "producer": "",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "",
    "durationMinutes": 0,
    "metadata": "",
    "version": 0,
    "createdBy": "",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": false,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "",
    "learningObjective": "",
    "prerequisites": "",
    "certificationInfo": "",
    "courseContent": {
      "id": 0,
      "courseId": "",
      "contentType": "",
      "contentTitle": "",
      "contentDescription": "",
      "contentUrl": "",
      "durationMinutes": 0,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "playbackSetting": {
      "id": 0,
      "courseId": "",
      "autoPlay": false,
      "rememberPlayback": false,
      "continuousPlayback": false,
      "defaultClarity": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": "",
      "commentEnabled": false,
      "likeEnabled": false,
      "shareEnabled": false,
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    },
    "relatedCourses": [
      {
        "id": 0,
        "relatedCourseId": "",
        "metadata": "",
        "version": 0,
        "createdBy": "",
        "createdAt": {
          "seconds": 0,
          "nanos": 0
        },
        "updatedBy": "",
        "updatedAt": {
          "seconds": 0,
          "nanos": 0
        },
        "isDeleted": false,
        "courseInfos": [
          {
            "id": 0,
            "courseCode": "",
            "courseName": "",
            "courseType": "",
            "trainingTheme": "",
            "difficultyLevel": "",
            "applicableRole": "",
            "instructor": "",
            "coursePoint": 0,
            "coverImageUrl": "",
            "producer": "",
            "releaseDate": {
              "seconds": 0,
              "nanos": 0
            },
            "lastUpdate": {
              "seconds": 0,
              "nanos": 0
            },
            "status": "",
            "durationMinutes": 0,
            "metadata": "",
            "version": 0,
            "createdBy": "",
            "createdAt": {
              "seconds": 0,
              "nanos": 0
            },
            "updatedBy": "",
            "updatedAt": {
              "seconds": 0,
              "nanos": 0
            },
            "isDeleted": false,
            "averageRating": 0,
            "ratingCount": 0,
            "bookmarkCount": 0,
            "shareCount": 0,
            "learnerCount": 0,
            "completionCount": 0,
            "courseOverview": "",
            "learningObjective": "",
            "prerequisites": "",
            "certificationInfo": "",
            "courseContent": {
              "id": 0,
              "courseId": "",
              "contentType": "",
              "contentTitle": "",
              "contentDescription": "",
              "contentUrl": "",
              "durationMinutes": 0,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "playbackSetting": {
              "id": 0,
              "courseId": "",
              "autoPlay": false,
              "rememberPlayback": false,
              "continuousPlayback": false,
              "defaultClarity": "",
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "interactiveFeature": {
              "id": 0,
              "courseId": "",
              "commentEnabled": false,
              "likeEnabled": false,
              "shareEnabled": false,
              "metadata": "",
              "version": 0,
              "createdBy": "",
              "createdAt": {
                "seconds": 0,
                "nanos": 0
              },
              "updatedBy": "",
              "updatedAt": {
                "seconds": 0,
                "nanos": 0
              },
              "isDeleted": false
            },
            "relatedCourses": [
              {
                "id": 0,
                "relatedCourseId": "",
                "metadata": "",
                "version": 0,
                "createdBy": "",
                "createdAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "updatedBy": "",
                "updatedAt": {
                  "seconds": 0,
                  "nanos": 0
                },
                "isDeleted": false,
                "courseInfos": [
                  {}
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityExamInfoDTO](#schemaresponseentityexaminfodto)|

# 数据模型

<h2 id="tocS_ZoneOffset">ZoneOffset</h2>

<a id="schemazoneoffset"></a>
<a id="schema_ZoneOffset"></a>
<a id="tocSzoneoffset"></a>
<a id="tocszoneoffset"></a>

```json
{
  "totalSeconds": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|totalSeconds|integer|false|none||The total offset in seconds.|

<h2 id="tocS_ZoneId">ZoneId</h2>

<a id="schemazoneid"></a>
<a id="schema_ZoneId"></a>
<a id="tocSzoneid"></a>
<a id="tocszoneid"></a>

```json
{}

```

### 属性

*None*

<h2 id="tocS_CourseChapterDTO">CourseChapterDTO</h2>

<a id="schemacoursechapterdto"></a>
<a id="schema_CourseChapterDTO"></a>
<a id="tocScoursechapterdto"></a>
<a id="tocscoursechapterdto"></a>

```json
{
  "id": 0,
  "courseId": 0,
  "parentChapterId": 0,
  "chapterTitle": "string",
  "chapterDescription": "string",
  "chapterOrder": 0,
  "chapterType": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "isRequired": true,
  "status": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "parentChapter": {
    "id": 0,
    "courseId": 0,
    "parentChapterId": 0,
    "chapterTitle": "string",
    "chapterDescription": "string",
    "chapterOrder": 0,
    "chapterType": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "isRequired": true,
    "status": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "parentChapter": {
      "id": 0,
      "courseId": 0,
      "parentChapterId": 0,
      "chapterTitle": "string",
      "chapterDescription": "string",
      "chapterOrder": 0,
      "chapterType": "string",
      "contentUrl": "string",
      "durationMinutes": 0,
      "isRequired": true,
      "status": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "parentChapter": {
        "id": 0,
        "courseId": 0,
        "parentChapterId": 0,
        "chapterTitle": "string",
        "chapterDescription": "string",
        "chapterOrder": 0,
        "chapterType": "string",
        "contentUrl": "string",
        "durationMinutes": 0,
        "isRequired": true,
        "status": "string",
        "metadata": "string",
        "version": 0,
        "createdBy": "string",
        "createdAt": {
          "seconds": null,
          "nanos": null
        },
        "updatedBy": "string",
        "updatedAt": {
          "seconds": null,
          "nanos": null
        },
        "isDeleted": true,
        "parentChapter": {
          "id": null,
          "courseId": null,
          "parentChapterId": null,
          "chapterTitle": null,
          "chapterDescription": null,
          "chapterOrder": null,
          "chapterType": null,
          "contentUrl": null,
          "durationMinutes": null,
          "isRequired": null,
          "status": null,
          "metadata": null,
          "version": null,
          "createdBy": null,
          "createdAt": null,
          "updatedBy": null,
          "updatedAt": null,
          "isDeleted": null,
          "parentChapter": null
        }
      }
    }
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|integer(int64)|true|none||关联课程ID|
|parentChapterId|integer(int64)|false|none||父章节ID（支持多级章节）|
|chapterTitle|string|true|none||章节标题|
|chapterDescription|string|false|none||章节描述|
|chapterOrder|integer|false|none||章节顺序|
|chapterType|string|false|none||章节类型（视频、文档、测试等）|
|contentUrl|string|false|none||视频/内容URL|
|durationMinutes|integer|false|none||章节时长（分钟）|
|isRequired|boolean|false|none||是否必修|
|status|string|false|none||章节状态（正常、维护中、已下线）|
|metadata|string|false|none||补充字段|
|version|integer|false|none||版本号|
|createdBy|string|false|none||创建者|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||更新者|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|isDeleted|boolean|false|none||是否删除|
|parentChapter|[CourseChapterDTO](#schemacoursechapterdto)|false|none||none|

<h2 id="tocS_ZonedDateTime">ZonedDateTime</h2>

<a id="schemazoneddatetime"></a>
<a id="schema_ZonedDateTime"></a>
<a id="tocSzoneddatetime"></a>
<a id="tocszoneddatetime"></a>

```json
{
  "dateTime": "string",
  "offset": {
    "totalSeconds": 0
  },
  "zone": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|dateTime|string|false|none||The local date-time.|
|offset|[ZoneOffset](#schemazoneoffset)|false|none||The offset from UTC/Greenwich.|
|zone|[ZoneId](#schemazoneid)|false|none||The time-zone.|

<h2 id="tocS_CourseContentDTO">CourseContentDTO</h2>

<a id="schemacoursecontentdto"></a>
<a id="schema_CourseContentDTO"></a>
<a id="tocScoursecontentdto"></a>
<a id="tocscoursecontentdto"></a>

```json
{
  "id": 0,
  "courseId": 0,
  "contentType": "string",
  "contentTitle": "string",
  "contentDescription": "string",
  "contentUrl": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|integer(int64)|true|none||外键关联 CourseInfo|
|contentType|string|false|none||内容类型|
|contentTitle|string|false|none||内容标题|
|contentDescription|string|false|none||内容描述|
|contentUrl|string|false|none||内容链接（如视频/文档地址）|
|durationMinutes|integer|false|none||内容时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_PlaybackSettingDTO">PlaybackSettingDTO</h2>

<a id="schemaplaybacksettingdto"></a>
<a id="schema_PlaybackSettingDTO"></a>
<a id="tocSplaybacksettingdto"></a>
<a id="tocsplaybacksettingdto"></a>

```json
{
  "id": 0,
  "courseId": 0,
  "autoPlay": true,
  "rememberPlayback": true,
  "continuousPlayback": true,
  "defaultClarity": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|integer(int64)|true|none||外键关联 CourseInfo|
|autoPlay|boolean|false|none||是否自动播放|
|rememberPlayback|boolean|false|none||是否记忆播放进度|
|continuousPlayback|boolean|false|none||是否连续播放|
|defaultClarity|string|false|none||默认清晰度|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_InteractiveFeatureDTO">InteractiveFeatureDTO</h2>

<a id="schemainteractivefeaturedto"></a>
<a id="schema_InteractiveFeatureDTO"></a>
<a id="tocSinteractivefeaturedto"></a>
<a id="tocsinteractivefeaturedto"></a>

```json
{
  "id": 0,
  "courseId": 0,
  "commentEnabled": true,
  "likeEnabled": true,
  "shareEnabled": true,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|integer(int64)|true|none||外键关联 CourseInfo|
|commentEnabled|boolean|false|none||是否允许评论|
|likeEnabled|boolean|false|none||是否允许点赞|
|shareEnabled|boolean|false|none||是否允许分享|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|

<h2 id="tocS_CourseInfoDTO">CourseInfoDTO</h2>

<a id="schemacourseinfodto"></a>
<a id="schema_CourseInfoDTO"></a>
<a id="tocScourseinfodto"></a>
<a id="tocscourseinfodto"></a>

```json
{
  "id": 0,
  "courseCode": "string",
  "courseName": "string",
  "courseType": "string",
  "trainingTheme": "string",
  "difficultyLevel": "string",
  "applicableRole": "string",
  "instructor": "string",
  "coursePoint": 0,
  "coverImageUrl": "string",
  "producer": "string",
  "releaseDate": {
    "seconds": 0,
    "nanos": 0
  },
  "lastUpdate": {
    "seconds": 0,
    "nanos": 0
  },
  "status": "string",
  "durationMinutes": 0,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "averageRating": 0,
  "ratingCount": 0,
  "bookmarkCount": 0,
  "shareCount": 0,
  "learnerCount": 0,
  "completionCount": 0,
  "courseOverview": "string",
  "learningObjective": "string",
  "prerequisites": "string",
  "certificationInfo": "string",
  "courseChapters": "new ArrayList<>()",
  "courseAttachments": "new ArrayList<>()",
  "playbackSetting": {
    "id": 0,
    "courseId": 0,
    "autoPlay": true,
    "rememberPlayback": true,
    "continuousPlayback": true,
    "defaultClarity": "string",
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "interactiveFeature": {
    "id": 0,
    "courseId": 0,
    "commentEnabled": true,
    "likeEnabled": true,
    "shareEnabled": true,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  },
  "relatedCourses": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseCode|string|false|none||课程代码（如 COURSE20240115001）|
|courseName|string|true|none||课程名称|
|courseType|string|true|none||课程类型|
|trainingTheme|string|false|none||培训主题（如“前端开发”）|
|difficultyLevel|string|false|none||难度等级|
|applicableRole|string|false|none||适用角色（如 ["frontend_dev", "backend_dev"]）|
|instructor|string|false|none||讲师姓名|
|coursePoint|integer(int64)|false|none||课程积分|
|coverImageUrl|string|false|none||封面图片地址|
|producer|string|false|none||制作人|
|releaseDate|[Instant](#schemainstant)|false|none||发布日期|
|lastUpdate|[Instant](#schemainstant)|false|none||最后更新时间|
|status|string|false|none||课程状态|
|durationMinutes|integer|false|none||课程总时长（分钟）|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|averageRating|number|false|none||平均评分（1-5星）|
|ratingCount|integer|false|none||评分总数|
|bookmarkCount|integer|false|none||收藏总数|
|shareCount|integer|false|none||分享总数|
|learnerCount|integer|false|none||学习人数|
|completionCount|integer|false|none||完成人数|
|courseOverview|string|false|none||课程概述|
|learningObjective|string|false|none||学习目标|
|prerequisites|string|false|none||先修要求|
|certificationInfo|string|false|none||认证信息|
|courseChapters|[[CourseChapterDTO](#schemacoursechapterdto)]|false|none||课程章节列表|
|courseAttachments|[[CourseAttachmentDTO](#schemacourseattachmentdto)]|false|none||课程附件列表|
|playbackSetting|[PlaybackSettingDTO](#schemaplaybacksettingdto)|false|none||none|
|interactiveFeature|[InteractiveFeatureDTO](#schemainteractivefeaturedto)|false|none||none|
|relatedCourses|[[RelatedCourseDTO](#schemarelatedcoursedto)]|false|none||none|

<h2 id="tocS_RelatedCourseDTO">RelatedCourseDTO</h2>

<a id="schemarelatedcoursedto"></a>
<a id="schema_RelatedCourseDTO"></a>
<a id="tocSrelatedcoursedto"></a>
<a id="tocsrelatedcoursedto"></a>

```json
{
  "id": 0,
  "relatedCourseId": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "courseInfos": "new HashSet<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedCourseId|string|true|none||外键关联 CourseInfo|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfos|[[CourseInfoDTO](#schemacourseinfodto)]|false|none||none|

<h2 id="tocS_ExamInfoDTO">ExamInfoDTO</h2>

<a id="schemaexaminfodto"></a>
<a id="schema_ExamInfoDTO"></a>
<a id="tocSexaminfodto"></a>
<a id="tocsexaminfodto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "courseId": "string",
  "examName": "string",
  "examDescription": "string",
  "examDuration": 0,
  "questionCount": 0,
  "scorePerQuestion": 0,
  "totalScore": 0,
  "passScore": 0,
  "examStatus": "DRAFT",
  "examNotice": "string",
  "isEnabled": true,
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "courseInfo": {
    "id": 0,
    "courseCode": "string",
    "courseName": "string",
    "courseType": "string",
    "trainingTheme": "string",
    "difficultyLevel": "string",
    "applicableRole": "string",
    "instructor": "string",
    "coursePoint": 0,
    "coverImageUrl": "string",
    "producer": "string",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "string",
    "learningObjective": "string",
    "prerequisites": "string",
    "certificationInfo": "string",
    "courseChapters": "new ArrayList<>()",
    "courseAttachments": "new ArrayList<>()",
    "playbackSetting": {
      "id": 0,
      "courseId": 0,
      "autoPlay": true,
      "rememberPlayback": true,
      "continuousPlayback": true,
      "defaultClarity": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": 0,
      "commentEnabled": true,
      "likeEnabled": true,
      "shareEnabled": true,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "relatedCourses": "new HashSet<>()"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|courseId|string|true|none||关联的课程ID|
|examName|string|true|none||考试名称|
|examDescription|string|false|none||考试描述|
|examDuration|integer|true|none||考试时长（分钟）|
|questionCount|integer|true|none||题目数量|
|scorePerQuestion|integer|true|none||每题分值|
|totalScore|integer|true|none||总分|
|passScore|integer|true|none||及格分数|
|examStatus|string|true|none||考试状态（草稿、发布、暂停、结束）|
|examNotice|string|false|none||考试须知|
|isEnabled|boolean|false|none||是否启用|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||版本号（默认值为 1）|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|[CourseInfoDTO](#schemacourseinfodto)|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|examStatus|DRAFT|
|examStatus|PUBLISHED|
|examStatus|PAUSED|
|examStatus|ENDED|

<h2 id="tocS_ResponseEntityExamInfoDTO">ResponseEntityExamInfoDTO</h2>

<a id="schemaresponseentityexaminfodto"></a>
<a id="schema_ResponseEntityExamInfoDTO"></a>
<a id="tocSresponseentityexaminfodto"></a>
<a id="tocsresponseentityexaminfodto"></a>

```json
{
  "id": 0,
  "courseId": "string",
  "examName": "string",
  "examDescription": "string",
  "examDuration": 0,
  "questionCount": 0,
  "scorePerQuestion": 0,
  "totalScore": 0,
  "passScore": 0,
  "examStatus": "DRAFT",
  "examNotice": "string",
  "isEnabled": true,
  "courseInfo": {
    "id": 0,
    "courseCode": "string",
    "courseName": "string",
    "courseType": "string",
    "trainingTheme": "string",
    "difficultyLevel": "string",
    "applicableRole": "string",
    "instructor": "string",
    "coursePoint": 0,
    "coverImageUrl": "string",
    "producer": "string",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "string",
    "learningObjective": "string",
    "prerequisites": "string",
    "certificationInfo": "string",
    "courseChapters": "new ArrayList<>()",
    "courseAttachments": "new ArrayList<>()",
    "playbackSetting": {
      "id": 0,
      "courseId": 0,
      "autoPlay": true,
      "rememberPlayback": true,
      "continuousPlayback": true,
      "defaultClarity": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": 0,
      "commentEnabled": true,
      "likeEnabled": true,
      "shareEnabled": true,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "relatedCourses": "new HashSet<>()"
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|courseId|string|true|none||关联的课程ID|
|examName|string|true|none||考试名称|
|examDescription|string|false|none||考试描述|
|examDuration|integer|true|none||考试时长（分钟）|
|questionCount|integer|true|none||题目数量|
|scorePerQuestion|integer|true|none||每题分值|
|totalScore|integer|true|none||总分|
|passScore|integer|true|none||及格分数|
|examStatus|string|true|none||考试状态（草稿、发布、暂停、结束）|
|examNotice|string|false|none||考试须知|
|isEnabled|boolean|false|none||是否启用|
|courseInfo|[CourseInfoDTO](#schemacourseinfodto)|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|examStatus|DRAFT|
|examStatus|PUBLISHED|
|examStatus|PAUSED|
|examStatus|ENDED|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_CourseAttachmentDTO">CourseAttachmentDTO</h2>

<a id="schemacourseattachmentdto"></a>
<a id="schema_CourseAttachmentDTO"></a>
<a id="tocScourseattachmentdto"></a>
<a id="tocscourseattachmentdto"></a>

```json
{
  "id": 0,
  "courseId": 0,
  "contentId": "string",
  "fileId": "string",
  "fileType": "string",
  "filePath": "string",
  "fileName": "string",
  "fileSize": "string",
  "uploadTime": {
    "dateTime": "string",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "description": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "courseInfo": {
    "id": 0,
    "courseCode": "string",
    "courseName": "string",
    "courseType": "string",
    "trainingTheme": "string",
    "difficultyLevel": "string",
    "applicableRole": "string",
    "instructor": "string",
    "coursePoint": 0,
    "coverImageUrl": "string",
    "producer": "string",
    "releaseDate": {
      "seconds": 0,
      "nanos": 0
    },
    "lastUpdate": {
      "seconds": 0,
      "nanos": 0
    },
    "status": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true,
    "averageRating": 0,
    "ratingCount": 0,
    "bookmarkCount": 0,
    "shareCount": 0,
    "learnerCount": 0,
    "completionCount": 0,
    "courseOverview": "string",
    "learningObjective": "string",
    "prerequisites": "string",
    "certificationInfo": "string",
    "courseChapters": "new ArrayList<>()",
    "courseAttachments": "new ArrayList<>()",
    "playbackSetting": {
      "id": 0,
      "courseId": 0,
      "autoPlay": true,
      "rememberPlayback": true,
      "continuousPlayback": true,
      "defaultClarity": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "interactiveFeature": {
      "id": 0,
      "courseId": 0,
      "commentEnabled": true,
      "likeEnabled": true,
      "shareEnabled": true,
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    },
    "relatedCourses": "new HashSet<>()"
  },
  "courseContent": {
    "id": 0,
    "courseId": 0,
    "contentType": "string",
    "contentTitle": "string",
    "contentDescription": "string",
    "contentUrl": "string",
    "durationMinutes": 0,
    "metadata": "string",
    "version": 0,
    "createdBy": "string",
    "createdAt": {
      "seconds": 0,
      "nanos": 0
    },
    "updatedBy": "string",
    "updatedAt": {
      "seconds": 0,
      "nanos": 0
    },
    "isDeleted": true
  }
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||附件ID|
|courseId|integer(int64)|false|none||课程ID|
|contentId|string|false|none||课程内容ID|
|fileId|string|false|none||文件ID|
|fileType|string|false|none||文件类型|
|filePath|string|false|none||文件路径|
|fileName|string|false|none||文件名称|
|fileSize|string|false|none||文件大小（字节）|
|uploadTime|[ZonedDateTime](#schemazoneddatetime)|false|none||上传时间|
|description|string|false|none||文件描述|
|metadata|string|false|none||补充字段，用于存储额外信息（如 JSON 格式）|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间（由系统自动填充）|
|updatedBy|string|false|none||最后修改者账号或姓名|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间（由系统自动填充）|
|isDeleted|boolean|false|none||是否删除：true 表示已删除，false 表示正常|
|courseInfo|[CourseInfoDTO](#schemacourseinfodto)|false|none||none|
|courseContent|[CourseContentDTO](#schemacoursecontentdto)|false|none||none|

