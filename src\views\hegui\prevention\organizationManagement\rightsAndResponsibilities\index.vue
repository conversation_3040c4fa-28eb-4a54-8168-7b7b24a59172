<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Edit as ElIconEdit,
  Download as ElIconDownload,
  Printer as ElIconPrinter,
  // Info as ElIconInfo,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'

const activeMenu = ref('1-1')
const searchText = ref('')
const viewType = ref('standard')
const filter = ref({
  businessAreas: [],
  responsibilityTypes: [],
  organizationLevel: 'all',
})

const businessAreas = [
  { value: 'finance', label: '财务合规' },
  { value: 'hr', label: '人力资源合规' },
  { value: 'operation', label: '业务运营合规' },
  { value: 'data', label: '数据保护合规' },
  { value: 'legal', label: '法律合规' },
]

const responsibilityTypes = [
  { value: 'R', label: '负责 (R)' },
  { value: 'A', label: '批准 (A)' },
  { value: 'C', label: '咨询 (C)' },
  { value: 'I', label: '知情 (I)' },
]

const matrixData = ref([
  { item: '合规政策制定', board: 'A', ceo: 'A', committee: 'R', director: 'R', legal: 'C', finance: 'I', hr: 'I', business: 'C' },
  { item: '合规风险识别', board: 'I', ceo: 'A', committee: 'A', director: 'R', legal: 'C', finance: 'C', hr: 'C', business: 'R' },
  { item: '合规培训', board: 'I', ceo: 'A', committee: 'A', director: 'R', legal: 'R', finance: 'C', hr: 'R', business: 'I' },
  { item: '合规审查', board: 'I', ceo: 'I', committee: 'A', director: 'R', legal: 'R', finance: 'C', hr: 'I', business: 'C' },
  { item: '合规检查', board: 'I', ceo: 'A', committee: 'A', director: 'R', legal: 'R', finance: 'C', hr: 'I', business: 'I' },
  { item: '合规报告', board: 'A', ceo: 'A', committee: 'R', director: 'R', legal: 'C', finance: 'C', hr: 'I', business: 'C' },
  { item: '合规调查', board: 'I', ceo: 'A', committee: 'A', director: 'R', legal: 'R', finance: 'C', hr: 'C', business: 'I' },
  { item: '违规处理', board: 'I', ceo: 'A', committee: 'R', director: 'R', legal: 'C', finance: 'I', hr: 'R', business: 'I' },
  { item: '法规更新跟踪', board: 'I', ceo: 'I', committee: 'I', director: 'A', legal: 'R', finance: 'C', hr: 'C', business: 'I' },
  { item: '合规文化建设', board: 'A', ceo: 'R', committee: 'R', director: 'R', legal: 'C', finance: 'C', hr: 'R', business: 'C' },
])

const workloadChart = ref<HTMLElement>()
const distributionChart = ref<HTMLElement>()

function handleSearch() {
  console.log('Search:', searchText.value)
}

onMounted(() => {
  nextTick(() => {
    if (workloadChart.value) {
      const chart = echarts.init(workloadChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
        },
        yAxis: {
          type: 'category',
          data: ['合规总监', '合规委员会', '业务部门', '法务部', '人力资源部'],
        },
        series: [
          {
            name: '工作量',
            type: 'bar',
            data: [28, 22, 18, 15, 12],
            itemStyle: {
              color: '#1E88E5',
            },
          },
        ],
      })
    }

    if (distributionChart.value) {
      const chart = echarts.init(distributionChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
        },
        series: [
          {
            name: '职责分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 32, name: '财务合规' },
              { value: 28, name: '人力资源合规' },
              { value: 25, name: '业务运营合规' },
              { value: 15, name: '数据保护合规' },
            ],
            color: ['#1E88E5', '#4CAF50', '#FFC107', '#9C27B0'],
          },
        ],
      })
    }
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              权责分配矩阵
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconEdit />
              </el-icon>编辑矩阵
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>导出矩阵
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconPrinter />
              </el-icon>打印
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <el-icon-info />
              </el-icon>图例说明
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!-- <template #header>
                <div class="f-16 fw-600">基本信息</div>
              </template> -->
              <div class="mb-4 flex flex-wrap items-center gap-4">
                <el-select v-model="filter.businessAreas" multiple placeholder="业务领域" class="w-48">
                  <el-option v-for="area in businessAreas" :key="area.value" :label="area.label" :value="area.value" />
                </el-select>
                <el-select v-model="filter.responsibilityTypes" multiple placeholder="职责类型" class="w-48">
                  <el-option
                    v-for="type in responsibilityTypes" :key="type.value" :label="type.label"
                    :value="type.value"
                  />
                </el-select>
                <el-select v-model="filter.organizationLevel" placeholder="组织层级" class="w-48">
                  <el-option label="全部" value="all" />
                  <el-option label="总部" value="headquarters" />
                  <el-option label="部门" value="department" />
                  <el-option label="子公司" value="subsidiary" />
                </el-select>
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  应用筛选
                </el-button>
                <el-button class="!rounded-button whitespace-nowrap">
                  重置
                </el-button>
              </div>
              <div class="flex space-x-1">
                <el-button
                  :type="viewType === 'standard' ? 'primary' : ''" class="!rounded-button whitespace-nowrap"
                  @click="viewType = 'standard'"
                >
                  标准视图
                </el-button>
                <el-button
                  :type="viewType === 'simple' ? 'primary' : ''" class="!rounded-button whitespace-nowrap"
                  @click="viewType = 'simple'"
                >
                  简化视图
                </el-button>
                <el-button
                  :type="viewType === 'byRole' ? 'primary' : ''" class="!rounded-button whitespace-nowrap"
                  @click="viewType = 'byRole'"
                >
                  按角色查看
                </el-button>
                <el-button
                  :type="viewType === 'byItem' ? 'primary' : ''" class="!rounded-button whitespace-nowrap"
                  @click="viewType = 'byItem'"
                >
                  按事项查看
                </el-button>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  合规权责分配矩阵
                </div>
              </template>
              <div class="overflow-auto">
                <el-table :data="matrixData" border style="width: 100%;" height="500">
                  <el-table-column prop="item" label="合规事项/职责" width="180" fixed />
                  <el-table-column prop="board" label="董事会" width="100" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.board === 'A',
                          'text-green-500': row.board === 'R',
                          'text-yellow-500': row.board === 'C',
                          'text-gray-500': row.board === 'I',
                        }"
                      >{{ row.board }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ceo" label="总经理" width="100" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.ceo === 'A',
                          'text-green-500': row.ceo === 'R',
                          'text-yellow-500': row.ceo === 'C',
                          'text-gray-500': row.ceo === 'I',
                        }"
                      >{{ row.ceo }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="committee" label="合规委员会" width="120" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.committee === 'A',
                          'text-green-500': row.committee === 'R',
                          'text-yellow-500': row.committee === 'C',
                          'text-gray-500': row.committee === 'I',
                        }"
                      >{{ row.committee }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="director" label="合规总监" width="120" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.director === 'A',
                          'text-green-500': row.director === 'R',
                          'text-yellow-500': row.director === 'C',
                          'text-gray-500': row.director === 'I',
                        }"
                      >{{ row.director }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="legal" label="法务部" width="100" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.legal === 'A',
                          'text-green-500': row.legal === 'R',
                          'text-yellow-500': row.legal === 'C',
                          'text-gray-500': row.legal === 'I',
                        }"
                      >{{ row.legal }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="finance" label="财务部" width="100" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.finance === 'A',
                          'text-green-500': row.finance === 'R',
                          'text-yellow-500': row.finance === 'C',
                          'text-gray-500': row.finance === 'I',
                        }"
                      >{{ row.finance }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="hr" label="人力资源部" width="120" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.hr === 'A',
                          'text-green-500': row.hr === 'R',
                          'text-yellow-500': row.hr === 'C',
                          'text-gray-500': row.hr === 'I',
                        }"
                      >{{ row.hr }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="business" label="业务部门" width="120" align="center">
                    <template #default="{ row }">
                      <span
                        :class="{
                          'text-blue-500': row.business === 'A',
                          'text-green-500': row.business === 'R',
                          'text-yellow-500': row.business === 'C',
                          'text-gray-500': row.business === 'I',
                        }"
                      >{{ row.business }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  图例说明
                </div>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <h3 class="mb-2 font-medium">
                    RACI 模型说明
                  </h3>
                  <ul class="space-y-2">
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-green-500 text-center text-white">R</span>
                      <span>负责 (Responsible) - 负责执行任务的角色</span>
                    </li>
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-blue-500 text-center text-white">A</span>
                      <span>批准 (Accountable) - 对任务结果负责的角色</span>
                    </li>
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-yellow-500 text-center text-white">C</span>
                      <span>咨询 (Consulted) - 在任务执行前需要咨询的角色</span>
                    </li>
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-gray-500 text-center text-white">I</span>
                      <span>知情 (Informed) - 需要被告知任务进展的角色</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h3 class="mb-2 font-medium">
                    特殊标识说明
                  </h3>
                  <ul class="space-y-2">
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-green-600 text-center text-white">R*</span>
                      <span>主要负责 - 主要执行者</span>
                    </li>
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-green-400 text-center text-white">R</span>
                      <span>备选负责 - 次要执行者</span>
                    </li>
                    <li class="flex items-center">
                      <span class="mr-2 inline-block h-6 w-6 rounded bg-blue-600 text-center text-white">A*</span>
                      <span>最终审批 - 最终决策者</span>
                    </li>
                  </ul>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  角色工作量
                </div>
              </template>
              <div ref="workloadChart" class="h-64" />
              <div class="mt-4">
                <h3 class="mb-2 font-medium">
                  工作量排名 TOP5
                </h3>
                <ul class="space-y-2">
                  <li class="flex justify-between">
                    <span>合规总监</span>
                    <span class="font-medium">28 项</span>
                  </li>
                  <li class="flex justify-between">
                    <span>合规委员会</span>
                    <span class="font-medium">22 项</span>
                  </li>
                  <li class="flex justify-between">
                    <span>业务部门</span>
                    <span class="font-medium">18 项</span>
                  </li>
                  <li class="flex justify-between">
                    <span>法务部</span>
                    <span class="font-medium">15 项</span>
                  </li>
                  <li class="flex justify-between">
                    <span>人力资源部</span>
                    <span class="font-medium">12 项</span>
                  </li>
                </ul>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  职责分布
                </div>
              </template>
              <div ref="distributionChart" class="h-64" />
              <div class="mt-4">
                <h3 class="mb-2 font-medium">
                  按业务领域分布
                </h3>
                <ul class="space-y-2">
                  <li class="flex justify-between">
                    <span>财务合规</span>
                    <span class="font-medium">32%</span>
                  </li>
                  <li class="flex justify-between">
                    <span>人力资源合规</span>
                    <span class="font-medium">28%</span>
                  </li>
                  <li class="flex justify-between">
                    <span>业务运营合规</span>
                    <span class="font-medium">25%</span>
                  </li>
                  <li class="flex justify-between">
                    <span>数据保护合规</span>
                    <span class="font-medium">15%</span>
                  </li>
                </ul>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  潜在问题
                </div>
              </template>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-1 text-red-500">
                    <ElIconWarning />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      职责过度集中
                    </p>
                    <p class="text-sm text-gray-500">
                      合规总监承担了过多责任
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-1 text-yellow-500">
                    <ElIconWarning />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      责任缺失
                    </p>
                    <p class="text-sm text-gray-500">
                      数据保护领域缺乏明确负责人
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-1 text-yellow-500">
                    <ElIconWarning />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      权责不匹配
                    </p>
                    <p class="text-sm text-gray-500">
                      业务部门有执行权但无审批权
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-1 text-blue-500">
                    <el-icon-info />
                  </el-icon>
                  <div>
                    <p class="font-medium">
                      咨询流程过长
                    </p>
                    <p class="text-sm text-gray-500">
                      财务合规事项需咨询过多部门
                    </p>
                  </div>
                </li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  :deep(.el-table .cell) {
    padding-right: 8px;
    padding-left: 8px;
  }

  :deep(.el-table th.el-table__cell) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--border .el-table__cell) {
    border-right: 1px solid #ebeef5;
  }

  :deep(.el-table--border) {
    border: 1px solid #ebeef5;
    border-right: none;
    border-bottom: none;
  }

  :deep(.el-table--border::after) {
    width: 1px;
    background-color: #ebeef5;
  }

  :deep(.el-table__fixed-right::before) {
    width: 1px;
    background-color: #ebeef5;
  }
</style>
