<script setup lang="ts">
import moment from 'moment'
import { onActivated, ref } from 'vue'
import Api from '@/api/modules/system/logon'

const tableData: any = ref([])

// 请求参数
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
  sizes: [10, 20, 50, 100],
  layout: 'total, sizes, ->, prev, pager, next, jumper',
  sort: null as string | null,
  order: null as string | null,
})
// 请求列表
function getList() {
  Api.actionloglist({
    page: pagination.value.page,
    limit: pagination.value.size,
  }).then((res: any) => {
    // console.log(res, '列表打印')
    tableData.value = res.data
    pagination.value.total = res.count
  })
}
getList()
onActivated(() => {
  getList()
})

function removeBatch(e: any) {

}
// 分页方法
function sizeChange(e: any) {
  pagination.value.size = e
  getList()
}
function currentChange(e: any) {
  pagination.value.page = e
  getList()
}
// 查看
// 查看
const visible: any = ref(false)
const data: any = ref({})
function see(e: any) {
  data.value = e
  visible.value = !visible.value
  // console.log(data.value, '查看')
}
</script>

<template>
  <div>
    <page-main style="position: relative;">
      <el-table :data="tableData" highlight-current-row border height="calc(100vh - 240px)">
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="username" label="操作账户" width="100" align="center" />
        <el-table-column prop="method" label="请求方式" width="100" align="center" />
        <el-table-column prop="module" label="操作模块" width="100" align="center" />
        <el-table-column prop="param" label="请求参数" width="120" align="center">
          <template #default="scope">
            <el-tooltip effect="dark" :content="scope.row.param" placement="top">
              <div style="height: 20px; overflow: hidden;">
                {{ scope.row.param }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="url" label="请求地址" width="120" align="center">
          <template #default="scope">
            <el-tooltip effect="dark" :content="scope.row.url" placement="top">
              <div style="height: 20px; overflow: hidden;">
                {{ scope.row.url }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="ip" label="IP地址" width="130" align="center" />
        <el-table-column prop="ip_city" label="IP所属地" align="center" />
        <el-table-column prop="os" label="操作系统" width="100" align="center" />
        <el-table-column prop="browser" label="浏览器" width="100" align="center" />
        <!-- <el-table-column prop="type" label="操作类型" width="100" align="center" /> -->
        <el-table-column prop="type" label="操作类型" width="120" align="center">
          <template #default="scope">
            <el-tag type="success">
              {{ ['登录系统', '注销系统', '操作日志'][scope.row.type - 1] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ moment(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="80" align="center">
          <template #default="scope">
            <el-button type="primary" text @click="see(scope.row)">
              <template #icon>
                <el-icon>
                  <View />
                </el-icon>
              </template>查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-pagination
        :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size"
        :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination"
        background @size-change="sizeChange" @current-change="currentChange"
      />
    </page-main>
    <el-dialog v-model="visible" title="修改用户" width="680px">
      <el-form label-width="82px" class="ele-form-detail">
        <el-row :gutter="15">
          <el-col :sm="12">
            <el-form-item label="操作模块:">
              <div class="ele-text-secondary">
                {{ data.module }}
              </div>
            </el-form-item>
            <el-form-item label="IP地址:">
              <div class="ele-text-secondary">
                {{ data.ip }}
              </div>
            </el-form-item>
            <el-form-item label="操作时间:">
              <div class="ele-text-secondary">
                {{ data.create_time }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :sm="12">
            <el-form-item label="操作人:">
              <div class="ele-text-secondary">
                {{ data.username }}
              </div>
            </el-form-item>
            <el-form-item label="IP区域:">
              <div class="ele-text-secondary">
                {{ data.ip_city }}
              </div>
            </el-form-item>
            <el-form-item label="日志类型:">
              <el-tag type="danger">
                {{ ['登录系统', '注销系统', '操作日志'][data.type - 1] }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <div style="margin: 12px 0;">
          <el-divider />
        </div>
        <el-form-item label="请求地址:">
          <div class="ele-text-secondary">
            {{ data.url }}
          </div>
        </el-form-item>
        <el-form-item label="调用方法:">
          <div class="ele-text-secondary">
            {{ data.method }}
          </div>
        </el-form-item>
        <el-form-item label="用户代理:">
          <div class="ele-text-secondary">
            {{ data.user_agent }}
          </div>
        </el-form-item>
        <el-form-item label="请求参数:">
          <div class="ele-text-secondary">
            {{ data.param }}
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .btnbox {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }
</style>
