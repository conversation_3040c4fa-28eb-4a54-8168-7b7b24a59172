---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 09-审批流程服务/待办事件

## GET 获取员工当天的待办事件

GET /whiskerguardapprovalservice/api/todo/events/today

描述：获取员工当天的待办事件

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "todayPendingCount": 0,
  "thisWeekCompletedCount": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ListTodoEventDTO](#schemalisttodoeventdto)|

# 数据模型

<h2 id="tocS_ListTodoEventDTO">ListTodoEventDTO</h2>

<a id="schemalisttodoeventdto"></a>
<a id="schema_ListTodoEventDTO"></a>
<a id="tocSlisttodoeventdto"></a>
<a id="tocslisttodoeventdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "assigneeId": 0,
  "businessId": 0,
  "eventType": "string",
  "title": "string",
  "description": "string",
  "status": "string",
  "approvalProcessId": "string",
  "approvalInstanceId": "string",
  "completedTime": {
    "seconds": 0,
    "nanos": 0
  },
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|assigneeId|integer(int64)|true|none||指派人ID|
|businessId|integer(int64)|false|none||关联的业务对象ID|
|eventType|string|true|none||事件类型|
|title|string|true|none||事件标题|
|description|string|false|none||事件描述|
|status|string|false|none||事件状态|
|approvalProcessId|string|false|none||审批流程ID|
|approvalInstanceId|string|false|none||审批实例ID|
|completedTime|[Instant](#schemainstant)|false|none||完成时间|
|metadata|string|false|none||补充字段，存储额外的元数据信息|
|version|integer|false|none||当前版本号|
|createdBy|string|false|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：false表示正常，true表示已删除|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

