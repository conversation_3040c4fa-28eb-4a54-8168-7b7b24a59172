---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/持续改进报告

## POST 创建新的持续改进报告

POST /whiskerguardviolationservice/api/continuous/improvement/reports

描述：创建新的持续改进报告。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "reportCycle": "string",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "summary": "string",
  "improveSummary": "string",
  "improveInvest": "string",
  "improveProcess": "string",
  "improveAchievement": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ContinuousImprovementReportDTO](#schemacontinuousimprovementreportdto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "",
  "reportCode": "",
  "reportType": "",
  "reportCycle": "",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "",
  "level": "",
  "summary": "",
  "improveSummary": "",
  "improveInvest": "",
  "improveProcess": "",
  "improveAchievement": "",
  "metadata": "",
  "version": 0,
  "createdBy": "",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": false,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "",
      "filePath": "",
      "fileType": "",
      "fileSize": "",
      "fileDesc": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": false
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityContinuousImprovementReportDTO](#schemaresponseentitycontinuousimprovementreportdto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_ContinuousImprovementAttachmentDTO">ContinuousImprovementAttachmentDTO</h2>

<a id="schemacontinuousimprovementattachmentdto"></a>
<a id="schema_ContinuousImprovementAttachmentDTO"></a>
<a id="tocScontinuousimprovementattachmentdto"></a>
<a id="tocscontinuousimprovementattachmentdto"></a>

```json
{
  "id": 0,
  "relatedId": 0,
  "relatedType": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedId|integer(int64)|true|none||关联ID|
|relatedType|integer|true|none||关联类型：1、经验教训 2、改进措施 3、优化报告|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_ResponseEntityContinuousImprovementReportDTO">ResponseEntityContinuousImprovementReportDTO</h2>

<a id="schemaresponseentitycontinuousimprovementreportdto"></a>
<a id="schema_ResponseEntityContinuousImprovementReportDTO"></a>
<a id="tocSresponseentitycontinuousimprovementreportdto"></a>
<a id="tocsresponseentitycontinuousimprovementreportdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "reportCycle": "string",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "summary": "string",
  "improveSummary": "string",
  "improveInvest": "string",
  "improveProcess": "string",
  "improveAchievement": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|true|none||报告编号|
|reportType|string|false|none||报告类型：系统升级、培训教育、流程优化、政策更新|
|reportCycle|string|false|none||报告周期|
|startDate|[Instant](#schemainstant)|false|none||覆盖开始时间|
|endDate|[Instant](#schemainstant)|false|none||覆盖结束时间|
|employeeId|integer(int64)|true|none||编制人id|
|orgId|integer(int64)|true|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||保密级别：普通、内部、保密、机密|
|summary|string|false|none||报告摘要|
|improveSummary|string|false|none||改进工作概述|
|improveInvest|string|false|none||改进资源投入|
|improveProcess|string|false|none||改进进度总览|
|improveAchievement|string|false|none||主要成果|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ContinuousImprovementAttachmentDTO](#schemacontinuousimprovementattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|reportType|SYSTEM_UPGRADE|
|reportType|TRAINING_EDUCATION|
|reportType|PROCESS_OPTIMIZATION|
|reportType|POLICY_UPDATE|
|level|ORDINARY|
|level|INTERNAL|
|level|CONFIDENTIAL|
|level|MOST_CONFIDENTIAL|

<h2 id="tocS_ContinuousImprovementReportDTO">ContinuousImprovementReportDTO</h2>

<a id="schemacontinuousimprovementreportdto"></a>
<a id="schema_ContinuousImprovementReportDTO"></a>
<a id="tocScontinuousimprovementreportdto"></a>
<a id="tocscontinuousimprovementreportdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "reportCycle": "string",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "summary": "string",
  "improveSummary": "string",
  "improveInvest": "string",
  "improveProcess": "string",
  "improveAchievement": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|true|none||报告编号|
|reportType|string|false|none||报告类型：系统升级、培训教育、流程优化、政策更新|
|reportCycle|string|false|none||报告周期|
|startDate|[Instant](#schemainstant)|false|none||覆盖开始时间|
|endDate|[Instant](#schemainstant)|false|none||覆盖结束时间|
|employeeId|integer(int64)|true|none||编制人id|
|orgId|integer(int64)|true|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||保密级别：普通、内部、保密、机密|
|summary|string|false|none||报告摘要|
|improveSummary|string|false|none||改进工作概述|
|improveInvest|string|false|none||改进资源投入|
|improveProcess|string|false|none||改进进度总览|
|improveAchievement|string|false|none||主要成果|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ContinuousImprovementAttachmentDTO](#schemacontinuousimprovementattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|reportType|SYSTEM_UPGRADE|
|reportType|TRAINING_EDUCATION|
|reportType|PROCESS_OPTIMIZATION|
|reportType|POLICY_UPDATE|
|level|ORDINARY|
|level|INTERNAL|
|level|CONFIDENTIAL|
|level|MOST_CONFIDENTIAL|

