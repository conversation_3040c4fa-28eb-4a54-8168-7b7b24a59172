<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()

// 表单数据
const formData = ref({
  regulationId: 0,
  auditType: '',
  commitBy: '',
  auditBy: '',
  deadlineTime: '',
  opinion: '',
  director: 0,
  explain: '',
  status: 'REVIEWING',
  attachments: [],
})

// 审查类型选项
const auditTypeOptions = [
  { label: '常规审查', value: 'CONVENTION' },
  { label: '紧急审查', value: 'EMERGENCY' },
]

// 状态选项
const statusOptions = [
  { label: '通过', value: 'PASS' },
  { label: '有条件通过', value: 'CONDITIONALPASS' },
  { label: '不通过', value: 'NOPASS' },
  { label: '待审查', value: 'REVIEWING' },
  { label: '过期', value: 'EXPIRED' },
]

// 表单验证规则
const rules: FormRules = {
  regulationId: [
    { required: true, message: '请输入制度ID', trigger: 'change' },
  ],
  auditType: [
    { required: true, message: '请选择审查类型', trigger: 'change' },
  ],
  commitBy: [
    { required: true, message: '请输入提交人', trigger: 'blur' },
  ],
  auditBy: [
    { required: true, message: '请输入审核人', trigger: 'blur' },
  ],
  deadlineTime: [
    { required: true, message: '请选择截止日期', trigger: 'change' },
  ],
  director: [
    { required: true, message: '请输入负责人ID', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
}

// 是否为编辑模式
const isEdit = ref(false)
const recordId = ref('')

// 获取审核记录详情（编辑模式）
function getRecordDetail() {
  try {
    const id = route.params.id || route.query.id
    if (!id) {
      return
    }

    recordId.value = id as string
    isEdit.value = true

    systemApi.reviewSystem({ id }, 'info').then((res) => {
      if (res.code === 200) {
        const data = res.data
        formData.value = {
          regulationId: data.regulationId || 0,
          auditType: data.auditType || '',
          commitBy: data.commitBy || '',
          auditBy: data.auditBy || '',
          deadlineTime: data.deadlineTime || '',
          opinion: data.opinion || '',
          director: data.director || 0,
          explain: data.explain || '',
          status: data.status || 'REVIEWING',
          attachments: data.attachments || [],
        }
      }
    })
  }
  catch (error) {
    console.error('获取审核记录详情失败:', error)
    ElMessage.error('获取审核记录详情失败')
  }
}

// 提交表单
function submitForm() {
  if (!formRef.value) {
    return
  }

  formRef.value.validate().then(() => {
    const params = {
      ...formData.value,
      ...(isEdit.value ? { id: recordId.value } : {}),
    }

    const action = isEdit.value ? 'update' : 'create'
    systemApi.reviewSystem(params, action).then((res) => {
      if (res.code === 200) {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        router.push('/system/review')
      }
    }).catch((error) => {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    })
  }).catch((error) => {
    console.error('表单验证失败:', error)
  })
}

// 重置表单
function resetForm() {
  if (!formRef.value) {
    return
  }
  formRef.value.resetFields()
}

// 返回列表
function goBack() {
  router.push('/system/review')
}

onMounted(() => {
  getRecordDetail()
})
</script>

<template>
  <div class="absolute-container">
    <PageMain>
      <el-card shadow="hover">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">
              {{ isEdit ? '编辑审核记录' : '新增审核记录' }}
            </h3>
            <el-button v-auth="['systemAdditionAndReview/addEdit/returnToList']" @click="goBack">
              返回列表
            </el-button>
          </div>
        </template>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          class="max-w-2xl"
        >
          <el-form-item label="制度ID" prop="regulationId">
            <el-input-number
              v-model="formData.regulationId"
              placeholder="请输入制度ID"
              :disabled="isEdit"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="审查类型" prop="auditType">
            <el-select
              v-model="formData.auditType"
              placeholder="请选择审查类型"
              style="width: 100%"
            >
              <el-option
                v-for="option in auditTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="提交人" prop="commitBy">
            <el-input
              v-model="formData.commitBy"
              placeholder="请输入提交人"
            />
          </el-form-item>
          
          <el-form-item label="审核人" prop="auditBy">
            <el-input
              v-model="formData.auditBy"
              placeholder="请输入审核人"
            />
          </el-form-item>
          
          <el-form-item label="截止日期" prop="deadlineTime">
            <el-date-picker
              v-model="formData.deadlineTime"
              type="datetime"
              placeholder="请选择截止日期"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          
          <el-form-item label="负责人ID" prop="director">
            <el-input-number
              v-model="formData.director"
              placeholder="请输入负责人ID"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="说明">
            <el-input
              v-model="formData.explain"
              type="textarea"
              :rows="3"
              placeholder="请输入说明"
            />
          </el-form-item>
          
          <el-form-item label="审核意见">
            <el-input
              v-model="formData.opinion"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见"
            />
          </el-form-item>
          
          <el-form-item label="附件">
            <el-upload
              v-model:file-list="formData.attachments"
              action="#"
              :auto-upload="false"
              multiple
              :show-file-list="true"
            >
              <el-button v-auth="['systemAdditionAndReview/addEdit/selectFile']" type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持多文件上传
                </div>
              </template>
            </el-upload>
          </el-form-item>
          
          <el-form-item>
            <el-button v-auth="['systemAdditionAndReview/addEdit/createOrUpdate']" type="primary" @click="submitForm">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
            <el-button v-auth="['systemAdditionAndReview/addEdit/reset']" @click="resetForm">
              重置
            </el-button>
            <el-button v-auth="['systemAdditionAndReview/addEdit/cancel']" @click="goBack">
              取消
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
.max-w-2xl {
  max-width: 42rem;
}
</style>