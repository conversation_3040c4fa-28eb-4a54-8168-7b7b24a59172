<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { Search } from '@element-plus/icons-vue'

const activeMenu = ref('1')
const activeTab = ref('1')
const searchQuery = ref('')
const selectAll = ref(false)
const selectedItems = ref([])

const filter = ref({
  type: '',
  source: '',
  department: '',
  dateRange: [],
  keyword: '',
})

const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 100,
})

const tableData = ref([
  {
    id: 'LL-2023-001',
    title: '财务报销流程中的审批漏洞',
    type: '流程问题',
    source: '内部审计',
    severity: '严重',
    department: '财务部',
    date: '2023-05-12',
    views: 156,
    status: '已发布',
  },
  {
    id: 'LL-2023-002',
    title: '新员工入职培训材料过时',
    type: '人员问题',
    source: '员工反馈',
    severity: '中等',
    department: '人力资源部',
    date: '2023-06-05',
    views: 89,
    status: '已发布',
  },
  {
    id: 'LL-2023-003',
    title: '客户数据备份策略不足',
    type: '系统问题',
    source: '外部检查',
    severity: '严重',
    department: '信息技术部',
    date: '2023-04-18',
    views: 112,
    status: '已发布',
  },
  {
    id: 'LL-2023-004',
    title: '合同审批权限设置不合理',
    type: '制度问题',
    source: '法务部自查',
    severity: '中等',
    department: '法务部',
    date: '2023-07-22',
    views: 67,
    status: '已发布',
  },
  {
    id: 'LL-2023-005',
    title: '供应商评估标准不明确',
    type: '其他',
    source: '采购部自查',
    severity: '中等',
    department: '采购部',
    date: '2023-08-15',
    views: 54,
    status: '草稿',
  },
])

const stats = ref([
  {
    title: '经验教训总数',
    value: 128,
    icon: 'fas fa-book',
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600',
  },
  {
    title: '改进措施总数',
    value: 89,
    icon: 'fas fa-tasks',
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600',
  },
  {
    title: '完成措施数',
    value: 56,
    icon: 'fas fa-check-circle',
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600',
  },
  {
    title: '优化报告数',
    value: 23,
    icon: 'fas fa-file-alt',
    bgColor: 'bg-yellow-50',
    iconColor: 'text-yellow-600',
  },
])

const hotItems = ref([
  {
    id: 1,
    title: '财务报销流程中的审批漏洞',
    department: '财务部',
    views: 156,
    likes: 32,
  },
  {
    id: 2,
    title: '新员工入职培训材料过时',
    department: '人力资源部',
    views: 89,
    likes: 18,
  },
  {
    id: 3,
    title: '客户数据备份策略不足',
    department: '信息技术部',
    views: 112,
    likes: 25,
  },
])

const typeChart = ref<HTMLElement>()
const departmentChart = ref<HTMLElement>()

function getTagType(type: string) {
  const types: Record<string, string> = {
    流程问题: '',
    人员问题: 'warning',
    制度问题: 'danger',
    系统问题: 'success',
    其他: 'info',
  }
  return types[type] || ''
}

function getSeverityTagType(severity: string) {
  const types: Record<string, string> = {
    严重: 'danger',
    中等: 'warning',
    轻微: 'success',
  }
  return types[severity] || ''
}

function getStatusTagType(status: string) {
  const types: Record<string, string> = {
    已发布: 'success',
    草稿: 'info',
  }
  return types[status] || ''
}

function handleSelectionChange(val: any[]) {
  selectedItems.value = val
}

function initCharts() {
  // 类型分布图表
  const typeChartInstance = echarts.init(typeChart.value)
  typeChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '改进类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 40, name: '流程问题' },
          { value: 30, name: '人员问题' },
          { value: 20, name: '制度问题' },
          { value: 10, name: '系统问题' },
          { value: 28, name: '其他' },
        ],
      },
    ],
  })

  // 部门分布图表
  const departmentChartInstance = echarts.init(departmentChart.value)
  departmentChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['财务部', '人力资源部', '信息技术部', '市场部', '法务部', '采购部'],
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [12, 19, 15, 8, 7, 5],
        itemStyle: {
          color(params: any) {
            const colorList = ['#1E88E5', '#4CAF50', '#9C27B0', '#FF9800', '#E91E63', '#607D8B']
            return colorList[params.dataIndex]
          },
        },
      },
    ],
  })

  window.addEventListener('resize', () => {
    typeChartInstance.resize()
    departmentChartInstance.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              持续改进优化
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-4">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <i class="fas fa-plus" />
              </el-icon>
              新增改进措施
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <i class="fas fa-plus" />
              </el-icon>
              新增经验教训
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <i class="fas fa-file-export" />
              </el-icon>
              生成优化报告
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-2">
                <i class="fas fa-chart-bar" />
              </el-icon>
              统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <!-- 筛选区域 -->
        <el-card class="mb-6">
          <div class="flex items-center space-x-4">
            <el-select v-model="filter.type" placeholder="所有类型" class="flex-1">
              <el-option label="流程问题" value="1" />
              <el-option label="人员问题" value="2" />
              <el-option label="制度问题" value="3" />
              <el-option label="系统问题" value="4" />
            </el-select>

            <el-select v-model="filter.source" placeholder="所有来源" class="flex-1">
              <el-option label="内部审计" value="1" />
              <el-option label="外部检查" value="2" />
              <el-option label="员工反馈" value="3" />
            </el-select>

            <el-select v-model="filter.department" placeholder="所有部门" class="flex-1">
              <el-option label="财务部" value="1" />
              <el-option label="人力资源部" value="2" />
              <el-option label="信息技术部" value="3" />
            </el-select>

            <el-date-picker
              v-model="filter.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" class="flex-1"
            />

            <el-input v-model="filter.keyword" placeholder="搜索关键字..." class="flex-1" :prefix-icon="Search" />

            <el-button type="text" class="text-primary">
              高级筛选
              <el-icon class="el-icon--right">
                <i class="fas fa-chevron-down" />
              </el-icon>
            </el-button>
          </div>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="mt-20">
          <div class="mb-4 flex justify-between">
            <div class="flex items-center">
              <el-button-group>
                <el-button type="primary">
                  表格视图
                </el-button>
                <el-button>卡片视图</el-button>
              </el-button-group>
            </div>
            <div class="flex items-center space-x-2">
              <el-button text circle>
                <el-icon><i class="fas fa-download" /></el-icon>
              </el-button>
              <el-button text circle>
                <el-icon><i class="fas fa-print" /></el-icon>
              </el-button>
            </div>
          </div>

          <el-table :data="tableData" style="width: 100%;" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="编号" width="120" />
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="type" label="类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getTagType(row.type)">
                  {{ row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" width="120" />
            <el-table-column prop="severity" label="严重程度" width="120">
              <template #default="{ row }">
                <el-tag :type="getSeverityTagType(row.severity)">
                  {{ row.severity }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="责任部门" width="120" />
            <el-table-column prop="date" label="创建日期" width="120" />
            <el-table-column prop="views" label="阅读量" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #default>
                <el-button type="text">
                  查看
                </el-button>
                <el-button type="text">
                  编辑
                </el-button>
                <el-dropdown>
                  <el-button type="text">
                    <el-icon><i class="fas fa-ellipsis-v" /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>删除</el-dropdown-item>
                      <el-dropdown-item>归档</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4 flex items-center justify-between">
            <div>
              <span class="text-sm text-gray-500">
                显示第 {{ pagination.currentPage }} 至 {{ pagination.pageSize }} 条，共 {{ pagination.total }} 条记录
              </span>
            </div>
            <el-pagination
              v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 30, 50]" layout="prev, pager, next, sizes" :total="pagination.total"
            />
          </div>
        </el-card>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-4 mt-20 gap-6">
          <el-card v-for="stat in stats" :key="stat.title" shadow="hover">
            <div class="flex items-center">
              <div class="mr-4 rounded-full p-3" :class="stat.bgColor">
                <el-icon :class="stat.iconColor">
                  <i :class="stat.icon" />
                </el-icon>
              </div>
              <div>
                <p class="text-sm text-gray-500 font-medium">
                  {{ stat.title }}
                </p>
                <p class="text-2xl text-gray-900 font-semibold">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </el-card>
        </div>
        <!-- 图表区域 -->
        <div class="grid grid-cols-2 mt-20 gap-6">
          <el-card>
            <template #header>
              <div class="f-16 fw-600">
                改进类型分布
              </div>
            </template>
            <div ref="typeChart" class="h-64" />
          </el-card>
          <el-card>
            <template #header>
              <div class="f-16 fw-600">
                部门分布
              </div>
            </template>
            <div ref="departmentChart" class="h-64" />
          </el-card>
        </div>
        <!-- 热门经验教训 -->
        <el-card class="mt-20">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="f-16 fw-600">
                热门经验教训
              </div>
              <el-link type="primary">
                查看全部
              </el-link>
            </div>
          </template>
          <div class="space-y-4">
            <div v-for="item in hotItems" :key="item.id" class="flex items-start">
              <div class="mr-4 flex-shrink-0 rounded-full bg-blue-50 p-2 text-blue-600">
                <el-icon><i class="fas fa-exclamation-circle" /></el-icon>
              </div>
              <div class="min-w-0 flex-1">
                <p class="truncate text-sm text-gray-900 font-medium">
                  {{ item.title }}
                </p>
                <p class="text-sm text-gray-500">
                  {{ item.department }} · {{ item.views }}次阅读 · {{ item.likes }}点赞
                </p>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .badge-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background-color: #f56c6c;
    border-radius: 50%;
  }
</style>
