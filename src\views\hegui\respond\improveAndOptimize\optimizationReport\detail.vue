<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  Calendar,
  Document,
  Download,
  Edit,
  Message,
  More,
  Printer,
  Share,
  Star,
  View,
} from '@element-plus/icons-vue'
import continuousApi from '@/api/problemTask/continuous'
import dictApi from '@/api/modules/system/dict'

const activeTab = ref('data')
const rating = ref(4.5)
const loading = ref(false)
const route = useRoute()
const router = useRouter()
const reportId = ref(route.query.id ? Number(route.query.id) : null)

// 字典选项
const reportTypeOptions = ref<Array<{ label: string, value: string }>>([])
const levelOptions = ref<Array<{ label: string, value: string }>>([])

// 报告详情数据
const reportDetail = ref({
  id: null,
  title: '',
  reportCode: '',
  reportType: '',
  reportCycle: '',
  startDate: '',
  endDate: '',
  employeeId: '',
  orgId: null,
  establishDate: '',
  level: '',
  summary: '',
  improveSummary: '',
  improveInvest: '',
  improveProcess: '',
  improveAchievement: '',
  metadata: '',
  version: 0,
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
  isDeleted: false,
  attachmentList: [],
  // 扩展字段
  creatorName: '',
  departmentName: '',
  statusText: '',
  viewCount: 0,
})

// 表格数据 - 将从接口获取
const tableData = ref([])

// 附件数据 - 将从接口获取
const attachmentData = ref([])

function statusTagType(status: string) {
  switch (status) {
    case '已完成': return 'success'
    case '进行中': return 'warning'
    case '待审核': return ''
    default: return 'info'
  }
}

// const improvementChart = ref<HTMLElement>()
const measuresChart = ref<HTMLElement>()
const departmentsChart = ref<HTMLElement>()

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取报告类型选项 (92)
    const typeResponse = await dictApi.dictAll(92)
    if (typeResponse && Array.isArray(typeResponse)) {
      reportTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取保密级别选项 (41)
    const levelResponse = await dictApi.dictAll(41)
    if (levelResponse && Array.isArray(levelResponse)) {
      levelOptions.value = levelResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

// 映射显示函数
function getReportTypeLabel(value: string) {
  const option = reportTypeOptions.value.find((item: any) => item.value === value)
  return option ? option.label : value
}

function getLevelLabel(value: string) {
  const option = levelOptions.value.find((item: any) => item.value === value)
  return option ? option.label : value
}

// 获取报告详情
async function fetchReportDetail() {
  if (!reportId.value) {
    ElMessage.error('缺少报告ID参数')
    return
  }

  try {
    loading.value = true
    const response = await continuousApi.getReportDetail(reportId.value)
    if (response) {
      Object.assign(reportDetail.value, {
        ...response,
        // 格式化日期显示
        startDate: response.startDate ? new Date(response.startDate).toLocaleDateString() : '',
        endDate: response.endDate ? new Date(response.endDate).toLocaleDateString() : '',
        establishDate: response.establishDate,
        // 处理附件数据
        attachmentList: response.attachmentList || [],
      })

      // 更新附件表格数据
      if (response.attachmentList && response.attachmentList.length > 0) {
        attachmentData.value = response.attachmentList.map((item: any) => ({
          name: item.fileName || '',
          type: item.fileType || '',
          uploadTime: item.createdAt,
          uploader: item.createdBy || '',
        }))
      }
    }
  }
  catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
  finally {
    loading.value = false
  }
}

// 编辑报告
function handleEdit() {
  router.push({
    path: '/hegui/respond/improveAndOptimize/optimizationReport/addEdit',
    query: { id: reportId.value },
  })
}

onMounted(() => {
  fetchDictOptions()
  fetchReportDetail()
  nextTick(() => {
    initCharts()
  })
})

function initCharts() {
  // 改进概况图表
  // const improvementChartInstance = echarts.init(improvementChart.value)
  // improvementChartInstance.setOption({
  //   animation: false,
  //   tooltip: {
  //     trigger: 'item',
  //   },
  //   legend: {
  //     top: '5%',
  //     left: 'center',
  //   },
  //   series: [
  //     {
  //       name: '改进措施分布',
  //       type: 'pie',
  //       radius: ['40%', '70%'],
  //       avoidLabelOverlap: false,
  //       itemStyle: {
  //         borderRadius: 10,
  //         borderColor: '#fff',
  //         borderWidth: 2,
  //       },
  //       label: {
  //         show: false,
  //         position: 'center',
  //       },
  //       emphasis: {
  //         label: {
  //           show: true,
  //           fontSize: '18',
  //           fontWeight: 'bold',
  //         },
  //       },
  //       labelLine: {
  //         show: false,
  //       },
  //       data: [
  //         { value: 6, name: '反洗钱系统' },
  //         { value: 8, name: '数据隐私' },
  //         { value: 7, name: '员工行为' },
  //         { value: 4, name: '合规培训' },
  //         { value: 3, name: '风险评估' },
  //       ],
  //     },
  //   ],
  // })

  // 改进措施状态图表
  const measuresChartInstance = echarts.init(measuresChart.value)
  measuresChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['已完成', '进行中', '待审核', '延期'],
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [18, 5, 3, 2],
        itemStyle: {
          color(params: any) {
            const colorList = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']
            return colorList[params.dataIndex]
          },
        },
      },
    ],
  })

  // 部门完成率图表
  const departmentsChartInstance = echarts.init(departmentsChart.value)
  departmentsChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['合规部', 'IT部', '人力资源部', '财务部', '市场部'],
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        data: [95, 85, 78, 65, 60],
        itemStyle: {
          color(params: any) {
            const value = params.data
            if (value >= 90) {
              return '#10B981'
            }
            else if (value >= 70) {
              return '#3B82F6'
            }
            else {
              return '#F59E0B'
            }
          },
        },
      },
    ],
  })

  window.addEventListener('resize', () => {
    measuresChartInstance.resize()
    departmentsChartInstance.resize()
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ reportDetail.title || '优化报告详情' }}
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-2">
            <el-button v-auth="'optimizationReport/detail/edit'" type="primary" class="rounded-button whitespace-nowrap" @click="handleEdit">
              <el-icon>
                <Edit />
              </el-icon>
              <span>编辑</span>
            </el-button>

            <el-dropdown>
              <el-button v-auth="['optimizationReport/detail/export']" type="primary" class="rounded-button whitespace-nowrap">
                <el-icon>
                  <Download />
                </el-icon>
                <span>导出</span>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportPDF'">
                    PDF格式
                  </el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportWord'">
                    Word格式
                  </el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/exportPPT'">
                    PPT格式
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button v-auth="'optimizationReport/detail/print'" type="primary" class="rounded-button whitespace-nowrap">
              <el-icon>
                <Printer />
              </el-icon>
              <span>打印</span>
            </el-button>

            <el-button v-auth="'optimizationReport/detail/share'" type="primary" class="rounded-button whitespace-nowrap">
              <el-icon>
                <Share />
              </el-icon>
              <span>分享</span>
            </el-button>

            <el-dropdown>
              <el-button v-auth="'optimizationReport/detail/archive'" type="primary" class="rounded-button whitespace-nowrap">
                <el-icon>
                  归档报告
                </el-icon>
              </el-button>
              <el-button v-auth="'optimizationReport/detail/delete'" type="primary" class="rounded-button whitespace-nowrap">
                <el-icon>
                  删除报告
                </el-icon>
              </el-button>
<<<<<<< HEAD
=======
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="'optimizationReport/detail/archive'">
                    归档报告
                  </el-dropdown-item>
                  <el-dropdown-item v-auth="'optimizationReport/detail/delete'">
                    删除报告
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
>>>>>>> f747218adcb88bf5c8c17d0a28a8ea91ea29a0df
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="24">
            <!-- 基本信息区 -->
            <el-card shadow="hover" class="">
              <template #header>
                <h2 class="text-lg text-gray-800 font-semibold">
                  报告基本信息
                </h2>
              </template>
              <div class="grid grid-cols-2 gap-4">
                <div class="flex">
                  <span class="w-24 text-gray-500">报告编号</span>
                  <span class="text-gray-800">{{ reportDetail.reportCode || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告标题</span>
                  <span class="text-gray-800">{{ reportDetail.title || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告类型</span>
                  <span class="text-gray-800">{{ reportDetail.reportType ? getReportTypeLabel(reportDetail.reportType) : '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">报告周期</span>
                  <span class="text-gray-800">{{ reportDetail.reportCycle || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">覆盖时间</span>
                  <span class="text-gray-800">{{ reportDetail.startDate && reportDetail.endDate ? `${reportDetail.startDate} 至 ${reportDetail.endDate}` : '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">编制人</span>
                  <span class="text-gray-800">{{ reportDetail.creatorName || reportDetail.createdBy || '-' }} {{ reportDetail.departmentName ? `(${reportDetail.departmentName})` : '' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">编制日期</span>
                  <span class="text-gray-800">{{ reportDetail.establishDate || '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">保密级别</span>
                  <span class="text-gray-800">{{ reportDetail.level ? getLevelLabel(reportDetail.level) : '-' }}</span>
                </div>
                <div class="flex">
                  <span class="w-24 text-gray-500">阅读量</span>
                  <span class="text-gray-800">{{ reportDetail.viewCount || 0 }}</span>
                </div>
              </div>
            </el-card>

            <!-- 报告内容区 -->
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-xl text-gray-800 font-bold">
                优化报告摘要
              </h2>
              <div class="text-gray-700 space-y-4">
                <div v-if="reportDetail.summary" class="whitespace-pre-wrap">
                  {{ reportDetail.summary }}
                </div>
                <div v-else class="text-gray-400">
                  暂无报告摘要
                </div>
              </div>
            </el-card>

            <!-- 详细内容部分 -->
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-xl text-gray-800 font-bold">
                一、改进概况
              </h2>
              <div class="text-gray-700 space-y-4">
                <div v-if="reportDetail.improveSummary">
                  <h3 class="mb-2 font-semibold">
                    改进工作概述
                  </h3>
                  <div class="mb-4 whitespace-pre-wrap">
                    {{ reportDetail.improveSummary }}
                  </div>
                </div>
                <div v-if="reportDetail.improveInvest">
                  <h3 class="mb-2 font-semibold">
                    改进资源投入
                  </h3>
                  <div class="mb-4 whitespace-pre-wrap">
                    {{ reportDetail.improveInvest }}
                  </div>
                </div>
                <div v-if="reportDetail.improveProcess">
                  <h3 class="mb-2 font-semibold">
                    改进进度总览
                  </h3>
                  <div class="mb-4 whitespace-pre-wrap">
                    {{ reportDetail.improveProcess }}
                  </div>
                </div>
                <div v-if="reportDetail.improveAchievement">
                  <h3 class="mb-2 font-semibold">
                    主要成果一览
                  </h3>
                  <div class="mb-4 whitespace-pre-wrap">
                    {{ reportDetail.improveAchievement }}
                  </div>
                </div>
                <div v-if="!reportDetail.improveSummary && !reportDetail.improveInvest && !reportDetail.improveProcess && !reportDetail.improveAchievement" class="text-gray-400">
                  暂无改进概况信息
                </div>
                <!-- <div ref="improvementChart" class="mt-6 h-80" /> -->
              </div>
            </el-card>

            <!-- 经验教训分析 -->
            <el-card shadow="hover" class="mt-20">
              <h2 class="mb-4 text-xl text-gray-800 font-bold">
                二、经验教训分析
              </h2>
              <div class="text-gray-700 space-y-4">
                <div v-if="reportDetail.metadata" class="whitespace-pre-wrap">
                  {{ reportDetail.metadata }}
                </div>
                <div v-else class="text-gray-400">
                  暂无经验教训分析
                </div>
              </div>
            </el-card>

            <!-- 标签页区 -->
            <el-card v-if="false" shadow="hover" class="mt-20">
              <el-tabs v-model="activeTab">
                <el-tab-pane label="改进数据" name="data">
                  <h3 class="mb-4 text-lg text-gray-800 font-semibold">
                    改进措施数据
                  </h3>
                  <el-table :data="tableData" style="width: 100%;">
                    <el-table-column prop="name" label="措施名称" />
                    <el-table-column prop="type" label="类型" />
                    <el-table-column prop="department" label="责任部门" />
                    <el-table-column prop="status" label="状态">
                      <template #default="{ row }">
                        <el-tag :type="statusTagType(row.status) || 'info'" effect="light">
                          {{ row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="completion" label="完成率" />
                  </el-table>
                  <div class="grid grid-cols-2 mt-6 gap-6">
                    <div ref="measuresChart" class="h-64" />
                    <div ref="departmentsChart" class="h-64" />
                  </div>
                </el-tab-pane>
                <el-tab-pane label="部门分析" name="department">
                  部门分析
                </el-tab-pane>
                <el-tab-pane label="反馈意见" name="feedback">
                  反馈意见
                </el-tab-pane>
                <el-tab-pane label="历史对比" name="history">
                  历史对比
                </el-tab-pane>
              </el-tabs>
            </el-card>

            <!-- 相关资料区 -->
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <h2 class="text-lg text-gray-800 font-semibold">
                  相关资料
                </h2>
              </template>
              <el-table :data="attachmentData">
                <el-table-column prop="name" label="附件名称" />
                <el-table-column prop="type" label="类型" />
                <el-table-column prop="uploadTime" label="上传时间" />
                <el-table-column prop="uploader" label="上传人" />
                <el-table-column label="操作">
                  <template #default>
                    <el-link v-auth="'optimizationReport/detail/viewAttachment'" type="primary" :underline="false" class="mr-3">
                      查看
                    </el-link>
                    <el-link v-auth="'optimizationReport/detail/downloadAttachment'" type="primary" :underline="false">
                      下载
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col v-if="false" :span="6">
            <!-- 报告评价模块 -->
            <el-card v-if="false" shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  报告评价
                </div>
              </template>
              <div class="mb-2 flex items-center">
                <el-rate v-model="rating" disabled show-score text-color="#ff9900" score-template="{value} (12人评价)" />
              </div>
              <el-button v-auth="'optimizationReport/detail/rate'" type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon>
                  <Star />
                </el-icon>
                <span>评价报告</span>
              </el-button>
            </el-card>

            <!-- 分享信息模块 -->
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  分享信息
                </div>
              </template>
              <div class="mb-2 flex items-center">
                <el-icon>
                  <Share />
                </el-icon>
                <span class="ml-2 text-sm text-gray-600">已分享 24 次</span>
              </div>
              <div class="mb-4 flex items-center">
                <el-icon>
                  <View />
                </el-icon>
                <span class="ml-2 text-sm text-gray-600">已查看 124 次</span>
              </div>
              <h3 class="mb-2 text-sm text-gray-700 font-medium">
                最近查看
              </h3>
              <el-avatar-group :max="4">
                <el-avatar src="https://randomuser.me/api/portraits/women/12.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/men/32.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/women/45.jpg" />
                <el-avatar src="https://randomuser.me/api/portraits/men/76.jpg" />
              </el-avatar-group>
            </el-card>

            <!-- 相关报告模块 -->
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关报告
                </div>
              </template>
              <div class="space-y-3">
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">2023年第三季度合规优化报告</span>
                    <span class="text-xs text-gray-500">2023-09-30</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    季度报告
                  </el-tag>
                </el-link>
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">2023年反洗钱专项报告</span>
                    <span class="text-xs text-gray-500">2023-11-15</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    专项报告
                  </el-tag>
                </el-link>
                <el-link :underline="false" class="group block">
                  <div class="flex items-start justify-between">
                    <span class="group-hover:text-primary text-sm text-gray-700 font-medium">数据隐私合规评估报告</span>
                    <span class="text-xs text-gray-500">2023-10-20</span>
                  </div>
                  <el-tag size="small" class="mt-1">
                    评估报告
                  </el-tag>
                </el-link>
              </div>
            </el-card>

            <!-- 快捷操作模块 -->
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-2">
                <div>
                  <el-button v-auth="'optimizationReport/detail/exportPDF'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#F56C6C">
                      <Document />
                    </el-icon>
                    <span>导出PDF</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/generatePPT'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#E6A23C">
                      <Document />
                    </el-icon>
                    <span>生成PPT</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/sendEmail'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#409EFF">
                      <Message />
                    </el-icon>
                    <span>发送邮件</span>
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'optimizationReport/detail/scheduleMeeting'" class="rounded-button w-full whitespace-nowrap text-left">
                    <el-icon color="#67C23A">
                      <Calendar />
                    </el-icon>
                    <span>召开会议</span>
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style scoped>
  .font-pacifico {
    font-family: Pacifico, serif;
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    margin: 0;
    appearance: none;
  }

  input[type="number"] {
    appearance: textfield;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
