import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTaskStore = defineStore('task', () => {
  // 当前创建的任务ID
  const currentTaskId = ref<string | null>(null)

  // 设置任务ID
  function setTaskId(taskId: string | null) {
    currentTaskId.value = taskId
  }

  // 获取任务ID
  function getTaskId() {
    return currentTaskId.value
  }

  // 清空任务ID
  function clearTaskId() {
    currentTaskId.value = null
  }

  return {
    currentTaskId,
    setTaskId,
    getTaskId,
    clearTaskId,
  }
})