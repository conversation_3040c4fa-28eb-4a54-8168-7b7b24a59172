<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import continuousApi from '@/api/problemTask/continuous'
import dictApi from '@/api/modules/system/dict'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import { disablePastDates } from '@/utils/dateUtils'

const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInput = ref()
const activeCollapse = ref(['1', '2', '3', '4'])
const loading = ref(false)
const router = useRouter()
const route = useRoute()
const reportId = ref(route.query.id ? Number(route.query.id) : null)

// 字典选项
const reportTypeOptions = ref([
  { value: 'monthly', label: '月度报告' },
  { value: 'quarterly', label: '季度报告' },
  { value: 'annual', label: '年度报告' },
])
const _reportCycleOptions = ref([
  { value: 'monthly', label: '月度' },
  { value: 'quarterly', label: '季度' },
  { value: 'annual', label: '年度' },
])
const levelOptions = ref([
  { value: '', label: '' },
])

// 附件类型定义
interface AttachmentItem {
  id?: number | null
  relatedId?: number
  relatedType?: number
  fileName: string
  filePath: string
  fileType: string
  fileSize?: string
  fileDesc?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
}

const form = reactive({
  // 接口字段 - 完全按照API文档定义
  id: null,
  title: '',
  reportCode: '',
  reportType: '',
  reportCycle: '',
  startDate: '',
  endDate: '',
  employeeId: '',
  orgId: null,
  establishDate: '',
  level: '',
  summary: '',
  improveSummary: '',
  improveInvest: '',
  improveProcess: '',
  improveAchievement: '',
  metadata: '',
  version: 0,
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
  isDeleted: false,
  attachmentList: [] as AttachmentItem[],

  // 页面显示用的字段（不提交到接口）
  tags: ['合规', '改进', '年度报告'],
  creator: '张合规',
  createDate: '2023-06-15',
})
function handleAttachmentUploadSuccess(_fileInfo: any) {
  // 附件上传成功，循环数据添加attachmentType字段
  form.attachmentList.forEach((item) => {
    if (!item.relatedType) {
      item.relatedType = 3
    }
  })
}
// 保存草稿
async function saveDraft() {
  try {
    loading.value = true
    const submitData = buildSubmitData()

    if (reportId.value) {
      // 编辑模式：调用更新接口
      await continuousApi.updateReport(submitData)
      ElMessage.success('保存成功')
      router.back()
    }
    else {
      // 新增模式：调用创建接口
      await continuousApi.createReport(submitData)
      ElMessage.success('保存成功')
      router.back()
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 获取字典数据
async function fetchDictOptions() {
  try {
    // 获取报告类型选项 (31)
    const typeResponse = await dictApi.dictAll(92)
    if (typeResponse && Array.isArray(typeResponse)) {
      reportTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取保密级别选项 (41)
    const levelResponse = await dictApi.dictAll(41)
    if (levelResponse && Array.isArray(levelResponse)) {
      levelOptions.value = levelResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

// 生成报告编号
async function generateReportCode() {
  try {
    const response = await dictApi.getCode('CONTINUOUS_REPORT')
    if (response) {
      form.reportCode = response
    }
  }
  catch (error) {
    console.error('获取报告编号失败:', error)
    ElMessage.error('获取报告编号失败')
  }
}

// 获取报告详情
async function fetchReportDetail() {
  if (!reportId.value) {
    return
  }

  try {
    loading.value = true
    const response = await continuousApi.getReportDetail(reportId.value)
    if (response) {
      // 将接口返回的数据填充到表单中，并处理日期格式
      Object.assign(form, {
        ...response,
        startDate: response.startDate ? new Date(response.startDate).toISOString().split('T')[0] : '',
        endDate: response.endDate ? new Date(response.endDate).toISOString().split('T')[0] : '',
      })
    }
  }
  catch (error) {
    console.error('获取报告详情失败:', error)
    ElMessage.error('获取报告详情失败')
  }
  finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  fetchDictOptions()
  if (reportId.value) {
    fetchReportDetail()
  }
  else {
    generateReportCode()
  }
})
// 处理返回按钮点击事件
function handleBack() {
  router.back()
}
// 提交审核
async function submitReview() {
  try {
    loading.value = true
    const submitData = buildSubmitData()

    if (reportId.value) {
      // 编辑模式：调用更新接口
      await continuousApi.updateReport(submitData)
      ElMessage.success('报告更新并提交审核成功')
    }
    else {
      // 新增模式：调用创建接口
      await continuousApi.createReport(submitData)
      ElMessage.success('提交审核成功')
    }
  }
  catch (error) {
    console.error('提交审核失败:', error)
    ElMessage.error('提交审核失败')
  }
  finally {
    loading.value = false
  }
}

// 构建提交数据
function buildSubmitData() {
  return {
    id: form.id,
    title: form.title,
    reportCode: form.reportCode,
    reportType: form.reportType,
    reportCycle: form.reportCycle,
    startDate: form.startDate ? new Date(`${form.startDate}T12:04:09Z`).toISOString() : null,
    endDate: form.endDate ? new Date(`${form.endDate}T12:04:09Z`).toISOString() : null,
    employeeId: form.employeeId,
    orgId: form.orgId,
    establishDate: form.establishDate,
    level: form.level,
    summary: form.summary,
    improveSummary: form.improveSummary,
    improveInvest: form.improveInvest,
    improveProcess: form.improveProcess,
    improveAchievement: form.improveAchievement,
    metadata: form.metadata,
    version: form.version,
    createdBy: form.createdBy,
    createdAt: form.createdAt,
    updatedBy: form.updatedBy,
    updatedAt: form.updatedAt,
    isDeleted: form.isDeleted,
    attachmentList: form.attachmentList,
  }
}

function removeTag(tag: string) {
  form.tags = form.tags.filter(t => t !== tag)
}
function showTagInput() {
  inputTagVisible.value = true
  nextTick(() => {
    tagInput.value.focus()
  })
}
function addTag() {
  if (inputTagValue.value) {
    form.tags.push(inputTagValue.value)
    inputTagValue.value = ''
  }
  inputTagVisible.value = false
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ reportId ? '编辑优化报告' : '新增优化报告' }}
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button
              v-auth="'optimizationReport/addEdit/saveDraft'"
              type="primary"
              class="!rounded-button whitespace-nowrap"
              :loading="loading"
              @click="saveDraft"
            >
              保存草稿
            </el-button>
            <el-button
              v-auth="'optimizationReport/addEdit/submitReview'"
              class="!rounded-button whitespace-nowrap"
              :loading="loading"
              @click="submitReview"
            >
              提交审核
            </el-button>
            <el-button v-auth="'optimizationReport/addEdit/cancel'" class="!rounded-button whitespace-nowrap" @click="handleBack">
              取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="!mx-0">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-18 fw-800">
                  基本信息
                </div>
              </template>
              <el-form :model="form" label-width="120px" label-position="right">
                <div class="grid grid-cols-2 gap-6">
                  <el-form-item label="报告标题" required>
                    <el-input v-model="form.title" placeholder="请输入报告标题" />
                  </el-form-item>
                  <el-form-item label="报告编号">
                    <el-input v-model="form.reportCode" placeholder="自动生成" readonly />
                  </el-form-item>
                  <el-form-item label="报告类型" required>
                    <el-select v-model="form.reportType" placeholder="请选择报告类型" class="w-full">
                      <el-option
                        v-for="option in reportTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="报告周期" required>
                    <el-date-picker
                      v-model="form.reportCycle"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择开始日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                    <!-- <el-input v-model="form.reportCycle" placeholder="请输入报告周期" /> -->
                  </el-form-item>
                  <el-form-item label="覆盖开始时间" required>
                    <el-date-picker
                      v-model="form.startDate"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择开始日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                  <el-form-item label="覆盖结束时间" required>
                    <el-date-picker
                      v-model="form.endDate"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择结束日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                  <el-form-item label="编制部门">
                    <DepartmentTreeSelect v-model="form.orgId" placeholder="请选择编制部门" />
                  </el-form-item>
                  <el-form-item label="编制人">
                    <DepartPerson v-model="form.employeeId" placeholder="请选择编制人" />
                  </el-form-item>

                  <el-form-item label="编制日期">
                    <el-date-picker
                      v-model="form.establishDate"
                      :disabled-date="disablePastDates"
                      type="date"
                      placeholder="选择编制日期"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                  <el-form-item label="保密级别">
                    <el-select v-model="form.level" placeholder="请选择保密级别" class="w-full">
                      <el-option
                        v-for="option in levelOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                  <!-- <el-form-item label="报告标签">
                    <el-tag v-for="tag in form.tags" :key="tag" closable class="mr-2" @close="removeTag(tag)">
                      {{ tag }}
                    </el-tag>
                    <el-input
                      v-if="inputTagVisible" ref="tagInput" v-model="inputTagValue" size="small" class="w-28"
                      @keyup.enter="addTag" @blur="addTag"
                    />
                    <el-button v-else v-auth="'optimizationReport/addEdit/addTag'" size="small" @click="showTagInput">
                      + 添加标签
                    </el-button>
                  </el-form-item> -->
                </div>
              </el-form>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告摘要
                </div>
              </template>
              <el-input
                v-model="form.summary" type="textarea" :rows="6" placeholder="请输入报告摘要，建议300-500字"
                show-word-limit maxlength="500"
              />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    一、改进概况
                  </div>
                  <el-button v-auth="'optimizationReport/addEdit/importImprovementData'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                    导入改进数据
                  </el-button>
                </div>
              </template>
              <el-collapse v-model="activeCollapse">
                <el-collapse-item title="改进工作概述" name="1">
                  <el-input v-model="form.improveSummary" type="textarea" :rows="4" placeholder="请输入改进工作概述" />
                </el-collapse-item>
                <el-collapse-item title="改进资源投入" name="2">
                  <el-input v-model="form.improveInvest" type="textarea" :rows="4" placeholder="请输入改进资源投入情况" />
                </el-collapse-item>
                <el-collapse-item title="改进进度总览" name="3">
                  <el-input v-model="form.improveProcess" type="textarea" :rows="4" placeholder="请输入改进进度总览" />
                </el-collapse-item>
                <el-collapse-item title="主要成果一览" name="4">
                  <el-input v-model="form.improveAchievement" type="textarea" :rows="4" placeholder="请输入主要成果一览" />
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-18 fw-800">
                    二、经验教训分析
                  </div>
                  <div class="flex space-x-2">
                    <el-button v-auth="'optimizationReport/addEdit/selectLessons'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      选择经验教训
                    </el-button>
                    <el-button v-auth="'optimizationReport/addEdit/importAnalysisData'" type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                      导入分析数据
                    </el-button>
                  </div>
                </div>
              </template>
              <el-input v-model="form.metadata" type="textarea" :rows="6" placeholder="请输入经验教训分析相关内容" />
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  八、附件
                </div>
              </template>
              <UploadMbb
                v-model="form.attachmentList"
                :max="10"
                :size="50"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.zip,.rar"
                tip-text="支持 PDF、DOC、XLS、JPG、PNG、ZIP、RAR 格式文件，大小不超过 50MB"
                service-name="whiskerguardregulatoryservice"
                category-name="optimizationReport"
                :auto-upload="false"
                :use-file-path="true"
                attachment-type="optimizationReport"
                @upload-success="handleAttachmentUploadSuccess"
              />
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-18 fw-800">
                  报告模板
                </div>
              </template>
              <ul class="mb-4 space-y-2">
                <li class="cursor-pointer hover:text-blue-500">
                  标准模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  简洁模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  详细模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  年度报告模板
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  专项报告模板
                </li>
              </ul>
              <el-button
                v-auth="'optimizationReport/addEdit/saveAsTemplate'" type="primary" size="small"
                class="!rounded-button mb-2 w-full whitespace-nowrap"
              >
                保存为模板
              </el-button>
              <el-link v-auth="'optimizationReport/addEdit/manageTemplate'" type="primary" class="mt-16">
                管理模板
              </el-link>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  内容助手
                </div>
              </template>
              <ul class="mb-4 space-y-3">
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进概况结构
                  </div>
                  <div class="text-xs text-gray-500">
                    包含资源投入、进度总览等标准结构
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    经验教训分析
                  </div>
                  <div class="text-xs text-gray-500">
                    问题分类、原因分析等标准内容
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进措施效果
                  </div>
                  <div class="text-xs text-gray-500">
                    完成情况、效果评估等标准内容
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    趋势分析框架
                  </div>
                  <div class="text-xs text-gray-500">
                    历史对比、预测分析等标准框架
                  </div>
                </li>
              </ul>
              <el-link type="primary" class="mt-16">
                自定义内容块
              </el-link>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  AI辅助
                </div>
              </template>
              <div class="mb-4 space-y-2">
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI完善内容
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI格式优化
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI生成摘要
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    AI数据分析
                  </el-button>
                </div>
              </div>
              <div class="rounded bg-gray-50 p-3 text-sm">
                <h4 class="mb-2 font-medium">
                  AI建议：
                </h4>
                <p class="text-gray-600">
                  当前报告可以增加更多数据可视化内容，建议在"趋势分析"部分添加图表。
                </p>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  报告检查
                </div>
              </template>
              <div class="mb-4 space-y-2">
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    拼写检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    格式检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    完整性检查
                  </el-button>
                </div>
                <div>
                  <el-button
                    type="primary" plain size="small"
                    class="!rounded-button w-full whitespace-nowrap"
                  >
                    逻辑一致性检查
                  </el-button>
                </div>
              </div>
              <div class="rounded bg-gray-50 p-3 text-sm">
                <h4 class="mb-2 font-medium">
                  检查结果：
                </h4>
                <ul class="text-gray-600 space-y-1">
                  <li>发现3处拼写错误</li>
                  <li>2处格式不一致</li>
                  <li>1处数据逻辑问题</li>
                </ul>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-18 fw-800">
                  参考资料
                </div>
              </template>
              <ul class="mb-4 space-y-3">
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    2023年度合规报告
                  </div>
                  <div class="text-xs text-gray-500">
                    可作为年度报告参考
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    改进措施实施指南
                  </div>
                  <div class="text-xs text-gray-500">
                    包含改进措施实施标准
                  </div>
                </li>
                <li class="cursor-pointer hover:text-blue-500">
                  <div class="font-medium">
                    数据分析方法手册
                  </div>
                  <div class="text-xs text-gray-500">
                    趋势分析方法参考
                  </div>
                  <span class="ml-2 rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">系统推荐</span>
                </li>
              </ul>
              <el-link type="primary" class="mt-16">
                查看更多
              </el-link>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu-vertical {
    border-right: none;
  }

  .el-menu-item.is-active {
    background-color: rgb(64 158 255 / 10%) !important;
  }

  .el-collapse-item__header {
    font-weight: 500;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-textarea__inner {
    min-height: 120px;
  }
</style>
