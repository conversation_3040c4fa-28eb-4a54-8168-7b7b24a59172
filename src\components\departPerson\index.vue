<!--
  部门人员选择组件

  功能：
  - 通过部门树选择人员
  - 支持显示组织架构树形结构
  - 点击部门后加载该部门下的人员列表
  - 支持选择人员并返回人员信息

  使用方式：
  <DepartPerson
    v-model="responsiblePerson"
    placeholder="请选择负责人"
    @change="handlePersonChange"
  />

  Props:
  - modelValue: 当前选中的人员姓名
  - placeholder: 输入框占位符文本

  Events:
  - update:modelValue: 更新选中人员姓名
  - change: 人员变化事件，返回完整的人员信息对象
-->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { Loading, OfficeBuilding, User, UserFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import roleApi from '@/api/permissions/role'

interface Employee {
  id: string
  name: string
  position?: string
  email?: string
  phone?: string
  departmentName?: string
}

interface OrgUnit {
  id: number
  name: string
  code: string
  type: 'COMPANY' | 'SUBSIDIARY' | 'BUSINESS_GROUP' | 'DEPARTMENT' | 'TEAM'
  level: number
  status: number
  sortOrder?: number
  description?: string
  metadata?: string
  version: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted: boolean
  parentId?: number
  children?: OrgUnit[]
}

// Props
interface Props {
  modelValue?: string | number
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请选择负责人',
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'change': [employee: Employee | null]
}>()

// Store
const userStore = useUserStore()

// Reactive data
const dialogVisible = ref(false)
const loading = ref(false)
const selectedDepartmentId = ref<string>('')
const selectedDepartmentName = ref<string>('')
const selectedEmployeeId = ref<string>('')
const selectedEmployee = ref<Employee | null>(null)
const employeeList = ref<Employee[]>([])
const departmentTreeRef = ref()

// 缓存已加载的部门人员数据，避免重复请求
const employeeCache = ref<Map<string, Employee[]>>(new Map())
// 所有员工数据
const allEmployees = ref<Employee[]>([])
// 是否已加载所有员工
const allEmployeesLoaded = ref(false)

// Tree props
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id',
}

// Computed
const displayValue = computed(() => {
  // 如果有选中的员工，显示员工姓名
  if (selectedEmployee.value?.name) {
    return selectedEmployee.value.name
  }
  // 如果没有选中员工但有modelValue（员工id），尝试从所有员工中查找
  if (props.modelValue && allEmployees.value.length > 0) {
    const employee = allEmployees.value.find(emp => emp.id === props.modelValue.toString())
    if (employee) {
      return employee.name
    }
  }
  // 如果有modelValue但还没加载完所有员工，显示提示信息
  if (props.modelValue) {
    return '已选择员工'
  }
  return ''
})

const departmentTreeData = computed(() => {
  // 首先尝试从响应式状态获取
  let orgTreeData = userStore.orgTree

  // 如果响应式状态为空，尝试调用getOrgTree方法
  if (!orgTreeData || orgTreeData.length === 0) {
    orgTreeData = userStore.getOrgTree()
  }

  if (!orgTreeData || !Array.isArray(orgTreeData)) {
    return []
  }

  // 直接返回原始的组织树数据，不进行转换
  return orgTreeData
})

// Methods
function openDialog() {
  dialogVisible.value = true
  // 打开对话框时，如果还没有选择部门，显示所有员工
  if (!selectedDepartmentId.value && allEmployees.value.length > 0) {
    employeeList.value = allEmployees.value
  }
}

function handleClose() {
  dialogVisible.value = false
}

// 获取所有员工
async function loadAllEmployees() {
  if (allEmployeesLoaded.value) {
    return
  }

  try {
    loading.value = true
    const response = await roleApi.getAllEmployee()

    let employees: Employee[] = []

    // 根据实际API返回的数据结构调整
    if (response && response.content) {
      employees = response.content.map((emp: any) => ({
        id: emp.id?.toString() || '',
        name: emp.realName || emp.username || '', // 优先使用realName（真实姓名），备选username
        position: emp.positionName || '',
        email: emp.email || '',
        phone: emp.phone || '',
        departmentName: emp.departmentName || '',
      }))
    }

    allEmployees.value = employees
    allEmployeesLoaded.value = true

    // 如果当前没有选择部门，显示所有员工
    if (!selectedDepartmentId.value) {
      employeeList.value = employees
    }
  }
  catch (error) {
    console.error('获取所有员工失败:', error)
    ElMessage.error('获取所有员工失败')
  }
  finally {
    loading.value = false
  }
}

async function handleDepartmentClick(data: OrgUnit) {
  selectedDepartmentId.value = data.id.toString()
  selectedDepartmentName.value = data.name

  // 清空之前选择的员工
  selectedEmployeeId.value = ''
  selectedEmployee.value = null

  await loadEmployeeList(data.id.toString())
}

async function loadEmployeeList(departmentId: string) {
  // 检查缓存
  if (employeeCache.value.has(departmentId)) {
    employeeList.value = employeeCache.value.get(departmentId) || []
    return
  }

  try {
    loading.value = true
    const response = await roleApi.getEmpByUnitId(departmentId)

    let employees: Employee[] = []

    // 根据实际API返回的数据结构调整，数据在response.content中
    if (response) {
      employees = response.content.map((emp: any) => ({
        id: emp.id?.toString() || '',
        name: emp.realName || emp.username || '', // 优先使用realName（真实姓名），备选username
        position: emp.positionName || '',
        email: emp.email || '',
        phone: emp.phone || '',
        departmentName: emp.departmentName || '',
      }))
    }

    // 缓存结果
    employeeCache.value.set(departmentId, employees)
    employeeList.value = employees
  }
  catch (error) {
    console.error('获取部门人员失败:', error)
    ElMessage.error('获取部门人员失败')
    employeeList.value = []
  }
  finally {
    loading.value = false
  }
}

function selectEmployee(employee: Employee) {
  selectedEmployeeId.value = employee.id
  selectedEmployee.value = employee
}

// 显示所有员工
function showAllEmployees() {
  selectedDepartmentId.value = ''
  selectedDepartmentName.value = ''
  employeeList.value = allEmployees.value
}

function confirmSelection() {
  if (selectedEmployee.value) {
    // 返回员工的id，而不是name
    emit('update:modelValue', selectedEmployee.value.id)
    emit('change', selectedEmployee.value)
    dialogVisible.value = false
  }
}

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape') {
    handleClose()
  }
  else if (event.key === 'Enter' && selectedEmployee.value) {
    confirmSelection()
  }
}

// 初始化时如果有值，尝试设置显示名称
onMounted(async () => {
  // 先加载所有员工
  await loadAllEmployees()

  if (props.modelValue) {
    selectedEmployeeId.value = props.modelValue.toString()
    // 从所有员工中查找对应的员工信息
    const employee = allEmployees.value.find(emp => emp.id === props.modelValue.toString())
    if (employee) {
      selectedEmployee.value = employee
    }
  }
})
</script>

<template>
  <div class="depart-person-selector">
    <el-input
      v-model="displayValue"
      placeholder="请选择负责人"
      readonly
      @click="openDialog"
    >
      <template #append>
        <el-button :icon="User" @click="openDialog" />
      </template>
    </el-input>

    <el-dialog
      v-model="dialogVisible"
      title="选择负责人"
      width="800px"
      :before-close="handleClose"
      @keydown="handleKeydown"
    >
      <div class="h-96 flex">
        <!-- 左侧部门树 -->
        <div class="w-1/2 border-r pr-4">
          <div class="mb-4">
            <h4 class="mb-2 text-sm text-gray-700 font-medium">
              选择部门
            </h4>
            <el-tree
              ref="departmentTreeRef"
              :data="departmentTreeData"
              :props="treeProps"
              node-key="id"
              :expand-on-click-node="false"
              class="max-h-80 overflow-y-auto"
              @node-click="handleDepartmentClick"
            >
              <template #default="{ node }">
                <span class="flex items-center">
                  <el-icon class="mr-1">
                    <OfficeBuilding />
                  </el-icon>
                  {{ node.label }}
                </span>
              </template>
            </el-tree>
          </div>
        </div>

        <!-- 右侧人员列表 -->
        <div class="w-1/2 pl-4">
          <div class="mb-4">
            <div class="mb-2 flex items-center justify-between">
              <h4 class="text-sm text-gray-700 font-medium">
                选择人员
                <span v-if="selectedDepartmentName" class="text-xs text-gray-500">
                  ({{ selectedDepartmentName }})
                </span>
              </h4>
              <el-button
                v-if="allEmployeesLoaded"
                size="small"
                type="text"
                @click="showAllEmployees"
              >
                显示所有员工
              </el-button>
            </div>
            <div v-if="loading" class="py-8 text-center">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <p class="mt-2 text-sm text-gray-500">
                加载中...
              </p>
            </div>
            <div v-else-if="!selectedDepartmentId && employeeList.length === 0" class="py-8 text-center text-gray-500">
              <el-icon class="mb-2 text-2xl">
                <User />
              </el-icon>
              <p class="text-sm">
                暂无员工数据
              </p>
            </div>
            <div v-else-if="selectedDepartmentId && employeeList.length === 0" class="py-8 text-center text-gray-500">
              <el-icon class="mb-2 text-2xl">
                <UserFilled />
              </el-icon>
              <p class="text-sm">
                该部门暂无人员
              </p>
            </div>
            <div v-else class="max-h-80 overflow-y-auto">
              <el-radio-group v-model="selectedEmployeeId" class="employee-radio-group w-full">
                <el-radio
                  v-for="employee in employeeList"
                  :key="employee.id"
                  :value="employee.id"
                  class="employee-radio-item mb-2 w-full"
                  @click="selectEmployee(employee)"
                >
                  <div class="w-full flex items-center">
                    <div class="ml-2 flex-1">
                      <div class="font-medium">
                        {{ employee.name }} <span v-if="employee.departmentName"> - {{ employee.departmentName }}</span>
                      </div>
                    </div>
                  </div>
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button type="primary" :disabled="!selectedEmployeeId" @click="confirmSelection">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.depart-person-selector {
  width: 100%;
}

.el-tree {
  background: transparent;
}

.el-tree-node__content {
  height: 32px;
}

.employee-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.employee-radio-item {
  width: 100% !important;
  margin-right: 0 !important;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.employee-radio-item:hover {
  background-color: #f5f7fa;
}

.employee-radio-item :deep(.el-radio__label) {
  width: 100%;
  padding-left: 0;
}

.employee-radio-item :deep(.el-radio__input) {
  margin-right: 8px;
}
</style>
