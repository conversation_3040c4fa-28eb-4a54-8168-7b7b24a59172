<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const formRef = ref<FormInstance>()

// 判断是编辑还是新增模式
const isEdit = ref(false)
const pageTitle = ref('新增法规')

// 表单数据
const formData = reactive({
  id: null,
  title: '',
  summary: '',
  detailUrl: '',
  content: '',
  province: '',
  department: '',
  code: '',
  pubDate: '',
  useDate: '',
  isTime: '现行有效',
  level: '',
  lawType: '',
  noUse: '',
  modifyAccording: '',
  changeReference: '',
  status: 'DRAFT',
  categoryName: '',
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入法规标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' },
  ],
  province: [
    { required: true, message: '请输入发布省份', trigger: 'blur' },
  ],
  department: [
    { required: true, message: '请输入发布部门', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入文件号', trigger: 'blur' },
  ],
  level: [
    { required: true, message: '请选择级别', trigger: 'change' },
  ],
  pubDate: [
    { required: true, message: '请选择发布日期', trigger: 'change' },
  ],
  useDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' },
  ],
})

// 级别选项
const levelOptions = [
  { label: '法律', value: '法律' },
  { label: '行政法规', value: '行政法规' },
  { label: '部门规章', value: '部门规章' },
  { label: '规范性文件', value: '规范性文件' },
]

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'DRAFT' },
  { label: '已发布', value: 'PUBLISHED' },
  { label: '已归档', value: 'ARCHIVED' },
]

// 时效性选项
const timeOptions = [
  { label: '现行有效', value: '现行有效' },
  { label: '失效', value: '失效' },
]

// 获取法规详情（编辑模式）
async function getRegulationDetail() {
  const id = route.query.id
  if (!id) {
    return
  }

  loading.value = true
  try {
    const response = await systemApi.lawsSystem({ id }, 'info')

    // 根据API拦截器逻辑，response就是实际数据
    if (response && typeof response === 'object') {
      // 安全地赋值，只赋值存在的字段
      const fieldsToUpdate = [
        'id', 'title', 'summary', 'detailUrl', 'content', 'province',
        'department', 'code', 'pubDate', 'useDate', 'isTime', 'level',
        'lawType', 'noUse', 'modifyAccording', 'changeReference',
        'status', 'categoryName',
      ]

      fieldsToUpdate.forEach((field) => {
        if (response[field] !== undefined && response[field] !== null) {
          (formData as any)[field] = response[field]
        }
      })
    }
  }
  catch (error) {
    ElMessage.error('获取法规详情失败')
  }
  finally {
    loading.value = false
  }
}

// 保存法规
async function saveRegulation() {
  if (!formRef.value) {
    return
  }

  try {
    await formRef.value.validate()
  }
  catch (error) {
    ElMessage.error('请检查表单填写是否正确')
    return
  }

  loading.value = true
  try {
    const apiKey = isEdit.value ? 'update' : 'create'
    // 根据是否为编辑模式决定是否传递id
    const submitData = isEdit.value ? formData : { ...formData }
    if (!isEdit.value) {
      const { id, ...dataWithoutId } = submitData
      Object.assign(submitData, dataWithoutId)
    }
    const response = await systemApi.lawsSystem(submitData, apiKey)

    if (response) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/database/laws')
    }
  }
  catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function goBack() {
  ElMessageBox.confirm(
    '确定要离开吗？未保存的更改将会丢失。',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    router.push('/database/laws')
  }).catch(() => {
    // 用户取消
  })
}

// 重置表单
function resetForm() {
  if (!formRef.value) {
    return
  }
  formRef.value.resetFields()
}

// 初始化页面
function initPage() {
  const id = route.query.id
  const mode = route.query.mode

  if (id && mode === 'edit') {
    isEdit.value = true
    pageTitle.value = '编辑法规'
    getRegulationDetail()
  }
  else {
    isEdit.value = false
    pageTitle.value = '新增法规'
  }
}

onMounted(() => {
  initPage()
})
</script>

<template>
  <div class="rounded-lg bg-white p-6 shadow-sm">
    <!-- 页面头部 -->
    <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
      <div class="flex items-center space-x-4">
        <el-button v-auth="'regulatoryDatabaseMode/addEdit/goBack'" class="flex items-center" @click="goBack">
          <svg-icon name="ep:arrow-left" class="mr-1" />
          返回列表
        </el-button>
        <h1 class="text-xl text-gray-800 font-semibold">
          {{ pageTitle }}
        </h1>
      </div>
      <div class="flex space-x-2">
        <el-button v-auth="'regulatoryDatabaseMode/addEdit/reset'" :disabled="loading" @click="resetForm">
          <svg-icon name="ep:refresh" class="mr-1" />
          重置
        </el-button>
        <el-button v-auth="'regulatoryDatabaseMode/addEdit/save'" type="primary" :loading="loading" @click="saveRegulation">
          <svg-icon name="ep:check" class="mr-1" />
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:document" class="mr-2" />
          基本信息
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <el-form-item label="法规标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入法规标题"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="文件号" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入文件号"
            />
          </el-form-item>
          <el-form-item label="发布省份" prop="province">
            <el-input
              v-model="formData.province"
              placeholder="请输入发布省份"
            />
          </el-form-item>
          <el-form-item label="发布部门" prop="department">
            <el-input
              v-model="formData.department"
              placeholder="请输入发布部门"
            />
          </el-form-item>
          <el-form-item label="级别" prop="level">
            <el-select
              v-model="formData.level"
              placeholder="请选择级别"
              class="w-full"
            >
              <el-option
                v-for="item in levelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="法规类型">
            <el-input
              v-model="formData.lawType"
              placeholder="请输入法规类型"
            />
          </el-form-item>
          <el-form-item label="发布日期" prop="pubDate">
            <el-date-picker
              v-model="formData.pubDate"
              type="date"
              placeholder="请选择发布日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="生效日期" prop="useDate">
            <el-date-picker
              v-model="formData.useDate"
              type="date"
              placeholder="请选择生效日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="时效性">
            <el-select
              v-model="formData.isTime"
              placeholder="请选择时效性"
              class="w-full"
            >
              <el-option
                v-for="item in timeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              class="w-full"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:document-copy" class="mr-2" />
          详细信息
        </h2>
        <div class="space-y-4">
          <el-form-item label="法规摘要">
            <el-input
              v-model="formData.summary"
              type="textarea"
              :rows="4"
              placeholder="请输入法规摘要"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="法规内容">
            <el-input
              v-model="formData.content"
              type="textarea"
              :rows="8"
              placeholder="请输入法规详细内容"
              maxlength="10000"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="详情链接">
            <el-input
              v-model="formData.detailUrl"
              placeholder="请输入详情链接URL"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="mb-6 rounded-lg bg-gray-50 p-6">
        <h2 class="mb-4 flex items-center text-lg text-gray-800 font-semibold">
          <svg-icon name="ep:info-filled" class="mr-2" />
          其他信息
        </h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <el-form-item label="分类名称">
            <el-input
              v-model="formData.categoryName"
              placeholder="请输入分类名称"
            />
          </el-form-item>
          <el-form-item label="废止信息">
            <el-input
              v-model="formData.noUse"
              placeholder="请输入废止信息"
            />
          </el-form-item>
          <el-form-item label="修改依据">
            <el-input
              v-model="formData.modifyAccording"
              placeholder="请输入修改依据"
            />
          </el-form-item>
          <el-form-item label="变更参考">
            <el-input
              v-model="formData.changeReference"
              placeholder="请输入变更参考"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-center pt-6 space-x-4">
        <el-button v-auth="'regulatoryDatabaseMode/addEdit/cancel'" size="large" :disabled="loading" @click="goBack">
          取消
        </el-button>
        <el-button v-auth="'regulatoryDatabaseMode/addEdit/reset'" size="large" :disabled="loading" @click="resetForm">
          重置
        </el-button>
        <el-button
          v-auth="'regulatoryDatabaseMode/addEdit/save'"
          type="primary"
          size="large"
          :loading="loading"
          @click="saveRegulation"
        >
          {{ isEdit ? '更新法规' : '保存法规' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
/* 自定义样式 */
:deep(.el-form-item__label) {
  @apply font-medium text-gray-700;
}

:deep(.el-input__wrapper) {
  @apply transition-all duration-200;
}

:deep(.el-input__wrapper:hover) {
  @apply shadow-sm;
}

:deep(.el-textarea__inner) {
  @apply transition-all duration-200;
}

:deep(.el-textarea__inner:hover) {
  @apply shadow-sm;
}

:deep(.el-button) {
  @apply transition-all duration-200;
}

:deep(.el-button:hover) {
  @apply transform scale-105;
}
</style>
