<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import Api from '@/api/modules/system/dict'

const props = defineProps<{
  obj: any
  id: any
}>()
const emits = defineEmits(['showeditfn'])
const formRef = ref<FormInstance>()
const title = ref('新增字典项')
const form: any = ref({
  name: '',
  code: '',
  sort: 1,
  note: '',
  dict_id: '',
})
const formRules = ref({
  name: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入字典值', trigger: 'blur' },
  ],
  sort: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
  ],
})

onMounted(() => {
  console.log(props.obj, 'props.obj')
  if (props.obj.id) {
    form.value = JSON.parse(JSON.stringify(props.obj))
    title.value = '编辑字典项'
  }
  if (props.id) {
    form.value.dict_id = props.id
  }
})
function onCancel() {
  // myVisible.value = false
  emits('showeditfn', false)
}

function onSubmit() {
  formRef.value && formRef.value.validate((valid) => {
    if (valid) {
      ElMessageBox.confirm(
        '确认提交?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        },
      )
        .then(() => {
          Api.dictdataedit(form.value).then((res: any) => {
            ElMessage({ message: '操作成功', type: 'success' })
            onCancel()
          })
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消',
          })
        })
    }
  })
}
</script>

<template>
  <el-dialog
    :title="title" width="440px" :close-on-click-modal="false" append-to-body destroy-on-close
    @close="onCancel"
  >
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="98px">
      <el-form-item label="字典项名称:" prop="name">
        <el-input v-model="form.name" placeholder="请输入字典项名称" clearable />
      </el-form-item>
      <!-- <el-form-item label="字典项值:" prop="code">
        <el-input v-model="form.code" placeholder="请输入字典项值" clearable />
      </el-form-item> -->
      <el-form-item label="排序号:" prop="sort">
        <el-input-number
          v-model="form.sort" style="width: 100%;" :min="0" placeholder="请输入排序号"
          controls-position="right" class="ele-fluid ele-text-left"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :value="1">
            在用
          </el-radio>
          <el-radio :value="2">
            停用
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注:">
        <el-input v-model="form.note" :maxlength="200" placeholder="请输入备注" :rows="4" type="textarea" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="onCancel">
        取消
      </el-button>
      <el-button type="primary" @click="onSubmit">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>
