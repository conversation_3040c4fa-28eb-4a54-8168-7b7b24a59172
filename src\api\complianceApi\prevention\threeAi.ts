import api from '@/api/index'

export default {
  // 获取会话ID
  getSessionId: () => {
    return api.post('/whiskerguardaiservice/api/ai/conversations/generate', {})
  },

  // 岗位职责综合分析
  analysisAI: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/ai/comprehensive/analysis', params)
  },

  // 岗位职责岗位说明书
  getDutyPosition: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/parse/job/description', params)
  },

  // 岗位 ai生成合规要求
  getCompliance: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/generate/compliance/requirements', params)
  },

  // 岗位 ai生成八大项
  getEight: (params: any) => {
    return api.post('/compliancelistservice/api/duty/positions/ai/power/analysis', params)
  },

  // AI风险识别综合分析
  getRiskAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/ai/comprehensive/analysis', params)
  },

  // AI风险识别业务类型风险点
  getRiskTypeAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/ai/analysis', params)
  },

  // AI风险识别分析风险点对应的法律法规
  getRiskLawAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/ai/regulation/analysis', params)
  },

  // AI风险识别分析风险点对应的防控措施和责任主体
  getRiskControlAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/ai/measure/analysis', params)
  },

  // AI风险识别分析风险点对应的风险等级
  getRiskRankAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/model/analysis', params)
  },

  getRiskLevelAI: (params: any) => {
    return api.post('/compliancelistservice/api/compliance/risk/list/ai/level/analysis', params)
  },

  // ai业务流程综合分析
  getProcessAI: (params: any) => {
    return api.post('/compliancelistservice/api/v2/biz/process/ai/analyze', params)
  },
}
