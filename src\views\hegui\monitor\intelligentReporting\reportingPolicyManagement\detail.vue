<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Clock as ElIconClock,
  DataLine as ElIconDataLine,
  DocumentChecked as ElIconDocumentChecked,
  Plus as ElIconPlus,
  Timer as ElIconTimer,
  Upload as ElIconUpload,
} from '@element-plus/icons-vue'

const activeTab = ref('basic')
const showFieldDialog = ref(false)
const currentField = ref({
  id: '',
  name: '',
  type: 'text',
  label: '',
  required: false,
  defaultValue: '',
  description: '',
  validationType: 'none',
  minLength: 0,
  maxLength: 0,
  regexPattern: '',
  errorMessage: '',
  order: 0,
  visibility: 'all',
})
const formFields = ref([
  {
    id: '1',
    name: '举报人姓名',
    type: 'text',
    label: '姓名',
    required: true,
    defaultValue: '',
    description: '请输入举报人姓名',
    validationType: 'length',
    minLength: 2,
    maxLength: 20,
    errorMessage: '姓名长度需在2-20个字符之间',
    order: 1,
    visibility: 'all',
  },
  {
    id: '2',
    name: '举报内容',
    type: 'textarea',
    label: '举报内容',
    required: true,
    defaultValue: '',
    description: '请详细描述举报内容',
    validationType: 'length',
    minLength: 10,
    maxLength: 1000,
    errorMessage: '举报内容需在10-1000个字符之间',
    order: 2,
    visibility: 'all',
  },
  {
    id: '3',
    name: '举报类型',
    type: 'select',
    label: '举报类型',
    required: true,
    defaultValue: '',
    description: '请选择举报类型',
    validationType: 'none',
    errorMessage: '请选择举报类型',
    order: 3,
    visibility: 'all',
    options: ['贪污腐败', '性骚扰', '财务违规', '其他'],
  },
])
const validationRules = ref({
  beforeSubmit: true,
  customRules: '',
  errorMessage: '请检查表单填写是否正确',
})
function getFieldComponent(type) {
  const components = {
    text: 'el-input',
    textarea: 'el-input',
    select: 'el-select',
    radio: 'el-radio-group',
    checkbox: 'el-checkbox-group',
    date: 'el-date-picker',
    time: 'el-time-picker',
    file: 'el-upload',
    divider: 'el-divider',
  }
  return components[type] || 'el-input'
}
function editField(field) {
  currentField.value = JSON.parse(JSON.stringify(field))
  showFieldDialog.value = true
}
function editClassificationRule(rule) {
  currentClassificationRule.value = JSON.parse(JSON.stringify(rule))
  showClassificationDialog.value = true
}
function editPriorityRule(rule) {
  currentPriorityRule.value = JSON.parse(JSON.stringify(rule))
  showPriorityDialog.value = true
}
function deleteClassificationRule(rule) {
  classificationRules.value = classificationRules.value.filter(r => r.id !== rule.id)
}
function deletePriorityRule(rule) {
  priorityRules.value = priorityRules.value.filter(r => r.id !== rule.id)
}
function previewField(field) {
  // Preview logic would be implemented here
}
function deleteField(field) {
  formFields.value = formFields.value.filter(f => f.id !== field.id)
}
function saveField() {
  if (currentField.value.id) {
    const index = formFields.value.findIndex(f => f.id === currentField.value.id)
    formFields.value.splice(index, 1, JSON.parse(JSON.stringify(currentField.value)))
  }
  else {
    currentField.value.id = Date.now().toString()
    formFields.value.push(JSON.parse(JSON.stringify(currentField.value)))
  }
  showFieldDialog.value = false
  resetField()
}
function resetField() {
  currentField.value = {
    id: '',
    name: '',
    type: 'text',
    label: '',
    required: false,
    defaultValue: '',
    description: '',
    validationType: 'none',
    minLength: 0,
    maxLength: 0,
    regexPattern: '',
    errorMessage: '',
    order: formFields.value.length + 1,
    visibility: 'all',
  }
}
function resetLayout() {
  // Reset layout logic would be implemented here
}
const trendTimeRange = ref('30')
const trendMetric = ref('count')
const typeData = ref([
  { type: '贪污腐败', count: 356, percentage: 28.5, validRate: 82 },
  { type: '性骚扰', count: 248, percentage: 19.9, validRate: 75 },
  { type: '财务违规', count: 198, percentage: 15.9, validRate: 85 },
  { type: '信息安全', count: 156, percentage: 12.5, validRate: 72 },
  { type: '其他', count: 290, percentage: 23.2, validRate: 80 },
])
const feedbackData = ref([
  { date: '2023-06-15', user: '张伟', rating: 5, comment: '举报流程很顺畅，操作简单' },
  { date: '2023-06-10', user: '李娜', rating: 4, comment: '希望能增加更多举报类型选项' },
  { date: '2023-06-05', user: '王强', rating: 5, comment: '处理速度快，反馈及时' },
  { date: '2023-05-28', user: '陈芳', rating: 4, comment: '界面友好，但提交后没有确认提示' },
  { date: '2023-05-20', user: '刘明', rating: 5, comment: '隐私保护做得很好，值得信赖' },
])
const trendChart = ref(null)
const typeChart = ref(null)
const statusChart = ref(null)
const timeChart = ref(null)
function initCharts() {
  // 趋势图
  const trendOption = {
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['举报数量'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['6/1', '6/5', '6/10', '6/15', '6/20', '6/25', '6/30'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '举报数量',
        type: 'line',
        data: [12, 15, 8, 18, 16, 20, 14],
        smooth: true,
        lineStyle: {
          width: 3,
        },
        itemStyle: {
          color: '#409EFF',
        },
      },
    ],
  }
  // 类型分布图
  const typeOption = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '举报类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 356, name: '贪污腐败' },
          { value: 248, name: '性骚扰' },
          { value: 198, name: '财务违规' },
          { value: 156, name: '信息安全' },
          { value: 290, name: '其他' },
        ],
      },
    ],
  }
  // 处理状态图
  const statusOption = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '处理状态',
        type: 'pie',
        radius: '70%',
        center: ['50%', '50%'],
        data: [
          { value: 320, name: '已处理' },
          { value: 240, name: '处理中' },
          { value: 149, name: '待分配' },
          { value: 100, name: '已关闭' },
        ],
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
      },
    ],
  }
  // 处理时效图
  const timeOption = {
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['贪污腐败', '性骚扰', '财务违规', '信息安全', '其他'],
    },
    series: [
      {
        name: '处理时效(天)',
        type: 'bar',
        data: [2.8, 3.5, 2.2, 4.1, 3.0],
        itemStyle: {
          color(params) {
            const colorList = ['#67C23A', '#E6A23C', '#F56C6C', '#909399', '#409EFF']
            return colorList[params.dataIndex]
          },
        },
      },
    ],
  }
  const trendInstance = echarts.init(trendChart.value)
  trendInstance.setOption(trendOption)
  const typeInstance = echarts.init(typeChart.value)
  typeInstance.setOption(typeOption)
  const statusInstance = echarts.init(statusChart.value)
  statusInstance.setOption(statusOption)
  const timeInstance = echarts.init(timeChart.value)
  timeInstance.setOption(timeOption)
  window.addEventListener('resize', () => {
    trendInstance.resize()
    typeInstance.resize()
    statusInstance.resize()
    timeInstance.resize()
  })
}
onMounted(() => {
  initCharts()
})
const formData = ref({
  name: '内部员工举报渠道',
  code: 'REPORT-2023-001',
  type: 'web',
  status: true,
  createTime: '2023-06-15 14:30:22',
  creator: '张明远',
  updateTime: '2023-06-20 09:15:43',
  updater: '李思思',
  description: '用于公司内部员工举报违规行为的专用渠道',
  usage: '<p>1. 访问举报页面</p><p>2. 填写举报表单</p><p>3. 提交举报信息</p>',
  contact: '<EMAIL>',
  remark: '仅限内部员工使用',
  web: {
    url: 'https://report.company.com/internal',
    access: 'internal',
  },
  email: {
    address: '<EMAIL>',
    autoReply: true,
    replyContent: '感谢您的举报，我们已收到您的信息并将尽快处理。',
    filterRules: '',
  },
})
const processTemplates = ref([
  { id: '1', name: '标准举报处理流程' },
  { id: '2', name: '紧急事件处理流程' },
  { id: '3', name: '匿名举报处理流程' },
])
const departments = ref([
  { id: '1', name: '合规部' },
  { id: '2', name: '人力资源部' },
  { id: '3', name: '法务部' },
  { id: '4', name: '审计部' },
])
const users = ref([
  { id: '1', name: '王明' },
  { id: '2', name: '李华' },
  { id: '3', name: '张伟' },
  { id: '4', name: '刘芳' },
])
const processConfig = ref({
  template: '1',
  department: '1',
  handler: '1',
  timeLimit: 3,
  timeUnit: 'day',
})
const classificationRules = ref([
  { id: '1', name: '财务违规', condition: '包含"贪污"或"受贿"', result: '财务违规', priority: 1 },
  { id: '2', name: '性骚扰', condition: '包含"骚扰"或"性骚扰"', result: '性骚扰', priority: 2 },
  { id: '3', name: '信息安全', condition: '包含"数据"或"泄露"', result: '信息安全', priority: 3 },
])
const priorityConfig = ref({
  default: 'medium',
})
const priorityRules = ref([
  { id: '1', name: '高管举报', condition: '举报对象为VP及以上', result: '高', priority: 1 },
  { id: '2', name: '金额较大', condition: '涉及金额超过10万', result: '高', priority: 2 },
  { id: '3', name: '紧急事件', condition: '包含"紧急"或"立即"', result: '高', priority: 3 },
])
const exceptionConfig = ref({
  timeoutStrategy: 'escalate',
  duplicateDetection: true,
  duplicateAction: 'merge',
  spamFilter: true,
  filterRules: '包含广告、推销等关键词',
})
const showClassificationDialog = ref(false)
const showPriorityDialog = ref(false)
const showReminderDialog = ref(false)
const currentClassificationRule = ref({})
const currentPriorityRule = ref({})
const currentReminderRule = ref({})
const notificationConfig = ref({
  newReport: {
    enabled: true,
    conditions: ['all'],
    receivers: [],
    methods: ['system'],
    templates: {
      system: '新举报通知：收到来自{reporter}的举报，举报类型：{type}，请及时处理。',
      email: '新举报通知\n\n举报人：{reporter}\n举报类型：{type}\n举报时间：{time}\n\n请登录系统查看详情。',
      sms: '新举报通知：{type}，请及时处理。',
      wechat: '新举报通知\n\n举报类型：{type}\n举报时间：{time}\n\n点击查看详情',
    },
  },
  statusChange: {
    triggers: ['received', 'processed'],
    receivers: [],
    methods: ['system'],
    templates: {
      system: '举报状态变更：举报ID {id} 状态已变更为 {status}',
      email: '举报状态变更通知\n\n举报ID：{id}\n当前状态：{status}\n处理人：{handler}\n\n请登录系统查看详情。',
      sms: '举报{id}状态变更为{status}',
      wechat: '举报状态变更\n\nID：{id}\n状态：{status}\n\n点击查看详情',
    },
  },
  reporterFeedback: {
    enabled: true,
    triggers: ['received', 'processed'],
    methods: ['original'],
    template: '尊敬的{reporter}：\n\n您的举报（ID：{id}）当前状态为：{status}\n\n处理人：{handler}\n处理时间：{time}\n\n感谢您的举报。',
  },
  reminder: {
    enabled: true,
    timeoutHours: 24,
    rules: [
      {
        id: '1',
        timeout: 24,
        receivers: ['1', 'role_1'],
        methods: ['system', 'email'],
      },
      {
        id: '2',
        timeout: 72,
        receivers: ['2', 'dept_1'],
        methods: ['system', 'sms'],
      },
    ],
  },
})
const roles = ref([
  { id: '1', name: '合规管理员' },
  { id: '2', name: '部门主管' },
  { id: '3', name: '审计员' },
])
const methodLabels = {
  system: '系统消息',
  email: '邮件',
  sms: '短信',
  wechat: '微信',
}
const privacyConfig = ref({
  allowAnonymous: true,
  anonymousPermission: 'all',
  anonymousLimits: ['noAttachment', 'noTracking'],
  sensitiveDataRules: [
    {
      id: '1',
      type: '身份证号',
      pattern: '\\d{17}[\\dXx]',
      method: '部分遮盖',
    },
    {
      id: '2',
      type: '手机号',
      pattern: '1[3-9]\\d{9}',
      method: '部分遮盖',
    },
    {
      id: '3',
      type: '银行卡号',
      pattern: '\\d{16,19}',
      method: '全部遮盖',
    },
  ],
  retentionPeriod: 12,
  retentionUnit: 'month',
  expirationAction: 'anonymize',
  specialRules: '涉及个人隐私的举报内容保留期满后自动匿名化处理',
  accessControlRules: [
    {
      id: '1',
      role: '合规管理员',
      view: '全部信息',
      edit: '处理意见',
      process: '全部流程',
    },
    {
      id: '2',
      role: '部门主管',
      view: '本部门举报',
      edit: '处理意见',
      process: '本部门流程',
    },
  ],
})
const showSensitiveDialog = ref(false)
const showAccessDialog = ref(false)
const showLanguageDialog = ref(false)
const currentSensitiveRule = ref({})
const currentAccessRule = ref({})
const currentLanguage = ref({})
const appearanceConfig = ref({
  pageTitle: '猫伯伯合规举报系统',
  logo: '',
  headerContent: '',
  footerContent: '',
  backgroundType: 'color',
  backgroundColor: '#f5f7fa',
  backgroundImage: '',
  themeColor: '#409EFF',
  buttonStyle: 'default',
  formStyle: 'default',
  fontFamily: 'system',
  welcomeText: '欢迎使用合规举报系统',
  reportInstructions: '<p>请如实填写举报信息，我们将严格保密。</p>',
  privacyStatement: '<p>您的个人信息将受到严格保护。</p>',
  thankYouText: '感谢您的举报',
  defaultLanguage: 'zh-CN',
  multiLanguageSupport: false,
  languages: [
    { id: '1', language: 'zh-CN', name: '简体中文', enabled: true, progress: 100 },
    { id: '2', language: 'en-US', name: 'English', enabled: true, progress: 80 },
  ],
  previewMode: 'desktop',
})
function handleLogoChange(file) {
  const reader = new FileReader()
  reader.onload = (e) => {
    appearanceConfig.value.logo = e.target.result
  }
  reader.readAsDataURL(file.raw)
}
function handleBgImageChange(file) {
  const reader = new FileReader()
  reader.onload = (e) => {
    appearanceConfig.value.backgroundImage = e.target.result
  }
  reader.readAsDataURL(file.raw)
}
function editLanguage(language) {
  currentLanguage.value = JSON.parse(JSON.stringify(language))
  showLanguageDialog.value = true
}
function deleteLanguage(language) {
  appearanceConfig.value.languages = appearanceConfig.value.languages.filter(l => l.id !== language.id)
}
function saveLanguage() {
  if (currentLanguage.value.id) {
    const index = appearanceConfig.value.languages.findIndex(l => l.id === currentLanguage.value.id)
    appearanceConfig.value.languages.splice(index, 1, JSON.parse(JSON.stringify(currentLanguage.value)))
  }
  else {
    currentLanguage.value.id = Date.now().toString()
    appearanceConfig.value.languages.push(JSON.parse(JSON.stringify(currentLanguage.value)))
  }
  showLanguageDialog.value = false
  resetLanguage()
}
function resetLanguage() {
  currentLanguage.value = {
    id: '',
    language: '',
    name: '',
    enabled: true,
    progress: 0,
  }
}
function editSensitiveRule(rule) {
  currentSensitiveRule.value = JSON.parse(JSON.stringify(rule))
  showSensitiveDialog.value = true
}
function deleteSensitiveRule(rule) {
  privacyConfig.value.sensitiveDataRules = privacyConfig.value.sensitiveDataRules.filter(r => r.id !== rule.id)
}
function editAccessRule(rule) {
  currentAccessRule.value = JSON.parse(JSON.stringify(rule))
  showAccessDialog.value = true
}
function deleteAccessRule(rule) {
  privacyConfig.value.accessControlRules = privacyConfig.value.accessControlRules.filter(r => r.id !== rule.id)
}
function getReceiverName(receiver) {
  if (receiver.startsWith('role_')) {
    const roleId = receiver.split('_')[1]
    const role = roles.value.find(r => r.id === roleId)
    return role ? `角色:${role.name}` : receiver
  }
  else if (receiver.startsWith('dept_')) {
    const deptId = receiver.split('_')[1]
    const dept = departments.value.find(d => d.id === deptId)
    return dept ? `部门:${dept.name}` : receiver
  }
  else {
    const user = users.value.find(u => u.id === receiver)
    return user ? user.name : receiver
  }
}
function editReminderRule(rule) {
  currentReminderRule.value = JSON.parse(JSON.stringify(rule))
  showReminderDialog.value = true
}
function deleteReminderRule(rule) {
  notificationConfig.value.reminder.rules = notificationConfig.value.reminder.rules.filter(r => r.id !== rule.id)
}
function saveReminderRule() {
  if (currentReminderRule.value.id) {
    const index = notificationConfig.value.reminder.rules.findIndex(r => r.id === currentReminderRule.value.id)
    notificationConfig.value.reminder.rules.splice(index, 1, JSON.parse(JSON.stringify(currentReminderRule.value)))
  }
  else {
    currentReminderRule.value.id = Date.now().toString()
    notificationConfig.value.reminder.rules.push(JSON.parse(JSON.stringify(currentReminderRule.value)))
  }
  showReminderDialog.value = false
  resetReminderRule()
}
function resetReminderRule() {
  currentReminderRule.value = {
    id: '',
    timeout: 24,
    receivers: [],
    methods: ['system'],
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="mr-4 text-2xl font-bold">
              举报渠道详情/编辑 - 内部员工举报渠道
            </h1>
            <el-tag type="success">
              启用
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              保存
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              编辑
            </el-button>
            <el-button type="success" class="!rounded-button whitespace-nowrap">
              启用
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              预览
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex">
          <!-- 主内容区 -->
          <div class="min-h-screen flex flex-1 flex-col">
            <!-- 页面内容 -->
            <div class="flex-1 bg-gray-100 p-6">
              <div class="flex space-x-6">
                <!-- 主内容区 -->
                <div class="flex-1">
                  <el-card shadow="hover" class="mb-6">
                    <el-tabs v-model="activeTab">
                      <el-tab-pane label="基本信息" name="basic">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              渠道基本信息
                            </h3>
                            <div class="grid grid-cols-2 gap-4">
                              <div>
                                <el-form-item label="渠道名称" required>
                                  <el-input v-model="formData.name" placeholder="请输入渠道名称" />
                                </el-form-item>
                                <el-form-item label="渠道编号">
                                  <el-input v-model="formData.code" placeholder="自动生成" />
                                </el-form-item>
                                <el-form-item label="渠道类型" required>
                                  <el-select v-model="formData.type" placeholder="请选择渠道类型" class="w-full">
                                    <el-option label="网页" value="web" />
                                    <el-option label="邮箱" value="email" />
                                    <el-option label="电话" value="phone" />
                                    <el-option label="微信" value="wechat" />
                                    <el-option label="手机APP" value="app" />
                                    <el-option label="其他" value="other" />
                                  </el-select>
                                </el-form-item>
                                <el-form-item label="状态">
                                  <el-switch v-model="formData.status" active-text="启用" inactive-text="禁用" />
                                </el-form-item>
                              </div>
                              <div>
                                <el-form-item label="创建时间">
                                  <el-input v-model="formData.createTime" disabled />
                                </el-form-item>
                                <el-form-item label="创建人">
                                  <el-input v-model="formData.creator" disabled />
                                </el-form-item>
                                <el-form-item label="最后更新时间">
                                  <el-input v-model="formData.updateTime" disabled />
                                </el-form-item>
                                <el-form-item label="更新人">
                                  <el-input v-model="formData.updater" disabled />
                                </el-form-item>
                              </div>
                            </div>
                          </div>
                          <div v-if="formData.type === 'web'">
                            <h3 class="mb-4 text-lg font-medium">
                              网页渠道配置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="URL设置">
                                <el-input v-model="formData.web.url" placeholder="请输入URL地址" />
                              </el-form-item>
                              <el-form-item label="访问权限">
                                <el-radio-group v-model="formData.web.access">
                                  <el-radio label="public">
                                    公开
                                  </el-radio>
                                  <el-radio label="internal">
                                    内部
                                  </el-radio>
                                  <el-radio label="specified">
                                    指定人员
                                  </el-radio>
                                </el-radio-group>
                              </el-form-item>
                              <div class="flex space-x-3">
                                <el-button class="!rounded-button whitespace-nowrap">
                                  生成短链接
                                </el-button>
                                <el-button class="!rounded-button whitespace-nowrap">
                                  测试链接
                                </el-button>
                              </div>
                            </div>
                          </div>
                          <div v-if="formData.type === 'email'">
                            <h3 class="mb-4 text-lg font-medium">
                              邮箱渠道配置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="邮箱地址">
                                <el-input v-model="formData.email.address" placeholder="请输入邮箱地址" />
                              </el-form-item>
                              <el-form-item label="自动回复设置">
                                <div class="flex items-center space-x-4">
                                  <el-switch v-model="formData.email.autoReply" />
                                  <el-input
                                    v-model="formData.email.replyContent" placeholder="请输入自动回复内容"
                                    type="textarea" :rows="3"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="邮件过滤规则">
                                <el-input
                                  v-model="formData.email.filterRules" placeholder="请输入邮件过滤规则" type="textarea"
                                  :rows="3"
                                />
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              渠道描述
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="渠道简介">
                                <el-input
                                  v-model="formData.description" placeholder="请输入渠道简介" type="textarea"
                                  :rows="3"
                                />
                              </el-form-item>
                              <el-form-item label="使用说明">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input v-model="formData.usage" placeholder="请输入使用说明" type="textarea" :rows="5" />
                                </div>
                              </el-form-item>
                              <el-form-item label="联系方式">
                                <el-input v-model="formData.contact" placeholder="请输入联系方式" />
                              </el-form-item>
                              <el-form-item label="备注">
                                <el-input v-model="formData.remark" placeholder="请输入备注" type="textarea" :rows="3" />
                              </el-form-item>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="表单配置" name="form">
                        <div class="space-y-6">
                          <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium">
                              表单字段管理
                            </h3>
                            <el-button
                              type="primary" class="!rounded-button whitespace-nowrap"
                              @click="showFieldDialog = true"
                            >
                              <el-icon class="mr-1">
                                <ElIconPlus />
                              </el-icon>
                              添加字段
                            </el-button>
                          </div>
                          <el-table :data="formFields" border style="width: 100%;">
                            <el-table-column prop="name" label="字段名称" width="150" />
                            <el-table-column prop="type" label="字段类型" width="120" />
                            <el-table-column prop="required" label="是否必填" width="100">
                              <template #default="{ row }">
                                <el-tag :type="row.required ? 'success' : 'info'">
                                  {{ row.required ? '是' : '否' }}
                                </el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column prop="order" label="显示顺序" width="100" />
                            <el-table-column prop="validation" label="验证规则" />
                            <el-table-column label="操作" width="180">
                              <template #default="{ row }">
                                <el-button size="small" @click="editField(row)">
                                  编辑
                                </el-button>
                                <el-button size="small" @click="previewField(row)">
                                  预览
                                </el-button>
                                <el-button size="small" type="danger" @click="deleteField(row)">
                                  删除
                                </el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                          <h3 class="mt-6 text-lg font-medium">
                            表单验证规则
                          </h3>
                          <div class="border rounded bg-gray-50 p-4">
                            <el-form :model="validationRules" label-width="120px">
                              <el-form-item label="提交前验证">
                                <el-switch v-model="validationRules.beforeSubmit" />
                              </el-form-item>
                              <el-form-item label="自定义规则">
                                <el-input
                                  v-model="validationRules.customRules" type="textarea" :rows="3"
                                  placeholder="输入自定义验证规则"
                                />
                              </el-form-item>
                              <el-form-item label="错误提示">
                                <el-input v-model="validationRules.errorMessage" placeholder="输入验证失败时的提示消息" />
                              </el-form-item>
                            </el-form>
                          </div>
                        </div>
                        <!-- Field Edit Dialog -->
                        <el-dialog v-model="showFieldDialog" title="字段编辑" width="600px">
                          <el-form :model="currentField" label-width="100px">
                            <el-form-item label="字段名称" required>
                              <el-input v-model="currentField.name" />
                            </el-form-item>
                            <el-form-item label="字段类型" required>
                              <el-select v-model="currentField.type" class="w-full">
                                <el-option label="单行文本" value="text" />
                                <el-option label="多行文本" value="textarea" />
                                <el-option label="下拉选择" value="select" />
                                <el-option label="单选按钮" value="radio" />
                                <el-option label="复选框" value="checkbox" />
                                <el-option label="日期" value="date" />
                                <el-option label="时间" value="time" />
                                <el-option label="文件上传" value="file" />
                                <el-option label="分隔符" value="divider" />
                              </el-select>
                            </el-form-item>
                            <el-form-item label="字段标签">
                              <el-input v-model="currentField.label" />
                            </el-form-item>
                            <el-form-item label="是否必填">
                              <el-switch v-model="currentField.required" />
                            </el-form-item>
                            <el-form-item label="默认值">
                              <el-input v-model="currentField.defaultValue" />
                            </el-form-item>
                            <el-form-item label="字段描述">
                              <el-input v-model="currentField.description" />
                            </el-form-item>
                            <el-form-item label="验证规则">
                              <el-select v-model="currentField.validationType" class="w-full">
                                <el-option label="无" value="none" />
                                <el-option label="长度限制" value="length" />
                                <el-option label="数字" value="number" />
                                <el-option label="电话" value="phone" />
                                <el-option label="邮箱" value="email" />
                                <el-option label="日期" value="date" />
                                <el-option label="自定义正则" value="regex" />
                              </el-select>
                            </el-form-item>
                            <el-form-item v-if="currentField.validationType === 'length'" label="长度限制">
                              <div class="flex items-center space-x-2">
                                <el-input-number v-model="currentField.minLength" :min="0" />
                                <span>至</span>
                                <el-input-number v-model="currentField.maxLength" :min="0" />
                              </div>
                            </el-form-item>
                            <el-form-item v-if="currentField.validationType === 'regex'" label="正则表达式">
                              <el-input v-model="currentField.regexPattern" />
                            </el-form-item>
                            <el-form-item label="错误提示">
                              <el-input v-model="currentField.errorMessage" />
                            </el-form-item>
                            <el-form-item label="显示顺序">
                              <el-input-number v-model="currentField.order" :min="0" />
                            </el-form-item>
                            <el-form-item label="字段权限">
                              <el-radio-group v-model="currentField.visibility">
                                <el-radio label="all">
                                  所有人可见
                                </el-radio>
                                <el-radio label="role">
                                  特定角色可见
                                </el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </el-form>
                          <template #footer>
                            <el-button @click="showFieldDialog = false">
                              取消
                            </el-button>
                            <el-button type="primary" @click="saveField">
                              保存
                            </el-button>
                          </template>
                        </el-dialog>
                      </el-tab-pane>
                      <el-tab-pane label="流程设置" name="process">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              处理流程配置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="流程模板选择">
                                <el-select v-model="processConfig.template" placeholder="请选择流程模板" class="w-full">
                                  <el-option
                                    v-for="item in processTemplates" :key="item.id" :label="item.name"
                                    :value="item.id"
                                  />
                                </el-select>
                              </el-form-item>
                              <div class="border rounded bg-gray-50 p-4">
                                <h4 class="mb-2 font-medium">
                                  流程图预览
                                </h4>
                                <div class="h-40 flex items-center justify-center rounded bg-white">
                                  <img
                                    src="https://ai-public.mastergo.com/ai/img_res/77554aa1083ed531432260db7daf748f.jpg"
                                    class="h-full w-full object-contain"
                                  >
                                </div>
                              </div>
                              <div class="grid grid-cols-2 gap-4">
                                <el-form-item label="默认处理部门">
                                  <el-select v-model="processConfig.department" placeholder="请选择部门" class="w-full">
                                    <el-option
                                      v-for="dept in departments" :key="dept.id" :label="dept.name"
                                      :value="dept.id"
                                    />
                                  </el-select>
                                </el-form-item>
                                <el-form-item label="默认处理人">
                                  <el-select v-model="processConfig.handler" placeholder="请选择处理人" class="w-full">
                                    <el-option
                                      v-for="user in users" :key="user.id" :label="user.name"
                                      :value="user.id"
                                    />
                                  </el-select>
                                </el-form-item>
                              </div>
                              <div class="flex items-center space-x-2">
                                <el-form-item label="处理时限" class="mb-0">
                                  <el-input-number v-model="processConfig.timeLimit" :min="1" />
                                </el-form-item>
                                <el-select v-model="processConfig.timeUnit" style="width: 100px;">
                                  <el-option label="小时" value="hour" />
                                  <el-option label="天" value="day" />
                                </el-select>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              自动分类设置
                            </h3>
                            <div class="space-y-4">
                              <el-table :data="classificationRules" border style="width: 100%;">
                                <el-table-column prop="name" label="规则名称" width="150" />
                                <el-table-column prop="condition" label="关键词/条件" />
                                <el-table-column prop="result" label="分类结果" width="120" />
                                <el-table-column prop="priority" label="优先级" width="80" />
                                <el-table-column label="操作" width="180">
                                  <template #default="{ row }">
                                    <el-button size="small" @click="editClassificationRule(row)">
                                      编辑
                                    </el-button>
                                    <el-button
                                      size="small" type="danger"
                                      @click="deleteClassificationRule(row)"
                                    >
                                      删除
                                    </el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                              <el-button
                                type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                @click="showClassificationDialog = true"
                              >
                                <el-icon class="mr-1">
                                  <ElIconPlus />
                                </el-icon>
                                添加规则
                              </el-button>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              优先级设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="默认优先级">
                                <el-radio-group v-model="priorityConfig.default">
                                  <el-radio-button label="low">
                                    低
                                  </el-radio-button>
                                  <el-radio-button label="medium">
                                    中
                                  </el-radio-button>
                                  <el-radio-button label="high">
                                    高
                                  </el-radio-button>
                                </el-radio-group>
                              </el-form-item>
                              <el-table :data="priorityRules" border style="width: 100%;">
                                <el-table-column prop="name" label="规则名称" width="150" />
                                <el-table-column prop="condition" label="条件" />
                                <el-table-column prop="result" label="优先级结果" width="100" />
                                <el-table-column label="操作" width="180">
                                  <template #default="{ row }">
                                    <el-button size="small" @click="editPriorityRule(row)">
                                      编辑
                                    </el-button>
                                    <el-button
                                      size="small" type="danger"
                                      @click="deletePriorityRule(row)"
                                    >
                                      删除
                                    </el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                              <el-button
                                type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                @click="showPriorityDialog = true"
                              >
                                <el-icon class="mr-1">
                                  <ElIconPlus />
                                </el-icon>
                                添加规则
                              </el-button>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              异常处理
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="超时处理策略">
                                <el-select v-model="exceptionConfig.timeoutStrategy" class="w-full">
                                  <el-option label="自动提醒" value="remind" />
                                  <el-option label="自动升级" value="escalate" />
                                  <el-option label="自动转派" value="reassign" />
                                  <el-option label="自动关闭" value="close" />
                                </el-select>
                              </el-form-item>
                              <el-form-item label="重复举报识别">
                                <div class="flex items-center space-x-4">
                                  <el-switch v-model="exceptionConfig.duplicateDetection" />
                                  <el-select
                                    v-if="exceptionConfig.duplicateDetection"
                                    v-model="exceptionConfig.duplicateAction" style="width: 200px;"
                                  >
                                    <el-option label="合并处理" value="merge" />
                                    <el-option label="标记为重复" value="mark" />
                                    <el-option label="自动关闭" value="autoClose" />
                                  </el-select>
                                </div>
                              </el-form-item>
                              <el-form-item label="垃圾信息过滤">
                                <div class="flex items-center space-x-4">
                                  <el-switch v-model="exceptionConfig.spamFilter" />
                                  <el-input
                                    v-if="exceptionConfig.spamFilter" v-model="exceptionConfig.filterRules"
                                    placeholder="输入过滤规则"
                                  />
                                </div>
                              </el-form-item>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="通知设置" name="notification">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              新举报通知
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="通知开关">
                                <el-switch v-model="notificationConfig.newReport.enabled" />
                              </el-form-item>
                              <el-form-item label="通知触发条件">
                                <el-checkbox-group v-model="notificationConfig.newReport.conditions">
                                  <el-checkbox label="all">
                                    每个新举报
                                  </el-checkbox>
                                  <el-checkbox label="specificType">
                                    特定类型举报
                                  </el-checkbox>
                                  <el-checkbox label="specificPriority">
                                    特定优先级举报
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                              <el-form-item label="通知接收人">
                                <el-select
                                  v-model="notificationConfig.newReport.receivers" multiple filterable
                                  class="w-full"
                                >
                                  <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
                                  <el-option-group label="按角色">
                                    <el-option
                                      v-for="role in roles" :key="role.id" :label="role.name"
                                      :value="`role_${role.id}`"
                                    />
                                  </el-option-group>
                                  <el-option-group label="按部门">
                                    <el-option
                                      v-for="dept in departments" :key="dept.id" :label="dept.name"
                                      :value="`dept_${dept.id}`"
                                    />
                                  </el-option-group>
                                </el-select>
                              </el-form-item>
                              <el-form-item label="通知方式">
                                <el-checkbox-group v-model="notificationConfig.newReport.methods">
                                  <el-checkbox label="system">
                                    系统消息
                                  </el-checkbox>
                                  <el-checkbox label="email">
                                    邮件
                                  </el-checkbox>
                                  <el-checkbox label="sms">
                                    短信
                                  </el-checkbox>
                                  <el-checkbox label="wechat">
                                    微信
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                              <el-form-item label="通知模板">
                                <el-tabs type="border-card">
                                  <el-tab-pane label="系统消息">
                                    <el-input
                                      v-model="notificationConfig.newReport.templates.system" type="textarea"
                                      :rows="5" placeholder="输入系统消息模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="邮件">
                                    <el-input
                                      v-model="notificationConfig.newReport.templates.email" type="textarea"
                                      :rows="5" placeholder="输入邮件模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="短信">
                                    <el-input
                                      v-model="notificationConfig.newReport.templates.sms" type="textarea"
                                      :rows="5" placeholder="输入短信模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="微信">
                                    <el-input
                                      v-model="notificationConfig.newReport.templates.wechat" type="textarea"
                                      :rows="5" placeholder="输入微信模板"
                                    />
                                  </el-tab-pane>
                                </el-tabs>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              状态变更通知
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="通知触发状态">
                                <el-checkbox-group v-model="notificationConfig.statusChange.triggers">
                                  <el-checkbox label="received">
                                    已受理
                                  </el-checkbox>
                                  <el-checkbox label="investigating">
                                    调查中
                                  </el-checkbox>
                                  <el-checkbox label="processed">
                                    已处理
                                  </el-checkbox>
                                  <el-checkbox label="closed">
                                    已关闭
                                  </el-checkbox>
                                  <el-checkbox label="rejected">
                                    已拒绝
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                              <el-form-item label="通知接收人设置">
                                <el-select
                                  v-model="notificationConfig.statusChange.receivers" multiple filterable
                                  class="w-full"
                                >
                                  <el-option v-for="user in users" :key="user.id" :label="user.name" :value="user.id" />
                                  <el-option-group label="按角色">
                                    <el-option
                                      v-for="role in roles" :key="role.id" :label="role.name"
                                      :value="`role_${role.id}`"
                                    />
                                  </el-option-group>
                                  <el-option-group label="按部门">
                                    <el-option
                                      v-for="dept in departments" :key="dept.id" :label="dept.name"
                                      :value="`dept_${dept.id}`"
                                    />
                                  </el-option-group>
                                </el-select>
                              </el-form-item>
                              <el-form-item label="通知方式设置">
                                <el-checkbox-group v-model="notificationConfig.statusChange.methods">
                                  <el-checkbox label="system">
                                    系统消息
                                  </el-checkbox>
                                  <el-checkbox label="email">
                                    邮件
                                  </el-checkbox>
                                  <el-checkbox label="sms">
                                    短信
                                  </el-checkbox>
                                  <el-checkbox label="wechat">
                                    微信
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                              <el-form-item label="通知模板设置">
                                <el-tabs type="border-card">
                                  <el-tab-pane label="系统消息">
                                    <el-input
                                      v-model="notificationConfig.statusChange.templates.system" type="textarea"
                                      :rows="5" placeholder="输入系统消息模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="邮件">
                                    <el-input
                                      v-model="notificationConfig.statusChange.templates.email" type="textarea"
                                      :rows="5" placeholder="输入邮件模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="短信">
                                    <el-input
                                      v-model="notificationConfig.statusChange.templates.sms" type="textarea"
                                      :rows="5" placeholder="输入短信模板"
                                    />
                                  </el-tab-pane>
                                  <el-tab-pane label="微信">
                                    <el-input
                                      v-model="notificationConfig.statusChange.templates.wechat" type="textarea"
                                      :rows="5" placeholder="输入微信模板"
                                    />
                                  </el-tab-pane>
                                </el-tabs>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              举报人反馈
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="自动反馈设置">
                                <el-switch v-model="notificationConfig.reporterFeedback.enabled" />
                              </el-form-item>
                              <el-form-item label="反馈触发事件">
                                <el-checkbox-group v-model="notificationConfig.reporterFeedback.triggers">
                                  <el-checkbox label="received">
                                    举报已受理
                                  </el-checkbox>
                                  <el-checkbox label="investigating">
                                    调查已开始
                                  </el-checkbox>
                                  <el-checkbox label="processed">
                                    处理已完成
                                  </el-checkbox>
                                  <el-checkbox label="closed">
                                    举报已关闭
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                              <el-form-item label="反馈内容模板">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input
                                    v-model="notificationConfig.reporterFeedback.template" type="textarea"
                                    :rows="5" placeholder="输入反馈内容模板"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="反馈方式">
                                <el-checkbox-group v-model="notificationConfig.reporterFeedback.methods">
                                  <el-checkbox label="original">
                                    原渠道反馈
                                  </el-checkbox>
                                  <el-checkbox label="email">
                                    邮件
                                  </el-checkbox>
                                  <el-checkbox label="sms">
                                    短信
                                  </el-checkbox>
                                  <el-checkbox label="system">
                                    系统消息
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              提醒设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="超时提醒">
                                <div class="flex items-center space-x-4">
                                  <el-switch v-model="notificationConfig.reminder.enabled" />
                                  <el-input-number
                                    v-if="notificationConfig.reminder.enabled"
                                    v-model="notificationConfig.reminder.timeoutHours" :min="1" :max="720"
                                  />
                                  <span v-if="notificationConfig.reminder.enabled">小时</span>
                                </div>
                              </el-form-item>
                              <el-table :data="notificationConfig.reminder.rules" border style="width: 100%;">
                                <el-table-column prop="timeout" label="超时时间" width="120">
                                  <template #default="{ row }">
                                    {{ row.timeout }}小时
                                  </template>
                                </el-table-column>
                                <el-table-column prop="receivers" label="提醒接收人">
                                  <template #default="{ row }">
                                    <el-tag v-for="receiver in row.receivers" :key="receiver" class="mb-1 mr-1">
                                      {{ getReceiverName(receiver) }}
                                    </el-tag>
                                  </template>
                                </el-table-column>
                                <el-table-column prop="methods" label="提醒方式" width="180">
                                  <template #default="{ row }">
                                    <el-tag v-for="method in row.methods" :key="method" class="mb-1 mr-1">
                                      {{ methodLabels[method] }}
                                    </el-tag>
                                  </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                  <template #default="{ row }">
                                    <el-button size="small" @click="editReminderRule(row)">
                                      编辑
                                    </el-button>
                                    <el-button
                                      size="small" type="danger"
                                      @click="deleteReminderRule(row)"
                                    >
                                      删除
                                    </el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                              <el-button
                                type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                @click="showReminderDialog = true"
                              >
                                <el-icon class="mr-1">
                                  <ElIconPlus />
                                </el-icon>
                                添加提醒规则
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="隐私保护" name="privacy">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              匿名设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="允许匿名举报">
                                <el-switch v-model="privacyConfig.allowAnonymous" />
                              </el-form-item>
                              <el-form-item label="匿名举报权限">
                                <el-radio-group v-model="privacyConfig.anonymousPermission">
                                  <el-radio label="all">
                                    所有人
                                  </el-radio>
                                  <el-radio label="internal">
                                    内部员工
                                  </el-radio>
                                  <el-radio label="specific">
                                    特定人员
                                  </el-radio>
                                </el-radio-group>
                              </el-form-item>
                              <el-form-item label="匿名举报功能限制">
                                <el-checkbox-group v-model="privacyConfig.anonymousLimits">
                                  <el-checkbox label="noAttachment">
                                    不能上传附件
                                  </el-checkbox>
                                  <el-checkbox label="noTracking">
                                    不能跟踪进度
                                  </el-checkbox>
                                  <el-checkbox label="noFeedback">
                                    不能接收反馈
                                  </el-checkbox>
                                </el-checkbox-group>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              敏感信息处理
                            </h3>
                            <div class="space-y-4">
                              <el-table :data="privacyConfig.sensitiveDataRules" border style="width: 100%;">
                                <el-table-column prop="type" label="信息类型" width="120" />
                                <el-table-column prop="pattern" label="匹配规则" />
                                <el-table-column prop="method" label="脱敏方式" width="120" />
                                <el-table-column label="操作" width="120">
                                  <template #default="{ row }">
                                    <el-button size="small" @click="editSensitiveRule(row)">
                                      编辑
                                    </el-button>
                                    <el-button
                                      size="small" type="danger"
                                      @click="deleteSensitiveRule(row)"
                                    >
                                      删除
                                    </el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                              <el-button
                                type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                @click="showSensitiveDialog = true"
                              >
                                <el-icon class="mr-1">
                                  <ElIconPlus />
                                </el-icon>
                                添加脱敏规则
                              </el-button>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              数据保留策略
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="数据保留期限">
                                <div class="flex items-center space-x-2">
                                  <el-input-number v-model="privacyConfig.retentionPeriod" :min="1" />
                                  <el-select v-model="privacyConfig.retentionUnit" style="width: 100px;">
                                    <el-option label="月" value="month" />
                                    <el-option label="年" value="year" />
                                  </el-select>
                                </div>
                              </el-form-item>
                              <el-form-item label="保留期满后处理">
                                <el-radio-group v-model="privacyConfig.expirationAction">
                                  <el-radio label="delete">
                                    彻底删除
                                  </el-radio>
                                  <el-radio label="anonymize">
                                    匿名化保留
                                  </el-radio>
                                  <el-radio label="archive">
                                    归档
                                  </el-radio>
                                </el-radio-group>
                              </el-form-item>
                              <el-form-item label="特殊数据处理规则">
                                <el-input
                                  v-model="privacyConfig.specialRules" type="textarea" :rows="3"
                                  placeholder="输入针对特定类型数据的特殊保留策略"
                                />
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              访问权限控制
                            </h3>
                            <div class="space-y-4">
                              <el-table :data="privacyConfig.accessControlRules" border style="width: 100%;">
                                <el-table-column prop="role" label="角色" width="120" />
                                <el-table-column prop="view" label="可查看内容" />
                                <el-table-column prop="edit" label="可编辑内容" />
                                <el-table-column prop="process" label="可处理内容" />
                                <el-table-column label="操作" width="120">
                                  <template #default="{ row }">
                                    <el-button size="small" @click="editAccessRule(row)">
                                      编辑
                                    </el-button>
                                    <el-button size="small" type="danger" @click="deleteAccessRule(row)">
                                      删除
                                    </el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                              <el-button
                                type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                @click="showAccessDialog = true"
                              >
                                <el-icon class="mr-1">
                                  <ElIconPlus />
                                </el-icon>
                                添加角色权限
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="外观设置" name="appearance">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              页面设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="页面标题">
                                <el-input v-model="appearanceConfig.pageTitle" placeholder="请输入页面标题" />
                              </el-form-item>
                              <el-form-item label="系统Logo">
                                <el-upload
                                  action="" :auto-upload="false" :on-change="handleLogoChange"
                                  :show-file-list="false"
                                >
                                  <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                      <ElIconUpload />
                                    </el-icon>
                                    上传Logo
                                  </el-button>
                                </el-upload>
                                <div v-if="appearanceConfig.logo" class="mt-2">
                                  <img :src="appearanceConfig.logo" class="h-16 object-contain">
                                </div>
                              </el-form-item>
                              <el-form-item label="页头内容">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input
                                    v-model="appearanceConfig.headerContent" type="textarea" :rows="4"
                                    placeholder="请输入页头内容"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="页脚内容">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input
                                    v-model="appearanceConfig.footerContent" type="textarea" :rows="4"
                                    placeholder="请输入页脚内容"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="背景设置">
                                <el-radio-group v-model="appearanceConfig.backgroundType">
                                  <el-radio-button label="color">
                                    纯色
                                  </el-radio-button>
                                  <el-radio-button label="image">
                                    图片
                                  </el-radio-button>
                                </el-radio-group>
                                <div v-if="appearanceConfig.backgroundType === 'color'" class="mt-2">
                                  <el-color-picker v-model="appearanceConfig.backgroundColor" />
                                </div>
                                <div v-else class="mt-2">
                                  <el-upload
                                    action="" :auto-upload="false" :on-change="handleBgImageChange"
                                    :show-file-list="false"
                                  >
                                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                      <el-icon class="mr-1">
                                        <ElIconUpload />
                                      </el-icon>
                                      上传背景图
                                    </el-button>
                                  </el-upload>
                                  <div v-if="appearanceConfig.backgroundImage" class="mt-2">
                                    <img
                                      :src="appearanceConfig.backgroundImage"
                                      class="h-32 w-full rounded object-cover"
                                    >
                                  </div>
                                </div>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              主题设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="主题色选择">
                                <el-color-picker v-model="appearanceConfig.themeColor" />
                              </el-form-item>
                              <el-form-item label="按钮样式">
                                <el-radio-group v-model="appearanceConfig.buttonStyle">
                                  <el-radio-button label="default">
                                    默认
                                  </el-radio-button>
                                  <el-radio-button label="rounded">
                                    圆角
                                  </el-radio-button>
                                  <el-radio-button label="outline">
                                    线框
                                  </el-radio-button>
                                </el-radio-group>
                              </el-form-item>
                              <el-form-item label="表单样式">
                                <el-radio-group v-model="appearanceConfig.formStyle">
                                  <el-radio-button label="default">
                                    默认
                                  </el-radio-button>
                                  <el-radio-button label="modern">
                                    现代
                                  </el-radio-button>
                                  <el-radio-button label="minimal">
                                    极简
                                  </el-radio-button>
                                </el-radio-group>
                              </el-form-item>
                              <el-form-item label="字体设置">
                                <el-select v-model="appearanceConfig.fontFamily" class="w-full">
                                  <el-option label="系统默认" value="system" />
                                  <el-option label="微软雅黑" value="Microsoft YaHei" />
                                  <el-option label="苹方" value="PingFang SC" />
                                  <el-option label="思源黑体" value="Source Han Sans CN" />
                                  <el-option label="Arial" value="Arial" />
                                </el-select>
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              欢迎语与说明文字
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="欢迎语">
                                <el-input v-model="appearanceConfig.welcomeText" placeholder="请输入欢迎语" />
                              </el-form-item>
                              <el-form-item label="举报说明">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input
                                    v-model="appearanceConfig.reportInstructions" type="textarea" :rows="4"
                                    placeholder="请输入举报说明"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="隐私声明">
                                <div class="border rounded">
                                  <div class="border-b bg-gray-50 p-2">
                                    <el-button size="small" class="mr-1">
                                      B
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      I
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      U
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      链接
                                    </el-button>
                                    <el-button size="small" class="mr-1">
                                      图片
                                    </el-button>
                                  </div>
                                  <el-input
                                    v-model="appearanceConfig.privacyStatement" type="textarea" :rows="4"
                                    placeholder="请输入隐私声明"
                                  />
                                </div>
                              </el-form-item>
                              <el-form-item label="感谢语">
                                <el-input v-model="appearanceConfig.thankYouText" placeholder="请输入感谢语" />
                              </el-form-item>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              语言设置
                            </h3>
                            <div class="space-y-4">
                              <el-form-item label="默认语言">
                                <el-select v-model="appearanceConfig.defaultLanguage" class="w-full">
                                  <el-option label="简体中文" value="zh-CN" />
                                  <el-option label="English" value="en-US" />
                                  <el-option label="日本語" value="ja-JP" />
                                </el-select>
                              </el-form-item>
                              <el-form-item label="支持多语言">
                                <el-switch v-model="appearanceConfig.multiLanguageSupport" />
                              </el-form-item>
                              <div v-if="appearanceConfig.multiLanguageSupport">
                                <h4 class="mb-2 font-medium">
                                  多语言设置
                                </h4>
                                <el-table :data="appearanceConfig.languages" border style="width: 100%;">
                                  <el-table-column prop="language" label="语言" width="120" />
                                  <el-table-column prop="status" label="状态" width="100">
                                    <template #default="{ row }">
                                      <el-tag :type="row.enabled ? 'success' : 'info'">
                                        {{ row.enabled ? '启用' : '禁用' }}
                                      </el-tag>
                                    </template>
                                  </el-table-column>
                                  <el-table-column prop="progress" label="翻译完成度" width="120">
                                    <template #default="{ row }">
                                      <el-progress :percentage="row.progress" :stroke-width="12" />
                                    </template>
                                  </el-table-column>
                                  <el-table-column label="操作" width="180">
                                    <template #default="{ row }">
                                      <el-button size="small" @click="editLanguage(row)">
                                        编辑
                                      </el-button>
                                      <el-button size="small" type="danger" @click="deleteLanguage(row)">
                                        删除
                                      </el-button>
                                    </template>
                                  </el-table-column>
                                </el-table>
                                <el-button
                                  type="primary" class="!rounded-button mt-2 whitespace-nowrap"
                                  @click="showLanguageDialog = true"
                                >
                                  <el-icon class="mr-1">
                                    <ElIconPlus />
                                  </el-icon>
                                  添加语言
                                </el-button>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              布局预览
                            </h3>
                            <div class="border rounded bg-gray-50 p-4">
                              <div class="mb-4 flex justify-between">
                                <el-radio-group v-model="appearanceConfig.previewMode">
                                  <el-radio-button label="desktop">
                                    桌面
                                  </el-radio-button>
                                  <el-radio-button label="tablet">
                                    平板
                                  </el-radio-button>
                                  <el-radio-button label="mobile">
                                    手机
                                  </el-radio-button>
                                </el-radio-group>
                                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                  在新窗口打开预览
                                </el-button>
                              </div>
                              <div class="border rounded bg-white p-4">
                                <!-- 预览内容 -->
                                <div class="mb-4 flex items-center justify-between">
                                  <div class="flex items-center">
                                    <img v-if="appearanceConfig.logo" :src="appearanceConfig.logo" class="mr-2 h-10">
                                    <h2 class="text-xl font-bold">
                                      {{ appearanceConfig.pageTitle || '举报系统' }}
                                    </h2>
                                  </div>
                                  <div class="flex items-center space-x-2">
                                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                                      登录
                                    </el-button>
                                    <el-button size="small" class="!rounded-button whitespace-nowrap">
                                      注册
                                    </el-button>
                                  </div>
                                </div>
                                <div v-if="appearanceConfig.headerContent" class="mb-4">
                                  <div v-html="appearanceConfig.headerContent" />
                                </div>
                                <div class="mb-4 rounded bg-gray-100 p-6">
                                  <h3 class="mb-2 text-lg font-medium">
                                    {{ appearanceConfig.welcomeText || '欢迎举报' }}
                                  </h3>
                                  <p class="mb-4 text-gray-600">
                                    {{ appearanceConfig.reportInstructions || '请填写举报信息' }}
                                  </p>
                                  <el-form label-position="top">
                                    <el-form-item label="举报类型">
                                      <el-select placeholder="请选择举报类型" class="w-full">
                                        <el-option label="贪污腐败" value="1" />
                                        <el-option label="性骚扰" value="2" />
                                        <el-option label="财务违规" value="3" />
                                      </el-select>
                                    </el-form-item>
                                    <el-form-item label="举报内容">
                                      <el-input type="textarea" :rows="3" placeholder="请输入举报内容" />
                                    </el-form-item>
                                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                      提交举报
                                    </el-button>
                                  </el-form>
                                </div>
                                <div v-if="appearanceConfig.footerContent" class="text-sm text-gray-500">
                                  <div v-html="appearanceConfig.footerContent" />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="统计数据" name="statistics">
                        <div class="space-y-6">
                          <!-- 使用情况概览 -->
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              使用情况概览
                            </h3>
                            <div class="grid grid-cols-4 mb-4 gap-4">
                              <el-card shadow="hover">
                                <div class="flex items-center">
                                  <el-icon class="mr-2 text-2xl text-blue-500">
                                    <ElIconDataLine />
                                  </el-icon>
                                  <div>
                                    <div class="text-sm text-gray-500">
                                      总举报数
                                    </div>
                                    <div class="text-xl font-bold">
                                      1,248
                                    </div>
                                  </div>
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex items-center">
                                  <el-icon class="mr-2 text-2xl text-green-500">
                                    <ElIconDocumentChecked />
                                  </el-icon>
                                  <div>
                                    <div class="text-sm text-gray-500">
                                      有效举报比例
                                    </div>
                                    <div class="text-xl font-bold">
                                      78.5%
                                    </div>
                                  </div>
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex items-center">
                                  <el-icon class="mr-2 text-2xl text-purple-500">
                                    <ElIconTimer />
                                  </el-icon>
                                  <div>
                                    <div class="text-sm text-gray-500">
                                      月平均举报量
                                    </div>
                                    <div class="text-xl font-bold">
                                      156
                                    </div>
                                  </div>
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex items-center">
                                  <el-icon class="mr-2 text-2xl text-orange-500">
                                    <ElIconClock />
                                  </el-icon>
                                  <div>
                                    <div class="text-sm text-gray-500">
                                      平均处理时间
                                    </div>
                                    <div class="text-xl font-bold">
                                      3.2天
                                    </div>
                                  </div>
                                </div>
                              </el-card>
                            </div>
                            <div class="h-80 border rounded bg-gray-50 p-4">
                              <div class="mb-4 flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                  <el-select v-model="trendTimeRange" size="small" style="width: 120px;">
                                    <el-option label="近7天" value="7" />
                                    <el-option label="近30天" value="30" />
                                    <el-option label="近90天" value="90" />
                                    <el-option label="近1年" value="365" />
                                  </el-select>
                                  <el-select v-model="trendMetric" size="small" style="width: 150px;">
                                    <el-option label="举报数量" value="count" />
                                    <el-option label="有效举报" value="valid" />
                                    <el-option label="处理时效" value="time" />
                                  </el-select>
                                </div>
                                <el-button size="small" class="!rounded-button whitespace-nowrap">
                                  导出数据
                                </el-button>
                              </div>
                              <div ref="trendChart" class="h-64 w-full" />
                            </div>
                          </div>
                          <!-- 举报类型分布 -->
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              举报类型分布
                            </h3>
                            <div class="grid grid-cols-2 gap-4">
                              <div class="h-80 border rounded bg-gray-50 p-4">
                                <div ref="typeChart" class="h-64 w-full" />
                              </div>
                              <div>
                                <el-table :data="typeData" border style="width: 100%;">
                                  <el-table-column prop="type" label="举报类型" width="150" />
                                  <el-table-column prop="count" label="数量" width="100" />
                                  <el-table-column prop="percentage" label="占比" width="100">
                                    <template #default="{ row }">
                                      {{ row.percentage }}%
                                    </template>
                                  </el-table-column>
                                  <el-table-column prop="validRate" label="有效比例" width="120">
                                    <template #default="{ row }">
                                      <el-progress :percentage="row.validRate" :stroke-width="12" />
                                    </template>
                                  </el-table-column>
                                </el-table>
                                <div class="mt-4 rounded bg-gray-50 p-3 text-sm">
                                  <div class="font-medium">
                                    对比分析
                                  </div>
                                  <p class="text-gray-500">
                                    本渠道的贪污腐败类举报占比高于其他渠道15%，建议加强相关领域合规培训。
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <!-- 举报处理统计 -->
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              举报处理统计
                            </h3>
                            <div class="grid grid-cols-3 gap-4">
                              <div class="h-80 border rounded bg-gray-50 p-4">
                                <h4 class="mb-2 font-medium">
                                  处理状态分布
                                </h4>
                                <div ref="statusChart" class="h-64 w-full" />
                              </div>
                              <div class="h-80 border rounded bg-gray-50 p-4">
                                <h4 class="mb-2 font-medium">
                                  处理时效分析
                                </h4>
                                <div ref="timeChart" class="h-64 w-full" />
                              </div>
                              <div class="border rounded bg-gray-50 p-4">
                                <h4 class="mb-2 font-medium">
                                  处理完成率
                                </h4>
                                <div class="h-64 flex flex-col items-center justify-center">
                                  <div class="mb-2 text-4xl text-green-500 font-bold">
                                    89.2%
                                  </div>
                                  <div class="text-gray-500">
                                    高于系统平均 3.5%
                                  </div>
                                  <div class="mt-4 w-full">
                                    <el-progress :percentage="89.2" :stroke-width="16" />
                                  </div>
                                  <div class="mt-2 text-sm text-gray-500">
                                    平均完成时间: 2.8天
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <!-- 用户体验分析 -->
                          <div>
                            <h3 class="mb-4 text-lg font-medium">
                              用户体验分析
                            </h3>
                            <div class="grid grid-cols-4 mb-4 gap-4">
                              <el-card shadow="hover">
                                <div class="flex flex-col items-center">
                                  <div class="mb-1 text-sm text-gray-500">
                                    表单提交完成率
                                  </div>
                                  <div class="text-xl font-bold">
                                    92.3%
                                  </div>
                                  <el-progress :percentage="92.3" :stroke-width="8" class="mt-2 w-full" />
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex flex-col items-center">
                                  <div class="mb-1 text-sm text-gray-500">
                                    页面停留时间
                                  </div>
                                  <div class="text-xl font-bold">
                                    4.2分钟
                                  </div>
                                  <el-progress :percentage="84" :stroke-width="8" class="mt-2 w-full" />
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex flex-col items-center">
                                  <div class="mb-1 text-sm text-gray-500">
                                    用户反馈评分
                                  </div>
                                  <div class="text-xl font-bold">
                                    4.5/5
                                  </div>
                                  <el-progress :percentage="90" :stroke-width="8" class="mt-2 w-full" />
                                </div>
                              </el-card>
                              <el-card shadow="hover">
                                <div class="flex flex-col items-center">
                                  <div class="mb-1 text-sm text-gray-500">
                                    渠道推荐率
                                  </div>
                                  <div class="text-xl font-bold">
                                    86%
                                  </div>
                                  <el-progress :percentage="86" :stroke-width="8" class="mt-2 w-full" />
                                </div>
                              </el-card>
                            </div>
                            <div class="border rounded bg-gray-50 p-4">
                              <h4 class="mb-2 font-medium">
                                用户反馈详情
                              </h4>
                              <el-table :data="feedbackData" border style="width: 100%;">
                                <el-table-column prop="date" label="日期" width="120" />
                                <el-table-column prop="user" label="用户" width="120" />
                                <el-table-column prop="rating" label="评分" width="100">
                                  <template #default="{ row }">
                                    <el-rate v-model="row.rating" disabled />
                                  </template>
                                </el-table-column>
                                <el-table-column prop="comment" label="反馈内容" />
                              </el-table>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </el-card>
                </div>
                <!-- 右侧辅助区 -->
                <div class="w-80 space-y-6">
                  <el-card shadow="hover">
                    <h3 class="mb-4 text-lg font-medium">
                      使用指南
                    </h3>
                    <div class="text-sm space-y-3">
                      <p>1. 填写渠道基本信息，带*号为必填项</p>
                      <p>2. 根据渠道类型配置相应参数</p>
                      <p>3. 完善渠道描述和使用说明</p>
                      <p>4. 保存前可点击预览查看效果</p>
                      <el-link type="primary" class="mt-2">
                        查看完整文档
                      </el-link>
                    </div>
                  </el-card>
                  <el-card shadow="hover">
                    <h3 class="mb-4 text-lg font-medium">
                      推荐模板
                    </h3>
                    <div class="space-y-3">
                      <div class="cursor-pointer border rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          员工举报渠道模板
                        </div>
                        <div class="text-sm text-gray-500">
                          适用于企业内部员工举报
                        </div>
                      </div>
                      <div class="cursor-pointer border rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          客户投诉渠道模板
                        </div>
                        <div class="text-sm text-gray-500">
                          适用于客户投诉和建议
                        </div>
                      </div>
                      <div class="cursor-pointer border rounded p-3 hover:bg-blue-50">
                        <div class="font-medium">
                          供应商举报模板
                        </div>
                        <div class="text-sm text-gray-500">
                          适用于供应商举报不当行为
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover">
                    <h3 class="mb-4 text-lg font-medium">
                      渠道测试
                    </h3>
                    <div class="space-y-3">
                      <el-button class="!rounded-button w-full whitespace-nowrap">
                        测试表单提交
                      </el-button>
                      <el-button class="!rounded-button w-full whitespace-nowrap">
                        测试通知发送
                      </el-button>
                      <el-button class="!rounded-button w-full whitespace-nowrap">
                        测试流程流转
                      </el-button>
                      <div class="rounded bg-gray-50 p-3 text-sm">
                        <div class="font-medium">
                          测试结果
                        </div>
                        <div class="text-gray-500">
                          尚未执行测试
                        </div>
                      </div>
                    </div>
                  </el-card>
                  <el-card shadow="hover">
                    <h3 class="mb-4 text-lg font-medium">
                      最佳实践
                    </h3>
                    <div class="text-sm space-y-3">
                      <p>• 为不同举报类型设置独立渠道</p>
                      <p>• 定期检查渠道可用性</p>
                      <p>• 设置自动回复提升用户体验</p>
                      <p>• 明确告知举报人隐私保护措施</p>
                      <p>• 定期分析举报数据优化流程</p>
                    </div>
                  </el-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-card {
    border-radius: 8px;
  }

  .el-tabs {
    --el-tabs-header-height: 48px;
  }

  .el-tabs__item {
    font-size: 14px;
    font-weight: 500;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-weight: 500;
  }

  .el-input__inner {
    border-radius: 4px;
  }

  .el-button {
    border-radius: 4px;
  }
</style>
