<script setup>
import { onMounted, ref } from 'vue'
import FixedActionBar from '@/components/FixedActionBar/index.vue'
import employeePicker from '@/components/employee-picker.vue'
import TreePicker from '@/components/tree-picker.vue'
import contractApi from '@/api/contract/index.js'
import orgApi from '@/api/org/index.js'
import getDictData from '@/utils/dict.js'
import {
  useUserStore,
} from '@/store/pinia.js'

const userStore = useUserStore()

// 从组织树中查找部门信息的辅助函数
function findDepartmentInTree(tree, targetId) {
  for (const node of tree) {
    if (node.id === targetId) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findDepartmentInTree(node.children, targetId)
      if (found) {
        return found
      }
    }
  }
  return null
}
const reviewTypes = ref([])
const selectedReviewTypeId = ref('')
// const selectedReviewType = ref('');
// 底部按钮配置
const footerButtons = ref([
  {
    text: '返回',
    type: 'cancel',
    slotName: 'cancel',
    bgColor: '#f5f5f5',
    textColor: '#666',
  },
  {
    text: '提交',
    type: 'submit',
    slotName: 'submit',
    bgColor: '#3c9cff',
    textColor: '#fff',
  },
])
// 审查成员表格数据
const memberList = ref([
  {
    id: 1,
    departmentId: '',
    departmentName: '',
    employeeId: '',
    employeeName: '',
    employees: [], // 当前部门的员工列表
    loadingEmployees: false, // 员工列表加载状态
  },
])

const deadline = ref('')
const description = ref('')
const attachments = ref([])
const responsiblePerson = ref('')
const responsiblePersonId = ref('')

// 处理负责人选择变化
function onResponsiblePersonChange(employee) {
  responsiblePerson.value = employee.name
  responsiblePersonId.value = employee.id
  console.log('选择的负责人:', employee)
}

// 控制是否显示保存按钮和添加成员按钮
const showEditControls = ref(true)

// 跟踪审批流程是否已保存
const isApprovalSaved = ref(false)

// 页面参数
const pageParams = ref({
  id: null,
  type: null,
  complianceReview: null,
})

// 获取审查类型字典数据
async function getReviewTypes() {
  try {
    const result = await getDictData(89)
    if (result && result.length > 0) {
      reviewTypes.value = result.map(item => ({
        name: item.name,
        value: item.value,
      }))
      console.log('审查类型字典数据:', reviewTypes.value)
    }
  }
  catch (error) {
    console.error('获取审查类型字典数据失败:', error)
  }
}

// 页面加载时获取参数并检查现有审批流程
onMounted(async () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  if (currentPage.options) {
    pageParams.value.id = currentPage.options.id
    pageParams.value.type = currentPage.options.type
    console.log('页面参数:', pageParams.value)
    if (currentPage.options.complianceReview) {
      pageParams.value.complianceReview = JSON.parse(currentPage.options.complianceReview)
      if (pageParams.value.complianceReview && pageParams.value.complianceReview.id) {
        const obj = pageParams.value.complianceReview
        selectedReviewTypeId.value = obj.periodType
        deadline.value = obj.deadlineTime
        description.value = obj.remark
        responsiblePersonId.value = obj.director
      }
    }
  }

  // 获取审查类型字典数据
  await getReviewTypes()

  // 调用getApprovalProcess检查是否已有审批流程
  await loadExistingApprovalProcess()
})

// 获取动态的流程类型和名称
function getProcessConfig() {
  const processTypeMap = {
    contract: 'CONTRACT',
    decision: 'DECISION',
    supplemental: 'SUPPLEMENTAL',
  }

  const processNameMap = {
    contract: '合同审批流程',
    decision: '重大决策审批流程',
    supplemental: '其它审批流程',
  }

  const type = pageParams.value.type || 'contract'
  return {
    processType: processTypeMap[type] || 'CONTRACT',
    processName: processNameMap[type] || '合同审批流程',
  }
}

// 加载现有审批流程
async function loadExistingApprovalProcess() {
  try {
    const { processType } = getProcessConfig()
    const response = await contractApi.getApprovalProcess(processType)

    if (response && response.approverList && response.approverList.length > 0) {
      // 如果有现有的审批流程，渲染到表格中
      showEditControls.value = false

      // 清空当前的memberList
      memberList.value = []

      // 将approverList转换为memberList格式
      for (let i = 0; i < response.approverList.length; i++) {
        const approver = response.approverList[i]

        // 根据orgUnitId获取部门信息
        const orgTree = userStore.getOrgTree() || []
        const department = findDepartmentInTree(orgTree, approver.orgUnitId)

        // 根据employeeId获取员工信息
        let employeeName = ''
        let employees = []
        if (department) {
          try {
            const empResponse = await orgApi.getEmpByUnitId(approver.orgUnitId)
            employees = empResponse.content || []
            const employee = employees.find(emp => emp.id === approver.employeeId)
            if (employee) {
              employeeName = employee.realName
            }
          }
          catch (error) {
            console.error('获取员工信息失败:', error)
          }
        }

        memberList.value.push({
          id: Date.now() + i,
          departmentId: approver.orgUnitId,
          departmentName: department ? department.name : '',
          employeeId: approver.employeeId,
          employeeName,
          employees,
          loadingEmployees: false,
        })
      }
    }
    else {
      // 如果没有现有审批流程，保持手动配置模式
      showEditControls.value = true
      isApprovalSaved.value = false
    }
  }
  catch (error) {
    console.error('获取审批流程失败:', error)
    // 出错时保持手动配置模式
    showEditControls.value = true
  }
}

function footerCancel() {
  uni.navigateBack()
}

// const footerSaveDraft = () => {
//     uni.showToast({
//         title: '已保存草稿',
//         icon: 'success'
//     });
// };

async function footerSubmitReview() {
  try {
    // 检查是否需要保存审批流程
    if (showEditControls.value && !isApprovalSaved.value) {
      uni.showModal({
        title: '提示',
        content: '您还未保存审批流程，是否继续提交？',
        success(res) {
          if (res.confirm) {
            // 用户确认继续提交
            performSubmit()
          }
        },
      })
      return
    }

    // 直接提交
    await performSubmit()
  }
  catch (error) {
    uni.hideLoading()
    console.error('提交失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    })
  }
}

// 执行提交的具体逻辑
async function performSubmit() {
  try {
    // 表单验证
    if (!responsiblePersonId.value) {
      uni.showToast({
        title: '请选择审查负责人',
        icon: 'none',
      })
      return
    }

    if (!deadline.value) {
      uni.showToast({
        title: '请选择截止日期',
        icon: 'none',
      })
      return
    }

    // 验证审查成员
    const validMembers = memberList.value.filter(member =>
      member.departmentId && member.employeeId,
    )
    if (memberList.value.length === 0) {
      uni.showToast({
        title: '请至少选择一个审查成员',
        icon: 'none',
      })
      return
    }

    if (!description.value.trim()) {
      uni.showToast({
        title: '请输入审查说明',
        icon: 'none',
      })
      return
    }

    uni.showLoading({
      title: '提交中...',
    })

    const { processType } = getProcessConfig()

    const params = {
      objectId: Number.parseInt(pageParams.value.id),
      reviewType: processType,
      periodType: selectedReviewTypeId.value || 'MONTHLY',
      director: responsiblePersonId.value, // 审核负责人ID，这里暂时使用固定值，实际应该从用户选择中获取
      deadlineTime: deadline.value,
      remark: description.value,
      // attachments: attachments.value,
      // auditedOpinion: '',
      // auditedConclusion: 0, // 0表示待审核
      // handleSuggestion: '',
      // metadata: JSON.stringify({
      //     reviewTypeName: selectedReviewType.value,
      //     responsiblePerson: responsiblePerson.value,
      //     members: validMembers.map(member => ({
      //         departmentId: member.departmentId,
      //         departmentName: member.departmentName,
      //         employeeId: member.employeeId,
      //         employeeName: member.employeeName
      //     })),
      //     attachments: attachments.value
      // }),
      // complianceReviewList: [],
      // complianceReviewAccordingList: []
    }

    // 调用API
    const result = await contractApi.createReview(params)

    uni.hideLoading()

    if (result) {
      uni.showToast({
        title: '提交成功',
        icon: 'success',
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  catch (error) {
    uni.hideLoading()
    console.error('提交失败:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    })
  }
}

// 添加成员
function addMember() {
  const newId = Date.now() // 使用时间戳作为唯一ID
  memberList.value.push({
    id: newId,
    departmentId: '',
    departmentName: '',
    employeeId: '',
    employeeName: '',
    employees: [],
    loadingEmployees: false,
  })
  // 重置保存状态
  if (showEditControls.value) {
    isApprovalSaved.value = false
  }
}

// 删除成员
function removeMember(index) {
  if (memberList.value.length > 1) {
    memberList.value.splice(index, 1)
    // 重置保存状态
    if (showEditControls.value) {
      isApprovalSaved.value = false
    }
  }
  else {
    uni.showToast({
      title: '至少保留一个成员',
      icon: 'none',
    })
  }
}

// 部门选择变化
async function onDepartmentChange(index, event) {
  console.log('部门选择变化:', index, event)
  const selectedNode = event.nodes && event.nodes[0]

  if (!selectedNode) {
    return
  }

  memberList.value[index].departmentId = selectedNode.id
  memberList.value[index].departmentName = selectedNode.name
  memberList.value[index].employeeId = ''
  memberList.value[index].employeeName = ''
  memberList.value[index].loadingEmployees = true

  // 重置保存状态
  if (showEditControls.value) {
    isApprovalSaved.value = false
  }

  // 获取该部门的员工列表
  try {
    const response = await orgApi.getEmpByUnitId(selectedNode.id)
    const employees = response.content || []
    memberList.value[index].employees = employees
    memberList.value[index].loadingEmployees = false
  }
  catch (error) {
    console.error('获取员工列表失败:', error)
    uni.showToast({
      title: '获取员工列表失败',
      icon: 'none',
    })
    memberList.value[index].employees = []
    memberList.value[index].loadingEmployees = false
  }
}

// 员工选择变化
function onEmployeeChange(index, event) {
  // 检查是否正在加载员工数据
  if (memberList.value[index].loadingEmployees) {
    uni.showToast({
      title: '员工数据加载中，请稍候',
      icon: 'none',
    })
    return
  }

  // 检查员工列表是否为空
  if (!memberList.value[index].employees || memberList.value[index].employees.length === 0) {
    uni.showToast({
      title: '该部门暂无员工数据',
      icon: 'none',
    })
    return
  }

  const selectedIndex = event.detail.value
  const selectedEmployee = memberList.value[index].employees[selectedIndex]

  if (selectedEmployee) {
    memberList.value[index].employeeId = selectedEmployee.id
    memberList.value[index].employeeName = selectedEmployee.realName
    // 重置保存状态
    if (showEditControls.value) {
      isApprovalSaved.value = false
    }
  }
}

function onDateChange(e) {
  deadline.value = e.detail.value
}

// onDescriptionChange函数已移除，因为使用uv-textarea的v-model绑定后不再需要它

async function saveApproval() {
  // 验证审查负责人
  // if (!responsiblePersonId.value) {
  //     uni.showToast({
  //         title: '请选择审查负责人',
  //         icon: 'none'
  //     });
  //     return;
  // }

  // 验证是否有有效的成员
  const validMembers = memberList.value.filter(member =>
    member.departmentId && member.employeeId,
  )

  if (validMembers.length === 0) {
    uni.showToast({
      title: '请至少添加一个审查成员',
      icon: 'none',
    })
    return
  }

  // 构建审批人列表
  const { processType, processName } = getProcessConfig()
  const approverList = validMembers.map((member, index) => ({
    employeeId: member.employeeId,
    orgUnitId: member.departmentId,
    level: index + 1,
    processType,
  }))

  const params = {
    processName,
    processType,
    approverList,
  }

  try {
    uni.showLoading({
      title: '保存中...',
    })

    await contractApi.createApproval(params)

    // 标记审批流程已保存
    isApprovalSaved.value = true

    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  }
  catch (error) {
    uni.hideLoading()
    console.error('保存审批流程失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  }
}

function goBack() {
  uni.navigateBack()
}

function cancel() {
  uni.navigateBack()
}

function saveDraft() {
  uni.showToast({
    title: '已保存草稿',
    icon: 'success',
  })
}

function submitReview() {
  uni.showLoading({
    title: '提交中...',
  })
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 1000)
}
</script>

<template>
  <view class="initiate-container">
    <!-- Main Content -->
    <scroll-view class="content" scroll-y>
      <!-- Review Form -->
      <view class="card form-card">
        <!-- Review Type -->
        <view class="form-item">
          <text class="form-label">
            1. 审查类型
          </text>
          <picker-input
            v-model="selectedReviewTypeId" :columns="[reviewTypes]" display-key="name"
            value-key="value" placeholder="请选择审查类型"
          />
        </view>

        <!-- Responsible Person -->
        <view class="form-item">
          <text class="form-label">
            2. 审查负责人
          </text>
          <employee-picker
            v-model="responsiblePersonId" placeholder="选择负责人"
            @change="onResponsiblePersonChange"
          />
        </view>

        <!-- Review Members -->
        <view class="form-item">
          <view class="member-header">
            <text class="form-label">
              3. 审查成员
            </text>
            <view v-if="showEditControls" class="save-btn" @tap="saveApproval">
              <text class="save-btn-text">
                保存
              </text>
            </view>
          </view>
          <view class="member-table-container">
            <!-- 表格头部 -->
            <view class="table-header">
              <view class="header-cell table-cell">
                序号
              </view>
              <view class="header-cell table-cell">
                部门
              </view>
              <view class="header-cell table-cell">
                员工
              </view>
              <view class="header-cell table-cell">
                操作
              </view>
            </view>
            <!-- 表格内容 -->
            <view class="table-body">
              <view v-for="(member, index) in memberList" :key="member.id" class="table-row">
                <view class="table-cell">
                  {{ index + 1 }}
                </view>
                <view class="table-cell">
                  <TreePicker
                    v-model="member.departmentId" placeholder="请选择部门" :multiple="false"
                    title="选择部门" @change="(event) => onDepartmentChange(index, event)"
                  />
                </view>
                <view class="table-cell">
                  <picker
                    :value="member.employeeId" :range="member.employees" range-key="realName"
                    :disabled="!member.departmentId || !showEditControls || member.loadingEmployees"
                    @change="onEmployeeChange(index, $event)"
                  >
                    <view
                      class="picker-content"
                      :class="{ disabled: !member.departmentId || !showEditControls || member.loadingEmployees }"
                    >
                      <template v-if="member.loadingEmployees">
                        <text class="loading-text">
                          加载中...
                        </text>
                        <uni-icons type="spinner-cycle" size="12" color="#999" />
                      </template>
                      <template v-else>
                        {{ member.employeeName || '请选择员工' }}
                        <uni-icons
                          v-if="showEditControls && !member.loadingEmployees"
                          type="arrowdown" size="12" color="#999"
                        />
                      </template>
                    </view>
                  </picker>
                </view>
                <view class="table-cell">
                  <view v-if="showEditControls" class="delete-btn" @tap="removeMember(index)">
                    <uni-icons type="trash" size="16" color="#ff4757" />
                  </view>
                </view>
              </view>
            </view>

            <!-- 添加成员按钮 -->
            <view v-if="showEditControls" class="add-member-btn-container" @tap="addMember">
              <view class="add-member-btn">
                <uni-icons type="plus" size="14" color="#1A73E8" />
                <text class="add-member-text">
                  添加成员
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- Deadline -->
        <view class="form-item">
          <text class="form-label">
            4. 截止日期
          </text>
          <picker mode="date" @change="onDateChange">
            <view class="input-with-icon">
              <uni-icons type="calendar" size="14" color="#999" />
              <text>{{ deadline || '选择日期' }}</text>
            </view>
          </picker>
        </view>

        <!-- Description -->
        <view class="form-item">
          <text class="form-label">
            5. 审查说明
          </text>
          <uv-textarea
            v-model="description" placeholder="请输入审查说明，最多300字" maxlength="300"
            count auto-height
          />
        </view>
      </view>
    </scroll-view>

    <!-- Bottom Action Bar -->
    <FixedActionBar>
      <div class="action-buttons">
        <el-button
          v-for="button in footerButtons"
          :key="button.type"
          :type="button.type === 'submit' ? 'primary' : 'default'"
          @click="button.type === 'cancel' ? footerCancel() : footerSubmitReview()"
        >
          {{ button.text }}
        </el-button>
      </div>
    </FixedActionBar>
    <!-- <view class="action-bar">
            <button  class="cancel-btn" @tap="cancel">取消</button>
            <button  class="draft-btn" @tap="saveDraft">保存草稿</button>
            <button  class="submit-btn" @tap="submitReview">发起审查</button>
        </view> -->
  </view>
</template>

<style lang="less" scoped>
.initiate-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #F5F5F5;

    .content {
        flex: 1;
        padding-top: 20rpx;
        padding-bottom: 100rpx;
        overflow: auto;
    }

    .card {
        background-color: #fff;
        border-radius: 16rpx;
        margin: 24rpx;
        padding: 32rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    }

    .policy-card {
        margin-bottom: 24rpx;
    }

    .policy-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
    }

    .policy-version {
        font-size: 24rpx;
        color: #999;
    }

    .form-item {
        margin-bottom: 48rpx;
    }

    .form-label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
    }

    .member-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
    }

    .save-btn {
        background: #1A73E8;
        color: white;
        padding: 12rpx 24rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
    }

    .save-btn-text {
        color: white;
        font-size: 24rpx;
    }

    .picker {
        width: 100%;
        height: 80rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .input-with-icon {
        width: 100%;
        height: 80rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
    }

    .input-with-icon input {
        flex: 1;
        height: 100%;
        margin-left: 16rpx;
    }

    .member-table-container {
        margin-top: 10rpx;
        border: 1rpx solid #E0E0E0;
        border-radius: 12rpx;
        overflow: hidden;
    }

    .table-header {
        display: flex;
        background: #F5F5F5;
        border-bottom: 1rpx solid #E0E0E0;
    }

    .table-body {
        background: #fff;
    }

    .table-row {
        display: flex;
        border-bottom: 1rpx solid #F0F0F0;
    }

    .table-row:last-child {
        border-bottom: none;
    }

    .table-cell {
        flex: 1;
        padding: 20rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #333;
        border-right: 1rpx solid #F0F0F0;
    }

    .table-cell:last-child {
        border-right: none;
    }

    .header-cell {
        font-weight: 600;
        color: #666;
        background: #F5F5F5;
    }

    .picker-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 10rpx 16rpx;
        background: #F8F9FA;
        border-radius: 8rpx;
        border: 1rpx solid #E0E0E0;
        font-size: 26rpx;
        color: #333;
    }

    .picker-content.disabled {
        background: #F0F0F0;
        color: #999;
    }

    .loading-text {
        color: #999;
        font-size: 26rpx;
        margin-right: 8rpx;
    }

    .delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        background: #FFF5F5;
        border-radius: 8rpx;
        cursor: pointer;
    }

    .delete-btn:hover {
        background: #FFE6E6;
    }

    .add-member-btn-container {
        padding: 20rpx;
        border-top: 1rpx solid #F0F0F0;
        background: #FAFAFA;
    }

    .add-member-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10rpx;
        width: 100%;
        padding: 20rpx;
        background: #F8F9FA;
        border: 2rpx dashed #D0D0D0;
        border-radius: 8rpx;
        cursor: pointer;
    }

    .add-member-btn:hover {
        background: #F0F0F0;
        border-color: #1A73E8;
    }

    .add-member-text {
        font-size: 28rpx;
        color: #1A73E8;
        font-weight: 500;
    }

    textarea {
        width: 100%;
        min-height: 160rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 24rpx;
        font-size: 28rpx;
    }

    .text-counter {
        text-align: right;
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
    }

    .upload-btn {
        width: 100%;
        height: 80rpx;
        border: 1px dashed #1A73E8;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        color: #1A73E8;
        font-size: 28rpx;
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        padding: 0 32rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
        z-index: 100;
    }

    .action-bar {
        left: 0;
        right: 0;
        bottom: 0;
        position: fixed;
        display: flex;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        background-color: #fff;
        border-top: 1px solid #f0f0f0;
        z-index: 10;
    }

    .action-bar button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        // border-radius: 40rpx;
        margin: 0 16rpx;
    }

    .cancel-btn {
        background-color: #f5f5f5;
        color: #666;
    }

    .draft-btn {
        background-color: #fff;
        color: #1a73e8;
        border: 1px solid #1a73e8;
    }

    .submit-btn {
        background-color: #1a73e8;
        color: #fff;
    }

    .action-buttons {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
        width: 100%;
    }

    .action-buttons .el-button {
        min-width: 80px;
    }

}
</style>
