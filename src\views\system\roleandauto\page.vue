<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
// import type Edit from './edit.vue'
// import Getuser from './getuser.vue'
// import Handover from './handover.vue'

// import systemApi from '@/api/modules/system/systemApi'
const headerCellStyle = ref({
  backgroundColor: '#e5e4e4',
  color: '#333',
  padding: '8px 0',
  fontSize: '16px',
})
const switch1 = ref(1)
const switch2 = ref(0)

const dialogVisibleauto = ref(false)
const roleautoid = ref({})
const autotitle = ref('权限配置')
const checkList = ref([])
const tableData: any = ref([
  // {
  //   id: 1,
  //   name: '普通成员',
  //   remark: '包含[外包人员] 权限，可见企业成员列表和内部开源仓库，无企业管理权限',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 2,
  //   name: '外包人员',
  //   remark: '仅可查看和编辑与该成员相关的数据',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 3,
  //   name: '人事管理员',
  //   remark: '包含【普通成员】权限，可管理企业成员',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 4,
  //   name: '管理员',
  //   remark: '包含【人事管理员】权限，可管理仓库',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 5,
  //   name: '超级管理员',
  //   remark: '包含【管理员】权限，同时可以创建和设置用户角色',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 6,
  //   name: '企业拥有者',
  //   remark: '具备企业内所有权限',
  //   num: 1,
  //   is_edit: false,
  // },
  // {
  //   id: 7,
  //   name: '【项目助理】',
  //   remark: '',
  //   num: 1,
  //   is_edit: true,
  // },
  // {
  //   id: 8,
  //   name: '【项目经理】',
  //   remark: '',
  //   num: 1,
  //   is_edit: true,
  // },
  // {
  //   id: 9,
  //   name: '【工程师】',
  //   remark: '',
  //   num: 1,
  //   is_edit: true,
  // },
  {
    id: 1,
    name: '管理员',
    remark: '具备系统内所有权限',
    num: 1,
    is_edit: false,
  },
  {
    id: 2,
    name: '财务主管',
    remark: '财务对账、会计凭证、应收应付、出纳流水、财务报表',
    num: 1,
    is_edit: false,
  },
  {
    id: 3,
    name: '计调主管',
    remark: '包含【发车计调】【服务计调】权限，审核成团',
    num: 1,
    is_edit: false,
  },
  {
    id: 4,
    name: '发车计调',
    remark: '包含【服务计调】权限，管理成团',
    num: 1,
    is_edit: false,
  },
  {
    id: 5,
    name: '服务计调',
    remark: '仅可管理客人数据 ',
    num: 1,
    is_edit: false,
  },
  {
    id: 6,
    name: '司机',
    remark: '仅接收成团和接人信息',
    num: 1,
    is_edit: false,
  },
  {
    id: 7,
    name: '导游',
    remark: '仅接收成团和接人信息、客人信息、财务上报等',
    num: 1,
    is_edit: false,
  },
  {
    id: 8,
    name: '业务',
    remark: '仅录入客人数据',
    num: 1,
    is_edit: false,
  },
  {
    id: 9,
    name: '外包人员',
    remark: '仅可查看和编辑与该成员相关的数据',
    num: 1,
    is_edit: true,
  },
  {
    id: 10,
    name: '人事管理员',
    remark: '包含【普通成员】权限，可管理企业成员',
    num: 1,
    is_edit: true,
  },
])
const loadingRef: any = ref(null)
const form: any = ref({})
const data = ref({
  page: 1,
  limit: 10,
})
const total: any = ref(0)
const paging: any = ref({
  page: data.value.page,
  limit: data.value.limit,
})

const currentPage4 = ref(1)
const pageSize4 = ref(10)
const background = ref(true)

const dialogVisible = ref(false)
onMounted(() => {
  // loadingRef.value.open(1500)
  // getList()// 请求数据
})
function getList() {
  loadingRef.value.open(1500)

  // systemApi.Ranklist(paging.value).then((res: any) => {
  //   tableData.value = res.data
  //   total.value = res.count
  // })
}
// 修改
const isedit: any = ref(null)
function modify(val: any) {
  isedit.value = true
  if (!val) {
    form.value = {}
    dialogVisible.value = !dialogVisible.value
  }
  else {
    form.value = JSON.parse(JSON.stringify(val))
    dialogVisible.value = !dialogVisible.value
  }
}

function openEdit() {
  isedit.value = false
  dialogVisible.value = !dialogVisible.value
  form.value = {}
}
// 分页
function sizeChange(val: any) {
  // console.log(val, '55555')
}
// 分页
function handleCurrentChange(val: any) {
  paging.value.page = val
  getList()
}
function submitForm() {
  console.log(form.value, ' form.value')

  if (!form.value.name) {
    ElMessage({ message: '请输入角色名称', type: 'error' })
    return false
  }
  // systemApi.Rank_edit(form.value).then((res: any) => {
  //   ElMessage({ message: res.msg, type: 'success' })
  //   dialogVisible.value = false
  //   getList()
  // })
  if (form.value.id) {
    tableData.value.forEach((element: any, index: any) => {
      if (element.id === form.value.id) {
        tableData.value[index] = form.value
      }
    })
  }
  else {
    form.value.is_edit = true
    form.value.id = tableData.value.length + 1
    tableData.value.push(form.value)
  }
  dialogVisible.value = false
}
// // 状态修改
// function onChangeStatus(row: any) {
//   console.log(row, 'rowrow')

//   ElMessageBox.confirm(
//       `确认${row.status === 1 ? '启用当前职位？' : '关闭当前职位？'}`,
//       '提示',
//       {
//         confirmButtonText: '确认',
//         cancelButtonText: '取消',
//         type: 'warning',
//       },
//   )
//     .then(() => {
//       systemApi.Rank_edit({
//         id: row.id,
//         status: row.status,
//       }).then(() => {
//         getList()
//         ElMessage({
//           type: 'success',
//           message: '修改成功',
//         })
//       })
//     })
//     .catch(() => {
//       // getList()
//       ElMessage({
//         type: 'info',
//         message: '已取消',
//       })
//     })
// }
function removeBatch(row: any) {
  tableData.value.forEach((element: any, index: any) => {
    if (element.id === row) {
      tableData.value.splice(index, 1)
    }
  })
  // systemApi.Rank_delete({
  //   id: row,
  // }).then(() => {
  //   getList()
  //   ElMessage({
  //     type: 'success',
  //     message: '操作成功',
  //   })
  // })
}
// 修改权限
function changerolepage(row: any) {
  dialogVisibleauto.value = true
  roleautoid.value = row
  autotitle.value = `权限配置` + ` - ${row.name}`
  // router.push({
  //   path: '/autopege',
  //   params: {
  //     id: row,
  //   },
  // })
}
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          角色与权限
        </div>
      </template>
      <!-- <template #content>
        <div class="text-sm/6">
          <div>
            这是一款<b class="text-emphasis">开箱即用</b>的中后台框架，同时它也经历过数十个真实项目的技术沉淀，确保框架在开发中可落地、可使用、可维护
          </div>
        </div>
      </template> -->
      <!-- <HButton outline @click="open('https://fantastic-admin.gitee.io')">
        <SvgIcon name="i-ri:file-text-line" />
        开发文档
      </HButton> -->
      <!-- <HDropdownMenu
        :items="[
          [
            { label: 'Gitee', handle: () => open('https://gitee.com/fantastic-admin/basic') },
            { label: 'Github', handle: () => open('https://github.com/fantastic-admin/basic') },
          ],
        ]"
      >
        <HButton class="ml-2">
          <SvgIcon name="i-ri:code-s-slash-line" />
          代码仓库
          <SvgIcon name="i-ep:arrow-down" />
        </HButton>
      </HDropdownMenu> -->
    </PageHeader>
    <page-main style="position: relative;">
      <!-- <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="角色名称:">
          <el-input v-model="paging.name" placeholder="请输入角色名称" clearable @clear="paging.name = null" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList()">
            <template #icon>
              <svg-icon name="ep:search" />
            </template>
            查询
          </el-button>
          <el-button
            @click="paging = {
              page: 1,
              limit: 10,
              name: null,
            }, getList()"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form> -->

      <div class="btnbox">
        <el-button type="primary" size="default" @click="openEdit">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          新建角色
        </el-button>
      </div>
      <div style="position: relative;margin-top: 53px;">
        <Loading ref="loadingRef" />
        <el-table
          :header-cell-style="headerCellStyle" :data="tableData" highlight-current-row :border="false"
          height="calc(100vh - 300px)"
        >
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column prop="id" label="ID" width="60" align="center" />

          <el-table-column prop="name" label="角色名称" width="220">
            <template #default="{ row: i }">
              {{ i.name }}<el-tag v-if="!i.is_edit" class="ml2" type="info" size="small">
                默认角色
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="角色描述" />
          <el-table-column prop="num" label="成员数量" width="120" align="center" />

          <!-- <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row: i }">
              <el-switch
                v-model="i.status" :active-value="1" :inactive-value="0"
                @change="onChangeStatus(i)"
              />
            </template>
          </el-table-column> -->

          <!-- <el-table-column prop="roles" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                {{ row.create_time }}
              </template>
            </el-table-column> -->
          <el-table-column fixed="right" label="操作" width="220" align="center">
            <template #default="scope">
              <el-tooltip content="系统内置角色不能修改" :disabled="scope.row.is_edit" placement="top" effect="dark">
                <el-button type="primary" :disabled="!scope.row.is_edit" text @click="modify(scope.row)">
                  <template #icon>
                    <svg-icon name="ep:edit" />
                  </template>修改
                </el-button>
              </el-tooltip>
              <el-tooltip content="系统内置角色不能设置权限" :disabled="scope.row.id !== 1" placement="top" effect="dark">
                <el-button type="primary" :disabled="scope.row.id === 1" text @click="changerolepage(scope.row)">
                  <template #icon>
                    <svg-icon name="ep:edit" />
                  </template>设置权限
                </el-button>
              </el-tooltip>
              <el-popconfirm v-if="scope.row.is_edit" title="确定删除吗？" @confirm="removeBatch(scope.row.id)">
                <template #reference>
                  <el-button type="danger" :disabled="!scope.row.is_edit" text>
                    <template #icon>
                      <svg-icon name="ep:delete" />
                    </template>删除
                  </el-button>
                </template>
              </el-popconfirm>
              <el-tooltip
                v-if="!scope.row.is_edit" content="系统内置角色不能删除" :disabled="scope.row.is_edit" placement="top"
                effect="dark"
              >
                <el-button type="danger" :disabled="!scope.row.is_edit" text>
                  <template #icon>
                    <svg-icon name="ep:delete" />
                  </template>删除
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <!--
        <el-pagination
          v-model:current-page="currentPage4"
          v-model:page-size="pageSize4"
          :page-sizes="[10, 20, 50, 100]"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="sizeChange"
          @current-change="handleCurrentChange"
        /> -->
      </div>
    </page-main>
    <HDialog v-model="dialogVisible" :title="form.id ? '修改角色' : '添加角色'">
      <!-- <HInput v-model="data1.name" class="w-full!" /> -->
      <div style="padding: 0 18px;">
        <el-form :model="form" label-width="86px">
          <el-form-item label="角色名称：" prop="name">
            <el-input v-model="form.name" size="large" placeholder="请输入职位" />
          </el-form-item>
          <el-form-item label="角色描述：" prop="remark">
            <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入角色描述" />
          </el-form-item>
          <!-- <el-form-item label="成员数量：" prop="num">
            <el-input-number
              v-model="form.num"
              size="large"
              style="width: 100%;"
              placeholder="请输入成员数量"
              controls-position="right"
              class="ele-fluid ele-text-left"
            />
          </el-form-item> -->
          <!-- <el-form-item label="排序：" prop="sort">
          <el-input-number
            v-model="form.sort"
            style="width: 100%;"
            :min="0"
            placeholder="请输入排序号"
            controls-position="right"
            class="ele-fluid ele-text-left"
          />
        </el-form-item> -->
          <!-- <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">
              启用
            </el-radio>
            <el-radio :label="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
        </el-form>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <!-- <HButton outline class="mr-2" @click="showEdit = false">
            取消
          </HButton>
          <HButton type="primary" @click="submitForm">
            确定
          </HButton> -->
          <el-button class="cancel" @click="dialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>

    <HDialog v-model="dialogVisibleauto" :title="autotitle" modulewidth="1120px">
      <!-- <HInput v-model="data1.name" class="w-full!" /> -->
      <div style=" height: 65vh;padding: 0 18px 6px; overflow-y: auto;">
        <div class="border-2-999">
          <div class="flex">
            <div class="bg-ccc bor-b bor-r fontW-600 h-16 w-50 flex items-center justify-center">
              模块
            </div>
            <div class="bg-ccc bor-b fontW-600 h-16 w-full flex items-center justify-center">
              功能
            </div>
          </div>
          <div class="flex">
            <div class="bor-r bor-b d-c w-50 flex items-center justify-center">
              <div>客人管理</div>
              <el-switch v-model="switch1" inline-prompt :active-value="1" :inactive-value="0" />
            </div>
            <div class="d-c w-full flex">
              <div class="bor-b h-16 w-full flex items-center p4">
                <el-checkbox-group v-model="checkList">
                  <el-checkbox label="客人管理总权限" />
                </el-checkbox-group>
              </div>
              <div class="bor-b w-full flex">
                <div class="w-full flex">
                  <div class="bor-r d-c w-30 flex items-center justify-center p4">
                    <div>添加客人</div>
                  </div>
                  <div class="d-c w-full flex">
                    <div class="h-12 w-full flex items-center p4">
                      <el-checkbox-group v-model="checkList">
                        <el-checkbox label="增加客人" />
                        <el-checkbox label="智能录入" />
                      </el-checkbox-group>
                    </div>
                    <div class="h-12 w-full flex items-center p4">
                      <el-checkbox-group v-model="checkList">
                        <el-checkbox label="修改" />
                        <el-checkbox label="删除" />
                        <el-checkbox label="日志" />
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bor-b w-full flex">
                <div class="w-full flex">
                  <div class="bor-r d-c w-30 flex items-center justify-center p4">
                    <div>搜索客人</div>
                  </div>
                  <div class="d-c w-full flex">
                    <div class="h-16 w-full flex items-center p4">
                      <el-checkbox-group v-model="checkList">
                        <el-checkbox label="搜索" />
                        <el-checkbox label="搜索设置" />
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bor-b w-full flex">
                <div class="w-full flex">
                  <div class="bor-r w-30 flex items-center justify-center p4">
                    回收站
                  </div>
                  <div class="d-c w-full flex">
                    <div class="h-16 w-full flex items-center p4">
                      <el-checkbox-group v-model="checkList">
                        <el-checkbox label="查看回收站" />
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex">
            <div class="bor-r d-c w-50 flex items-center justify-center">
              <div>计调成团</div>
              <el-switch v-model="switch2" inline-prompt :active-value="1" :inactive-value="0" />
            </div>
            <div class="d-c w-full flex">
              <div class="bor-b h-16 w-full flex items-center p4">
                <el-checkbox-group v-model="checkList">
                  <el-checkbox label="计调成团总权限" />
                </el-checkbox-group>
              </div>
              <div class="w-full flex">
                <div class="w-full flex">
                  <div class="bor-r w-30 flex items-center justify-center p4">
                    成团
                  </div>
                  <div class="d-c w-full flex">
                    <div class="w-full flex">
                      <div class="bor-r bor-b w-30 flex items-center justify-center p4">
                        成团功能
                      </div>
                      <div class="d-c w-full flex">
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="发起成团" />
                            <el-checkbox label="导出数据" />
                          </el-checkbox-group>
                        </div>
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="修改" />
                            <el-checkbox label="删除" />
                          </el-checkbox-group>
                        </div>
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="立即审核" />
                            <el-checkbox label="取消审核" />
                            <el-checkbox label="设置团接" />
                            <el-checkbox label="整车转移" />
                            <el-checkbox label="操作日志" />
                          </el-checkbox-group>
                        </div>
                        <div class="bor-b h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="分享" />
                            <el-checkbox label="打印" />
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                    <div class="w-full flex">
                      <div class="bor-r w-30 flex items-center justify-center p4">
                        客人功能
                      </div>
                      <div class="d-c w-full flex">
                        <div class="h-16 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="修改" />
                            <el-checkbox label="删除" />
                            <el-checkbox label="调车" />
                            <el-checkbox label="日志" />
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex">
            <div class="bor-r d-c w-50 flex items-center justify-center">
              <div>计调成团</div>
              <el-switch v-model="switch2" inline-prompt :active-value="1" :inactive-value="0" />
            </div>
            <div class="d-c w-full flex">
              <div class="bor-b h-16 w-full flex items-center p4">
                <el-checkbox-group v-model="checkList">
                  <el-checkbox label="计调成团总权限" />
                </el-checkbox-group>
              </div>
              <div class="w-full flex">
                <div class="w-full flex">
                  <div class="bor-r w-30 flex items-center justify-center p4">
                    成团
                  </div>
                  <div class="d-c w-full flex">
                    <div class="w-full flex">
                      <div class="bor-r bor-b w-30 flex items-center justify-center p4">
                        成团功能
                      </div>
                      <div class="d-c w-full flex">
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="发起成团" />
                            <el-checkbox label="导出数据" />
                          </el-checkbox-group>
                        </div>
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="修改" />
                            <el-checkbox label="删除" />
                          </el-checkbox-group>
                        </div>
                        <div class="h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="立即审核" />
                            <el-checkbox label="取消审核" />
                            <el-checkbox label="设置团接" />
                            <el-checkbox label="整车转移" />
                            <el-checkbox label="操作日志" />
                          </el-checkbox-group>
                        </div>
                        <div class="bor-b h-12 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="分享" />
                            <el-checkbox label="打印" />
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                    <div class="w-full flex">
                      <div class="bor-r w-30 flex items-center justify-center p4">
                        客人功能
                      </div>
                      <div class="d-c w-full flex">
                        <div class="h-16 w-full flex items-center p4">
                          <el-checkbox-group v-model="checkList">
                            <el-checkbox label="修改" />
                            <el-checkbox label="删除" />
                            <el-checkbox label="调车" />
                            <el-checkbox label="日志" />
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="dialogVisibleauto = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogVisibleauto = false">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  :deep(.main-container) {
    padding: 15px 32px;
  }

  .hdialogwidth {
    padding: 120px;
  }

  .fotterbtn {

    // padding: 8px 32px;
    .cancel {
      color: #409eff;
      border: 1px solid #409eff;
    }
  }

  .custom-tooltip {
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s, color 0.3s;
  }

  .btnbox {
    position: absolute;
    top: 20px;
    right: 35px;
  }

  .btnbox>.el-button {
    font-size: 12px;
  }

  .el-table {
    :deep(.is-text) {
      padding: 8px 0;
    }
  }

  :deep(.el-button__text--expand) {
    margin-right: 0;
    letter-spacing: 0;
  }

  :deep(.el-checkbox__label) {
    font-size: 16px;
  }

  :deep(.el-checkbox) {
    width: 120px;
    margin-right: 0;
  }

  .border-2-999 {
    border: 1px solid #e9e8e8;
  }

  .bg-ccc {
    background: #f5f5f5;
  }

  .fontW-500 {
    font-weight: 500;
  }

  .fontW-600 {
    font-weight: 600;
  }

  .d-c {
    flex-direction: column;
  }

  .bor-r {
    border-right: 1px solid #e9e8e8;
  }

  .bor-t {
    border-top: 1px solid #e9e8e8;
  }

  .bor-l {
    border-left: 1px solid #e9e8e8;
  }

  .bor-b {
    border-bottom: 1px solid #e9e8e8;
  }
</style>
