{"type": "module", "version": "4.7.0", "engines": {"node": "^18.0.0 || ^20.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "postinstall": "simple-git-hooks", "preinstall": "npx only-allow pnpm", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@headlessui/vue": "^1.7.19", "@imengyu/vue3-context-menu": "^1.3.9", "@qiniu/wechat-miniprogram-upload": "^1.0.3", "@tinymce/tinymce-vue": "^5.1.1", "@vueuse/core": "^10.9.0", "@vueuse/integrations": "^10.9.0", "@wangeditor/editor": "^5.1.23", "axios": "^1.6.8", "bignumber.js": "^9.1.2", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "defu": "^6.1.4", "echarts": "^5.5.0", "element-plus": "^2.7.0", "eruda": "^3.0.1", "floating-vue": "5.2.2", "highlight.js": "^11.11.1", "hotkeys-js": "^3.13.7", "html2canvas": "^1.4.1", "js-pinyin": "^0.2.5", "lodash-es": "^4.17.21", "marked": "^12.0.2", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "overlayscrollbars": "^2.6.1", "overlayscrollbars-vue": "^0.5.8", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pinia": "^2.1.7", "pinyin-pro": "^3.19.6", "pnpm": "^10.8.1", "qiniu-js": "4.0.0-beta.4", "qs": "^6.12.0", "scule": "^1.3.0", "sortablejs": "^1.15.2", "spinkit": "^2.0.1", "timeago.js": "^4.0.2", "tinymce": "^7.0.1", "v-wave": "^2.0.0", "vconsole": "^3.15.1", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.10.2", "vue-m-message": "^4.0.2", "vue-router": "^4.3.0", "vue-ueditor-wrap": "^3.0.8", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "2.11.6", "@iconify/json": "^2.2.196", "@iconify/vue": "^4.1.1", "@intlify/unplugin-vue-i18n": "^4.0.0", "@stylistic/stylelint-config": "^1.0.1", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/qs": "^6.9.14", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^0.58.8", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.19", "boxen": "^7.1.1", "bumpp": "^9.4.0", "cz-git": "^1.9.1", "eslint": "^8.57.0", "esno": "^4.7.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "inquirer": "^9.2.17", "lint-staged": "^15.2.2", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "plop": "^4.0.1", "sass": "^1.72.0", "simple-git-hooks": "^2.11.1", "stylelint": "^16.3.1", "stylelint-config-recess-order": "^5.0.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.2.1", "svgo": "^3.2.0", "terser": "^5.30.0", "typescript": "^5.4.3", "unocss": "^0.58.8", "unplugin-auto-import": "^0.17.5", "unplugin-turbo-console": "^1.5.1", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.7", "vite-plugin-banner": "^0.7.1", "vite-plugin-compression2": "^1.0.0", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-pages": "^0.32.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.0.25", "vite-plugin-vue-meta-layouts": "^0.4.2", "vue-tsc": "^2.0.7"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}