<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  Bell,
  ChatLineRound,
  Check,
  Document,
  DocumentCopy,
  Files,
  House,
  List,
  Loading,
  Message,
  Notebook,
  School,
  Search,
  Sunny,
  Tickets,
  User,
  VideoPlay,
  Warning,
} from '@element-plus/icons-vue'
import taskApi from '@/api/todoTask/task'
import type {
  CourseProgressDTO,
  DutyPositionStatsResponse,
  LearningProgressStatisticsDTO,
  PageResponse,
  PositionDutyItem,
  RegulationUpdate,
  SystemNotification,
  TodayTodoStats,
  TodoEvent,
} from '@/api/todoTask/types'

const router = useRouter()

const activeTab = ref<'regulation' | 'system'>('regulation')

const showResponsibilityDialog = ref(false)

const showUpdateDialog = ref(false)
const activeResponsibilityTab = ref('basic')

// 学习培训相关数据
const learningData = ref<LearningProgressStatisticsDTO | null>(null)
const currentCourse = ref<CourseProgressDTO | null>(null)
const recommendedCourses = ref<CourseProgressDTO[]>([])
const loading = ref(false)

// 最新动态相关数据
const regulationUpdates = ref<RegulationUpdate[]>([])
const systemNotifications = ref<SystemNotification[]>([])
const updatesLoading = ref(false)

// 待办事件相关数据
const todoEvents = ref<TodoEvent[]>([])
const allTodoEvents = ref<TodoEvent[]>([])
const todayTodoStats = ref<TodayTodoStats>({ todayPendingCount: 0, thisWeekCompletedCount: 0, totalTaskCompletionRate: '0%' })
const todoLoading = ref(false)
const showAllTodos = ref(false)

// 岗位职责相关数据
const dutyData = ref<DutyPositionStatsResponse | null>(null)
const currentPositionIndex = ref(0)
const dutyLoading = ref(false)

// 获取学习进度数据
function fetchLearningData() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        loading.value = true
        const response = await taskApi.learningApi({}, 'overview')
        learningData.value = response

        // 找到当前正在学习的课程（进度不为100%的第一个课程）
        if (learningData.value?.courseProgresses) {
          currentCourse.value = learningData.value.courseProgresses.find(
            (course: CourseProgressDTO) => !course.isCompleted && course.progressPercent > 0,
          ) || learningData.value.courseProgresses[0] || null

          // 推荐课程（除当前课程外的其他课程，最多显示2个）
          recommendedCourses.value = learningData.value.courseProgresses
            .filter((course: CourseProgressDTO) => course.courseId !== currentCourse.value?.courseId)
            .slice(0, 2)
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取学习数据失败')
        reject(error)
      }
      finally {
        loading.value = false
      }
    }
    fetchData()
  })
}

// 计算剩余学习时间
function getRemainingTime(course: CourseProgressDTO) {
  if (!course.totalDurationMinutes || !course.studiedDurationMinutes) {
    return ''
  }
  const remaining = course.totalDurationMinutes - course.studiedDurationMinutes
  if (remaining <= 0) {
    return '已完成'
  }

  const hours = Math.floor(remaining / 60)
  const minutes = remaining % 60

  if (hours > 0) {
    return minutes > 0 ? `还需${hours}小时${minutes}分钟` : `还需${hours}小时`
  }
  return `还需${minutes}分钟`
}

// 格式化课程时长
function formatDuration(minutes: number) {
  if (!minutes) {
    return ''
  }
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60

  if (hours > 0) {
    return mins > 0 ? `${hours}.${Math.round(mins / 6)}小时` : `${hours}小时`
  }
  return `${mins}分钟`
}

// 获取法规更新数据
function fetchRegulationUpdates() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        updatesLoading.value = true
        const response = await taskApi.regulationApi({ page: 1, size: 3 })
        if (response?.content) {
          regulationUpdates.value = response.content
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取法规更新数据失败')
        reject(error)
      }
      finally {
        updatesLoading.value = false
      }
    }
    fetchData()
  })
}

// 获取系统通知数据
function fetchSystemNotifications() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        updatesLoading.value = true
        const response = await taskApi.notificationApi({ page: 1, size: 3, categoryName: 'SYSTEM' })
        if (response) {
          systemNotifications.value = response.content
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取系统通知数据失败')
        reject(error)
      }
      finally {
        updatesLoading.value = false
      }
    }
    fetchData()
  })
}

// 获取最新动态数据
function fetchUpdatesData() {
  return Promise.all([
    fetchRegulationUpdates(),
    fetchSystemNotifications(),
  ])
}

// 获取待办事件统计数据
function fetchTodoEventsStatistics() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        const response = await taskApi.eventsStatisticsApi()
        if (response) {
          todayTodoStats.value = {
            todayPendingCount: response.todayPendingCount || 0,
            thisWeekCompletedCount: response.thisWeekCompletedCount || 0,
            totalTaskCompletionRate: response.totalTaskCompletionRate || '0%',
          }
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取待办事件统计失败')
        reject(error)
      }
    }
    fetchData()
  })
}

// 获取今日待办事件
function fetchTodayTodoEvents() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        todoLoading.value = true
        const response = await taskApi.eventsApi({}, {}, 'today')

        if (response) {
          // 如果响应中包含事件列表，则使用；否则需要调用分页接口获取具体事件
          if (response) {
            allTodoEvents.value = response // 保存完整数据
            todoEvents.value = showAllTodos.value ? response : response.slice(0, 3) // 根据状态显示
          }
          else {
            allTodoEvents.value = []
            todoEvents.value = []
          }
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取今日待办事件失败')
        reject(error)
      }
      finally {
        todoLoading.value = false
      }
    }
    fetchData()
  })
}

// 获取待办事件列表（分页）
// function fetchTodoEventsList(page: number = 1, size: number = 10) {
//   return new Promise<void>((resolve, reject) => {
//     const fetchData = async () => {
//       try {
//         todoLoading.value = true
//         const response = await taskApi.eventsApi({ page: page - 1, size }, {}, 'list')

//         if (response) {
//           allTodoEvents.value = response // 保存完整数据
//           todoEvents.value = showAllTodos.value ? response : response.slice(0, 3) // 根据状态显示
//         }
//         resolve()
//       }
//       catch (error) {
//         ElMessage.error('获取待办事件列表失败')
//         reject(error)
//       }
//       finally {
//         todoLoading.value = false
//       }
//     }
//     fetchData()
//   })
// }

// 查看全部待办事件
function viewAllTodos() {
  showAllTodos.value = true
  // 直接使用已有的完整数据，无需重新请求
  todoEvents.value = allTodoEvents.value
}

// 处理待办事件
function handleTodoEvent(event: TodoEvent) {
  console.log('处理待办事件:', event)

  // 根据事件类型判断跳转页面
  let routePath = ''

  switch (event.eventType) {
    case 'CONTRACT':
      // 合同审查详情页
      routePath = `/monitor/examination/contractReview/detail?id=${event.businessId}`
      break
    case 'DECISION':
      // 决策审查详情页
      routePath = `/monitor/examination/decisionReview/detail?id=${event.businessId}`
      break
    case 'SUPPLEMENTAL':
      // 补充审查详情页
      routePath = `/monitor/examination/ohter/detail?id=${event.businessId}`
      break
    case 'COURSE_LEARNING':
      // 课程学习详情页
      routePath = `/prevention/training/learningCenter_detail?id=${event.businessId}`
      break
    default:
      // 未知类型，显示提示
      ElMessage.warning('暂不支持该类型任务')
      return
  }

  // 执行页面跳转
  router.push(routePath).catch((error) => {
    console.error('页面跳转失败:', error)
    ElMessage.error('页面跳转失败')
  })
}

// 格式化事件类型显示
function getEventTypeIcon(eventType: string) {
  switch (eventType) {
    case 'APPROVAL':
      return 'Document'
    case 'TRAINING':
      return 'School'
    case 'REVIEW':
      return 'Search'
    default:
      return 'Document'
  }
}

// 获取事件优先级标签
function getEventPriorityTag(metadata: string) {
  try {
    const meta = JSON.parse(metadata || '{}')
    return meta.priority || 'normal'
  }
  catch {
    return 'normal'
  }
}

// 获取岗位职责数据
function fetchDutyData() {
  return new Promise<void>((resolve, reject) => {
    const fetchData = async () => {
      try {
        dutyLoading.value = true
        const response = await taskApi.dutyApi()

        if (response) {
          dutyData.value = response
          // 默认显示第一个岗位的职责
          currentPositionIndex.value = 0
        }
        resolve()
      }
      catch (error) {
        ElMessage.error('获取岗位职责数据失败')
        reject(error)
      }
      finally {
        dutyLoading.value = false
      }
    }
    fetchData()
  })
}

// 获取当前选中的岗位职责
function getCurrentPosition(): PositionDutyItem | null {
  if (!dutyData.value?.positionDuties || dutyData.value.positionDuties.length === 0) {
    return null
  }
  return dutyData.value.positionDuties[currentPositionIndex.value] || null
}

// 切换岗位
function switchPosition(index: number) {
  if (dutyData.value?.positionDuties && index >= 0 && index < dutyData.value.positionDuties.length) {
    currentPositionIndex.value = index
  }
}

// 获取风险等级颜色
function getRiskLevelColor(riskLevel: string) {
  switch (riskLevel?.toLowerCase()) {
    case 'high':
    case '高':
      return 'danger'
    case 'medium':
    case '中':
      return 'warning'
    case 'low':
    case '低':
      return 'success'
    default:
      return 'info'
  }
}

// 跳转到AI智能问答页面
function goToAIChat() {
  router.push('/one/qa/index')
}

onMounted(() => {
  fetchLearningData()
  fetchUpdatesData()
  fetchTodoEventsStatistics()
  fetchTodayTodoEvents()
  fetchDutyData()
})
</script>

<template>
  <div class="flex bg-gray-50">
    <!-- 主内容区 -->
    <div class="flex flex-1 flex-col overflow-hidden">
      <!-- 顶部标题栏 -->
      <header v-if="false" class="h-15 flex items-center justify-between bg-white px-6 shadow-sm">
        <div class="flex items-center space-x-4">
          <el-icon class="text-gray-500">
            <Search />
          </el-icon>
          <input type="text" placeholder="搜索..." class="w-64 border-none text-sm outline-none">
        </div>
        <div class="flex items-center space-x-4">
          <el-icon class="text-gray-500">
            <Bell />
          </el-icon>
          <el-icon class="text-gray-500">
            <Message />
          </el-icon>
          <div class="flex items-center">
            <img
              src="https://ai-public.mastergo.com/ai/img_res/87c045e36f085a1cce2d05312d306d9a.jpg"
              class="mr-2 h-8 w-8 rounded-full"
              alt="用户头像"
            >
            <span class="text-sm">张三</span>
          </div>
        </div>
      </header>
      <!-- 主内容 -->
      <main class="flex-1 overflow-auto bg-gray-50 p-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-4 mb-6 gap-4">
          <div class="rounded-lg bg-white p-6 shadow-sm">
            <div class="flex items-baseline text-blue-500">
              <span class="text-3xl font-bold">{{ todayTodoStats.todayPendingCount }}</span>
              <span class="ml-1 text-sm">项</span>
            </div>
            <div class="mt-2 text-sm text-gray-500">
              今日待办任务
            </div>
          </div>
          <div class="rounded-lg bg-white p-6 shadow-sm">
            <div class="flex items-baseline text-green-500">
              <span class="text-3xl font-bold">{{ todayTodoStats.thisWeekCompletedCount }}</span>
              <span class="ml-1 text-sm">项</span>
            </div>
            <div class="mt-2 text-sm text-gray-500">
              本周完成任务
            </div>
          </div>
          <div class="rounded-lg bg-white p-6 shadow-sm">
            <div class="flex items-baseline text-orange-500">
              <span class="text-3xl font-bold">{{ todayTodoStats.totalTaskCompletionRate || '0%' }}</span>
            </div>
            <div class="mt-2 text-sm text-gray-500">
              任务完成率
            </div>
          </div>
          <div class="rounded-lg bg-white p-6 shadow-sm">
            <div class="flex items-baseline text-purple-500">
              <span class="text-3xl font-bold">{{ learningData?.monthlyLearningDurationHours || 0 }}</span>
              <span class="ml-1 text-sm">小时</span>
            </div>
            <div class="mt-2 text-sm text-gray-500">
              本月学习
            </div>
          </div>
        </div>
        <!-- 三列布局 -->
        <div class="grid grid-cols-12 gap-6">
          <!-- 左列 -->
          <div class="col-span-5 space-y-6">
            <!-- 今日任务 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                今日任务
              </h3>
              <div v-loading="todoLoading" class="space-y-4">
                <div v-if="todoEvents.length === 0 && !todoLoading" class="py-8 text-center text-gray-500">
                  暂无待办任务
                </div>
                <div
                  v-for="(event, index) in todoEvents"
                  :key="event.id"
                  class="flex items-center justify-between p-3" :class="[
                    index < todoEvents.length - 1 ? 'border-b border-gray-100' : '',
                  ]"
                >
                  <div class="flex items-center">
                    <el-icon class="mr-3 text-blue-500">
                      <component :is="getEventTypeIcon(event.eventType)" />
                    </el-icon>
                    <div>
                      <p class="font-medium">
                        {{ event.title }}
                      </p>
                      <p class="text-sm text-gray-500">
                        {{ event.description || '暂无描述' }}
                      </p>
                      <p v-if="event.createdAt" class="text-xs text-gray-400">
                        创建时间: {{ typeof event.createdAt === 'object' && event.createdAt.seconds ? new Date(event.createdAt.seconds * 1000).toISOString() : (event.createdAt || '') }}
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <el-tag
                      v-if="getEventPriorityTag(event.metadata) === 'high'"
                      type="danger"
                      size="small"
                      class="mr-3"
                    >
                      紧急
                    </el-tag>
                    <el-tag
                      v-else-if="getEventPriorityTag(event.metadata) === 'medium'"
                      type="warning"
                      size="small"
                      class="mr-3"
                    >
                      重要
                    </el-tag>
                    <el-button v-debounce="2000" type="primary" size="small" @click="handleTodoEvent(event)">
                      处理
                    </el-button>
                  </div>
                </div>
              </div>
              <el-button
                type="primary"
                plain
                class="mt-4 w-full"
                :disabled="todoEvents.length === 0"
                @click="viewAllTodos"
              >
                {{ showAllTodos ? '已显示全部' : '查看全部' }}
              </el-button>
            </div>
            <!-- 岗位合规职责 -->
            <div v-loading="dutyLoading" class="rounded-lg bg-white p-6 shadow-sm">
              <div class="mb-6 flex items-center">
                <h3 class="text-xl font-bold">
                  我的合规职责
                </h3>
                <el-tag
                  v-if="getCurrentPosition()?.riskLevel"
                  :type="getRiskLevelColor(getCurrentPosition()?.riskLevel || '')"
                  size="small"
                  class="ml-2"
                >
                  {{ getCurrentPosition()?.riskLevel || '重要' }}
                </el-tag>
              </div>

              <!-- 多岗位选择 -->
              <div v-if="dutyData?.positionDuties && dutyData.positionDuties.length > 1" class="mb-4">
                <el-select
                  v-model="currentPositionIndex"
                  placeholder="选择岗位"
                  class="w-full"
                  @change="(value: number) => switchPosition(value)"
                >
                  <el-option
                    v-for="(position, index) in dutyData.positionDuties"
                    :key="index"
                    :label="`${position.postName} - ${position.orgUnitName}`"
                    :value="index"
                  />
                </el-select>
              </div>

              <div v-if="getCurrentPosition()" class="mb-6 rounded-lg bg-gray-50 p-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500">岗位名称：</span>
                    <span class="font-bold">{{ getCurrentPosition()?.postName || '-' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500">所属部门：</span>
                    <span class="font-bold">{{ getCurrentPosition()?.orgUnitName || '-' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500">职责生效日期：</span>
                    <span class="font-bold">{{ getCurrentPosition()?.effectiveDate || '-' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm text-gray-500">最后更新：</span>
                    <span class="font-bold">{{ getCurrentPosition()?.lastUpdateTime || '-' }}</span>
                  </div>
                </div>
              </div>

              <div v-else-if="!dutyLoading" class="mb-6 rounded-lg bg-gray-50 p-4 text-center text-gray-500">
                暂无岗位职责数据
              </div>
              <el-tabs v-if="getCurrentPosition()" v-model="activeResponsibilityTab" class="mb-4">
                <el-tab-pane label="基本职责" name="basic" />
                <el-tab-pane label="合规要求" name="compliance" />
                <el-tab-pane label="防控措施" name="control" />
              </el-tabs>
              <div v-if="getCurrentPosition()" class="max-h-[200px] overflow-y-auto">
                <div v-if="activeResponsibilityTab === 'basic'" class="rounded-lg bg-gray-50 p-4">
                  <p class="text-gray-600 leading-relaxed">
                    {{ getCurrentPosition()?.basicDuty || '暂无基本职责信息' }}
                  </p>
                </div>
                <div v-if="activeResponsibilityTab === 'compliance'" class="rounded-lg bg-gray-50 p-4">
                  <p class="text-gray-600 leading-relaxed">
                    {{ getCurrentPosition()?.complianceRequirement || '暂无合规要求信息' }}
                  </p>
                </div>
                <div v-if="activeResponsibilityTab === 'control'" class="rounded-lg bg-gray-50 p-4">
                  <p class="text-gray-600 leading-relaxed">
                    {{ getCurrentPosition()?.controlMeasures || '暂无防控措施信息' }}
                  </p>
                </div>
              </div>
              <div v-if="getCurrentPosition()" class="mb-6 border border-[#FFEDD5] rounded-lg bg-[#FFF7ED] p-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <el-tag v-if="getCurrentPosition()?.postStatus" size="small" class="mr-2">
                      {{ getCurrentPosition()?.postStatus }}
                    </el-tag>
                  </div>
                  <p class="text-xs text-gray-400">
                    最后更新：{{ (getCurrentPosition()?.lastUpdateTime || '未知') as string }}
                  </p>
                </div>
              </div>
              <!-- 职责更新详情弹窗 -->
              <el-dialog
                v-model="showUpdateDialog"
                title="职责更新详情"
                width="600px"
                destroy-on-close
              >
                <div class="space-y-6">
                  <div class="rounded-lg bg-gray-50 p-4">
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <p class="text-sm text-gray-500">
                          更新时间
                        </p>
                        <p class="mt-1 font-medium">
                          2024-12-25 14:30
                        </p>
                      </div>
                      <div>
                        <p class="text-sm text-gray-500">
                          更新类型
                        </p>
                        <p class="mt-1 font-medium">
                          法规变更导致
                        </p>
                      </div>
                      <div class="col-span-2">
                        <p class="text-sm text-gray-500">
                          影响范围
                        </p>
                        <p class="mt-1 font-medium">
                          合规要求部分
                        </p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 class="mb-3 font-medium">
                      具体更新内容
                    </h4>
                    <div class="space-y-3">
                      <div class="rounded-lg bg-blue-50 p-3">
                        <div class="flex items-center">
                          <div class="mr-2 h-1.5 w-1.5 rounded-full bg-blue-500" />
                          <p class="text-blue-500 font-medium">
                            新版企业合规管理办法第15条
                          </p>
                        </div>
                      </div>
                      <div class="rounded-lg bg-blue-50 p-3">
                        <div class="flex items-center">
                          <div class="mr-2 h-1.5 w-1.5 rounded-full bg-blue-500" />
                          <p class="text-blue-500 font-medium">
                            数据安全法实施细则
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 class="mb-3 font-medium">
                      学习材料
                    </h4>
                    <div class="space-y-3">
                      <div class="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                        <div class="flex items-center">
                          <el-icon class="mr-2 text-blue-500">
                            <Document />
                          </el-icon>
                          <span>新版管理办法解读PPT</span>
                        </div>
                        <el-button type="primary" link size="small">
                          查看
                        </el-button>
                      </div>
                      <div class="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                        <div class="flex items-center">
                          <el-icon class="mr-2 text-blue-500">
                            <Document />
                          </el-icon>
                          <span>合同审查操作指南更新版</span>
                        </div>
                        <el-button type="primary" link size="small">
                          查看
                        </el-button>
                      </div>
                      <div class="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                        <div class="flex items-center">
                          <el-icon class="mr-2 text-blue-500">
                            <VideoPlay />
                          </el-icon>
                          <span>在线培训视频 (15分钟)</span>
                        </div>
                        <el-button type="primary" link size="small">
                          播放
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
                <template #footer>
                  <div class="flex justify-end space-x-3">
                    <el-button
                      type="default"
                      @click="showUpdateDialog = false"
                    >
                      关闭
                    </el-button>
                    <el-button
                      type="primary"
                      plain
                    >
                      需要培训
                    </el-button>
                    <el-button
                      type="primary"
                      @click="showUpdateDialog = false"
                    >
                      我已了解
                    </el-button>
                  </div>
                </template>
              </el-dialog>
              <!-- <div class="flex justify-between space-x-3">
                <el-button type="primary" @click="showResponsibilityDialog = true">
                  查看详情
                </el-button>
              </div> -->
            </div>

            <!-- 合规职责详情弹窗 -->
            <el-dialog
              v-model="showResponsibilityDialog"
              title="我的合规职责详情"
              width="800px"
              destroy-on-close
            >
              <div class="mb-6 rounded-lg bg-gray-50 p-4">
                <div class="grid grid-cols-4 gap-4">
                  <div class="rounded-lg bg-white p-3">
                    <p class="mb-1 text-sm text-gray-500">
                      岗位名称
                    </p>
                    <p class="font-bold">
                      法务专员
                    </p>
                  </div>
                  <div class="rounded-lg bg-white p-3">
                    <p class="mb-1 text-sm text-gray-500">
                      所属部门
                    </p>
                    <p class="font-bold">
                      法务部
                    </p>
                  </div>
                  <div class="rounded-lg bg-white p-3">
                    <p class="mb-1 text-sm text-gray-500">
                      职责生效日期
                    </p>
                    <p class="font-bold">
                      2024-01-01
                    </p>
                  </div>
                  <div class="rounded-lg bg-white p-3">
                    <p class="mb-1 text-sm text-gray-500">
                      最后更新
                    </p>
                    <p class="font-bold">
                      2024-12-25
                    </p>
                  </div>
                </div>
              </div>
              <el-tabs v-model="activeResponsibilityTab">
                <el-tab-pane label="基本职责" name="basic">
                  <div class="rounded-lg bg-gray-50 p-4">
                    <p class="text-gray-600 leading-relaxed">
                      {{ getCurrentPosition()?.basicDuty || '暂无基本职责信息' }}
                    </p>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="合规要求" name="compliance">
                  <div class="rounded-lg bg-gray-50 p-4">
                    <p class="text-gray-600 leading-relaxed">
                      {{ getCurrentPosition()?.complianceRequirement || '暂无合规要求信息' }}
                    </p>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="防控措施" name="control">
                  <div class="rounded-lg bg-gray-50 p-4">
                    <p class="text-gray-600 leading-relaxed">
                      {{ getCurrentPosition()?.controlMeasures || '暂无防控措施信息' }}
                    </p>
                  </div>
                </el-tab-pane>
              </el-tabs>
              <template #footer>
                <div class="w-full flex justify-between">
                  <div class="flex space-x-3">
                    <el-button type="primary">
                      我已知悉
                    </el-button>
                  </div>
                  <el-button type="default">
                    <el-icon class="mr-1">
                      <Document />
                    </el-icon>
                    导出PDF
                  </el-button>
                </div>
              </template>
            </el-dialog>
          </div>
          <!-- 中列 -->
          <div class="col-span-4 space-y-6">
            <!-- 快捷操作 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                快捷操作
              </h3>
              <div class="grid grid-cols-3 gap-4">
                <el-card shadow="hover" class="cursor-pointer hover:bg-blue-50">
                  <div class="flex flex-col items-center justify-center p-1">
                    <el-icon class="mb-2 text-2xl text-orange-500">
                      <Warning />
                    </el-icon>
                    <span class="text-center text-sm">风险上报</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer hover:bg-blue-50">
                  <div class="flex flex-col items-center justify-center p-2">
                    <el-icon class="mb-2 text-2xl text-green-500">
                      <Files />
                    </el-icon>
                    <span class="text-center text-sm">合同查询</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer hover:bg-blue-50">
                  <div class="flex flex-col items-center justify-center p-2">
                    <el-icon class="mb-2 text-2xl text-purple-500">
                      <School />
                    </el-icon>
                    <span class="text-center text-sm">培训中心</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer hover:bg-blue-50">
                  <div class="flex flex-col items-center justify-center p-2">
                    <el-icon class="mb-2 text-2xl text-indigo-500">
                      <Notebook />
                    </el-icon>
                    <span class="text-center text-sm">法规库</span>
                  </div>
                </el-card>
                <el-card shadow="hover" class="cursor-pointer hover:bg-blue-50">
                  <div class="flex flex-col items-center justify-center p-2">
                    <el-icon class="mb-2 text-2xl text-blue-500">
                      <ChatLineRound />
                    </el-icon>
                    <span class="text-center text-sm">智能问答</span>
                  </div>
                </el-card>
              </div>
            </div>
            <!-- 最新动态 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                最新动态
              </h3>
              <el-tabs v-model="activeTab" class="mb-4">
                <el-tab-pane label="法规更新" name="regulation" />
                <el-tab-pane label="系统通知" name="system" />
              </el-tabs>
              <div v-if="activeTab === 'regulation'" class="space-y-4">
                <div v-if="updatesLoading" class="flex justify-center py-8">
                  <el-icon class="animate-spin text-2xl text-blue-500">
                    <Loading />
                  </el-icon>
                </div>
                <div v-else-if="regulationUpdates.length > 0">
                  <div
                    v-for="(update, index) in regulationUpdates"
                    :key="update.id"
                    class="flex"
                  >
                    <div class="mr-4 flex flex-col items-center">
                      <div class="h-3 w-3 rounded-full bg-blue-500" />
                      <div v-if="index < regulationUpdates.length - 1" class="mt-1 h-full w-px bg-gray-200" />
                    </div>
                    <div>
                      <p class="font-medium">
                        {{ update.title }}
                      </p>
                      <p class="mt-1 text-sm text-gray-500">
                        {{ update.updateTime }}
                      </p>
                      <p class="mt-1 text-sm text-gray-600">
                        {{ update.summary || '暂无摘要' }}
                      </p>
                    </div>
                  </div>
                </div>
                <div v-else class="py-8 text-center text-gray-500">
                  暂无法规更新
                </div>
              </div>
              <div v-else class="space-y-4">
                <div v-if="updatesLoading" class="flex justify-center py-8">
                  <el-icon class="animate-spin text-2xl text-orange-500">
                    <Loading />
                  </el-icon>
                </div>
                <div v-else-if="systemNotifications.length > 0">
                  <div
                    v-for="(notification, index) in systemNotifications"
                    :key="notification.id"
                    class="flex"
                  >
                    <div class="mr-4 flex flex-col items-center">
                      <div class="h-3 w-3 rounded-full bg-orange-500" />
                      <div v-if="index < systemNotifications.length - 1" class="mt-1 h-full w-px bg-gray-200" />
                    </div>
                    <div>
                      <p class="font-medium">
                        {{ notification.title }}
                      </p>
                      <p class="mt-1 text-sm text-gray-500">
                        {{ notification.createTime || notification.updateTime }}
                      </p>
                      <p class="mt-1 text-sm text-gray-600">
                        {{ notification.content }}
                      </p>
                    </div>
                  </div>
                </div>
                <div v-else class="py-8 text-center text-gray-500">
                  暂无系统通知
                </div>
              </div>
            </div>
          </div>
          <!-- 右列 -->
          <div class="col-span-3 space-y-6">
            <!-- 合规状态 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                合规状态
              </h3>
              <div class="mb-4 flex flex-col items-center">
                <div class="relative mb-2 h-20 w-20">
                  <div class="absolute inset-0 border-8 border-gray-200 rounded-full" />
                  <div class="absolute inset-0 border-8 border-green-500 rounded-full" style="clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);" />
                  <div class="absolute inset-0 flex items-center justify-center">
                    <span class="text-2xl text-green-500 font-bold">92</span>
                  </div>
                </div>
                <el-tag type="success" size="small">
                  优秀
                </el-tag>
              </div>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-500">任务完成率</span>
                  <span class="text-green-500 font-medium">{{ todayTodoStats?.totalTaskCompletionRate ? `${Math.round(todayTodoStats.totalTaskCompletionRate)}%` : '0%' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">培训完成率</span>
                  <span class="text-blue-500 font-medium">{{ learningData?.courseCompletionRate ? `${learningData.courseCompletionRate}%` : '0%' }}</span>
                </div>
              </div>
            </div>
            <!-- 学习培训 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                学习培训
              </h3>
              <div v-if="loading" class="flex justify-center py-8">
                <el-icon class="animate-spin text-2xl text-blue-500">
                  <Loading />
                </el-icon>
              </div>
              <div v-else-if="currentCourse" class="mb-4">
                <img
                  :src="currentCourse.coverImageUrl || 'https://ai-public.mastergo.com/ai/img_res/2c38a0382b9dd3367c5dd56f6fa3fd82.jpg'"
                  class="mb-3 h-20 w-full rounded object-cover"
                  alt="课程封面"
                >
                <h4 class="line-clamp-2 font-medium">
                  {{ currentCourse.courseName }}
                </h4>
                <div class="mt-2 flex items-center justify-between">
                  <span class="text-sm text-blue-500">{{ `${Math.round(currentCourse.progressPercent)}%` }}</span>
                  <span class="text-xs text-gray-500">{{ getRemainingTime(currentCourse) }}</span>
                </div>
                <div class="mt-1 h-1.5 w-full rounded-full bg-gray-200">
                  <div
                    class="h-1.5 rounded-full bg-blue-500"
                    :style="{ width: `${currentCourse.progressPercent}%` }"
                  />
                </div>
                <el-button type="primary" class="mt-3 w-full" size="small">
                  {{ currentCourse.isCompleted ? '已完成' : (currentCourse.progressPercent > 0 ? '继续学习' : '开始学习') }}
                </el-button>
              </div>
              <div v-else class="mb-4 py-8 text-center text-gray-500">
                暂无学习课程
              </div>
              <div v-if="recommendedCourses.length > 0" class="space-y-3">
                <div
                  v-for="course in recommendedCourses"
                  :key="course.courseId"
                  class="flex items-center"
                >
                  <img
                    :src="course.coverImageUrl || 'https://ai-public.mastergo.com/ai/img_res/0fd2b91d26f6d4f487e0a81dce86b2a3.jpg'"
                    class="mr-3 h-10 w-10 rounded"
                    alt="推荐课程"
                  >
                  <div class="flex-1">
                    <p class="line-clamp-1 text-sm font-medium">
                      {{ course.courseName }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ formatDuration(course.totalDurationMinutes) }}
                    </p>
                  </div>
                  <el-button type="primary" plain size="small">
                    {{ course.progressPercent > 0 ? '继续' : '开始' }}
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 智能助手 -->
            <div class="rounded-lg bg-white p-6 shadow-sm">
              <h3 class="mb-4 text-xl font-bold">
                智能助手
              </h3>
              <div class="flex flex-col items-center">
                <img
                  src="https://ai-public.mastergo.com/ai/img_res/02dabfec39efbe980092d1f3a1c69129.jpg"
                  class="mb-3 h-12 w-12 rounded-full"
                  alt="智能助手"
                >
                <p class="mb-4 text-sm text-gray-600">
                  有合规问题随时问我
                </p>
                <div class="mb-4 flex space-x-2">
                  <el-tag type="primary" size="small">
                    法规查询
                  </el-tag>
                  <el-tag type="primary" size="small">
                    合同问题
                  </el-tag>
                </div>
                <el-button type="primary" class="w-full" size="small" @click="goToAIChat">
                  开始对话
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-1 {
display: -webkit-box;
-webkit-line-clamp: 1;
-webkit-box-orient: vertical;
overflow: hidden;
}
.line-clamp-2 {
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}
</style>
