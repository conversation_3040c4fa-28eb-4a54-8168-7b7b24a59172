<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'

interface Props {
  content: string
}

const props = defineProps<Props>()

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext'
    return hljs.highlight(code, { language }).value
  },
  langPrefix: 'hljs language-',
  breaks: true,
  gfm: true
})

const renderedContent = computed(() => {
  return marked(props.content)
})
</script>

<style scoped lang="scss">
.markdown-renderer {
  line-height: 1.6;
  color: #333;

  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin: 1.5em 0 0.5em 0;
    font-weight: 600;
    line-height: 1.25;
  }

  :deep(h1) { font-size: 2em; }
  :deep(h2) { font-size: 1.5em; }
  :deep(h3) { font-size: 1.25em; }
  :deep(h4) { font-size: 1em; }
  :deep(h5) { font-size: 0.875em; }
  :deep(h6) { font-size: 0.85em; }

  :deep(p) {
    margin: 0.5em 0;
  }

  :deep(pre) {
    background-color: #f6f8fa !important;
    border-radius: 6px;
    padding: 16px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid #e1e4e8;
  }

  :deep(code) {
    background-color: #f6f8fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
  }

  :deep(pre code) {
    background-color: transparent;
    padding: 0;
  }

  :deep(blockquote) {
    border-left: 4px solid #dfe2e5;
    padding-left: 16px;
    margin: 12px 0;
    color: #6a737d;
    background-color: #f8f9fa;
    padding: 8px 16px;
    border-radius: 0 4px 4px 0;
  }

  :deep(ul), :deep(ol) {
    padding-left: 2em;
    margin: 0.5em 0;
  }

  :deep(li) {
    margin: 0.25em 0;
  }

  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    border: 1px solid #dfe2e5;
    border-radius: 6px;
    overflow: hidden;

    th, td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }

    th {
      background-color: #f6f8fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f8f9fa;
    }
  }

  :deep(a) {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  :deep(strong) {
    font-weight: 600;
  }

  :deep(em) {
    font-style: italic;
  }

  :deep(hr) {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 24px 0;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }
}
</style>