import api from '@/api/index'

export default {
  // 责任追究处理列表
  getHandlingMeasuresList(params: any): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/responsibility/investigate/deals/search', params)
  },

  // 创建新的责任追究处理
  createHandlingMeasure(params: any): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/responsibility/investigate/deals', params)
  },
  // 获取详情
  getHandlingMeasureDetail(id: any): Promise<any> {
    return api.get(`/whiskerguardviolationservice/api/responsibility/investigate/deals/${id}`, {})
  },
  // 更新
  updateHandlingMeasure(params: any): Promise<any> {
    return api.patch(`/whiskerguardviolationservice/api/responsibility/investigate/deals/${params.id}`, params)
  },

  // 责任追究措施相关接口
  // 查询责任追究措施列表
  getResponsibilityMeasuresList(params: any): Promise<any> {
    return api.get('/whiskerguardviolationservice/api/responsibility/investigate/measures', { params })
  },

  // 创建责任追究措施
  createResponsibilityMeasure(data: any): Promise<any> {
    return api.post('/whiskerguardviolationservice/api/responsibility/investigate/measures', data)
  },

  // 获取责任追究措施详情
  getResponsibilityMeasureDetail(id: string): Promise<any> {
    return api.get(`/whiskerguardviolationservice/api/responsibility/investigate/measures/${id}`)
  },

  // 更新责任追究措施
  updateResponsibilityMeasure(id: string, data: any): Promise<any> {
    return api.patch(`/whiskerguardviolationservice/api/responsibility/investigate/measures/${id}`, data)
  },

  // 删除责任追究措施
  deleteResponsibilityMeasure(id: string): Promise<any> {
    return api.delete(`/whiskerguardviolationservice/api/responsibility/investigate/measures/${id}`)
  },
}
