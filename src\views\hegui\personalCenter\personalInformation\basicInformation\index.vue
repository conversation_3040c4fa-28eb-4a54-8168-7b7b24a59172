<script lang="ts" setup>
import {
  Edit,
  Lock,
  Setting,
} from '@element-plus/icons-vue'
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import storage from '@/utils/storage'
import uploadApi from '@/api/upload'

const router = useRouter()
const _userinfo: any = ref(JSON.parse(storage.local.get('userinfo') || '') ?? '')
const userDetail: any = ref(JSON.parse(storage.local.get('userDetail') || '') ?? '')
const form: any = ref({})

// 头像URL
const avatarUrl = ref('https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png')

// 获取头像URL
async function getAvatarUrl(key: string) {
  try {
    const response = await uploadApi.getFileUrl(key)
    return response || 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png'
  }
  catch (error) {
    console.error('获取头像URL失败:', error)
    return 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png'
  }
}

// 监听userDetail变化，更新头像URL
watch(() => userDetail.value.avatar, async (newAvatar) => {
  if (newAvatar) {
    // 如果avatar是完整的URL，直接使用
    if (newAvatar.startsWith('http')) {
      avatarUrl.value = newAvatar
    }
    else {
      // 否则通过接口获取文件URL
      avatarUrl.value = await getAvatarUrl(newAvatar)
    }
  }
  else {
    // 使用默认头像
    avatarUrl.value = 'https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png'
  }
}, { immediate: true })
const formPop: any = ref(false)
onMounted(() => {
  form.value = Object.assign({}, userDetail.value)
  // console.log(userDetail.value, 'userDetail')
  // loadingRef.value.open(1500)
  // getList()// 请求数据
})

//
const formRef: any = ref(null)
const formRules = ref({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
  // code: [
  //   { required: true, message: '请输入角色标识', trigger: 'blur' },
  // ],
  // status: [
  //   { required: true, message: '请选择职级状态', trigger: 'blur' },
  // ],
  // sort: [
  //   { required: true, message: '请输入排序号', trigger: 'blur' },
  // ],
})
function submitForm() {
  formRef.value && formRef.value.validate((valid: boolean) => {
    if (valid) {
      const _formData = Object.assign({}, form.value)
      // 表单数据处理
      // Api.edit(form.value).then((res: any) => {
      //   ElMessage({ message: res.msg, type: 'success' })
      //   dialogVisible.value = false
      //   getList()
      // })
    }
  })
}

// 跳转到编辑页面
function goToEdit() {
  router.push('/personalCenter/personalInformation/basicInformation/detail')
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              个人资料
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="['basicInformation/index/editInfomation']" type="primary" class="!rounded-button whitespace-nowrap" @click="goToEdit">
              <i class="el-icon-check mr-1" />编辑资料
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  个人资料
                </div>
              </template>
              <!-- 个人信息卡片 -->
              <!-- <div class="bg-white rounded-lg shadow-sm p-6"> -->
              <div class="flex space-x-6">
                <!-- 头像区域 -->
                <div class="flex flex-col items-center space-y-4">
                  <div class="h-24 w-24 overflow-hidden rounded-full bg-gray-200">
                    <img
                      :src="avatarUrl" alt="用户头像"
                      class="h-full w-full object-cover"
                    >
                  </div>
                  <!-- <button class="text-sm text-blue-500 hover:text-blue-600">
                    更换头像
                  </button> -->
                </div>

                <!-- 基本信息 -->
                <div class="grid grid-cols-2 flex-1 gap-4">
                  <div class="space-y-4">
                    <div>
                      <div class="text-sm text-gray-500">
                        账号
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.username || '***' }}
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        姓名
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.realName || '***' }}
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        性别
                      </div>
                      <div class="text-gray-800">
                        <span v-if="userDetail.gender === 'MALE'">男</span>
                        <span v-else-if="userDetail.gender === 'FEMALE'">女</span>
                        <span v-else>未知</span>
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        手机号码
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.phone || '***' }}
                      </div>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <div class="text-sm text-gray-500">
                        电子邮箱
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.email || '***' }}
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        所属部门
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.orgUnitList && userDetail.orgUnitList.length > 0 ? userDetail.orgUnitList[0].name : '***' }}
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        岗位
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.positionName || '***' }}
                      </div>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500">
                        入职日期
                      </div>
                      <div class="text-gray-800">
                        {{ userDetail.hireDate || '***' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- </div> -->
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  账号信息
                </div>
              </template>
              <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      账号创建时间
                    </div>
                    <div class="text-gray-800">
                      {{ userDetail.createdAt || '***' }}
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      账号状态
                    </div>
                    <div class="text-gray-800">
                      <span v-if="userDetail.status === 'ACTIVE'">正常</span>
                      <span v-else-if="userDetail.status === 'INACTIVE'">停用</span>
                      <span v-else-if="userDetail.status === 'FROZEN'">冻结</span>
                      <span v-else>未知</span>
                    </div>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      最后登录时间
                    </div>
                    <div class="text-gray-800">
                      {{ userDetail.lastLoginTime || '***' }}
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      最后登录IP
                    </div>
                    <div class="text-gray-800">
                      {{ userDetail.lastLoginIp || '***' }}
                    </div>
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    员工编号
                  </div>
                  <div class="text-gray-800">
                    {{ userDetail.employeeNo || '***' }}
                  </div>
                </div>
              </div>
              <div class="mt-4">
                <a href="#" class="text-sm text-blue-500 hover:text-blue-600">查看登录记录</a>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  补充信息
                </div>
              </template>
              <div class="space-y-6">
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    个人简介
                  </div>
                  <div class="text-gray-800">
                    张三，法务部合规专员，拥有3年合规管理经验，熟悉企业合规体系建设，擅长风险评估与合规培训。
                  </div>
                </div>
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    专业技能
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">合规管理</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">风险评估</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">法律文书</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">合规培训</span>
                  </div>
                </div>
                <div>
                  <div class="mb-2 text-sm text-gray-500">
                    兴趣爱好
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">阅读</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">羽毛球</span>
                    <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800">旅行</span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <div class="text-sm text-gray-500">
                      联系地址
                    </div>
                    <div class="text-gray-800">
                      上海市浦东新区张江高科技园区
                    </div>
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">
                      紧急联系人
                    </div>
                    <div class="text-gray-800">
                      李四
                    </div>
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    紧急联系电话
                  </div>
                  <div class="text-gray-800">
                    138****1234
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  系统角色
                </div>
              </template>
              <div class="space-y-4">
                <div v-if="userDetail.roleList && userDetail.roleList.length > 0">
                  <div v-for="role in userDetail.roleList" :key="role.id" class="mb-4">
                    <div class="font-medium">
                      {{ role.name }}
                    </div>
                    <div class="mt-1 text-sm text-gray-500">
                      {{ role.description || '暂无描述' }}
                    </div>
                  </div>
                </div>
                <div v-else class="text-gray-500">
                  暂无角色信息
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  <span>修改密码</span>
                </a>
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Lock />
                  </el-icon>
                  <span>安全设置</span>
                </a>
                <a href="#" class="flex items-center text-blue-500 space-x-2 hover:text-blue-600">
                  <el-icon>
                    <Setting />
                  </el-icon>
                  <span>通知设置</span>
                </a>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
    <HDialog v-model="formPop" :title="form.id ? '编辑资料' : '新增资料'">
      <!-- <HInput v-model="data1.name" class="w-full!" /> -->
      <div style="padding: 0 18px;">
        <el-form ref="formRef" :rules="formRules" :model="form" label-width="86px">
          <el-form-item label="真实姓名：" prop="realName">
            <el-input v-model="form.realName" size="large" placeholder="请输入真实姓名" />
          </el-form-item>

          <el-form-item label="手机号：" prop="phone">
            <el-input v-model="form.phone" type="number" size="large" placeholder="请输入手机号" />
            <!-- <el-input v-model="form.phone" type="textarea" :rows="3" placeholder="请输入手机号" /> -->
          </el-form-item>
          <el-form-item label="真实姓名：" prop="email">
            <el-input v-model="form.email" size="large" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="身份证号：" prop="idCard">
            <el-input v-model="form.idCard" size="large" placeholder="请输入身份证号" />
          </el-form-item>

          <el-form-item label="状态：" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="ACTIVE">
                ACTIVE
              </el-radio>
              <el-radio label="INACTIVE">
                INACTIVE
              </el-radio>
              <el-radio label="FROZEN">
                FROZEN
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="formPop = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
</style>
