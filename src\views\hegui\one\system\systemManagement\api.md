---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 05-法律法规管理服务/企业内部制度类别

## POST 添加企业内部法规分类

POST /whiskerguardregulatoryservice/api/enterprise/categories/create

描述：添加企业内部法规分类。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "string",
  "categoryCode": "string",
  "sortNum": 0,
  "status": 0,
  "parentId": 0,
  "description": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "children": "new ArrayList<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[EnterpriseCategoryDTO](#schemaenterprisecategorydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "",
  "parentId": 0,
  "description": "",
  "createdAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "updatedAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "categoryName": "",
      "parentId": 0,
      "description": "",
      "createdAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "updatedAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "categoryName": "",
          "parentId": 0,
          "description": "",
          "createdAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "updatedAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseCategoryDTO](#schemaresponseentityenterprisecategorydto)|

## POST 部分更新企业内部法规分类

POST /whiskerguardregulatoryservice/api/enterprise/categories/partialUpdate/{id}

描述：部分更新企业内部法规分类。

> Body 请求参数

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "string",
  "categoryCode": "string",
  "sortNum": 0,
  "status": 0,
  "parentId": 0,
  "description": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "children": "new ArrayList<>()"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |分类id|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[EnterpriseCategoryDTO](#schemaenterprisecategorydto)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "",
  "parentId": 0,
  "description": "",
  "createdAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "updatedAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "categoryName": "",
      "parentId": 0,
      "description": "",
      "createdAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "updatedAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "categoryName": "",
          "parentId": 0,
          "description": "",
          "createdAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "updatedAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseCategoryDTO](#schemaresponseentityenterprisecategorydto)|

## GET 获取指定租户分类的树形结构

GET /whiskerguardregulatoryservice/api/enterprise/categories/tree

描述：获取指定租户分类的树形结构。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenantId|query|integer| 是 |租户id|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
[
  {
    "id": 0,
    "tenantId": 0,
    "categoryName": "",
    "parentId": 0,
    "description": "",
    "createdAt": {
      "dateTime": "",
      "offset": {
        "totalSeconds": 0
      },
      "zone": {}
    },
    "updatedAt": {
      "dateTime": "",
      "offset": {
        "totalSeconds": 0
      },
      "zone": {}
    },
    "children": [
      {
        "id": 0,
        "tenantId": 0,
        "categoryName": "",
        "parentId": 0,
        "description": "",
        "createdAt": {
          "dateTime": "",
          "offset": {
            "totalSeconds": 0
          },
          "zone": {}
        },
        "updatedAt": {
          "dateTime": "",
          "offset": {
            "totalSeconds": 0
          },
          "zone": {}
        },
        "children": [
          {
            "id": 0,
            "tenantId": 0,
            "categoryName": "",
            "parentId": 0,
            "description": "",
            "createdAt": {
              "dateTime": "",
              "offset": {
                "totalSeconds": 0
              },
              "zone": {}
            },
            "updatedAt": {
              "dateTime": "",
              "offset": {
                "totalSeconds": 0
              },
              "zone": {}
            },
            "children": []
          }
        ]
      }
    ]
  }
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

*返回分类结果*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|*anonymous*|[[ListEnterpriseCategoryDTO](#schemalistenterprisecategorydto)]|false|none||返回分类结果|
|» id|integer(int64)|false|none||none|
|» tenantId|integer(int64)|true|none||租户ID|
|» categoryName|string|true|none||分类名称|
|» categoryCode|string|false|none||分类编码|
|» sortNum|integer|false|none||排序|
|» status|integer|false|none||状态：0、未启用 1、启用|
|» parentId|integer(int64)|false|none||父级分类ID，NULL 表示顶级分类|
|» description|string|false|none||分类描述|
|» createdAt|[Instant](#schemainstant)|false|none||创建时间|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|»» seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|»» nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|
|» children|[[EnterpriseCategoryDTO](#schemaenterprisecategorydto)]|false|none||none|
|»» id|integer(int64)|false|none||none|
|»» tenantId|integer(int64)|true|none||租户ID|
|»» categoryName|string|true|none||分类名称|
|»» categoryCode|string|false|none||分类编码|
|»» sortNum|integer|false|none||排序|
|»» status|integer|false|none||状态：0、未启用 1、启用|
|»» parentId|integer(int64)|false|none||父级分类ID，NULL 表示顶级分类|
|»» description|string|false|none||分类描述|
|»» createdAt|[Instant](#schemainstant)|false|none||创建时间|
|»» updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|»» children|[[EnterpriseCategoryDTO](#schemaenterprisecategorydto)]|false|none||none|

## GET 根据分类id查询指定的分类

GET /whiskerguardregulatoryservice/api/enterprise/categories/get/id

描述：根据分类id查询指定的分类。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "",
  "parentId": 0,
  "description": "",
  "createdAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "updatedAt": {
    "dateTime": "",
    "offset": {
      "totalSeconds": 0
    },
    "zone": {}
  },
  "children": [
    {
      "id": 0,
      "tenantId": 0,
      "categoryName": "",
      "parentId": 0,
      "description": "",
      "createdAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "updatedAt": {
        "dateTime": "",
        "offset": {
          "totalSeconds": 0
        },
        "zone": {}
      },
      "children": [
        {
          "id": 0,
          "tenantId": 0,
          "categoryName": "",
          "parentId": 0,
          "description": "",
          "createdAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "updatedAt": {
            "dateTime": "",
            "offset": {
              "totalSeconds": 0
            },
            "zone": {}
          },
          "children": []
        }
      ]
    }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityEnterpriseCategoryDTO](#schemaresponseentityenterprisecategorydto)|

## GET 删除分类

GET /whiskerguardregulatoryservice/api/enterprise/categories/delete/id

描述：删除分类。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_EnterpriseCategoryDTO">EnterpriseCategoryDTO</h2>

<a id="schemaenterprisecategorydto"></a>
<a id="schema_EnterpriseCategoryDTO"></a>
<a id="tocSenterprisecategorydto"></a>
<a id="tocsenterprisecategorydto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "string",
  "categoryCode": "string",
  "sortNum": 0,
  "status": 0,
  "parentId": 0,
  "description": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|categoryName|string|true|none||分类名称|
|categoryCode|string|false|none||分类编码|
|sortNum|integer|false|none||排序|
|status|integer|false|none||状态：0、未启用 1、启用|
|parentId|integer(int64)|false|none||父级分类ID，NULL 表示顶级分类|
|description|string|false|none||分类描述|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|children|[[EnterpriseCategoryDTO](#schemaenterprisecategorydto)]|false|none||none|

<h2 id="tocS_ResponseEntityEnterpriseCategoryDTO">ResponseEntityEnterpriseCategoryDTO</h2>

<a id="schemaresponseentityenterprisecategorydto"></a>
<a id="schema_ResponseEntityEnterpriseCategoryDTO"></a>
<a id="tocSresponseentityenterprisecategorydto"></a>
<a id="tocsresponseentityenterprisecategorydto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "string",
  "categoryCode": "string",
  "sortNum": 0,
  "status": 0,
  "parentId": 0,
  "description": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|categoryName|string|true|none||分类名称|
|categoryCode|string|false|none||分类编码|
|sortNum|integer|false|none||排序|
|status|integer|false|none||状态：0、未启用 1、启用|
|parentId|integer(int64)|false|none||父级分类ID，NULL 表示顶级分类|
|description|string|false|none||分类描述|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|children|[[EnterpriseCategoryDTO](#schemaenterprisecategorydto)]|false|none||none|

<h2 id="tocS_ListEnterpriseCategoryDTO">ListEnterpriseCategoryDTO</h2>

<a id="schemalistenterprisecategorydto"></a>
<a id="schema_ListEnterpriseCategoryDTO"></a>
<a id="tocSlistenterprisecategorydto"></a>
<a id="tocslistenterprisecategorydto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "categoryName": "string",
  "categoryCode": "string",
  "sortNum": 0,
  "status": 0,
  "parentId": 0,
  "description": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "children": "new ArrayList<>()"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID|
|categoryName|string|true|none||分类名称|
|categoryCode|string|false|none||分类编码|
|sortNum|integer|false|none||排序|
|status|integer|false|none||状态：0、未启用 1、启用|
|parentId|integer(int64)|false|none||父级分类ID，NULL 表示顶级分类|
|description|string|false|none||分类描述|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedAt|[Instant](#schemainstant)|false|none||更新时间|
|children|[[EnterpriseCategoryDTO](#schemaenterprisecategorydto)]|false|none||none|

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

