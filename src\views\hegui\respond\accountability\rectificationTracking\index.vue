<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import * as echarts from 'echarts'
import {
  ArrowDown, <PERSON>Right, <PERSON>Up, Checked, CircleCheck,
  Clock, Connection, DataAnalysis, DataLine, Document,
  Download, Finished, Grid, Loading, MagicStick,
  Plus, RefreshLeft, Search, Upload, User,
  Warning,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import correctionsApi from '@/api/problemTask/corrections'
import dictApi from '@/api/modules/system/dict'

const router = useRouter()
// 定义整改数据类型
interface CorrectionItem {
  id: number
  tenantId: number
  name: string
  correctionCode: string
  correctionType: string
  level: 'LOW' | 'MIDDLE' | 'HIGH'
  investigateId: number
  dutyEmployeeId: number
  dutyEmployeeOrgId: number
  dutyDeptName?: string
  collaborationEmployeeId: number
  supervisionEmployeeId: number
  startDate: string
  finishDate: string
  status: 'NO_START' | 'PROGRESSING' | 'FINISHED' | 'PAUSED' | 'CANCELED'
  correctionBackground?: string
  correctionRequire?: string
  correctionRange?: string
  correctionScheme?: string
  metadata?: string
  version?: number
  createdBy?: string
  createdAt?: any
  updatedBy?: string
  updatedAt?: any
  isDeleted?: boolean
  progress?: number
}

// 筛选条件
const filter = reactive({
  status: null as any,
  type: null as any,
  department: null as any,
  dateRange: null as any,
  keyword: null as any,
  owner: null as any,
  supervisor: null as any,
  priority: null as any,
  source: null as any,
})
const _showAdvanced = ref(false)
function resetFilter() {
  filter.status = null
  filter.type = null
  filter.department = null
  filter.dateRange = null
  filter.keyword = null
  filter.owner = null
  filter.supervisor = null
  filter.priority = null
  filter.source = null
}
function applyFilter() {
  // 应用筛选条件逻辑
  fetchCorrections()
}

// 重置筛选条件
function handleReset() {
  resetFilter()
  fetchCorrections()
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(128)

// 查询数据
function handleSearch() {
  currentPage.value = 1
  fetchCorrections()
}
// 整改类型树数据
const _typeTreeData = [
  {
    id: 1,
    label: '合规风险',
    children: [
      { id: 11, label: '反洗钱' },
      { id: 12, label: '数据隐私' },
      { id: 13, label: '反腐败' },
    ],
  },
  {
    id: 2,
    label: '操作风险',
    children: [
      { id: 21, label: '流程缺陷' },
      { id: 22, label: '人为错误' },
      { id: 23, label: '系统故障' },
    ],
  },
  {
    id: 3,
    label: '系统风险',
    children: [
      { id: 31, label: '信息安全' },
      { id: 32, label: '系统稳定性' },
    ],
  },
]
const _defaultProps = {
  children: 'children',
  label: 'label',
}
function handleAdd() {
  router.push({
    path: '/respond/accountability/rectificationTracking/addEdit',
  })
}
// 表格数据
const tableData = ref<CorrectionItem[]>([])
const loading = ref(false)

// 获取整改数据
function fetchCorrections() {
  loading.value = true
  correctionsApi.getCorrectionsList({
    page: currentPage.value,
    size: pageSize.value,
    ...filter,
  }).then((response: any) => {
    // 根据API文档，数据在content字段中
    tableData.value = response.content || []
    total.value = response.totalElements || 0
  }).catch((error: any) => {
    console.error('获取整改数据失败:', error)
  }).finally(() => {
    loading.value = false
  })
}
// 图表相关
const trendTimeRange = ref('月度')
const trendMetric = ref('count')
const deptTimeRange = ref('本月')
const trendChart = ref()
const statusChart = ref()
const deptChart = ref()
// 部门整改数据
const deptTableData = ref([
  { deptName: '合规部', total: 42, completed: 28, completionRate: 66.7, avgDays: 18, onTimeRate: 85 },
  { deptName: '技术部', total: 35, completed: 22, completionRate: 62.9, avgDays: 21, onTimeRate: 78 },
  { deptName: '运营部', total: 28, completed: 15, completionRate: 53.6, avgDays: 25, onTimeRate: 72 },
  { deptName: '财务部', total: 15, completed: 10, completionRate: 66.7, avgDays: 16, onTimeRate: 90 },
  { deptName: '人力资源部', total: 8, completed: 5, completionRate: 62.5, avgDays: 19, onTimeRate: 80 },
])
// 初始化图表
function initCharts() {
  // 整改完成趋势图
  const trendChartInstance = echarts.init(trendChart.value)
  trendChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['完成数量', '完成率'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '完成数量',
        type: 'line',
        data: [12, 19, 15, 27, 23, 32, 35],
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF',
        },
        itemStyle: {
          color: '#409EFF',
        },
      },
      {
        name: '完成率',
        type: 'line',
        data: [45, 52, 60, 68, 72, 75, 80],
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#67C23A',
        },
        itemStyle: {
          color: '#67C23A',
        },
      },
    ],
  })
  // 整改状态分布图
  const statusChartInstance = echarts.init(statusChart.value)
  statusChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '整改状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 24, name: '未开始', itemStyle: { color: '#909399' } },
          { value: 68, name: '进行中', itemStyle: { color: '#409EFF' } },
          { value: 28, name: '已完成', itemStyle: { color: '#67C23A' } },
          { value: 8, name: '已逾期', itemStyle: { color: '#F56C6C' } },
        ],
      },
    ],
  })
  // 部门整改堆叠柱状图
  const deptChartInstance = echarts.init(deptChart.value)
  deptChartInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['未开始', '进行中', '已完成', '已逾期'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['合规部', '技术部', '运营部', '财务部', '人力资源部'],
    },
    series: [
      {
        name: '未开始',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
        },
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: '#909399',
        },
        data: [5, 6, 4, 2, 1],
      },
      {
        name: '进行中',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
        },
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: '#409EFF',
        },
        data: [15, 12, 10, 4, 2],
      },
      {
        name: '已完成',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
        },
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: '#67C23A',
        },
        data: [18, 14, 12, 8, 5],
      },
      {
        name: '已逾期',
        type: 'bar',
        stack: 'total',
        label: {
          show: true,
        },
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          color: '#F56C6C',
        },
        data: [4, 3, 2, 1, 0],
      },
    ],
  })
}
function viewDeptDetail(_row: any) {
  // 查看部门详情逻辑
}

// 右侧面板数据
const urgentItems = ref([
  {
    id: 'CM20230009',
    name: '反洗钱系统紧急补丁',
    department: '技术部',
    owner: '王技术',
    priority: '高',
    status: '已逾期',
    days: -3,
    progress: 60,
  },
  {
    id: 'CM20230010',
    name: '客户数据泄露应急处理',
    department: '合规部',
    owner: '张合规',
    priority: '高',
    status: '进行中',
    days: 1,
    progress: 45,
  },
  {
    id: 'CM20230011',
    name: '财务系统权限漏洞修复',
    department: '财务部',
    owner: '李财务',
    priority: '中',
    status: '进行中',
    days: 2,
    progress: 30,
  },
])

const stats = reactive({
  completionRate: 53,
  onTimeRate: 78,
  acceptanceRate: 85,
})

const rankView = ref('个人')
const efficiencyRanking = ref([
  { rank: 1, name: '张合规', completionRate: 92 },
  { rank: 2, name: '李财务', completionRate: 88 },
  { rank: 3, name: '王技术', completionRate: 85 },
  { rank: 4, name: '吴客服', completionRate: 80 },
  { rank: 5, name: '周人事', completionRate: 78 },
])

const aiSuggestion = ref('')
const taskStatsChart = ref()
const acceptanceChart = ref()

// 进度更新相关变量
const progressDialogVisible = ref(false)
const currentItem = ref({
  id: '',
  name: '',
  department: '',
  owner: '',
  progress: 0,
})
const progressForm = reactive({
  description: '',
  problems: '',
  solutions: '',
  files: [],
})

function handleUrgentItem(item: any) {
  currentItem.value = { ...item }
  progressDialogVisible.value = true
}

function analyzeEfficiency() {
  aiSuggestion.value = '根据分析，技术部门的整改效率较低，建议加强技术团队与合规团队的协作，优化工作流程。'
}

function predictRisk() {
  aiSuggestion.value = '预测显示财务部有2个项目可能在3天内逾期，建议优先处理并加强监督。'
}

function suggestOptimization() {
  aiSuggestion.value = '建议：1. 建立整改项目优先级评估机制 2. 优化跨部门协作流程 3. 引入自动化进度跟踪工具'
}

// 初始化右侧图表
function initSidebarCharts() {
  // 任务统计图表
  const taskStatsInstance = echarts.init(taskStatsChart.value)
  taskStatsInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    series: [{
      name: '整改类型',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      data: [
        { value: 45, name: '合规风险' },
        { value: 35, name: '操作风险' },
        { value: 20, name: '系统风险' },
      ],
    }],
  })

  // 验收统计图表
  const acceptanceInstance = echarts.init(acceptanceChart.value)
  acceptanceInstance.setOption({
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    series: [{
      name: '验收结果',
      type: 'pie',
      radius: ['60%', '80%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      data: [
        { value: 85, name: '通过', itemStyle: { color: '#67C23A' } },
        { value: 10, name: '有条件通过', itemStyle: { color: '#E6A23C' } },
        { value: 5, name: '不通过', itemStyle: { color: '#F56C6C' } },
      ],
    }],
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
    initSidebarCharts()
  })
})
onMounted(() => {
  nextTick(() => {
    initCharts()
  })
  fetchCorrections()
  fetchCorrectionTypeOptions()
})
// 状态文本转换
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    NO_START: '未开始',
    PROGRESSING: '进行中',
    FINISHED: '已完成',
    PAUSED: '已暂停',
    CANCELED: '已取消',
  }
  return statusMap[status] || status
}

// 整改类型选项
const correctionTypeOptions = ref<Array<{ name: string, value: string }>>([])

// 获取整改类型选项
async function fetchCorrectionTypeOptions() {
  try {
    const response = await dictApi.dictAll(24)
    if (response) {
      correctionTypeOptions.value = response
    }
  }
  catch (error) {
    console.error('获取整改类型选项失败:', error)
  }
}

// 获取整改类型文本
function getCorrectionTypeText(type: string): string {
  const option = correctionTypeOptions.value.find(item => item.value === type)
  return option ? option.name : type
}

// 获取优先级文本
function formatLevel(level: string): string {
  const levelMap: Record<string, string> = {
    HIGH: '高',
    MIDDLE: '中',
    LOW: '低',
  }
  return levelMap[level] || level
}

// 获取优先级标签类型
function getLevelTagType(level: string): 'danger' | 'warning' | 'success' | 'info' | 'primary' {
  switch (level) {
    case 'HIGH': return 'danger'
    case 'MIDDLE': return 'warning'
    case 'LOW': return 'success'
    default: return 'info'
  }
}

// 视图类型
const _viewType = ref('table')
function handleExceed() {
  // 文件上传超出限制处理
}
function _updateProgress(item: any) {
  currentItem.value = { ...item }
  progressForm.description = ''
  progressForm.problems = ''
  progressForm.solutions = ''
  progressDialogVisible.value = true
}
function submitProgress() {
  // 提交进度更新逻辑
  progressDialogVisible.value = false
}
function showDetail(_item: any) {
  router.push({
    path: '/respond/accountability/rectificationTracking/detail',
    query: {
      id: _item.id,
    },
  })
}
function showEdit(_item: any) {
  router.push({
    path: '/respond/accountability/rectificationTracking/addEdit',
    query: {
      id: _item.id,
    },
  })
}
function _editItem(_item: any) {
  // 编辑项目逻辑
}
function _terminateItem(_item: any) {
  // 终止项目逻辑
}
function _deleteItem(item: any) {
  ElMessageBox.confirm(
    `确定要删除整改项目"${item.name}"吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: false,
    },
  ).then(() => {
    correctionsApi.deleteCorrection(item.id).then(() => {
      ElMessage.success('删除成功')
      fetchCorrections() // 重新获取列表数据
    }).catch((error) => {
      ElMessage.error('删除失败')
      console.error('删除失败:', error)
    })
  }).catch(() => {
    // 用户取消删除，不做任何操作
  })
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              整改跟踪
            </h1>
            <!-- <el-tag type="warning" class="ml-4">进行中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button v-auth="'rectificationTracking/add'" type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>新增整改项目
            </el-button>
            <el-button v-auth="'rectificationTracking/batchImport'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Upload />
              </el-icon>批量导入
            </el-button>
            <el-button v-auth="'rectificationTracking/export'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Download />
              </el-icon>导出
            </el-button>
            <el-button v-auth="'rectificationTracking/statistics'" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <DataAnalysis />
              </el-icon>统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="!mx-0">
          <el-col :span="18">
            <div class="flex">
              <el-card shadow="hover" class="mr-10 flex-1">
                <div class="flex flex-col items-center">
                  <div class="text-3xl text-blue-500 font-bold">
                    128
                  </div>
                  <div class="mt-2 text-gray-500">
                    总整改数
                  </div>
                  <el-icon class="mt-2 text-blue-400">
                    <Document />
                  </el-icon>
                </div>
              </el-card>
              <el-card shadow="hover" class="ml-10 mr-10 flex-1">
                <div class="flex flex-col items-center">
                  <div class="text-3xl font-bold">
                    24
                  </div>
                  <div class="mt-2 text-gray-500">
                    未开始数
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    同比 +12%
                  </div>
                  <el-icon class="mt-1 text-gray-400">
                    <Clock />
                  </el-icon>
                </div>
              </el-card>
              <el-card shadow="hover" class="ml-10 mr-10 flex-1">
                <div class="flex flex-col items-center">
                  <div class="text-3xl text-blue-400 font-bold">
                    68
                  </div>
                  <div class="mt-2 text-gray-500">
                    进行中数
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    同比 -5%
                  </div>
                  <el-icon class="mt-1 text-blue-400">
                    <Loading />
                  </el-icon>
                </div>
              </el-card>
              <el-card shadow="hover" class="ml-10 mr-10 flex-1">
                <div class="flex flex-col items-center">
                  <div class="text-3xl text-green-500 font-bold">
                    28
                  </div>
                  <div class="mt-2 text-gray-500">
                    已完成数
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    同比 +8%
                  </div>
                  <el-icon class="mt-1 text-green-400">
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-card>
              <el-card shadow="hover" class="ml-10 flex-1">
                <div class="flex flex-col items-center">
                  <div class="text-3xl text-red-500 font-bold">
                    8
                  </div>
                  <div class="mt-2 text-gray-500">
                    已逾期数
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    同比 +3%
                  </div>
                  <el-icon class="mt-1 text-red-400">
                    <Warning />
                  </el-icon>
                </div>
              </el-card>
            </div>
            <el-row :gutter="20" class="mt-20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">总体完成率</span>
                    <span class="text-blue-500 font-bold">53%</span>
                  </div>
                  <el-progress :percentage="53" class="mt-2" />
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">平均完成时间</span>
                    <span class="text-blue-500 font-bold">23 天</span>
                  </div>
                  <div class="mt-2 text-sm text-gray-500">
                    较上月缩短 2 天
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt-20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex">
                    <div class="flex-1">
                      <h3 class="mb-3 text-lg font-bold">
                        我负责的整改
                      </h3>
                      <div class="mb-2 text-2xl font-bold">
                        12
                      </div>
                      <div class="mb-3 flex justify-between text-sm text-gray-500">
                        <span>未开始 3</span>
                        <span>进行中 7</span>
                        <span>已完成 2</span>
                      </div>
                      <el-progress :percentage="60" :format="() => ''" />
                      <el-button v-auth="'rectificationTracking/viewDetail'" link class="mt-3 pl-0">
                        查看详情 <el-icon><ArrowRight /></el-icon>
                      </el-button>
                    </div>
                    <div class="w-16 flex items-center justify-center">
                      <el-icon class="text-3xl text-blue-400">
                        <User />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex">
                    <div class="flex-1">
                      <h3 class="mb-3 text-lg font-bold">
                        我参与的整改
                      </h3>
                      <div class="mb-2 text-2xl font-bold">
                        18
                      </div>
                      <div class="mb-3 flex justify-between text-sm text-gray-500">
                        <span>未开始 5</span>
                        <span>进行中 10</span>
                        <span>已完成 3</span>
                      </div>
                      <el-progress :percentage="45" :format="() => ''" />
                      <el-button v-auth="'rectificationTracking/viewDetail'" link class="mt-3 pl-0">
                        查看详情 <el-icon><ArrowRight /></el-icon>
                      </el-button>
                    </div>
                    <div class="w-16 flex items-center justify-center">
                      <el-icon class="text-3xl text-green-400">
                        <Connection />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-row :gutter="20" class="mt-20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex">
                    <div class="flex-1">
                      <h3 class="mb-3 text-lg font-bold">
                        我监督的整改
                      </h3>
                      <div class="mb-2 text-2xl font-bold">
                        9
                      </div>
                      <div class="mb-3 flex justify-between text-sm text-gray-500">
                        <span>未开始 1</span>
                        <span>进行中 6</span>
                        <span>已完成 2</span>
                      </div>
                      <el-progress :percentage="70" :format="() => ''" />
                      <el-button v-auth="'rectificationTracking/viewDetail'" link class="mt-3 pl-0">
                        查看详情 <el-icon><ArrowRight /></el-icon>
                      </el-button>
                    </div>
                    <div class="w-16 flex items-center justify-center">
                      <el-icon class="text-3xl text-orange-400">
                        <view />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div class="flex">
                    <div class="flex-1">
                      <h3 class="mb-3 text-lg font-bold">
                        待验收的整改
                      </h3>
                      <div class="mb-2 text-2xl font-bold">
                        5
                      </div>
                      <div class="mb-3 text-sm text-gray-500">
                        <span class="text-red-500">2 项已超期</span>
                        <span class="ml-4">3 项即将到期</span>
                      </div>
                      <el-button type="primary" plain class="!rounded-button mt-1 whitespace-nowrap">
                        <el-icon class="mr-1">
                          <Finished />
                        </el-icon>立即验收
                      </el-button>
                    </div>
                    <div class="w-16 flex items-center justify-center">
                      <el-icon class="text-3xl text-purple-400">
                        <Checked />
                      </el-icon>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            <el-card shadow="hover" class="mt-20">
              <div class="flex">
                <!-- 整改类型树 -->
                <!-- <div class="w-48 border-r pr-4">
                  <div class="mb-2 text-gray-500">
                    整改类型
                  </div>
                  <el-tree
                    :data="typeTreeData" :props="defaultProps" node-key="id" default-expand-all highlight-current
                    class="border-none"
                  />
                </div> -->
                <!-- 筛选条件 -->
                <div class="flex-1 pl-4">
                  <div class="mb-4">
                    <!-- 第一行筛选条件 -->
                    <div class="mb-3 flex flex-wrap items-center gap-3">
                      <el-select v-model="filter.status" placeholder="整改状态" clearable size="default" class="w-36">
                        <el-option label="未开始" value="NOT_STARTED" />
                        <el-option label="进行中" value="PROGRESSING" />
                        <el-option label="已完成" value="FINISHED" />
                        <el-option label="已逾期" value="OVERDUE" />
                        <el-option label="已终止" value="TERMINATED" />
                      </el-select>
                      <el-select v-model="filter.type" placeholder="整改类型" clearable size="default" class="w-36">
                        <el-option label="合规风险" value="COMPLIANCE_RISK" />
                        <el-option label="操作风险" value="OPERATIONAL_RISK" />
                        <el-option label="系统风险" value="SYSTEM_RISK" />
                      </el-select>
                      <el-select v-model="filter.department" placeholder="责任部门" clearable size="default" class="w-36">
                        <el-option label="合规部" value="1" />
                        <el-option label="运营部" value="2" />
                        <el-option label="技术部" value="3" />
                        <el-option label="财务部" value="4" />
                      </el-select>
                      <el-date-picker
                        v-model="filter.dateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" size="default" class="w-72"
                      />
                    </div>
                    <!-- 第二行搜索和操作按钮 -->
                    <div class="flex items-center gap-3">
                      <el-input v-model="filter.keyword" placeholder="请输入整改编号、名称等关键字" size="default" class="w-80" clearable>
                        <template #prefix>
                          <el-icon><Search /></el-icon>
                        </template>
                      </el-input>
                      <div class="flex gap-2">
                        <el-button size="default" @click="handleReset">
                          <el-icon><RefreshLeft /></el-icon>
                          重置
                        </el-button>
                        <el-button type="primary" size="default" @click="handleSearch">
                          <el-icon><Search /></el-icon>
                          查询
                        </el-button>
                      </div>
                    </div>
                  </div>
                  <!-- <el-button link @click="showAdvanced = !showAdvanced">
                    {{ showAdvanced ? '收起高级筛选' : '高级筛选' }}
                    <el-icon>
                      <ArrowDown v-if="!showAdvanced" />
                      <ArrowUp v-else />
                    </el-icon>
                  </el-button> -->
                  <!-- 高级筛选 -->
                  <div v-if="false" class="rounded bg-gray-50 p-4">
                    <div class="grid grid-cols-3 gap-4">
                      <div>
                        <div class="mb-1 text-sm text-gray-500">
                          负责人
                        </div>
                        <el-select v-model="filter.owner" placeholder="请选择负责人" clearable size="small" class="w-full">
                          <el-option label="张合规" value="1" />
                          <el-option label="李运营" value="2" />
                          <el-option label="王技术" value="3" />
                        </el-select>
                      </div>
                      <div>
                        <div class="mb-1 text-sm text-gray-500">
                          监督人
                        </div>
                        <el-select
                          v-model="filter.supervisor" placeholder="请选择监督人" clearable size="small"
                          class="w-full"
                        >
                          <el-option label="赵监督" value="1" />
                          <el-option label="钱监督" value="2" />
                        </el-select>
                      </div>
                      <div>
                        <div class="mb-1 text-sm text-gray-500">
                          优先级
                        </div>
                        <el-select v-model="filter.priority" placeholder="请选择优先级" clearable size="small" class="w-full">
                          <el-option label="高" value="1" />
                          <el-option label="中" value="2" />
                          <el-option label="低" value="3" />
                        </el-select>
                      </div>
                      <div>
                        <div class="mb-1 text-sm text-gray-500">
                          来源
                        </div>
                        <el-select v-model="filter.source" placeholder="请选择来源" clearable size="small" class="w-full">
                          <el-option label="内部检查" value="1" />
                          <el-option label="外部审计" value="2" />
                          <el-option label="投诉举报" value="3" />
                        </el-select>
                      </div>
                    </div>
                    <div class="mt-4 flex justify-end space-x-3">
                      <el-button type="primary" size="small" @click="applyFilter">
                        应用
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  整改趋势分析
                </div>
              </template>
              <div class="mb-4 flex items-center justify-between">
                <h3 class="text-lg font-bold">
                  整改趋势分析
                </h3>
                <div class="flex space-x-2">
                  <el-radio-group v-model="trendTimeRange" size="small">
                    <el-radio-button label="月度" />
                    <el-radio-button label="季度" />
                    <el-radio-button label="年度" />
                  </el-radio-group>
                </div>
              </div>
              <div class="grid grid-cols-2 h-80 gap-4">
                <!-- 左侧：整改完成趋势图 -->
                <div class="rounded bg-gray-50 p-4">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-600">整改完成趋势</span>
                    <el-select v-model="trendMetric" size="small" class="w-32">
                      <el-option label="完成数量" value="count" />
                      <el-option label="完成率" value="rate" />
                    </el-select>
                  </div>
                  <div ref="trendChart" class="h-full w-full" />
                </div>
                <!-- 右侧：整改状态分布图 -->
                <div class="rounded bg-gray-50 p-4">
                  <div class="mb-2 text-sm text-gray-600">
                    整改状态分布
                  </div>
                  <div ref="statusChart" class="h-full w-full" />
                </div>
              </div>
            </el-card>
            <el-card v-if="false" shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  部门整改情况
                </div>
              </template>
              <div class="mb-4 flex items-center justify-between">
                <h3 class="text-lg font-bold">
                  部门整改情况
                </h3>
                <div class="flex space-x-2">
                  <el-radio-group v-model="deptTimeRange" size="small">
                    <el-radio-button label="本月" />
                    <el-radio-button label="本季度" />
                    <el-radio-button label="本年" />
                  </el-radio-group>
                </div>
              </div>
              <!-- 部门整改堆叠柱状图 -->
              <div class="mb-4 h-64 rounded bg-gray-50 p-4">
                <div ref="deptChart" class="h-full w-full" />
              </div>
              <!-- 部门整改数据表格 -->
              <el-table :data="deptTableData" style="width: 100%;">
                <el-table-column prop="deptName" label="部门" sortable />
                <el-table-column prop="total" label="总项目数" sortable />
                <el-table-column prop="completed" label="已完成数" sortable />
                <el-table-column prop="completionRate" label="完成率" sortable>
                  <template #default="{ row }">
                    <el-progress :percentage="row.completionRate" :show-text="false" />
                    <span class="ml-2">{{ row.completionRate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="avgDays" label="平均完成时间" sortable>
                  <template #default="{ row }">
                    {{ row.avgDays }} 天
                  </template>
                </el-table-column>
                <el-table-column prop="onTimeRate" label="及时率" sortable>
                  <template #default="{ row }">
                    <el-progress :percentage="row.onTimeRate" :show-text="false" status="success" />
                    <span class="ml-2">{{ row.onTimeRate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ row }">
                    <el-button type="primary" size="small" @click="viewDeptDetail(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <el-card shadow="hover" class="mt-20">
              <div>
                <el-table v-loading="loading" :data="tableData" style="width: 100%;">
                  <el-table-column type="selection" width="50" />
                  <el-table-column prop="correctionCode" label="整改编号" width="120" />
                  <el-table-column prop="name" label="整改项目名称" width="200" />
                  <el-table-column prop="correctionType" label="整改类型" width="120">
                    <template #default="{ row }">
                      <el-tag size="small">
                        {{ getCorrectionTypeText(row.correctionType) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="level" label="优先级" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="getLevelTagType(row.level)"
                        size="small"
                      >
                        {{ formatLevel(row.level) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="dutyDeptName" label="责任部门" width="120" />
                  <el-table-column prop="dutyEmployeeId" label="负责人" width="120" />
                  <el-table-column prop="supervisionEmployeeId" label="监督人" width="120" />
                  <el-table-column prop="startDate" label="开始日期" width="120" />
                  <el-table-column prop="finishDate" label="计划完成日期" width="140">
                    <template #default="{ row }">
                      <span :class="{ 'text-red-500': row.status === 'OVERDUE' }">{{ row.finishDate }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="当前状态" width="120">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.status === 'NO_START' ? 'info' : row.status === 'IN_PROGRESS' ? 'primary' : row.status === 'COMPLETED' ? 'success' : 'danger'"
                        size="small"
                      >
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="progress" label="进度" width="150">
                    <template #default="{ row }">
                      <el-progress :percentage="row.progress || 0" :color="row.progress === 100 ? '#67C23A' : '#409EFF'" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="210">
                    <template #default="{ row }">
                      <el-button v-auth="'rectificationTracking/view'" type="primary" size="small" @click="showDetail(row)">
                        查看
                      </el-button>
                      <el-button v-auth="'rectificationTracking/delete'" type="danger" size="small" @click="_deleteItem(row)">
                        删除
                      </el-button>
                      <el-button v-auth="'rectificationTracking/edit'" type="warning" size="small" @click="showEdit(row)">
                        编辑
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!-- 分页 -->
                <div class="mt-4 flex justify-end">
                  <el-pagination
                    v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total"
                  />
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  紧急事项
                </div>
              </template>
              <div class="space-y-3">
                <div v-for="(item, index) in urgentItems" :key="index" class="border-b pb-3 last:border-b-0">
                  <div class="flex items-start justify-between">
                    <div>
                      <div class="font-medium">
                        {{ item.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ item.department }} · {{ item.owner }}
                      </div>
                    </div>
                    <el-tag
                      :type="item.priority === '高' ? 'danger' : 'warning'"
                      size="small"
                    >
                      {{ item.priority }}
                    </el-tag>
                  </div>
                  <div class="mt-2 flex items-center justify-between">
                    <div>
                      <el-tag :type="item.status === '已逾期' ? 'danger' : 'primary'" size="small">
                        {{ item.status }}
                      </el-tag>
                      <span :class="{ 'text-red-500': item.days < 0 }" class="ml-2 text-sm">
                        {{ item.days < 0 ? `逾期 ${-item.days} 天` : `剩余 ${item.days} 天` }}
                      </span>
                    </div>
                    <el-button v-auth="'rectificationTracking/immediateProcess'" link size="small" @click="handleUrgentItem(item)">
                      立即处理
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  整改任务统计
                </div>
              </template>
              <div class="grid grid-cols-2 mb-3 gap-2">
                <div class="text-center">
                  <div class="text-2xl text-blue-500 font-bold">
                    {{ stats.completionRate }}%
                  </div>
                  <div class="text-sm text-gray-500">
                    完成率
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-2xl font-bold">
                    {{ stats.onTimeRate }}%
                  </div>
                  <div class="text-sm text-gray-500">
                    及时率
                  </div>
                </div>
              </div>
              <div ref="taskStatsChart" class="h-40" />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  验收统计
                </div>
              </template>
              <div class="mb-3 flex justify-center">
                <div ref="acceptanceChart" class="h-32 w-32" />
              </div>
              <div class="grid grid-cols-3 gap-2 text-center">
                <div>
                  <div class="text-sm font-medium">
                    85%
                  </div>
                  <div class="text-xs text-gray-500">
                    验收通过率
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium">
                    78%
                  </div>
                  <div class="text-xs text-gray-500">
                    一次通过率
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium">
                    92%
                  </div>
                  <div class="text-xs text-gray-500">
                    及时验收率
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    人员效率排名
                  </div>
                  <el-radio-group v-model="rankView" size="small">
                    <el-radio-button label="个人" />
                    <el-radio-button label="部门" />
                  </el-radio-group>
                </div>
              </template>
              <div class="mb-3 flex items-center justify-between">
                <div class="text-lg font-bold">
                  人员效率排名
                </div>
              </div>
              <el-table :data="efficiencyRanking" size="small" class="w-full">
                <el-table-column prop="rank" label="排名" width="50" />
                <el-table-column prop="name" label="负责人" />
                <el-table-column prop="completionRate" label="完成率" width="70">
                  <template #default="{ row }">
                    {{ row.completionRate }}%
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  AI助手
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button v-auth="'rectificationTracking/analyzeEfficiency'" type="primary" class="!rounded-button w-full whitespace-nowrap" @click="analyzeEfficiency">
                    <el-icon class="mr-1">
                      <DataAnalysis />
                    </el-icon>分析整改效率
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'rectificationTracking/predictRisk'" type="warning" class="!rounded-button w-full whitespace-nowrap" @click="predictRisk">
                    <el-icon class="mr-1">
                      <Warning />
                    </el-icon>预测逾期风险
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'rectificationTracking/suggestOptimization'" type="info" class="!rounded-button w-full whitespace-nowrap" @click="suggestOptimization">
                    <el-icon class="mr-1">
                      <MagicStick />
                    </el-icon>建议优化方案
                  </el-button>
                </div>
                <div v-if="aiSuggestion" class="mt-3 rounded bg-gray-100 p-3 text-sm">
                  {{ aiSuggestion }}
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>

    <!-- 进度更新弹窗 -->
    <el-dialog v-model="progressDialogVisible" title="整改进度更新" width="600px">
      <el-form label-width="100px">
        <el-form-item label="整改项目">
          <el-input v-model="currentItem.name" disabled />
        </el-form-item>
        <el-form-item label="责任部门">
          <el-input v-model="currentItem.department" disabled />
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model="currentItem.owner" disabled />
        </el-form-item>
        <el-form-item label="当前进度">
          <el-slider
            v-model="currentItem.progress" show-input
            :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
          />
        </el-form-item>
        <el-form-item label="进度说明">
          <el-input v-model="progressForm.description" type="textarea" :rows="3" placeholder="请简要描述当前进度情况" />
        </el-form-item>
        <el-form-item label="遇到的问题">
          <el-input v-model="progressForm.problems" type="textarea" :rows="3" placeholder="请描述当前遇到的问题" />
        </el-form-item>
        <el-form-item label="解决方案">
          <el-input v-model="progressForm.solutions" type="textarea" :rows="3" placeholder="请描述解决方案或需要的支持" />
        </el-form-item>
        <el-form-item label="上传附件">
          <el-upload action="https://jsonplaceholder.typicode.com/posts/" multiple :limit="3" :on-exceed="handleExceed">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <Upload />
              </el-icon>点击上传
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传jpg/png/doc/pdf文件，且不超过5MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="progressDialogVisible = false">取消</el-button>
          <el-button v-auth="'rectificationTracking/submitProgress'" type="primary" @click="submitProgress">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-card {
    border-radius: 4px;
  }

  .right-panel-card {
    border-radius: 6px;
  }

  .right-panel-card :deep(.el-card__body) {
    padding: 16px;
  }

  .el-table {
    --el-table-border-color: #f0f0f0;
  }

  .el-tree {
    --el-tree-node-hover-bg-color: #f5f7fa;
  }

  .el-upload__tip {
    margin-top: 7px;
    font-size: 12px;
    color: #909399;
  }

  :deep(.el-progress-bar) {
    padding-right: 0;
    margin-right: 0;
  }

  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
