<script setup>
import { reactive, ref, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const courseList = ref([])
const selectedCourses = ref([])
const tableRef = ref()

const searchParams = reactive({
  courseName: '',
  courseType: '',
  status: '',
  instructor: '',
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 重置状态
    selectedCourses.value = []
    currentPage.value = 1
    // 清空表格选择
    if (tableRef.value) {
      tableRef.value.clearSelection()
    }
    // 获取课程列表
    getCourseList()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 获取课程列表
async function getCourseList() {
  try {
    loading.value = true
    const queryParams = {
      courseName: searchParams.courseName,
      courseType: searchParams.courseType,
      status: searchParams.status,
      instructor: searchParams.instructor,
    }

    const pagingParams = {
      page: currentPage.value - 1, // 后端分页从0开始
      size: pageSize.value,
    }

    const res = await trainingCurriculum.system(pagingParams, queryParams)

    if (res) {
      let courseData = []
      if (res.content && Array.isArray(res.content)) {
        courseData = res.content
        total.value = res.totalElements || res.total || 0
      }
      else if (Array.isArray(res)) {
        courseData = res
        total.value = res.length
      }
      else if (res.data) {
        if (res.data.content && Array.isArray(res.data.content)) {
          courseData = res.data.content
          total.value = res.data.totalElements || res.data.total || 0
        }
        else if (Array.isArray(res.data)) {
          courseData = res.data
          total.value = res.data.length
        }
      }

      courseList.value = courseData
    }
    else {
      courseList.value = []
      total.value = 0
    }
  }
  catch (error) {
    console.error('获取课程列表失败:', error)
    ElMessage.error('获取课程列表失败')
    courseList.value = []
    total.value = 0
  }
  finally {
    loading.value = false
  }
}

// 搜索课程
function searchCourses() {
  currentPage.value = 1
  getCourseList()
}

// 重置搜索
function resetSearch() {
  searchParams.courseName = ''
  searchParams.courseType = ''
  searchParams.status = ''
  searchParams.instructor = ''
  currentPage.value = 1
  getCourseList()
}

// 分页变化
function handlePageChange() {
  getCourseList()
}

function handleSizeChange() {
  currentPage.value = 1
  getCourseList()
}

// 选择变化
function handleSelectionChange(selection) {
  selectedCourses.value = selection
}

// 确认选择
function confirmSelect() {
  if (selectedCourses.value.length === 0) {
    ElMessage.warning('请选择至少一门课程')
    return
  }

  emit('confirm', selectedCourses.value)
  handleClose()
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
  selectedCourses.value = []
  // 清空表格选择
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
  // 通知父组件关闭
  emit('update:visible', false)
}
</script>

<template>
  <!-- 课程选择弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    title="选择课程"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 搜索区域 -->
    <div class="mb-4">
      <el-form :inline="true" :model="searchParams">
        <el-form-item label="课程名称">
          <el-input
            v-model="searchParams.courseName"
            placeholder="请输入课程名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="课程类型">
          <el-select
            v-model="searchParams.courseType"
            placeholder="请选择课程类型"
            clearable
            style="width: 150px"
          >
            <el-option label="合规知识" value="合规知识" />
            <el-option label="案例分析" value="案例分析" />
            <el-option label="技能培训" value="技能培训" />
          </el-select>
        </el-form-item>
        <el-form-item label="课程状态">
          <el-select
            v-model="searchParams.status"
            placeholder="请选择课程状态"
            clearable
            style="width: 150px"
          >
            <el-option label="已发布" value="已发布" />
            <el-option label="草稿" value="草稿" />
            <el-option label="已下架" value="已下架" />
          </el-select>
        </el-form-item>
        <el-form-item label="讲师">
          <el-input
            v-model="searchParams.instructor"
            placeholder="请输入讲师姓名"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchCourses">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="courseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="courseName" label="课程名称" min-width="200" show-overflow-tooltip />
      <el-table-column prop="durationMinutes" label="课程时长" width="120">
        <template #default="{ row }">
          {{ row.durationMinutes || 0 }}分钟
        </template>
      </el-table-column>
      <el-table-column prop="courseType" label="课程类型" width="120" />
      <el-table-column prop="instructor" label="讲师" width="120" />
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" @click="confirmSelect">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 组件样式 */
</style>
