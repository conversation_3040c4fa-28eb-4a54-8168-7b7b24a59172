<script setup lang="ts">
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import DataEdit from './dict-data-edit.vue'
import Api from '@/api/modules/system/dict'
import useUserStore from '@/store/modules/user'

const props = defineProps<{
  id: any
}>()

const emits = defineEmits(['pagChange'])

const userStore: any = useUserStore()

const isShow: any = ref(null)
const showDataEdit = ref(false)

const paging: any = ref({
  page: 1,
  limit: 10,
  name: '',
  code: null,
  total: 0,
  dictId: '',
})
const data = ref({
  dataList: [],
  obj: {},
})

onMounted(() => {
  paging.value.dictId = props.id
  console.log(paging.value.dictId, 'jkjkj')
  if (paging.value.dictId) {
    getList()
  }
})
function modify(val: any) {
  // modifyID.value = val
  data.value.obj = val
  showDataEdit.value = true
  isShow.value = true
}
function addData() {
  showDataEdit.value = true
  isShow.value = true
}
function getList() {
  Api.index(paging.value).then((res: any) => {
    data.value.dataList = res.data
    paging.value.total = res.count
  })
}
function showEditFn() {
  showDataEdit.value = false
  getList()
  isShow.value = false
  data.value.obj = {}
}
const { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()
pagination.value.size = 20
pagination.value.sizes = [20, 50, 100]

function remove(e: any) {
  Api.dictdatadelete({ id: e.id }).then((res: any) => {
    ElMessage({
      type: 'success',
      message: '删除成功',
    })
    getList()
  })
}
// 每页数量切换
function pagChange(e: any) {
  if (e) {
    if (e.size) {
      paging.value.limit = e.size
    }
    else if (e.page) {
      paging.value.page = e.page
    }
  }
  getList()
}
</script>

<template>
  <div>
    <el-form :inline="true" class="demo-form-inline" :model="paging">
      <el-form-item>
        <el-input v-model="paging.name" placeholder="请输入字典项名称" clearable />
      </el-form-item>
      <!-- <el-form-item>
        <el-input
          v-model="paging.code"
          placeholder="请输入字典项值"
          clearable
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getList">
          <template #icon>
            <svg-icon name="ep:search" />
          </template>
          查询
        </el-button>
        <el-button type="primary" @click="addData">
          <template #icon>
            <svg-icon name="ep:plus" />
          </template>
          添加
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 数据表格 -->
    <el-table :data="data.dataList" highlight-current-row border height="100%">
      <el-table-column prop="id" label="ID" width="60" align="center" fixed="left" />
      <el-table-column prop="name" label="字典项名称" width="120" align="center" />
      <!-- <el-table-column prop="code" label="字典项值" width="140" align="center" /> -->
      <el-table-column prop="sort" label="排序号" width="80" align="center" />
      <el-table-column prop="note" label="备注" align="center" />
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <div v-if="row.status === 1" class="relative flex items-center justify-center">
            <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
              <span
                class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
              >true</span>
            </div>
            正常
          </div>
          <div v-else class="relative flex items-center justify-center">
            <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
              <span
                class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty"
              >true</span>
            </div>
            暂停
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" width="180" align="center">
        <template #default="scope">
          <span>{{ scope.row.create_time > 0 ? dayjs(scope.row.create_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="update_time" label="更新时间" width="180" align="center">
				<template #default="scope">
					<span>{{ scope.row.update_time > 0 ? dayjs(scope.row.update_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
				</template>
			</el-table-column> -->
      <el-table-column fixed="right" width="220" align="center" label="操作">
        <template #default="scope">
          <el-button type="primary" text @click="modify(scope.row)">
            <template #icon>
              <svg-icon name="ep:edit" />
            </template>
            修改
          </el-button>

          <el-popconfirm title="确定删除吗？" @confirm="remove(scope.row)">
            <template #reference>
              <el-button type="danger" text>
                <template #icon>
                  <svg-icon name="ep:delete" />
                </template>删除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-pagination :current-page="pagination.page" :total="pagination.total" :page-size="pagination.size" :page-sizes="pagination.sizes" :layout="pagination.layout" :hide-on-single-page="false" class="pagination" background @current-change="currentChange" @size-change="sizeChange" /> -->
    <page-compon
      :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
      @pag-change="pagChange"
    />
    <!-- 修改添加 -->
    <DataEdit v-if="isShow" :id="paging.dictId" v-model="showDataEdit" :obj="data.obj" @showeditfn="showEditFn" />
  </div>
</template>

<style scoped>
  .el-table .el-button+.el-button {
    margin-left: 2px;
  }
</style>
