---
title: 猫伯伯合规管家
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 猫伯伯合规管家

Base URLs:

# Authentication

# 21-违规举报服务/持续改进报告

## POST 分页查询所有持续改进报告列表

POST /whiskerguardviolationservice/api/continuous/improvement/reports/search

描述：分页查询所有持续改进报告列表，支持排序和过滤。

> Body 请求参数

```json
{
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "createdAtStart": "string",
  "createdAtEnd": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |当前页码|
|size|query|integer| 否 |记录数|
|Authorization|header|string| 否 |访问token|
|X-TENANT-ID|header|integer| 否 |none|
|X-VERSION|header|string| 否 |当前版本|
|X-SOURCE|header|string| 否 |访问客户端|
|X-SYSTEM|header|string| 否 |访问系统|
|body|body|[ContinuousImprovementReportReq](#schemacontinuousimprovementreportreq)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "title": "",
      "reportCode": "",
      "reportType": "",
      "reportCycle": "",
      "startDate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "endDate": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "employeeId": 0,
      "orgId": 0,
      "establishDate": "",
      "level": "",
      "summary": "",
      "improveSummary": "",
      "improveInvest": "",
      "improveProcess": "",
      "improveAchievement": "",
      "metadata": "",
      "version": 0,
      "createdBy": "",
      "createdAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "updatedBy": "",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0,
        "epochSecond": 0,
        "nano": 0
      },
      "isDeleted": false,
      "attachmentList": [
        {
          "id": 0,
          "relatedId": 0,
          "relatedType": 0,
          "fileName": "",
          "filePath": "",
          "fileType": "",
          "fileSize": "",
          "fileDesc": "",
          "metadata": "",
          "version": 0,
          "createdBy": "",
          "createdAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "updatedBy": "",
          "updatedAt": {
            "seconds": 0,
            "nanos": 0,
            "epochSecond": 0,
            "nano": 0
          },
          "isDeleted": false
        }
      ]
    }
  ],
  "pageable": {
    "paged": false,
    "unpaged": false,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "",
        "property": "",
        "ignoreCase": false,
        "nullHandling": "",
        "ascending": false,
        "descending": false
      }
    ]
  },
  "total": 0,
  "empty": false,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "",
      "property": "",
      "ignoreCase": false,
      "nullHandling": "",
      "ascending": false,
      "descending": false
    }
  ],
  "first": false,
  "last": false,
  "totalPages": 0,
  "totalElements": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[ResponseEntityPageContinuousImprovementReportDTO](#schemaresponseentitypagecontinuousimprovementreportdto)|

# 数据模型

<h2 id="tocS_Instant">Instant</h2>

<a id="schemainstant"></a>
<a id="schema_Instant"></a>
<a id="tocSinstant"></a>
<a id="tocsinstant"></a>

```json
{
  "seconds": 0,
  "nanos": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|seconds|integer(int64)|false|none||The number of seconds from the epoch of 1970-01-01T00:00:00Z.|
|nanos|integer|false|none||The number of nanoseconds, later along the time-line, from the seconds field.<br />This is always positive, and never exceeds 999,999,999.|

<h2 id="tocS_Sort">Sort</h2>

<a id="schemasort"></a>
<a id="schema_Sort"></a>
<a id="tocSsort"></a>
<a id="tocssort"></a>

```json
{
  "direction": "ASC",
  "property": "string",
  "ignoreCase": true,
  "nullHandling": "NATIVE",
  "ascending": true,
  "descending": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|direction|string|false|none||none|
|property|string|false|none||none|
|ignoreCase|boolean|false|none||none|
|nullHandling|string|false|none||none|
|ascending|boolean|false|none||Returns whether sorting for this property shall be ascending.|
|descending|boolean|false|none||Returns whether sorting for this property shall be descending.|

#### 枚举值

|属性|值|
|---|---|
|direction|ASC|
|direction|DESC|
|nullHandling|NATIVE|
|nullHandling|NULLS_FIRST|
|nullHandling|NULLS_LAST|

<h2 id="tocS_Pageable">Pageable</h2>

<a id="schemapageable"></a>
<a id="schema_Pageable"></a>
<a id="tocSpageable"></a>
<a id="tocspageable"></a>

```json
{
  "paged": true,
  "unpaged": true,
  "pageNumber": 0,
  "pageSize": 0,
  "offset": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|paged|boolean|false|none||Returns whether the current{@link Pageable} contains pagination information.|
|unpaged|boolean|false|none||Returns whether the current{@link Pageable} does not contain pagination information.|
|pageNumber|integer|false|none||Returns the page to be returned.|
|pageSize|integer|false|none||Returns the number of items to be returned.|
|offset|integer(int64)|false|none||Returns the offset to be taken according to the underlying page and page size.|
|sort|[[Sort](#schemasort)]|false|none||Returns the sorting parameters.|

<h2 id="tocS_ContinuousImprovementAttachmentDTO">ContinuousImprovementAttachmentDTO</h2>

<a id="schemacontinuousimprovementattachmentdto"></a>
<a id="schema_ContinuousImprovementAttachmentDTO"></a>
<a id="tocScontinuousimprovementattachmentdto"></a>
<a id="tocscontinuousimprovementattachmentdto"></a>

```json
{
  "id": 0,
  "relatedId": 0,
  "relatedType": 0,
  "fileName": "string",
  "filePath": "string",
  "fileType": "string",
  "fileSize": "string",
  "fileDesc": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|relatedId|integer(int64)|true|none||关联ID|
|relatedType|integer|true|none||关联类型：1、经验教训 2、改进措施 3、优化报告|
|fileName|string|true|none||附件名称|
|filePath|string|true|none||附件存储路径或URL|
|fileType|string|true|none||附件类型|
|fileSize|string|false|none||附件大小|
|fileDesc|string|false|none||附件描述|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|

<h2 id="tocS_ContinuousImprovementReportDTO">ContinuousImprovementReportDTO</h2>

<a id="schemacontinuousimprovementreportdto"></a>
<a id="schema_ContinuousImprovementReportDTO"></a>
<a id="tocScontinuousimprovementreportdto"></a>
<a id="tocscontinuousimprovementreportdto"></a>

```json
{
  "id": 0,
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "reportCycle": "string",
  "startDate": {
    "seconds": 0,
    "nanos": 0
  },
  "endDate": {
    "seconds": 0,
    "nanos": 0
  },
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "summary": "string",
  "improveSummary": "string",
  "improveInvest": "string",
  "improveProcess": "string",
  "improveAchievement": "string",
  "metadata": "string",
  "version": 0,
  "createdBy": "string",
  "createdAt": {
    "seconds": 0,
    "nanos": 0
  },
  "updatedBy": "string",
  "updatedAt": {
    "seconds": 0,
    "nanos": 0
  },
  "isDeleted": true,
  "attachmentList": [
    {
      "id": 0,
      "relatedId": 0,
      "relatedType": 0,
      "fileName": "string",
      "filePath": "string",
      "fileType": "string",
      "fileSize": "string",
      "fileDesc": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||none|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|true|none||报告编号|
|reportType|string|false|none||报告类型：系统升级、培训教育、流程优化、政策更新|
|reportCycle|string|false|none||报告周期|
|startDate|[Instant](#schemainstant)|false|none||覆盖开始时间|
|endDate|[Instant](#schemainstant)|false|none||覆盖结束时间|
|employeeId|integer(int64)|true|none||编制人id|
|orgId|integer(int64)|true|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||保密级别：普通、内部、保密、机密|
|summary|string|false|none||报告摘要|
|improveSummary|string|false|none||改进工作概述|
|improveInvest|string|false|none||改进资源投入|
|improveProcess|string|false|none||改进进度总览|
|improveAchievement|string|false|none||主要成果|
|metadata|string|false|none||补充字段|
|version|integer|false|none||当前版本号|
|createdBy|string|true|none||创建者账号或姓名|
|createdAt|[Instant](#schemainstant)|false|none||创建时间|
|updatedBy|string|false|none||最后修改者|
|updatedAt|[Instant](#schemainstant)|false|none||最后更新时间|
|isDeleted|boolean|false|none||是否删除：0 表示正常 1 表示已删除|
|attachmentList|[[ContinuousImprovementAttachmentDTO](#schemacontinuousimprovementattachmentdto)]|false|none||附件列表|

#### 枚举值

|属性|值|
|---|---|
|reportType|SYSTEM_UPGRADE|
|reportType|TRAINING_EDUCATION|
|reportType|PROCESS_OPTIMIZATION|
|reportType|POLICY_UPDATE|
|level|ORDINARY|
|level|INTERNAL|
|level|CONFIDENTIAL|
|level|MOST_CONFIDENTIAL|

<h2 id="tocS_ResponseEntityPageContinuousImprovementReportDTO">ResponseEntityPageContinuousImprovementReportDTO</h2>

<a id="schemaresponseentitypagecontinuousimprovementreportdto"></a>
<a id="schema_ResponseEntityPageContinuousImprovementReportDTO"></a>
<a id="tocSresponseentitypagecontinuousimprovementreportdto"></a>
<a id="tocsresponseentitypagecontinuousimprovementreportdto"></a>

```json
{
  "content": [
    {
      "id": 0,
      "tenantId": 0,
      "title": "string",
      "reportCode": "string",
      "reportType": "SYSTEM_UPGRADE",
      "reportCycle": "string",
      "startDate": {
        "seconds": 0,
        "nanos": 0
      },
      "endDate": {
        "seconds": 0,
        "nanos": 0
      },
      "employeeId": 0,
      "orgId": 0,
      "establishDate": "string",
      "level": "ORDINARY",
      "summary": "string",
      "improveSummary": "string",
      "improveInvest": "string",
      "improveProcess": "string",
      "improveAchievement": "string",
      "metadata": "string",
      "version": 0,
      "createdBy": "string",
      "createdAt": {
        "seconds": 0,
        "nanos": 0
      },
      "updatedBy": "string",
      "updatedAt": {
        "seconds": 0,
        "nanos": 0
      },
      "isDeleted": true,
      "attachmentList": [
        {
          "id": 0,
          "relatedId": 0,
          "relatedType": 0,
          "fileName": "string",
          "filePath": "string",
          "fileType": "string",
          "fileSize": "string",
          "fileDesc": "string",
          "metadata": "string",
          "version": 0,
          "createdBy": "string",
          "createdAt": {
            "seconds": null,
            "nanos": null
          },
          "updatedBy": "string",
          "updatedAt": {
            "seconds": null,
            "nanos": null
          },
          "isDeleted": true
        }
      ]
    }
  ],
  "pageable": {
    "paged": true,
    "unpaged": true,
    "pageNumber": 0,
    "pageSize": 0,
    "offset": 0,
    "sort": [
      {
        "direction": "ASC",
        "property": "string",
        "ignoreCase": true,
        "nullHandling": "NATIVE",
        "ascending": true,
        "descending": true
      }
    ]
  },
  "total": 0,
  "empty": true,
  "number": 0,
  "size": 0,
  "numberOfElements": 0,
  "sort": [
    {
      "direction": "ASC",
      "property": "string",
      "ignoreCase": true,
      "nullHandling": "NATIVE",
      "ascending": true,
      "descending": true
    }
  ],
  "first": true,
  "last": true,
  "totalPages": 0,
  "totalElements": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|content|[[ContinuousImprovementReportDTO](#schemacontinuousimprovementreportdto)]|false|none||none|
|pageable|[Pageable](#schemapageable)|false|none||none|
|total|integer(int64)|false|none||none|
|empty|boolean|false|none||none|
|number|integer|false|none||none|
|size|integer|false|none||none|
|numberOfElements|integer|false|none||none|
|sort|[[Sort](#schemasort)]|false|none||none|
|first|boolean|false|none||none|
|last|boolean|false|none||none|
|totalPages|integer|false|none||none|
|totalElements|integer(int64)|false|none||none|

<h2 id="tocS_ContinuousImprovementReportReq">ContinuousImprovementReportReq</h2>

<a id="schemacontinuousimprovementreportreq"></a>
<a id="schema_ContinuousImprovementReportReq"></a>
<a id="tocScontinuousimprovementreportreq"></a>
<a id="tocscontinuousimprovementreportreq"></a>

```json
{
  "tenantId": 0,
  "title": "string",
  "reportCode": "string",
  "reportType": "SYSTEM_UPGRADE",
  "employeeId": 0,
  "orgId": 0,
  "establishDate": "string",
  "level": "ORDINARY",
  "createdAtStart": "string",
  "createdAtEnd": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|tenantId|integer(int64)|true|none||租户ID，标识不同公司的数据隔离|
|title|string|false|none||报告标题|
|reportCode|string|false|none||报告编号|
|reportType|string|false|none||报告类型：系统升级、培训教育、流程优化、政策更新|
|employeeId|integer(int64)|false|none||编制人id|
|orgId|integer(int64)|false|none||编制部门id|
|establishDate|string|false|none||编制日期|
|level|string|false|none||保密级别：普通、内部、保密、机密|
|createdAtStart|string|false|none||创建开始时间|
|createdAtEnd|string|false|none||创建结束时间|

#### 枚举值

|属性|值|
|---|---|
|reportType|SYSTEM_UPGRADE|
|reportType|TRAINING_EDUCATION|
|reportType|PROCESS_OPTIMIZATION|
|reportType|POLICY_UPDATE|
|level|ORDINARY|
|level|INTERNAL|
|level|CONFIDENTIAL|
|level|MOST_CONFIDENTIAL|

