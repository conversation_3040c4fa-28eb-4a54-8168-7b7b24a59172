<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { View } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import correctionsApi, { type ResponsibilityInvestigateCorrectionDTO } from '@/api/problemTask/corrections'
import DepartPerson from '@/components/departPerson/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import dictApi from '@/api/modules/system/dict'

const router = useRouter()
const route = useRoute()

// 表单数据结构，对应API文档中的ResponsibilityInvestigateCorrectionDTO
const formData = reactive<ResponsibilityInvestigateCorrectionDTO>({
  id: undefined,
  name: '', // 整改项目名称
  correctionCode: '', // 整改编号
  correctionType: null, // 整改类型：COMPLIANCE_RISK, OPERATIONAL_RISK, SYSTEM_RISK
  level: 'LOW', // 优先级：LOW, MIDDLE, HIGH
  investigateId: undefined, // 违规调查id
  dutyEmployeeId: undefined, // 责任人id
  dutyEmployeeOrgId: undefined, // 责任部门id
  collaborationEmployeeId: undefined, // 协作人id
  supervisionEmployeeId: undefined, // 监督人id
  startDate: '', // 开始日期
  finishDate: '', // 完成日期
  status: 'NO_START', // 状态：NO_START, PROGRESSING, FINISHED, PAUSED, CANCELED
  correctionBackground: '', // 整改背景
  correctionRequire: '', // 整改要求
  correctionRange: '', // 整改范围
  correctionScheme: '', // 整改方案
  metadata: '', // 补充字段
  version: 0,
  createdBy: '',
  createdAt: null,
  updatedBy: '',
  updatedAt: null,
  isDeleted: false,
})
const correctionTypeOptions = ref([])
async function fetchCorrectionCode() {
  try {
    const response = await dictApi.getCode('RESPONSIBILITY_CORRECTION')
    if (response) {
      formData.correctionCode = response
    }
  }
  catch (error) {
  }
}

async function fetchCorrectionTypeOptions() {
  try {
    const response = await dictApi.dictAll(24)
    if (response) {
      correctionTypeOptions.value = response
    }
  }
  catch (error) {
  }
}

// 是否为编辑模式
const isEdit = ref(false)
const loading = ref(false)
const formRef = ref()

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入整改项目名称', trigger: 'blur' }],
  correctionCode: [{ required: true, message: '请输入整改编号', trigger: 'blur' }],
  correctionType: [{ required: true, message: '请选择整改类型', trigger: 'change' }],
  level: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  investigateId: [{ required: true, message: '请输入违规调查ID', trigger: 'blur' }],
  dutyEmployeeId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  dutyEmployeeOrgId: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
  collaborationEmployeeId: [{ required: false, message: '请选择协作人', trigger: 'change' }],
  supervisionEmployeeId: [{ required: false, message: '请选择监督人', trigger: 'change' }],
  finishDate: [{ required: true, message: '请选择计划完成日期', trigger: 'change' }],
}

// 获取详情数据
function getDetail() {
  if (!route.query.id) {
    return
  }

  loading.value = true
  correctionsApi.getCorrectionDetail(route.query.id as string)
    .then((response) => {
      Object.assign(formData, response)
    })
    .catch((error) => {
      ElMessage.error('获取详情失败')
      console.error(error)
    })
    .finally(() => {
      loading.value = false
    })
}

// 保存数据
function handleSave() {
  if (!formRef.value) {
    return
  }

  formRef.value.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error('请填写必填字段')
      return
    }

    loading.value = true

    const apiCall = isEdit.value
      ? correctionsApi.updateCorrection((formData.id || 0).toString(), formData)
      : correctionsApi.createCorrection(formData)

    apiCall
      .then(() => {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        router.back()
      })
      .catch((error) => {
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
        console.error(error)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

// 取消操作
function handleCancel() {
  ElMessageBox.confirm('确定要取消吗？未保存的数据将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.back()
  })
}

// 预览功能
function handlePreview() {
  // TODO: 实现预览功能
  ElMessage.info('预览功能开发中')
}

function _viewCaseDetail(_id: number) {
  // 查看案例详情逻辑
  // TODO: 实现查看案例详情功能
}

// 初始化
onMounted(() => {
  fetchCorrectionTypeOptions()
  if (route.query.id) {
    isEdit.value = true
    getDetail()
  }
  else {
    fetchCorrectionCode()
  }
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增整改项目
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap" @click="handleCancel">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap" @click="handlePreview">
              <View class="mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <el-row :gutter="20" class="">
        <el-col class="m-auto" :span="18">
          <!-- 基本信息区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                基本信息
              </div>
            </template>
            <el-form ref="formRef" :model="formData" :rules="formRules" label-position="right" label-width="120px">
              <div class="grid grid-cols-2 gap-6">
                <el-form-item label="整改项目名称" prop="name" required>
                  <el-input v-model="formData.name" placeholder="请输入整改项目名称" />
                </el-form-item>
                <el-form-item label="整改编号" prop="correctionCode">
                  <el-input v-model="formData.correctionCode" placeholder="系统自动生成" readonly />
                </el-form-item>
                <el-form-item label="整改类型" prop="correctionType" required>
                  <el-select v-model="formData.correctionType" placeholder="请选择整改类型" class="w-full">
                    <el-option v-for="item in correctionTypeOptions" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item label="优先级" prop="level" required>
                  <el-radio-group v-model="formData.level">
                    <el-radio-button label="HIGH" class="text-red-500">
                      高
                    </el-radio-button>
                    <el-radio-button label="MIDDLE" class="text-orange-500">
                      中
                    </el-radio-button>
                    <el-radio-button label="LOW" class="text-green-500">
                      低
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="违规调查ID" prop="investigateId" required>
                  <el-input-number v-model="formData.investigateId" placeholder="请输入违规调查ID" class="w-full" />
                </el-form-item>
                <el-form-item label="责任部门" prop="dutyEmployeeOrgId" required>
                  <DepartmentTreeSelect v-model="formData.dutyEmployeeOrgId" placeholder="请选择责任部门" />
                </el-form-item>
                <el-form-item label="负责人" prop="dutyEmployeeId" required>
                  <DepartPerson v-model="formData.dutyEmployeeId" placeholder="请选择负责人" />
                </el-form-item>
                <el-form-item label="协作人" prop="collaborationEmployeeId">
                  <DepartPerson v-model="formData.collaborationEmployeeId" placeholder="请选择协作人" />
                </el-form-item>
                <el-form-item label="监督人" prop="supervisionEmployeeId">
                  <DepartPerson v-model="formData.supervisionEmployeeId" placeholder="请选择监督人" />
                </el-form-item>
                <el-form-item label="开始日期" prop="startDate">
                  <el-date-picker v-model="formData.startDate" type="date" placeholder="选择开始日期" class="w-full" />
                </el-form-item>
                <el-form-item label="计划完成日期" prop="finishDate" required>
                  <el-date-picker v-model="formData.finishDate" type="date" placeholder="选择计划完成日期" class="w-full" />
                </el-form-item>
                <el-form-item label="整改状态" prop="status">
                  <el-radio-group v-model="formData.status">
                    <el-radio-button label="NO_START">
                      未开始
                    </el-radio-button>
                    <el-radio-button label="PROGRESSING">
                      进行中
                    </el-radio-button>
                    <el-radio-button label="FINISHED">
                      已完成
                    </el-radio-button>
                    <el-radio-button label="PAUSED">
                      已暂停
                    </el-radio-button>
                    <el-radio-button label="CANCELED">
                      已取消
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </el-card>

          <!-- 整改内容区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                整改内容
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="整改背景" prop="correctionBackground">
                <el-input v-model="formData.correctionBackground" type="textarea" :rows="4" placeholder="请输入整改背景和原因" />
              </el-form-item>
              <el-form-item label="整改要求" prop="correctionRequire">
                <el-input v-model="formData.correctionRequire" type="textarea" :rows="4" placeholder="请输入整改的具体要求和目标" />
              </el-form-item>
              <el-form-item label="整改范围" prop="correctionRange">
                <el-input v-model="formData.correctionRange" type="textarea" :rows="4" placeholder="请输入整改的范围和边界" />
              </el-form-item>
              <el-form-item label="涉及系统/流程">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加系统/流程
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="type" label="类型" width="120" />
                    <el-table-column prop="name" label="名称" />
                    <el-table-column prop="version" label="版本号" width="120" />
                    <el-table-column prop="module" label="涉及模块/环节" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 整改方案区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                整改方案
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="整改方案概述" prop="correctionScheme">
                <el-input v-model="formData.correctionScheme" type="textarea" :rows="4" placeholder="请输入整改的总体方案和思路" />
              </el-form-item>
              <el-form-item label="补充字段" prop="metadata">
                <el-input v-model="formData.metadata" type="textarea" :rows="3" placeholder="请输入补充信息" />
              </el-form-item>
              <el-form-item label="整改措施">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加措施
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="name" label="措施名称" />
                    <el-table-column prop="owner" label="负责人" width="120" />
                    <el-table-column prop="startDate" label="开始日期" width="120" />
                    <el-table-column prop="endDate" label="计划完成日期" width="120" />
                    <el-table-column prop="desc" label="措施描述" />
                    <el-table-column label="操作" width="120">
                      <template #default>
                        <div class="flex space-x-1">
                          <el-button text size="small">
                            上移
                          </el-button>
                          <el-button text size="small">
                            下移
                          </el-button>
                          <el-button text type="danger" size="small">
                            删除
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="整改步骤">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加步骤
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="step" label="步骤序号" width="80" />
                    <el-table-column prop="name" label="步骤名称" />
                    <el-table-column prop="startDate" label="计划开始日期" width="120" />
                    <el-table-column prop="endDate" label="计划完成日期" width="120" />
                    <el-table-column prop="owner" label="负责人" width="120" />
                    <el-table-column prop="desc" label="步骤描述" />
                    <el-table-column label="操作" width="120">
                      <template #default>
                        <div class="flex space-x-1">
                          <el-button text size="small">
                            上移
                          </el-button>
                          <el-button text size="small">
                            下移
                          </el-button>
                          <el-button text type="danger" size="small">
                            删除
                          </el-button>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="预期效果">
                <el-input type="textarea" :rows="4" placeholder="请输入整改预期达到的效果和成果" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 资源需求区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                资源需求
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="人力资源">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加人力需求
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="role" label="角色" />
                    <el-table-column prop="count" label="人数" width="80" />
                    <el-table-column prop="work" label="工作内容" />
                    <el-table-column prop="time" label="时间投入" width="120" />
                    <el-table-column prop="department" label="来源部门" width="120" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="物料资源">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加物料需求
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="name" label="资源名称" />
                    <el-table-column prop="count" label="数量" width="80" />
                    <el-table-column prop="spec" label="规格" />
                    <el-table-column prop="usage" label="用途" />
                    <el-table-column prop="source" label="获取方式" width="120" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="费用预算">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加费用项目
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="item" label="费用项目" />
                    <el-table-column prop="amount" label="预算金额" width="120" />
                    <el-table-column prop="usage" label="用途说明" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="系统资源">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加系统资源
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="name" label="系统名称" />
                    <el-table-column prop="usage" label="用途" />
                    <el-table-column prop="permission" label="权限要求" />
                    <el-table-column prop="source" label="获取方式" width="120" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 验收标准区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                验收标准
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="验收指标">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加验收指标
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="name" label="指标名称" />
                    <el-table-column prop="type" label="指标类型" width="120">
                      <template #default="{ row }">
                        <el-tag :type="row.type === '定量' ? 'success' : 'warning'">
                          {{ row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="target" label="目标值" width="120" />
                    <el-table-column prop="method" label="验收方法" />
                    <el-table-column prop="owner" label="验收负责人" width="120" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="验收材料">
                <el-input type="textarea" :rows="4" placeholder="请输入验收需要提供的材料和文档" />
              </el-form-item>
              <el-form-item label="验收流程">
                <el-input type="textarea" :rows="4" placeholder="请输入验收的流程和步骤" />
              </el-form-item>
              <el-form-item label="验收时间">
                <el-date-picker type="date" placeholder="选择计划验收时间" class="w-full" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 风险管理区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                风险管理
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="风险点识别">
                <div class="w-full">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm text-gray-500">已添加 0 项</span>
                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap">
                      添加风险点
                    </el-button>
                  </div>
                  <el-table :data="[]" border class="w-full">
                    <el-table-column prop="desc" label="风险描述" />
                    <el-table-column prop="level" label="风险等级" width="120">
                      <template #default="{ row }">
                        <el-tag :type="row.level === '高' ? 'danger' : row.level === '中' ? 'warning' : 'success'">
                          {{ row.level }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="scope" label="影响范围" />
                    <el-table-column prop="measure" label="应对措施" />
                    <el-table-column prop="owner" label="责任人" width="120" />
                    <el-table-column label="操作" width="80">
                      <template #default>
                        <el-button text type="danger" size="small">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-form-item>
              <el-form-item label="应急预案">
                <el-input type="textarea" :rows="4" placeholder="请输入整改过程中可能遇到的问题和应对方案" />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 通知设置区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                通知设置
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="通知对象">
                <el-select placeholder="请选择通知对象" multiple class="w-full">
                  <el-option label="张伟" value="1" />
                  <el-option label="李娜" value="2" />
                  <el-option label="王强" value="3" />
                  <el-option label="赵敏" value="4" />
                  <el-option label="郑阳" value="5" />
                  <el-option label="孙丽" value="6" />
                  <el-option label="钱勇" value="7" />
                </el-select>
              </el-form-item>
              <el-form-item label="通知方式">
                <el-checkbox-group>
                  <el-checkbox label="系统消息" />
                  <el-checkbox label="电子邮件" />
                  <el-checkbox label="短信" />
                  <el-checkbox label="其他" />
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="通知事件">
                <el-checkbox-group>
                  <el-checkbox label="整改开始" />
                  <el-checkbox label="进度更新" />
                  <el-checkbox label="整改完成" />
                  <el-checkbox label="状态变更" />
                  <el-checkbox label="验收结果" />
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="自动提醒">
                <div class="flex items-center space-x-4">
                  <el-switch />
                  <span class="text-sm text-gray-500">提前</span>
                  <el-input-number :min="1" :max="30" size="small" />
                  <span class="text-sm text-gray-500">天提醒</span>
                  <el-select placeholder="选择频率" size="small" style="width: 120px">
                    <el-option label="每周" value="weekly" />
                    <el-option label="每三天" value="3days" />
                    <el-option label="每天" value="daily" />
                  </el-select>
                </div>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 权限设置区块 -->
          <el-card shadow="hover" class="mb-8">
            <template #header>
              <div class="f-16 fw-600">
                权限设置
              </div>
            </template>
            <el-form label-position="right" label-width="120px">
              <el-form-item label="查看权限">
                <el-radio-group>
                  <el-radio label="全公司" />
                  <el-radio label="责任部门" />
                  <el-radio label="整改团队" />
                  <el-radio label="指定人员" />
                </el-radio-group>
                <el-select
                  v-if="false"
                  placeholder="请选择查看人员"
                  multiple
                  class="mt-2 w-full"
                >
                  <el-option label="张伟" value="1" />
                  <el-option label="李娜" value="2" />
                  <el-option label="王强" value="3" />
                  <el-option label="赵敏" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="编辑权限">
                <el-radio-group>
                  <el-radio label="仅负责人" />
                  <el-radio label="整改团队" />
                  <el-radio label="指定人员" />
                </el-radio-group>
                <el-select
                  v-if="false"
                  placeholder="请选择编辑人员"
                  multiple
                  class="mt-2 w-full"
                >
                  <el-option label="张伟" value="1" />
                  <el-option label="李娜" value="2" />
                  <el-option label="王强" value="3" />
                  <el-option label="赵敏" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="进度更新权限">
                <el-radio-group>
                  <el-radio label="仅负责人" />
                  <el-radio label="整改团队" />
                  <el-radio label="协作人" />
                  <el-radio label="指定人员" />
                </el-radio-group>
                <el-select
                  v-if="false"
                  placeholder="请选择更新人员"
                  multiple
                  class="mt-2 w-full"
                >
                  <el-option label="张伟" value="1" />
                  <el-option label="李娜" value="2" />
                  <el-option label="王强" value="3" />
                  <el-option label="赵敏" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="验收权限">
                <el-radio-group>
                  <el-radio label="监督人" />
                  <el-radio label="部门主管" />
                  <el-radio label="指定人员" />
                </el-radio-group>
                <el-select
                  v-if="false"
                  placeholder="请选择验收人员"
                  multiple
                  class="mt-2 w-full"
                >
                  <el-option label="张伟" value="1" />
                  <el-option label="李娜" value="2" />
                  <el-option label="王强" value="3" />
                  <el-option label="赵敏" value="4" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
    </PageMain>
  </div>
</template>

<style scoped>
.el-form-item {
margin-bottom: 24px;
}
.el-form-item__label {
color: #666666;
font-size: 14px;
}
.el-input,
.el-select,
.el-date-editor {
width: 100%;
}
.el-textarea__inner {
min-height: 100px !important;
}
.el-table {
margin-top: 8px;
}
.el-breadcrumb {
font-size: 14px;
}
.el-breadcrumb :deep(.el-breadcrumb__inner) {
font-weight: normal;
}
.el-breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
font-weight: bold;
color: #333;
}
.el-radio-button :deep(.el-radio-button__inner) {
padding: 8px 15px;
}
.el-radio-button.text-red-500 :deep(.el-radio-button__inner) {
color: #f56c6c;
}
.el-radio-button.text-orange-500 :deep(.el-radio-button__inner) {
color: #e6a23c;
}
.el-radio-button.text-green-500 :deep(.el-radio-button__inner) {
color: #67c23a;
}
</style>
