<!-- animation: rotate 5s linear infinite; -->

# 状态
```vue
<template #default="{ row: i }">
  <div v-if="i.status == 1" class="css-new-status">
    <div class="left-box" />
    <div>正常</div>
  </div>
  <div v-else class="css-new-status" :style="{ '--color': '#f56C6C' }">
    <div class="left-box" />
    <div>停用</div>
  </div>
</template>
```
# 表格高度自适应
```vue
<template>
  <div class="absolute-container">
    <PageHeader title="表格高度自适应" />
    <PageMain>
      <ElTable :data="dataList" stripe highlight-current-row border height="100%">
        <ElTableColumn type="index" width="50" />
        <ElTableColumn prop="date" label="日期" width="180" />
        <ElTableColumn prop="name" label="姓名" width="180" />
        <ElTableColumn prop="address" label="地址" />
      </ElTable>
    </PageMain>
  </div>
</template>
```

```vue
<style lang="scss" scoped>
.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  // 让 page-main 的高度自适应
  .page-main {
    flex: 1;
    overflow: auto;

    :deep(.main-container) {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: auto;
    }
  }
}
</style>
```

# 分页
```javascript
function pagChange(e : any) {
  paging.value = Object.assign(paging.value, e)
  getList()
}
```
```vue
<page-compon :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
  @pag-change="pagChange"
/>
```

# 操作
```vue
<el-dropdown split-button trigger="click">
  <div class="flex items-center justify-center">
    <svg-icon name="ep:edit" class="mr2" />操作
  </div>
  <template #dropdown>
    <el-dropdown-menu>
      <el-dropdown-item v-if="i.status === 0">
        <el-button type="primary" text @click="modify('上架',i,)">
          <template #icon>
            <svg-icon name="ep:edit" />
          </template>上架
        </el-button>
      </el-dropdown-item>
      <el-dropdown-item v-else>
        <el-button type="primary" text @click="modify('下架',i)">
          <template #icon>
            <svg-icon name="ep:edit" />
          </template>下架
        </el-button>
      </el-dropdown-item>
      <el-dropdown-item v-if="i.status === 0">
        <el-button type="danger" text :disabled="i.status === 1 ? true : false" @click="modify('删除',i)">
          <template #icon>
            <svg-icon name="ep:delete" />
          </template>删除
        </el-button>
      </el-dropdown-item>
    </el-dropdown-menu>
  </template>
</el-dropdown>
```

<el-tag type="primary">Tag 1</el-tag>
<el-tag type="success">Tag 2</el-tag>
<el-tag type="info">Tag 3</el-tag>
<el-tag type="warning">Tag 4</el-tag>
<el-tag type="danger">Tag 5</el-tag>

# 布局
<el-row :gutter="24">
    <el-col :span="6">
    111
    </el-col>
    <el-col :span="6">
    222
    </el-col>
    <el-col :span="6">
    333
    </el-col>
    <el-col :span="6">
    444
    </el-col>
</el-row>
```vue
const topIndex : any = ref(1)
const topList : any = ref([
  {
    id: 1,
    name: '余额明细',
    value: 1,
    num: 10,
  },
  {
    id: 2,
    name: '使用记录',
    value: 2,
    num: 19,
  },
  {
    id: 3,
    name: '提现记录',
    value: 3,
    num: 5,
  },
  {
    id: 4,
    name: '设置',
    value: 4,
  },
])
<PageHeader>
  <template #title>
    <div class="custom-style">
      <el-segmented v-model="topIndex" :options="topList" class="custom-style">
        <template #default="{ item }">
          <div class="flex flex-col items-center gap-2 p-2">
            <div>
              <span>{{item.name}}</span>
              <span v-if="item.num">({{item.num}})</span>
            </div>
          </div>
        </template>
      </el-segmented>
    </div>
  </template>
</PageHeader>
```

```vue
  页面模型
<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content />
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <!-- 页面内容 -->
      </div>
    </PageMain>
  </div>
</template>
```

```vue
<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              新增处理措施
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />保存并开始处理
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-close mr-1" />取消
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <i class="el-icon-view mr-1" />预览
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18" />
          <el-col :span="6" />
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      background: #F5F7FA;
      height: 55px;
    }
  }
</style>
```

账户信息
13812773190
3edc4rfvP

<EMAIL>
Abcd#1234
