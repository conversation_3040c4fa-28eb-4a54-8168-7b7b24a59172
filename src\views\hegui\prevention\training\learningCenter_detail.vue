<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Bell,
  Calendar,
  CircleCheck,
  Clock,
  DataBoard,
  Document,
  Download,
  Fold,
  Folder,
  FullScreen,
  Headset,
  InfoFilled,
  Platform,
  Refresh,
  Search,
  Trophy,
  User,
  VideoPause,
  VideoPlay } from '@element-plus/icons-vue'
import curriculumApi from '@/api/curriculum/index'
import uploadApi from '@/api/upload'
import useUserStore from '@/store/modules/user'
import dictApi from '@/api/modules/system/dict'

const userStore = useUserStore()

// 学习行为追踪器类
class LearningTracker {
  private userId: string
  private courseId: string
  private chapterId: number
  private sessionStartTime: number
  private lastUpdateTime: number
  private learningInterval: number | null = null
  private sessionId: string
  private getBehaviorValueFn: (behaviorName: string) => string
  private isUnmountedFn: () => boolean

  constructor(userId: string, courseId: string, chapterId: number, getBehaviorValueFn: (behaviorName: string) => string, isUnmountedFn: () => boolean) {
    this.userId = userId
    this.courseId = courseId
    this.chapterId = chapterId
    this.sessionStartTime = Date.now()
    this.lastUpdateTime = Date.now()
    this.sessionId = this.generateSessionId()
    this.getBehaviorValueFn = getBehaviorValueFn
    this.isUnmountedFn = isUnmountedFn
  }

  // 开始学习
  async startLearning() {
    await this.logBehavior(this.getBehaviorValueFn('开始学习'), {
      chapterId: this.chapterId,
      sessionId: this.sessionId,
    })

    // 每30秒记录一次学习时长
    this.learningInterval = window.setInterval(() => {
      this.updateLearningDuration()
    }, 30000)
  }

  // 更新学习时长
  async updateLearningDuration() {
    const now = Date.now()
    const duration = Math.floor((now - this.lastUpdateTime) / 1000)

    await this.logBehavior(this.getBehaviorValueFn('继续学习'), {
      chapterId: this.chapterId,
      sessionDuration: duration,
    }, duration)

    this.lastUpdateTime = now
  }

  // 完成章节
  async completeChapter() {
    if (this.learningInterval) {
      clearInterval(this.learningInterval)
      this.learningInterval = null
    }

    const totalDuration = Math.floor((Date.now() - this.sessionStartTime) / 1000)
    await this.logBehavior(this.getBehaviorValueFn('完成章节'), {
      chapterId: this.chapterId,
      totalSessionDuration: totalDuration,
    }, totalDuration)

    // 注意：updateProgressStatistics接口只在所有课程学习完成后调用，章节完成不需要调用
  }

  // 暂停学习
  async pauseLearning() {
    if (this.learningInterval) {
      clearInterval(this.learningInterval)
      this.learningInterval = null
    }

    const duration = Math.floor((Date.now() - this.lastUpdateTime) / 1000)
    await this.logBehavior(this.getBehaviorValueFn('暂停学习'), {
      chapterId: this.chapterId,
      sessionDuration: duration,
    }, duration)
  }

  // 恢复学习
  async resumeLearning() {
    this.lastUpdateTime = Date.now()
    await this.logBehavior(this.getBehaviorValueFn('恢复学习'), {
      chapterId: this.chapterId,
    })

    // 重新开始定时记录
    this.learningInterval = window.setInterval(() => {
      this.updateLearningDuration()
    }, 30000)
  }

  // 记录学习行为
  private async logBehavior(behaviorType: string, _details: any = {}, durationSeconds = 0) {
    // 检查组件是否已卸载
    if (this.isUnmountedFn()) {
      return
    }

    try {
      const response = await curriculumApi.learning.logBehavior({
        userId: this.userId,
        courseId: Number(this.courseId),
        behaviorType,
        durationSeconds,
      })
      return response
    }
    catch (error) {
      console.error('记录学习行为失败:', error)
    }
  }

  // 更新学习进度
  private async updateProgress() {
    try {
      await curriculumApi.learning.updateProgressStatistics(this.userId, Number(this.courseId))
    }
    catch (error) {
      console.error('更新学习进度失败:', error)
    }
  }

  // 获取设备类型
  private getDeviceType() {
    const userAgent = navigator.userAgent
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      return 'Mobile'
    }
    if (/Tablet/.test(userAgent)) {
      return 'Tablet'
    }
    return 'PC'
  }

  // 生成会话ID
  private generateSessionId() {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // 停止追踪
  stop() {
    if (this.learningInterval) {
      clearInterval(this.learningInterval)
      this.learningInterval = null
    }
  }
}

// 路由实例
const router = useRouter()
const route = useRoute()

// 响应式数据
const activeTab = ref('courseware')
const currentChapter = ref<any>(null)
const currentLesson = ref<any>(null)
const showTestDialog = ref(false)
const loading = ref(false)
const courseDetail = ref<any>({})
const chapters = ref<any[]>([])
const materials = ref<any[]>([])
const videoRef = ref<HTMLVideoElement>()
const isVideoContent = ref(false)
const currentVideoSrc = ref('')
const videoProgress = ref(0)
const videoDuration = ref(0)
const videoCurrentTime = ref(0)
const isPlaying = ref(false)
const playbackRate = ref(1.0)
const lastProgressSaveTime = ref(0) // 上次保存进度的时间
const isPositionSet = ref(false) // 标记播放位置是否已设置

// 学习追踪相关
const learningTracker = ref<LearningTracker | null>(null)
const isLearningActive = ref(false) // 是否正在学习追踪
const behaviorDict = ref<any[]>([]) // 学习行为字典
const isUnmounted = ref(false) // 组件是否已卸载

// 计算属性
const courseId = computed(() => route.query.id as string)
const isVideoLesson = computed(() => {
  return currentLesson.value?.chapterType === 'VIDEO' || currentLesson.value?.chapterType === 'video'
})

// 格式化时间
function formatTime(seconds: number) {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 获取学习行为字典
async function getBehaviorDict() {
  try {
    const response = await dictApi.dictAll(91)
    if (response && response.data) {
      behaviorDict.value = response.data
    }
  }
  catch (error) {
    console.error('获取学习行为字典失败:', error)
  }
}

// 根据行为名称获取字典值
function getBehaviorValue(behaviorName: string) {
  const behaviorMap: Record<string, string> = {
    开始学习: 'START_LEARNING',
    暂停学习: 'PAUSE_LEARNING',
    继续学习: 'RESUME_LEARNING',
    完成章节: 'COMPLETE_COURSE',
  }
  return behaviorMap[behaviorName] || behaviorName
}

// 获取课程详情
async function getCourseDetail() {
  try {
    loading.value = true
    const response = await curriculumApi.learning.getCourseDetail(courseId.value)
    if (response) {
      courseDetail.value = response
    }
  }
  catch (error) {
    console.error('获取课程详情失败:', error)
    ElMessage.error('获取课程详情失败')
  }
  finally {
    loading.value = false
  }
}

// 获取课程章节和课时
async function getCourseChapters() {
  try {
    const response = await curriculumApi.learning.getCourseContentByCourseId(courseId.value)
    if (response) {
      chapters.value = response || []

      // 确保每个lesson都有completionStatus属性
      chapters.value.forEach((chapter) => {
        if (chapter.lessons) {
          chapter.lessons.forEach((lesson: any) => {
            // 如果没有completionStatus属性，初始化为'未完成'
            if (!lesson.completionStatus) {
              lesson.completionStatus = '未完成'
            }
            // 同步completed属性以保持兼容性
            lesson.completed = lesson.completionStatus === '已完成'
          })
        }
      })

      // 设置默认选中第一个课时
      if (chapters.value.length > 0) {
        const firstChapter = chapters.value[0]
        if (firstChapter.lessons && firstChapter.lessons.length > 0) {
          selectLesson(firstChapter, firstChapter.lessons[0])
        }
      }
    }
  }
  catch (error) {
    console.error('获取课程章节失败:', error)
    ElMessage.error('获取课程章节失败')
  }
}

// 选择课时
async function selectLesson(chapter: any, lesson: any) {
  // 如果切换到不同的课时，先保存当前进度并停止学习追踪
  if (currentLesson.value && currentLesson.value.id !== lesson.id) {
    await saveProgress()
    await stopLearningTracking()
  }

  currentChapter.value = chapter
  currentLesson.value = lesson
  isPositionSet.value = false // 重置播放位置设置标志

  // 启动新课时的学习追踪
  await startLearningTracking()

  // 如果是视频课时，加载视频
  if (isVideoLesson.value && lesson.contentUrl) {
    await loadVideo(lesson.contentUrl)
  }
  else {
    isVideoContent.value = false
    currentVideoSrc.value = ''
  }
}

// 加载视频
async function loadVideo(videoUrl: string) {
  try {
    // 先获取真实的视频文件地址
    const realVideoUrl = await uploadApi.getFileUrl(videoUrl)
    currentVideoSrc.value = realVideoUrl
    isVideoContent.value = true
    // 移除这里的setVideoPosition调用，让视频事件处理器来设置
  }
  catch (error) {
    console.error('加载视频失败:', error)
    ElMessage.error('视频加载失败')
    isVideoContent.value = false
  }
}

// 设置视频播放位置
function setVideoPosition() {
  // 如果已经设置过播放位置，则不再重复设置
  if (isPositionSet.value) {
    // eslint-disable-next-line no-console
    console.log('播放位置已设置，跳过重复设置')
    return
  }

  if (videoRef.value && currentLesson.value?.playbackPosition) {
    // 支持多种格式的播放位置
    let positionInSeconds = 0

    if (typeof currentLesson.value.playbackPosition === 'number') {
      // 如果是毫秒，转换为秒
      positionInSeconds = currentLesson.value.playbackPosition > 1000
        ? currentLesson.value.playbackPosition / 1000
        : currentLesson.value.playbackPosition
    }
    else if (typeof currentLesson.value.playbackPosition === 'string') {
      positionInSeconds = Number.parseFloat(currentLesson.value.playbackPosition)
    }

    if (positionInSeconds > 0) {
      // eslint-disable-next-line no-console
      console.log('设置播放位置:', positionInSeconds, '秒')
      attemptSetPosition(positionInSeconds, 0)
    }
    else {
      // 如果没有有效的播放位置，也标记为已设置
      isPositionSet.value = true
    }
  }
  else {
    // 如果没有播放位置信息，标记为已设置
    isPositionSet.value = true
  }
}

// 尝试设置播放位置（带重试机制）
function attemptSetPosition(positionInSeconds: number, attemptCount: number) {
  const maxAttempts = 3

  if (attemptCount >= maxAttempts) {
    // eslint-disable-next-line no-console
    console.log('设置播放位置达到最大尝试次数，停止尝试')
    isPositionSet.value = true // 标记为已设置，避免继续尝试
    return
  }

  const delay = attemptCount === 0 ? 500 : 1000 * attemptCount

  setTimeout(() => {
    if (videoRef.value) {
      try {
        videoRef.value.currentTime = positionInSeconds
        // eslint-disable-next-line no-console
        console.log(`播放位置设置成功 (尝试${attemptCount + 1}次):`, positionInSeconds)

        // 验证设置是否成功
        setTimeout(() => {
          if (videoRef.value) {
            const currentTime = videoRef.value.currentTime || 0
            const timeDiff = Math.abs(currentTime - positionInSeconds)

            if (timeDiff > 5) { // 如果时间差超过5秒，认为设置失败
              // eslint-disable-next-line no-console
              console.log('播放位置验证失败，重新尝试设置')
              attemptSetPosition(positionInSeconds, attemptCount + 1)
            }
            else {
              // eslint-disable-next-line no-console
              console.log('播放位置验证成功:', currentTime)
              isPositionSet.value = true // 标记为已成功设置
            }
          }
        }, 1000)
      }
      catch (error) {
        console.error(`设置播放位置失败 (尝试${attemptCount + 1}次):`, error)
        attemptSetPosition(positionInSeconds, attemptCount + 1)
      }
    }
    else {
      // eslint-disable-next-line no-console
      console.log('视频播放器不存在，重新尝试')
      attemptSetPosition(positionInSeconds, attemptCount + 1)
    }
  }, delay)
}

// 视频播放/暂停
function togglePlay() {
  if (videoRef.value) {
    if (isPlaying.value) {
      videoRef.value.pause()
    }
    else {
      videoRef.value.play()
    }
  }
}

// 视频时间更新
function onVideoTimeUpdate() {
  if (isUnmounted.value || !videoRef.value) {
    return
  }

  videoCurrentTime.value = videoRef.value.currentTime
  videoProgress.value = (videoRef.value.currentTime / videoRef.value.duration) * 100

  // 每10秒保存一次进度，避免频繁请求
  const now = Date.now()
  if (now - lastProgressSaveTime.value > 10000) {
    lastProgressSaveTime.value = now
    saveProgress()
  }
}

// 视频加载完成
function onVideoLoadedMetadata() {
  if (videoRef.value) {
    videoDuration.value = videoRef.value.duration
    // 只在播放位置未设置时才设置播放位置
    if (!isPositionSet.value) {
      setTimeout(() => {
        setVideoPosition()
      }, 100)
    }
  }
}

// 视频可以播放时
function onVideoCanPlay() {
  // 只在播放位置未设置时才设置播放位置
  if (!isPositionSet.value) {
    setTimeout(() => {
      setVideoPosition()
    }, 200)
  }
}

// 视频播放状态改变
function onVideoPlay() {
  isPlaying.value = true
  // 恢复学习追踪
  if (learningTracker.value && !isLearningActive.value) {
    learningTracker.value.resumeLearning()
    isLearningActive.value = true
  }
}

function onVideoPause() {
  isPlaying.value = false
  // 暂停学习追踪
  if (learningTracker.value && isLearningActive.value) {
    learningTracker.value.pauseLearning()
    isLearningActive.value = false
  }
}

// 视频结束
function onVideoEnded() {
  isPlaying.value = false
  // 保存完成进度
  saveProgress()

  ElMessageBox.confirm('视频播放完成，是否标记为已完成？', '提示', {
    confirmButtonText: '标记完成',
    cancelButtonText: '重新播放',
    type: 'success',
  }).then(() => {
    markLessonComplete()
  }).catch(() => {
    replayVideo()
  })
}

// 重新播放视频
function replayVideo() {
  if (videoRef.value) {
    videoRef.value.currentTime = 0
    videoRef.value.play()
  }
}

// 检查所有课程是否完成
function checkAllLessonsCompleted() {
  const allLessons = getAllLessons()
  return allLessons.length > 0 && allLessons.every(lesson => lesson.completionStatus === '已完成')
}

// 标记课时完成
async function markLessonComplete() {
  // 调用updateProgress接口标记为已完成
  try {
    await curriculumApi.learning.updateProgress({
      userId: userStore.userId,
      courseId: courseId.value,
      chapterId: currentLesson.value.id,
      playbackPosition: videoRef.value ? Math.floor(videoRef.value.currentTime) : 0,
      completionStatus: '已完成',
    })
  }
  catch (error) {
    console.error('更新完成状态失败:', error)
    return
  }

  // 更新本地课时状态
  if (currentLesson.value) {
    currentLesson.value.completed = true
    currentLesson.value.completionStatus = '已完成'

    // 同时更新chapters数组中对应的lesson对象
    chapters.value.forEach((chapter) => {
      if (chapter.lessons) {
        chapter.lessons.forEach((lesson: any) => {
          if (lesson.id === currentLesson.value.id) {
            lesson.completed = true
            lesson.completionStatus = '已完成'
          }
        })
      }
    })
  }

  // 检查是否所有课程都已完成
  const allCompleted = checkAllLessonsCompleted()

  if (allCompleted) {
    // 只有当所有课程都完成时，才记录"完成章节"行为并停止学习追踪
    if (learningTracker.value) {
      await learningTracker.value.completeChapter()
      isLearningActive.value = false
    }
    ElMessage.success('恭喜！您已完成所有课程学习')
  }
  else {
    // 单个课时完成时，暂停当前学习追踪但不记录"完成章节"行为
    if (learningTracker.value && isLearningActive.value) {
      await learningTracker.value.pauseLearning()
      isLearningActive.value = false
    }
    ElMessage.success('课时已标记为完成')
  }
}

// 调整播放速度
function changePlaybackRate(rate: number) {
  playbackRate.value = rate
  if (videoRef.value) {
    videoRef.value.playbackRate = rate
  }
}

// 进度条点击
function onProgressClick(event: MouseEvent) {
  if (videoRef.value && event.target) {
    const progressBar = event.target as HTMLElement
    const rect = progressBar.getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * videoRef.value.duration
    videoRef.value.currentTime = newTime
  }
}

// 保存学习进度
async function saveProgress() {
  if (isUnmounted.value || !currentLesson.value || !videoRef.value || !isVideoLesson.value) {
    return
  }

  try {
    const currentTime = Math.floor(videoRef.value.currentTime) // 保存为秒数
    if (currentTime > 0) {
      await curriculumApi.learning.updateProgress({
        userId: userStore.userId,
        courseId: courseId.value,
        chapterId: currentLesson.value.id,
        playbackPosition: currentTime,
      })

      // 更新本地进度
      currentLesson.value.playbackPosition = currentTime

      // 保存进度不需要记录学习行为
    }
  }
  catch (error) {
    console.error('保存进度失败:', error)
  }
}

// 下载文件
async function downloadFile(lesson: any) {
  if (lesson.contentUrl) {
    try {
      // 先获取真实的文件地址
      const realUrl = await uploadApi.getFileUrl(lesson.contentUrl)
      const link = document.createElement('a')
      link.href = realUrl
      link.download = lesson.chapterTitle || '课件'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('开始下载')
    }
    catch (error) {
      console.error('获取文件地址失败:', error)
      ElMessage.error('文件下载失败，请稍后重试')
    }
  }
  else {
    ElMessage.warning('文件链接不存在')
  }
}

// 获取课时图标
function getLessonIcon(lesson: any) {
  if (lesson.chapterType === 'VIDEO' || lesson.chapterType === 'video') {
    return VideoPlay
  }
  else {
    return Document
  }
}

// 获取课时类型文本
function getContentTypeText(type: string) {
  const typeMap: Record<string, string> = {
    VIDEO: '视频',
    video: '视频',
    DOCUMENT: '文档',
    document: '文档',
    PDF: 'PDF',
    PPT: 'PPT',
    DOC: 'DOC',
  }
  return typeMap[type] || '内容'
}

// 跳转到测试页面
function startTest() {
  router.push({
    name: '/training/learningCenter/test',
    query: { id: route.query.id },
  })
}

// 上一节/下一节
function goToPrevLesson() {
  // 实现上一节逻辑
  const allLessons = getAllLessons()
  const currentIndex = getCurrentLessonIndex(allLessons)
  if (currentIndex > 0) {
    const prevLesson = allLessons[currentIndex - 1]
    const chapter = findChapterByLesson(prevLesson)
    if (chapter) {
      selectLesson(chapter, prevLesson)
    }
  }
}

function goToNextLesson() {
  // 实现下一节逻辑
  const allLessons = getAllLessons()
  const currentIndex = getCurrentLessonIndex(allLessons)
  if (currentIndex < allLessons.length - 1) {
    const nextLesson = allLessons[currentIndex + 1]
    const chapter = findChapterByLesson(nextLesson)
    if (chapter) {
      selectLesson(chapter, nextLesson)
    }
  }
}

// 获取所有课时
function getAllLessons() {
  const allLessons: any[] = []
  chapters.value.forEach((chapter) => {
    if (chapter.lessons) {
      chapter.lessons.forEach((lesson: any) => {
        allLessons.push(lesson)
      })
    }
  })
  return allLessons
}

// 获取当前课时索引
function getCurrentLessonIndex(allLessons: any[]) {
  return allLessons.findIndex(lesson => lesson.id === currentLesson.value?.id)
}

// 根据课时查找章节
function findChapterByLesson(lesson: any) {
  return chapters.value.find(chapter =>
    chapter.lessons && chapter.lessons.some((l: any) => l.id === lesson.id),
  )
}

// 计算学习进度
const learningProgress = computed(() => {
  const allLessons = getAllLessons()
  const completedLessons = allLessons.filter(lesson => lesson.completionStatus === '已完成')
  return allLessons.length > 0 ? Math.round((completedLessons.length / allLessons.length) * 100) : 0
})

// 组件挂载时获取数据
onMounted(() => {
  // 获取学习行为字典
  getBehaviorDict()

  if (courseId.value) {
    getCourseDetail()
    getCourseChapters()
  }
})

// 启动学习追踪
async function startLearningTracking() {
  if (currentLesson.value && userStore.userId && courseId.value) {
    try {
      // 停止之前的追踪器
      if (learningTracker.value) {
        learningTracker.value.stop()
      }

      // 创建新的学习追踪器
      learningTracker.value = new LearningTracker(
        userStore.userId,
        courseId.value,
        currentLesson.value.id,
        getBehaviorValue,
        () => isUnmounted.value,
      )

      // 开始学习追踪
      await learningTracker.value.startLearning()
      isLearningActive.value = true
    }
    catch (error) {
      console.error('启动学习追踪失败:', error)
    }
  }
}

// 停止学习追踪
async function stopLearningTracking() {
  if (learningTracker.value) {
    try {
      // 如果正在学习中，先暂停
      if (isLearningActive.value) {
        await learningTracker.value.pauseLearning()
      }

      // 停止追踪器
      learningTracker.value.stop()
      learningTracker.value = null
      isLearningActive.value = false
    }
    catch (error) {
      console.error('停止学习追踪失败:', error)
    }
  }
}

// 组件卸载时保存进度
onUnmounted(async () => {
  // 设置卸载标志，防止后续异步操作继续执行
  isUnmounted.value = true

  // 页面卸载时保存当前进度并停止学习追踪
  try {
    await saveProgress()
    await stopLearningTracking()
  }
  catch (error) {
    console.error('页面卸载时清理失败:', error)
  }
})
</script>

<template>
  <div v-loading="loading" class="min-h-screen flex flex-col bg-gray-50">
    <div class="flex flex-1">
      <!-- 主内容区 -->
      <div class="flex-1 p-6">
        <!-- 页面标题 -->
        <h1 class="mb-6 text-2xl text-gray-800 font-bold">
          {{ courseDetail.courseName || '课程学习' }}
        </h1>

        <!-- 课程信息卡片 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <div class="flex flex-wrap gap-4 text-sm">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Clock />
              </el-icon>
              <span>课程时长：{{ courseDetail.durationMinutes || 0 }} 分钟</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <User />
              </el-icon>
              <span>课程讲师：{{ courseDetail.instructor || '未知' }}</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Calendar />
              </el-icon>
              <span>学习期限：{{ courseDetail.deadline || '无限制' }}</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Trophy />
              </el-icon>
              <span>考核要求：{{ courseDetail.examRequirement || '需完成课后测试，通过分数 80 分' }}</span>
            </div>
          </div>
        </div>

        <!-- 课程内容区 -->
        <div class="flex gap-6">
          <!-- 左侧章节导航 -->
          <div class="w-64 rounded-lg bg-white p-4 shadow-sm">
            <div class="mb-4 flex items-center justify-between">
              <h3 class="text-gray-800 font-medium">
                课程章节
              </h3>
              <el-icon class="cursor-pointer">
                <Fold />
              </el-icon>
            </div>
            <div class="space-y-1">
              <template v-for="chapter in chapters" :key="chapter.id">
                <!-- 章节标题 -->
                <div class="rounded bg-gray-100 px-3 py-2 text-gray-700 font-medium">
                  <div class="flex items-center">
                    <el-icon class="mr-2">
                      <Folder />
                    </el-icon>
                    <span>{{ chapter.chapterTitle }}</span>
                    <span class="ml-auto text-xs text-gray-500">{{ chapter.durationMinutes || 0 }} 分钟</span>
                  </div>
                </div>
                <!-- 课时列表 -->
                <div v-if="chapter.lessons">
                  <div
                    v-for="lesson in chapter.lessons"
                    :key="lesson.id"
                    class="cursor-pointer rounded px-3 py-2 hover:bg-gray-50"
                    :class="{ 'bg-blue-50 text-blue-600': currentLesson?.id === lesson.id }"
                    @click="selectLesson(chapter, lesson)"
                  >
                    <div class="flex items-center">
                      <el-icon class="mr-2" :class="lesson.completionStatus === '已完成' ? 'text-green-500' : 'text-gray-400'">
                        <component :is="lesson.completionStatus === '已完成' ? CircleCheck : getLessonIcon(lesson)" />
                      </el-icon>
                      <span class="flex-1">{{ lesson.chapterTitle }}</span>
                      <span class="ml-auto text-xs text-gray-500">{{ lesson.durationMinutes || 0 }} 分钟</span>
                    </div>
                    <!-- 学习进度条 -->
                    <div v-if="lesson.playbackPosition && lesson.playbackPosition > 0" class="ml-6 mt-1">
                      <div class="h-1 w-full overflow-hidden rounded-full bg-gray-200">
                        <div
                          class="h-full bg-blue-500 transition-all duration-300"
                          :style="{ width: `${(lesson.playbackPosition / (lesson.durationMinutes * 60 * 1000)) * 100}%` }"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 右侧内容区 -->
          <div class="flex-1 overflow-hidden rounded-lg bg-white shadow-sm">
            <!-- 内容标签页 -->
            <el-tabs v-model="activeTab" class="px-4 pt-4">
              <el-tab-pane label="课件" name="courseware">
                <!-- 视频播放器 -->
                <div v-if="isVideoContent && currentVideoSrc" class="mb-4">
                  <div class="aspect-w-16 aspect-h-9 overflow-hidden rounded-lg bg-black">
                    <video
                      ref="videoRef"
                      :src="currentVideoSrc"
                      class="h-full w-full"
                      controls
                      @timeupdate="onVideoTimeUpdate"
                      @loadedmetadata="onVideoLoadedMetadata"
                      @canplay="onVideoCanPlay"
                      @play="onVideoPlay"
                      @pause="onVideoPause"
                      @ended="onVideoEnded"
                    />
                  </div>

                  <!-- 视频控制栏 -->
                  <div class="mt-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <button
                        class="h-8 w-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200"
                        @click="togglePlay"
                      >
                        <el-icon>
                          <component :is="isPlaying ? VideoPause : VideoPlay" />
                        </el-icon>
                      </button>
                      <div
                        class="h-1 w-64 cursor-pointer overflow-hidden rounded-full bg-gray-200"
                        @click="onProgressClick"
                      >
                        <div
                          class="h-full bg-blue-500 transition-all duration-300"
                          :style="{ width: `${videoProgress}%` }"
                        />
                      </div>
                      <span class="text-sm text-gray-500">
                        {{ formatTime(videoCurrentTime) }} / {{ formatTime(videoDuration) }}
                      </span>
                    </div>
                    <div class="flex items-center space-x-4">
                      <button
                        v-for="rate in [0.5, 1.0, 1.5, 2.0]"
                        :key="rate"
                        class="text-sm text-gray-600 hover:text-gray-800"
                        :class="{ 'text-blue-600 font-medium': playbackRate === rate }"
                        @click="changePlaybackRate(rate)"
                      >
                        {{ rate }}x
                      </button>
                      <button class="text-sm text-gray-600 hover:text-gray-800">
                        <el-icon><Headset /></el-icon>
                      </button>
                      <button class="text-sm text-gray-600 hover:text-gray-800">
                        <el-icon><FullScreen /></el-icon>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 非视频内容提示 -->
                <div v-else-if="currentLesson && !isVideoLesson" class="mb-4 py-12 text-center">
                  <div class="flex flex-col items-center space-y-4">
                    <el-icon size="48" class="text-gray-400">
                      <Document />
                    </el-icon>
                    <p class="text-gray-600">
                      {{ getContentTypeText(currentLesson.chapterType) }}内容
                    </p>
                    <p class="text-sm text-gray-500">
                      该内容为文档类型，请点击下载查看
                    </p>
                    <el-button
                      type="primary"
                      :icon="Download"
                      @click="downloadFile(currentLesson)"
                    >
                      下载课件
                    </el-button>
                  </div>
                </div>

                <!-- 默认提示 -->
                <div v-else class="mb-4 py-12 text-center">
                  <div class="flex flex-col items-center space-y-4">
                    <el-icon size="48" class="text-gray-400">
                      <VideoPlay />
                    </el-icon>
                    <p class="text-gray-600">
                      请选择课时开始学习
                    </p>
                  </div>
                </div>

                <!-- 课程内容文字 -->
                <div v-if="currentLesson" class="mb-6 pb-12 space-y-4">
                  <h2 class="text-xl font-bold">
                    {{ currentLesson.chapterTitle }}
                  </h2>
                  <div v-if="currentLesson.description" class="text-gray-700" v-html="currentLesson.description" />
                  <div v-else class="text-gray-700">
                    <p>{{ currentLesson.chapterTitle }} 的详细内容将在这里显示。</p>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="资料" name="materials">
                <el-table :data="materials" class="w-full">
                  <el-table-column prop="type" label="类型" width="120">
                    <template #default="{ row }">
                      <el-icon v-if="row.type === 'PDF'">
                        <Document />
                      </el-icon>
                      <el-icon v-else-if="row.type === 'PPT'">
                        <DataBoard />
                      </el-icon>
                      <el-icon v-else>
                        <Folder />
                      </el-icon>
                      <span class="ml-2">{{ row.type }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="资料名称" />
                  <el-table-column prop="size" label="大小" width="120" />
                  <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                      <el-button type="primary" text size="small" @click="downloadFile(row)">
                        下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部控制栏 -->
    <footer class="fixed bottom-0 left-55 right-0 border-t border-gray-200 bg-white p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">
            进度：{{ getAllLessons().filter(l => l.completed).length }}/{{ getAllLessons().length }} 课时
          </span>
          <el-progress :percentage="learningProgress" :show-text="false" class="w-40" />
        </div>
        <div class="flex items-center space-x-4">
          <el-button :icon="ArrowLeft" @click="goToPrevLesson">
            上一节
          </el-button>
          <el-button type="primary" :icon="CircleCheck" @click="showTestDialog = true">
            标记完成
          </el-button>
          <el-button @click="goToNextLesson">
            下一节
          </el-button>
        </div>
      </div>
    </footer>

    <!-- 测试弹窗 -->
    <el-dialog v-model="showTestDialog" title="课程测试" width="500px">
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <div class="space-y-6">
          <div class="mb-4 text-lg text-gray-800 font-medium">
            考核测试说明
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Document />
              </el-icon>
              <span class="text-gray-700">测试题数：20 题</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Clock />
              </el-icon>
              <span class="text-gray-700">测试时长：30 分钟</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <CircleCheck />
              </el-icon>
              <span class="text-gray-700">通过分数：80 分</span>
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2 text-blue-500">
                <Refresh />
              </el-icon>
              <span class="text-gray-700">考试次数：1 次机会</span>
            </div>
          </div>
          <div class="mt-4 border-t border-gray-200 pt-4">
            <div class="mb-2 text-sm text-gray-600 font-medium">
              考试规则说明：
            </div>
            <ul class="text-sm text-gray-500 space-y-2">
              <li>1. 测试开始后不能暂停</li>
              <li>2. 提交后立即显示成绩</li>
              <li>3. 未通过可重新测试</li>
              <li>4. 测试过程中禁止切换页面</li>
              <li>5. 系统将自动记录测试时间</li>
            </ul>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-4">
          <el-button class="!rounded-button whitespace-nowrap" @click="showTestDialog = false">
            稍后测试
          </el-button>
          <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="startTest">
            开始测试
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.aspect-w-16 {
  position: relative;
  width: 100%;
}

.aspect-h-9 {
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.aspect-w-16 video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 视频控制栏样式 */
.video-controls {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
}

/* 进度条样式 */
.progress-bar {
  position: relative;
  cursor: pointer;
}

.progress-bar:hover .progress-thumb {
  opacity: 1;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: #409eff;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s;
}

/* 章节导航样式 */
.chapter-nav {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.chapter-nav::-webkit-scrollbar {
  width: 4px;
}

.chapter-nav::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.chapter-nav::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.chapter-nav::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 课时项样式 */
.lesson-item {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.lesson-item:hover {
  background-color: #f5f7fa;
  border-left-color: #409eff;
}

.lesson-item.active {
  background-color: #ecf5ff;
  border-left-color: #409eff;
  color: #409eff;
}

.lesson-item.completed {
  background-color: #f0f9ff;
}

/* 学习进度条样式 */
.lesson-progress {
  height: 2px;
  background: #e4e7ed;
  border-radius: 1px;
  overflow: hidden;
  margin-top: 4px;
  margin-left: 24px;
}

.lesson-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  transition: width 0.3s ease;
}

/* 内容区域样式 */
.content-area {
  min-height: calc(100vh - 300px);
}

/* 视频播放器容器 */
.video-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 空状态样式 */
.empty-state {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 60px 20px;
}

/* 资料表格样式 */
.materials-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 底部控制栏样式 */
.bottom-controls {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .course-layout {
    flex-direction: column;
  }

  .chapter-nav {
    width: 100%;
    max-height: 200px;
    margin-bottom: 16px;
  }

  .content-area {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .video-controls {
    flex-direction: column;
    gap: 12px;
  }

  .progress-container {
    order: -1;
    width: 100%;
  }

  .playback-rates {
    justify-content: center;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态样式 */
.loading-overlay {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

/* 按钮样式增强 */
.control-button {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.control-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.control-button:active {
  transform: translateY(0);
}

/* 标签页样式 */
.el-tabs {
  --el-tabs-header-height: 48px;
}

.el-tabs__nav-wrap::after {
  height: 1px;
  background: #e4e7ed;
}

.el-tabs__item {
  font-weight: 500;
  color: #606266;
}

.el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

/* 表格样式增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background: #fafafa;
  font-weight: 600;
  color: #303133;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table tr:hover td {
  background: #f5f7fa;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.el-dialog__header {
  background: #fafafa;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.el-dialog__body {
  padding: 24px;
}

.el-dialog__footer {
  padding: 16px 24px 24px;
  border-top: 1px solid #e4e7ed;
}

/* 进度条样式增强 */
.el-progress-bar__outer {
  border-radius: 10px;
  overflow: hidden;
}

.el-progress-bar__inner {
  border-radius: 10px;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
}

/* 图标样式 */
.el-icon {
  transition: color 0.2s ease;
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}
</style>
