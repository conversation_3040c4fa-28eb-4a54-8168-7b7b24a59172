<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import curriculumApi from '@/api/curriculum'

// 类型定义
interface LearningStatistics {
  completedCourseCount: number
  inProgressCourseCount: number
  certificateCount: number
  totalLearningDuration: number
  totalLearningDurationFormatted: string
}

interface LearningTask {
  courseId: number
  courseName: string
  coverImageUrl: string
  deadline: {
    seconds: number
    nanos: number
  }
  deadlineFormatted: string
  learningProgress: number
  learningProgressFormatted: string
  trainingPlanId: number
  trainingPlanName: string
  isCompleted: boolean
  taskStatus: string
}

interface RecentLearningCourse {
  courseId: number
  courseName: string
  coverImageUrl: string
  lastLearningTime: {
    seconds: number
    nanos: number
  }
  lastLearningTimeFormatted: string
  learningProgress: number
  learningProgressFormatted: string
  isCompleted: boolean
  courseDuration: number
  learnedDuration: number
  learningStatus: string
}

// 路由实例
const router = useRouter()
const loading = ref(false)

// 学习统计数据
const statistics = ref<LearningStatistics>({
  completedCourseCount: 0,
  inProgressCourseCount: 0,
  certificateCount: 0,
  totalLearningDuration: 0,
  totalLearningDurationFormatted: '0分钟',
})

// 学习任务列表
const learningTasks = ref<LearningTask[]>([])
const taskCurrentPage = ref(1)
const taskPageSize = ref(4)
const taskTotal = computed(() => learningTasks.value.length)

// 最近学习课程列表
const recentLearningCourses = ref<RecentLearningCourse[]>([])
const recordCurrentPage = ref(1)
const recordPageSize = ref(4)
const recordTotal = computed(() => recentLearningCourses.value.length)

// 分页计算
const paginatedTasks = computed(() => {
  const start = (taskCurrentPage.value - 1) * taskPageSize.value
  const end = start + taskPageSize.value
  return learningTasks.value.slice(start, end)
})

const paginatedRecords = computed(() => {
  const start = (recordCurrentPage.value - 1) * recordPageSize.value
  const end = start + recordPageSize.value
  return recentLearningCourses.value.slice(start, end)
})

// 获取学习中心数据
async function fetchLearningCenterData() {
  try {
    loading.value = true
    const response = await curriculumApi.learningCenter.getLearningCenterData()
    const data = response

    // 更新统计数据
    statistics.value = data.statistics || statistics.value

    // 更新学习任务
    learningTasks.value = data.learningTasks || []

    // 更新最近学习课程
    recentLearningCourses.value = data.recentLearningCourses || []
  }
  catch (error) {
    console.error('获取学习中心数据失败:', error)
    ElMessage.error('获取学习中心数据失败')
  }
  finally {
    loading.value = false
  }
}

// 学习详情
function goDetail(item: LearningTask | RecentLearningCourse) {
  if (!item || !item.courseId) {
    console.warn('无效的数据对象或缺少courseId')
    return
  }
  router.push({
    path: '/training/learningCenter/detail',
    query: { id: item.courseId },
  })
}

// 判断截止日期是否临近
function isDeadlineApproaching(deadline: { seconds: number, nanos: number } | null | undefined) {
  if (!deadline) {
    return false
  }
  const today = new Date()
  const deadlineDate = new Date(deadline.seconds * 1000)
  const diffTime = deadlineDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 3
}

// 获取任务状态颜色
function getTaskStatusType(taskStatus: string) {
  switch (taskStatus) {
    case 'PENDING':
      return 'info'
    case 'IN_PROGRESS':
      return 'warning'
    case 'COMPLETED':
      return 'success'
    case 'OVERDUE':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取学习状态颜色
function getLearningStatusType(learningStatus: string) {
  switch (learningStatus) {
    case 'NOT_STARTED':
      return 'info'
    case 'IN_PROGRESS':
      return 'warning'
    case 'COMPLETED':
      return 'success'
    default:
      return 'info'
  }
}

// 任务分页处理
function handleTaskPageChange(page: number) {
  taskCurrentPage.value = page
}

// 记录分页处理
function handleRecordPageChange(page: number) {
  recordCurrentPage.value = page
}

// 组件挂载时获取数据
onMounted(() => {
  fetchLearningCenterData()
})
</script>

<template>
  <div class="learning-center">
    <el-card v-loading="loading" class="mb-4">
      <!-- 个人学习统计区 -->
      <template #header>
        <div class="card-header">
          <span class="text-lg font-bold">学习统计</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="已完成课程" :value="statistics.completedCourseCount" suffix="门" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="学习进行中" :value="statistics.inProgressCourseCount" suffix="门" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="获得证书" :value="statistics.certificateCount" suffix="个" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="学习总时长" :value="statistics.totalLearningDurationFormatted" />
        </el-col>
      </el-row>
    </el-card>

    <!-- 学习任务区 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span class="text-lg font-bold">我的学习任务</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col v-for="task in paginatedTasks" :key="task.courseId" :span="6">
          <el-card class="task-card" shadow="hover">
            <div class="task-content">
              <div class="task-header">
                <h3 class="task-title">
                  {{ task.courseName }}
                </h3>
                <el-tag
                  :type="isDeadlineApproaching(task.deadline) ? 'danger' : 'info'"
                  size="small"
                >
                  {{ task.deadlineFormatted }}
                </el-tag>
              </div>

              <div class="task-progress">
                <el-progress
                  :percentage="task.learningProgress"
                  :status="task.isCompleted ? 'success' : undefined"
                  :stroke-width="8"
                />
                <div class="progress-text">
                  {{ task.learningProgressFormatted }}
                </div>
              </div>

              <div class="task-footer">
                <el-tag :type="getTaskStatusType(task.taskStatus)" size="small">
                  {{
                    task.taskStatus === 'PENDING'
                      ? '待开始'
                      : task.taskStatus === 'IN_PROGRESS'
                        ? '进行中'
                        : task.taskStatus === 'COMPLETED'
                          ? '已完成'
                          : task.taskStatus === 'OVERDUE'
                            ? '已逾期'
                            : task.taskStatus
                  }}
                </el-tag>
                <el-button
                  type="primary"
                  size="small"
                  @click="goDetail(task)"
                >
                  {{ task.isCompleted ? '查看详情' : '继续学习' }}
                </el-button>
              </div>

              <div v-if="task.trainingPlanName" class="training-plan">
                <el-text type="info" size="small">
                  培训计划：{{ task.trainingPlanName }}
                </el-text>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div v-if="taskTotal > taskPageSize" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="taskCurrentPage"
          :page-size="taskPageSize"
          :total="taskTotal"
          layout="prev, pager, next"
          @current-change="handleTaskPageChange"
        />
      </div>
    </el-card>

    <!-- 最近学习区 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="text-lg font-bold">最近学习</span>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col v-for="course in paginatedRecords" :key="course.courseId" :span="6">
          <el-card class="course-card" shadow="hover">
            <div class="course-content">
              <div class="course-header">
                <h3 class="course-title">
                  {{ course.courseName }}
                </h3>
                <el-text type="info" size="small">
                  {{ course.lastLearningTimeFormatted }}
                </el-text>
              </div>

              <div class="course-progress">
                <el-progress
                  :percentage="course.learningProgress"
                  :status="course.isCompleted ? 'success' : undefined"
                  :stroke-width="8"
                />
                <div class="progress-text">
                  {{ course.learningProgressFormatted }}
                </div>
              </div>

              <div class="course-duration">
                <el-text size="small">
                  已学习 {{ course.learnedDuration }} 分钟 / 总计 {{ course.courseDuration }} 分钟
                </el-text>
              </div>

              <div class="course-footer">
                <el-tag :type="getLearningStatusType(course.learningStatus)" size="small">
                  {{
                    course.learningStatus === 'NOT_STARTED'
                      ? '未开始'
                      : course.learningStatus === 'IN_PROGRESS'
                        ? '学习中'
                        : course.learningStatus === 'COMPLETED'
                          ? '已完成'
                          : course.learningStatus
                  }}
                </el-tag>
                <el-button
                  type="primary"
                  size="small"
                  @click="goDetail(course)"
                >
                  {{ course.isCompleted ? '查看详情' : '继续学习' }}
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div v-if="recordTotal > recordPageSize" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="recordCurrentPage"
          :page-size="recordPageSize"
          :total="recordTotal"
          layout="prev, pager, next"
          @current-change="handleRecordPageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.learning-center {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-card,
.course-card {
  height: 100%;
  margin-bottom: 20px;
}

.task-content,
.course-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-header,
.course-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title,
.course-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  flex: 1;
  margin-right: 8px;
  line-height: 1.4;
}

.task-progress,
.course-progress {
  margin-bottom: 16px;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.task-footer,
.course-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.training-plan {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.course-duration {
  margin-bottom: 16px;
  text-align: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-statistic__content) {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

:deep(.el-statistic__title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
</style>
