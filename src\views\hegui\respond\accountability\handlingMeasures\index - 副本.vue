<script lang="ts" setup>
import { ref } from 'vue'
import {
  Check as El<PERSON>con<PERSON>he<PERSON>,
  Clock as ElIconClock,
  DataAnalysis as ElIconDataAnalysis,
  Document as ElIconDocument,
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Upload as ElIconUpload,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'

const activeTab = ref('measures')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(24)

const tableData = [
  {
    id: 'CL-2023-001',
    title: '违规销售行为处理',
    type: '财务违规',
    severity: '严重',
    responsible: '销售部/李经理',
    status: '处理中',
    startDate: '2023-05-10',
    endDate: '-',
  },
  {
    id: 'CL-2023-002',
    title: '数据泄露整改',
    type: '信息安全',
    severity: '中等',
    responsible: 'IT部/王主管',
    status: '已完成',
    startDate: '2023-05-15',
    endDate: '2023-06-01',
  },
  {
    id: 'CL-2023-003',
    title: '客户投诉处理',
    type: '合规风险',
    severity: '中等',
    responsible: '客服部/张专员',
    status: '处理中',
    startDate: '2023-05-20',
    endDate: '-',
  },
  {
    id: 'CL-2023-004',
    title: '财务报告违规',
    type: '财务违规',
    severity: '严重',
    responsible: '财务部/赵总监',
    status: '待处理',
    startDate: '2023-06-05',
    endDate: '-',
  },
  {
    id: 'CL-2023-005',
    title: '员工行为规范培训',
    type: '合规培训',
    severity: '一般',
    responsible: '人事部/刘经理',
    status: '已完成',
    startDate: '2023-05-25',
    endDate: '2023-06-10',
  },
]

function getSeverityTagType(severity: string) {
  switch (severity) {
    case '严重': return 'danger'
    case '中等': return 'warning'
    default: return 'info'
  }
}

function getStatusTagType(status: string) {
  switch (status) {
    case '处理中': return 'primary'
    case '已完成': return 'success'
    case '待处理': return 'info'
    default: return 'info'
  }
}

function handleCurrentChange(val: number) {
  currentPage.value = val
}

const format = () => '78%'
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              责任追究处理
            </h1>
            <!-- <el-tag type="warning" class="ml-4">进行中</el-tag> -->
          </div>
          <div class="flex space-x-2">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconPlus />
              </el-icon>新增处理措施
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconUpload />
              </el-icon>批量导入
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDownload />
              </el-icon>导出
            </el-button>
            <el-button plain class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconDataAnalysis />
              </el-icon>统计分析
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card class="">
          <div class="grid grid-cols-3 gap-4">
            <div>
              <div class="mb-3 flex items-center justify-between">
                <h3 class="text-gray-800 font-medium">
                  最近访问
                </h3>
                <el-link type="primary" :underline="false">
                  查看全部
                </el-link>
              </div>
              <div class="space-y-3">
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <el-avatar :size="32" class="mr-3" :style="{ backgroundColor: '#e6f7ff', color: '#1890ff' }">
                    <el-icon><ElIconDocument /></el-icon>
                  </el-avatar>
                  <div>
                    <div class="text-sm font-medium">
                      违规销售行为处理
                    </div>
                    <div class="text-xs text-gray-500">
                      财务违规 · 处理中
                    </div>
                  </div>
                </div>
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <el-avatar :size="32" class="mr-3" :style="{ backgroundColor: '#f6ffed', color: '#52c41a' }">
                    <el-icon><ElIconCheck /></el-icon>
                  </el-avatar>
                  <div>
                    <div class="text-sm font-medium">
                      数据泄露整改
                    </div>
                    <div class="text-xs text-gray-500">
                      信息安全 · 已完成
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="mb-3 flex items-center justify-between">
                <h3 class="text-gray-800 font-medium">
                  待办事项
                </h3>
                <el-link type="primary" :underline="false">
                  查看全部
                </el-link>
              </div>
              <div class="space-y-3">
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <el-avatar :size="32" class="mr-3" :style="{ backgroundColor: '#fffbe6', color: '#faad14' }">
                    <el-icon><ElIconWarning /></el-icon>
                  </el-avatar>
                  <div>
                    <div class="text-sm font-medium">
                      客户投诉处理确认
                    </div>
                    <div class="text-xs text-gray-500">
                      截止: 2023-06-15 · 高优先级
                    </div>
                  </div>
                </div>
                <div class="flex cursor-pointer items-center rounded p-2 hover:bg-gray-50">
                  <el-avatar :size="32" class="mr-3" :style="{ backgroundColor: '#fff2f0', color: '#ff4d4f' }">
                    <el-icon><ElIconClock /></el-icon>
                  </el-avatar>
                  <div>
                    <div class="text-sm font-medium">
                      合规培训跟进
                    </div>
                    <div class="text-xs text-gray-500">
                      截止: 2023-06-20 · 中优先级
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
        <!--  -->
        <el-card class="mt-20">
          <div class="flex items-center space-x-4">
            <div class="flex-1">
              <div class="grid grid-cols-5 gap-4">
                <div>
                  <el-select placeholder="处理状态" clearable>
                    <el-option label="全部状态" value="" />
                    <el-option label="待处理" value="pending" />
                    <el-option label="处理中" value="processing" />
                    <el-option label="已完成" value="completed" />
                  </el-select>
                </div>
                <div>
                  <el-select placeholder="处理类型" clearable>
                    <el-option label="全部类型" value="" />
                    <el-option label="财务违规" value="finance" />
                    <el-option label="信息安全" value="security" />
                    <el-option label="合规培训" value="training" />
                  </el-select>
                </div>
                <div>
                  <el-select placeholder="责任部门" clearable>
                    <el-option label="全部部门" value="" />
                    <el-option label="财务部" value="finance" />
                    <el-option label="合规部" value="compliance" />
                    <el-option label="IT部" value="it" />
                  </el-select>
                </div>
                <div>
                  <el-date-picker
                    type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    style="width: 100%;"
                  />
                </div>
              </div>
            </div>
            <div class="w-64">
              <el-input placeholder="搜索处理措施..." prefix-icon="el-icon-search" />
            </div>
            <el-link type="primary" :underline="false">
              高级筛选
            </el-link>
          </div>
        </el-card>
        <!-- 处理措施列表 -->
        <el-card class="mt-20">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <el-button size="small" type="text">
                  表格视图
                </el-button>
                <el-button size="small" type="text">
                  卡片视图
                </el-button>
              </div>
              <div class="text-sm text-gray-500">
                共 24 条记录
              </div>
            </div>
          </template>

          <el-table :data="tableData" style="width: 100%;">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="处理编号" width="120" />
            <el-table-column prop="title" label="处理标题" />
            <el-table-column prop="type" label="处理类型" width="120" />
            <el-table-column prop="severity" label="严重程度" width="120">
              <template #default="{ row }">
                <el-tag :type="getSeverityTagType(row.severity)" size="small">
                  {{ row.severity }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="responsible" label="责任人/部门" width="150" />
            <el-table-column prop="status" label="处理状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startDate" label="提出日期" width="120" />
            <el-table-column prop="endDate" label="完成日期" width="120" />
            <el-table-column label="操作" width="150">
              <template #default>
                <el-button type="text" size="small">
                  查看
                </el-button>
                <el-button type="text" size="small">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="mt-4 flex items-center justify-between">
            <el-pagination
              :current-page="currentPage" :page-size="pageSize" :total="total"
              layout="prev, pager, next, jumper" @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .box-card {
    margin-bottom: 20px;
  }
</style>
