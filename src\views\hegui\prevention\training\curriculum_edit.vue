<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowRight, Bottom, Check, Close, Delete, Edit, Plus, Search, Top, Upload, View,
} from '@element-plus/icons-vue'
import trainingCurriculum from '@/api/complianceApi/prevention/trainingCurriculum'
import UploadMbb from '@/components/uploadMbb/index.vue'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import ImageUpload from '@/components/ImageUpload/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const isEdit = ref(false)

// 课程表单数据
const courseForm = ref({
  id: null,
  courseCode: '',
  courseName: '',
  courseType: '',
  trainingTheme: '',
  applicableRole: [],
  instructor: '',
  coverImageUrl: '',
  producer: '',
  status: 'draft',
  durationMinutes: 0,
  courseOverview: '',
  learningObjective: '',
  prerequisites: '',
  courseChapters: [],
  courseAttachments: [],
  // playbackSetting: {
  //   autoPlay: false,
  //   rememberPlayback: false,
  //   continuousPlayback: false,
  //   defaultClarity: '',
  // },
  // interactiveFeature: {
  //   commentEnabled: false,
  //   likeEnabled: false,
  //   shareEnabled: false,
  // },
})

// 章节数据和当前选中的章节
const chapterData = ref([])
const selectedChapter = ref(null)
const currentChapterForm = ref({
  id: null,
  chapterTitle: '',
  chapterDescription: '',
  chapterOrder: 1,
  chapterType: 'DOCUMENT',
  durationMinutes: 0,
  isRequired: true,
  status: 'PUBLISHED',
  parentChapterId: null,
  contentUrl: '',
})
const tempIdCounter = ref(1000) // 临时ID计数器

// 将树形章节数据平铺为数组
function flattenChapters(chapters, parentId = null) {
  const result = []
  chapters.forEach((chapter) => {
    const flatChapter = {
      ...chapter,
      parentChapterId: parentId,
    }
    if (chapter.children) {
      delete flatChapter.children
    }
    // 如果是新增的子章节（有parentChapterId且临时ID），则不传入id字段
    if (typeof flatChapter.id === 'number' && flatChapter.id >= 1000 && parentId !== null) {
      delete flatChapter.id
    }
    result.push(flatChapter)
    if (chapter.children && chapter.children.length > 0) {
      result.push(...flattenChapters(chapter.children, chapter.id))
    }
  })
  return result
}

// 将后端返回的章节数据转换为前端树形结构
function buildChapterTreeFromBackend(chapters) {
  if (!chapters || !Array.isArray(chapters)) {
    return []
  }

  return chapters.map((chapter) => {
    const treeNode = {
      ...chapter,
      children: [],
    }

    // 如果有子章节（后端使用 subChapters 字段）
    if (chapter.subChapters && Array.isArray(chapter.subChapters)) {
      treeNode.children = chapter.subChapters.map(subChapter => ({
        ...subChapter,
        children: [],
      }))
    }

    return treeNode
  })
}

// 保存课程
async function saveCourse() {
  try {
    loading.value = true
    // 清理学习资料数据，移除files字段
    const cleanedAttachments = courseForm.value.courseAttachments.map((attachment) => {
      const { files: _files, ...cleanAttachment } = attachment
      return cleanAttachment
    })
    const params = {
      ...courseForm.value,
      applicableRole: Array.isArray(courseForm.value.applicableRole)
        ? courseForm.value.applicableRole.join(',')
        : courseForm.value.applicableRole,
      courseChapters: flattenChapters(chapterData.value),
      courseAttachments: cleanedAttachments,
    }

    let res
    if (isEdit.value && courseForm.value.id) {
      res = await trainingCurriculum.courseInfo({}, params, 'update')
    }
    else {
      res = await trainingCurriculum.courseInfo({}, params, 'create')
    }

    if (res) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/training/curriculum')
    }
  }
  catch (error) {
    console.error('保存课程失败', error)
    ElMessage.error('保存失败')
  }
  finally {
    loading.value = false
  }
}

// 保存并新增
async function saveAndNew() {
  try {
    await saveCourse()
    // 重置表单
    courseForm.value = {
      id: null,
      courseName: '',
      courseCode: '',
      courseType: '',
      durationMinutes: 0,
      applicableRole: [],
      status: 'draft',
      trainingTheme: '',
      instructor: '',
      coverImageUrl: '',
      producer: '',
      courseOverview: '',
      learningObjective: '',
      prerequisites: '',
      courseChapters: [],
      courseAttachments: [],
      // playbackSetting: {
      //   autoPlay: false,
      //   rememberPlayback: false,
      //   continuousPlayback: false,
      //   defaultClarity: '',
      // },
      // interactiveFeature: {
      //   commentEnabled: false,
      //   likeEnabled: false,
      //   shareEnabled: false,
      // },
    }
    chapterData.value = []
    selectedChapter.value = null
    tempIdCounter.value = 1000
    isEdit.value = false
  }
  catch (error) {
    console.error('保存并新增失败', error)
  }
}

// 取消
function cancel() {
  router.push('/training/curriculum')
}

// 获取课程详情（编辑模式）
async function getCourseDetail() {
  if (route.query.id) {
    try {
      isEdit.value = true
      const res = await trainingCurriculum.courseInfo({}, { id: route.query.id }, 'info')
      if (res) {
        courseForm.value = {
          ...res,
          applicableRole: res.applicableRole ? res.applicableRole.split(',') : [],
          courseChapters: res.courseChapters || [],
          courseAttachments: res.courseAttachments || [],
        }
        // 将后端返回的章节数据转换为前端树形结构
        chapterData.value = buildChapterTreeFromBackend(res.courseChapters || [])
      }
    }
    catch (error) {
      console.error('获取课程详情失败', error)
      ElMessage.error('获取课程详情失败')
    }
  }
}

// 文件上传相关处理函数（暂时保留以备后续使用）
// function handlePreview(file) {
//   // 处理文件预览
//   if (file.url) {
//     // 如果是图片，可以使用预览组件
//     if (file.url.match(/\.(jpeg|jpg|gif|png)$/i)) {
//       // 这里可以使用 Element Plus 的图片预览组件
//       // 或者打开新窗口预览
//       window.open(file.url)
//     }
//     else {
//       // 其他类型文件直接打开
//       window.open(file.url)
//     }
//   }
// }

// function handleRemove(file, fileList) {
//   // 处理文件移除
//   console.log(file, fileList)
// }

// function beforeRemove(file, fileList) {
//   return ElMessageBox.confirm(`确定移除 ${file.name}？`)
// }

// function handleExceed(files, fileList) {
//   ElMessage.warning(
//     `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`,
//   )
// }

// 课程封面上传成功回调
function handleCoverImageSuccess(response) {
  // ImageUpload 组件上传成功后的回调
  if (response && response.url) {
    courseForm.value.coverImageUrl = response.url
  }
  else if (response && response.data && response.data.url) {
    courseForm.value.coverImageUrl = response.data.url
  }
  ElMessage.success('封面上传成功')
}

// 课程封面上传成功回调（保留原有函数以兼容）
function _handleAvatarSuccess(response, uploadFile) {
  // 假设上传成功后，服务器返回的数据中包含文件URL
  if (response && response.data && response.data.url) {
    courseForm.value.coverImageUrl = response.data.url
  }
  else {
    // 模拟上传成功，实际开发中应该使用服务器返回的URL
    courseForm.value.coverImageUrl = URL.createObjectURL(uploadFile.raw)
  }
}

// 课程封面上传前的校验
function _beforeAvatarUpload(file) {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG && !isPNG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 章节内容上传成功回调（暂时保留以备后续使用）
// function handleChapterContentUpload(response, uploadFile) {
//   // 假设上传成功后，服务器返回的数据中包含文件URL
//   if (response && response.data && response.data.url) {
//     courseForm.value.courseContent.contentUrl = response.data.url
//   }
//   else {
//     // 模拟上传成功，实际开发中应该使用服务器返回的URL
//     courseForm.value.courseContent.contentUrl = URL.createObjectURL(uploadFile.raw)
//   }
// }

// 章节内容上传成功回调
function handleChapterContentUploadSuccess(fileKey, _fileInfo) {
  if (fileKey && selectedChapter.value) {
    selectedChapter.value.contentUrl = fileKey
    currentChapterForm.value.contentUrl = fileKey
    ElMessage.success('上传成功')
  }
}

// 学习资料上传成功回调
function handleMaterialUploadSuccess(files, row) {
  // 处理学习资料上传成功后的逻辑
  if (files && files.length > 0) {
    const file = files[0]
    row.filePath = file.fileUrl || file.url || file.filePath
    row.fileName = file.fileName || file.name
    row.fileSize = typeof file.fileSize === 'number' ? file.fileSize.toString() : (file.rawSize || '0')
    row.fileType = file.fileType || file.type || 'pdf'
    // 清理files数组，避免数据冗余
    delete row.files
    ElMessage.success('上传成功')
  }
}

// 添加学习资料
function addMaterial() {
  if (!Array.isArray(courseForm.value.courseAttachments)) {
    courseForm.value.courseAttachments = []
  }
  courseForm.value.courseAttachments.push({
    fileName: '',
    fileType: 'pdf',
    filePath: '',
    fileSize: '0',
    description: '',
  })
}

// 删除学习资料
function removeMaterial(index) {
  ElMessageBox.confirm('确定要删除该学习资料吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    courseForm.value.courseAttachments.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除操作
  })
}

// 添加章节
function addChapter() {
  const newChapter = {
    id: tempIdCounter.value++,
    chapterTitle: `第${chapterData.value.length + 1}章 新章节`,
    chapterDescription: '',
    chapterOrder: chapterData.value.length + 1,
    chapterType: 'DOCUMENT',
    durationMinutes: 0,
    isRequired: true,
    status: 'PUBLISHED',
    parentChapterId: null,
    contentUrl: '',
    children: [],
  }
  chapterData.value.push(newChapter)
  selectedChapter.value = newChapter
  updateCurrentChapterForm(newChapter)
}

// 添加子章节（限制只能在父章节下添加，不支持多层嵌套）
function addSubChapter() {
  if (!selectedChapter.value) {
    ElMessage.warning('请先选择一个父章节')
    return
  }
  // 确保只有父章节（没有parentChapterId的章节）才能添加子章节
  const parentChapter = selectedChapter.value
  if (parentChapter.parentChapterId) {
    ElMessage.warning('只能在父章节下添加子章节，子章节不能再添加下级章节')
    return
  }
  if (!parentChapter.children) {
    parentChapter.children = []
  }
  const newSubChapter = {
    id: tempIdCounter.value++,
    chapterTitle: `${parentChapter.chapterOrder}.${parentChapter.children.length + 1} 新子章节`,
    chapterDescription: '',
    chapterOrder: parentChapter.children.length + 1,
    chapterType: 'DOCUMENT',
    durationMinutes: 0,
    isRequired: true,
    status: 'PUBLISHED',
    parentChapterId: parentChapter.id,
    contentUrl: '',
  }
  parentChapter.children.push(newSubChapter)
  selectedChapter.value = newSubChapter
  updateCurrentChapterForm(newSubChapter)
}

// 编辑章节
function editChapter(data, _node) {
  selectedChapter.value = data
  updateCurrentChapterForm(data)
}

// 删除章节
function deleteChapter(node, data) {
  ElMessageBox.confirm('确定要删除该章节吗？删除后不可恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const parent = node.parent
    const children = parent.data.children || parent.data
    const index = children.findIndex(d => d.id === data.id)
    children.splice(index, 1)
    if (selectedChapter.value && selectedChapter.value.id === data.id) {
      selectedChapter.value = null
      resetCurrentChapterForm()
    }
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 更新当前章节表单
function updateCurrentChapterForm(chapter) {
  currentChapterForm.value = {
    id: chapter.id,
    chapterTitle: chapter.chapterTitle || '',
    chapterDescription: chapter.chapterDescription || '',
    chapterOrder: chapter.chapterOrder || 1,
    chapterType: chapter.chapterType || 'DOCUMENT',
    durationMinutes: chapter.durationMinutes || 0,
    isRequired: chapter.isRequired !== undefined ? chapter.isRequired : true,
    status: chapter.status || 'PUBLISHED',
    parentChapterId: chapter.parentChapterId || null,
    contentUrl: chapter.contentUrl || '',
  }
}

// 重置当前章节表单
function resetCurrentChapterForm() {
  currentChapterForm.value = {
    id: null,
    chapterTitle: '',
    chapterDescription: '',
    chapterOrder: 1,
    chapterType: 'DOCUMENT',
    durationMinutes: 0,
    isRequired: true,
    status: 'PUBLISHED',
    parentChapterId: null,
    contentUrl: '',
  }
}

// 保存章节修改
function saveChapterChanges() {
  if (selectedChapter.value) {
    Object.assign(selectedChapter.value, currentChapterForm.value)
    ElMessage.success('章节信息已更新')
  }
}

onMounted(() => {
  getCourseDetail()
})

// 初始化学习资料数据
if (!courseForm.value.courseAttachments || !Array.isArray(courseForm.value.courseAttachments)) {
  courseForm.value.courseAttachments = []
}

// 这些变量暂时保留以备后续功能扩展
// const linkData = ref([
//   { name: '合规官网', url: 'https://example.com', description: '公司合规官方网站' },
// ])

// const passScore = ref(60)

// const questionData = ref([
//   { type: '单选题', difficulty: '中等', score: 5 },
//   { type: '多选题', difficulty: '较难', score: 10 },
// ])

// const lawData = ref([
//   { name: '中华人民共和国公司法', version: '2021版' },
// ])

// const policyData = ref([
//   { name: '员工行为规范', version: '2023版' },
// ])

// const courseData = ref([
//   { name: '合规意识培训', type: '合规意识' },
// ])
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <!-- 标题和操作按钮 -->
        <div class="mb-6 flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            {{ isEdit ? '编辑培训课程' : '新增培训课程' }}
          </h1>
          <div class="flex space-x-3">
            <el-button v-auth="'curriculum/edit/save'" type="primary" class="!rounded-button whitespace-nowrap" :loading="loading" @click="saveCourse">
              <el-icon class="mr-1">
                <Check />
              </el-icon>保存
            </el-button>
            <el-button v-auth="'curriculum/edit/saveAndNew'" class="!rounded-button whitespace-nowrap" :disabled="isEdit" @click="saveAndNew">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>保存并新增
            </el-button>
            <el-button v-auth="'curriculum/edit/cancel'" class="!rounded-button whitespace-nowrap" @click="cancel">
              <el-icon class="mr-1">
                <Close />
              </el-icon>取消
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <div class="mx-5 flex-1 p-6">
        <!-- 表单内容 -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
          <el-form :model="courseForm" label-position="right" label-width="120px" class="course-form">
            <!-- 基本信息 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                基本信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <el-form-item label="课程名称" required>
                  <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" />
                </el-form-item>
                <!-- <el-form-item label="课程编号">
                  <div class="w-full flex items-center">
                    <el-input v-model="courseForm.courseCode" placeholder="系统自动生成" class="flex-1" />
                    <el-button class="!rounded-button ml-2 whitespace-nowrap">
                      自动生成
                    </el-button>
                  </div>
                </el-form-item> -->
                <el-form-item label="课程类型" required>
                  <el-select v-model="courseForm.courseType" placeholder="请选择课程类型" class="w-full">
                    <el-option label="视频课程" value="视频课程" />
                    <el-option label="文档课程" value="文档课程" />
                    <el-option label="混合课程" value="混合课程" />
                    <el-option label="法律法规" value="法律法规" />
                    <el-option label="合规意识" value="合规意识" />
                    <el-option label="业务技能" value="业务技能" />
                    <el-option label="管理能力" value="管理能力" />
                    <el-option label="其他" value="其他" />
                  </el-select>
                </el-form-item>
                <el-form-item label="课程时长" required>
                  <div class="w-full flex items-center">
                    <el-input-number v-model="courseForm.durationMinutes" :min="0" class="flex-1" />
                    （分钟）
                    <!-- <el-select class="ml-2 w-24" value="minute">
                      <el-option label="小时" value="hour" />
                      <el-option label="分钟" value="minute" />
                    </el-select> -->
                  </div>
                </el-form-item>
                <el-form-item label="培训对象" required>
                  <DepartmentTreeSelect
                    v-model="courseForm.applicableRole"
                    placeholder="请选择培训对象"
                    :multiple="true"
                    width="100%"
                  />
                </el-form-item>
                <el-form-item label="课程状态" required>
                  <el-select v-model="courseForm.status" placeholder="请选择课程状态" class="w-full">
                    <el-option label="草稿" value="draft" />
                    <el-option label="已发布" value="published" />
                    <el-option label="已下线" value="offline" />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!-- 课程信息 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                课程信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div class="flex flex-col">
                  <el-form-item label="课程简介" required>
                    <el-input v-model="courseForm.courseOverview" type="textarea" :rows="5" placeholder="请输入课程简介" />
                  </el-form-item>
                  <el-form-item label="适合人群">
                    <el-input v-model="courseForm.prerequisites" type="textarea" :rows="3" placeholder="请输入适合人群描述" />
                  </el-form-item>
                  <el-form-item label="学习目标">
                    <el-input v-model="courseForm.learningObjective" type="textarea" :rows="3" placeholder="请输入学习目标" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="课程封面">
                    <div class="flex flex-col">
                      <ImageUpload
                        v-model:url="courseForm.coverImageUrl"
                        :width="300"
                        :height="169"
                        :size="5"
                        :ext="['jpg', 'jpeg', 'png', 'gif']"
                        placeholder=""
                        :notip="false"
                        dirname="course"
                        @on-success="handleCoverImageSuccess"
                      />
                      <div class="mt-2 text-xs text-gray-500">
                        建议尺寸：16:9比例，最小分辨率800x450px
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <!-- 讲师信息 -->
              <div class="grid grid-cols-3 gap-6">
                <el-form-item label="讲师姓名">
                  <el-input v-model="courseForm.instructor" placeholder="请输入讲师姓名" />
                </el-form-item>
                <el-form-item label="制作人">
                  <el-input v-model="courseForm.producer" placeholder="请输入制作人" />
                </el-form-item>
              </div>
            </div>

            <!-- 课程内容 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                课程内容
              </h2>
              <div class="flex">
                <div class="w-64 border-r border-gray-200 pr-4">
                  <div class="mb-2 flex justify-between">
                    <el-button size="small" class="!rounded-button whitespace-nowrap" @click="addChapter">
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>添加章节
                    </el-button>
                    <el-button
                      v-if="!selectedChapter || !selectedChapter.parentChapterId"
                      size="small"
                      class="!rounded-button whitespace-nowrap"
                      @click="addSubChapter"
                    >
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>添加子章节
                    </el-button>
                  </div>
                  <el-tree
                    :data="chapterData"
                    node-key="id"
                    :props="{ children: 'children', label: 'chapterTitle' }"
                    default-expand-all
                    :expand-on-click-node="false"
                    draggable
                    class="border border-gray-200 rounded p-2"
                    @node-click="editChapter"
                  >
                    <template #default="{ node, data }">
                      <span class="w-full flex items-center">
                        <span class="min-w-0 flex-1 truncate pr-4" :title="data.chapterTitle" style="max-width: calc(100% - 120px);">{{ data.chapterTitle }}</span>
                        <span class="ml-2 flex flex-shrink-0">
                          <!-- 只有父章节才显示添加子章节按钮 -->
                          <el-button
                            v-if="!data.parentChapterId"
                            type="text"
                            size="small"
                            title="添加子章节"
                            @click.stop="selectedChapter = data; addSubChapter()"
                          >
                            <el-icon><Plus /></el-icon>
                          </el-button>
                          <el-button type="text" size="small" @click.stop="editChapter(data, node)">
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button type="text" size="small" class="text-red-500" @click.stop="deleteChapter(node, data)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </span>
                      </span>
                    </template>
                  </el-tree>
                </div>
                <div class="flex-1 pl-6">
                  <div v-if="selectedChapter">
                    <el-form-item label="章节标题" label-width="100px">
                      <el-input v-model="currentChapterForm.chapterTitle" placeholder="请输入章节标题" @blur="saveChapterChanges" />
                    </el-form-item>
                    <el-form-item label="章节时长" label-width="100px">
                      <div class="flex items-center">
                        <el-input-number v-model="currentChapterForm.durationMinutes" :min="0" class="w-32" @blur="saveChapterChanges" />
                        （分钟）
                      </div>
                    </el-form-item>
                    <el-form-item label="内容类型" label-width="100px">
                      <el-radio-group v-model="currentChapterForm.chapterType" @change="saveChapterChanges">
                        <el-radio label="VIDEO">
                          视频
                        </el-radio>
                        <el-radio label="DOCUMENT">
                          文档
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否必修" label-width="100px">
                      <el-switch v-model="currentChapterForm.isRequired" @change="saveChapterChanges" />
                    </el-form-item>
                    <el-form-item label="内容上传" label-width="100px">
                      <DocumentUpload
                        v-model="currentChapterForm.contentUrl"
                        :ext="currentChapterForm.chapterType === 'VIDEO' ? ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'] : ['pdf', 'doc', 'docx', 'ppt', 'pptx']"
                        :placeholder="currentChapterForm.chapterType === 'VIDEO' ? '上传视频文件' : '上传文档文件'"
                        :tip-text="currentChapterForm.chapterType === 'VIDEO' ? '支持 MP4、AVI、MOV、WMV、FLV、MKV 格式，文件大小不超过 100MB' : '支持 PDF、DOC、DOCX、PPT、PPTX 格式，文件大小不超过 10MB'"
                        :max-size="currentChapterForm.chapterType === 'VIDEO' ? 100 : 10"
                        @on-success="handleChapterContentUploadSuccess"
                      />
                    </el-form-item>
                    <el-form-item label="内容描述" label-width="100px">
                      <el-input v-model="currentChapterForm.chapterDescription" type="textarea" :rows="4" placeholder="请输入内容描述" @blur="saveChapterChanges" />
                    </el-form-item>
                  </div>
                  <div v-else class="py-8 text-center text-gray-500">
                    <p>请选择或创建一个章节来编辑内容</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 学习资料 -->
            <div class="mb-8">
              <h2 class="mb-6 border-b border-gray-200 pb-2 text-lg text-gray-800 font-bold">
                学习资料
              </h2>
              <el-form-item label="资料列表">
                <div class="mb-2">
                  <el-button size="small" class="!rounded-button whitespace-nowrap" @click="addMaterial">
                    <el-icon class="mr-1">
                      <Plus />
                    </el-icon>添加资料
                  </el-button>
                </div>
                <el-table :data="courseForm.courseAttachments" border class="w-full">
                  <el-table-column prop="fileName" label="文件名称" width="180">
                    <template #default="{ row }">
                      <el-input v-model="row.fileName" size="small" placeholder="请输入文件名称" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="fileType" label="类型" width="120">
                    <template #default="{ row }">
                      <el-select v-model="row.fileType" size="small">
                        <el-option label="PDF" value="pdf" />
                        <el-option label="Word" value="word" />
                        <el-option label="Excel" value="excel" />
                        <el-option label="PPT" value="ppt" />
                        <el-option label="其他" value="other" />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="文件" width="400">
                    <template #default="{ row }">
                      <UploadMbb
                        v-model="row.files"
                        :max="1"
                        :size="10"
                        @upload-success="(files) => handleMaterialUploadSuccess(files, row)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="描述">
                    <template #default="{ row }">
                      <el-input v-model="row.description" size="small" placeholder="请输入描述" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ $index }">
                      <el-button type="text" size="small" class="text-red-500" @click="removeMaterial($index)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss.scss";
</style>

<style scoped>
/* 自定义样式 */
:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-textarea__inner) {
  min-height: 150px;
}

:deep(.el-upload--picture-card) {
  width: 120px;
  height: 67.5px;
  line-height: 67.5px;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 67.5px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 67.5px;
  display: block;
}
</style>
