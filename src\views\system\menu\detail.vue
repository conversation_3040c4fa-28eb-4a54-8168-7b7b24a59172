<script setup lang="ts">
import Sortable from 'sortablejs'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import useSettingsStore from '@/store/modules/settings'
import Api from '@/api/modules/system/menu'

defineOptions({
  name: 'PagesExampleMenuDetail',
})

const route = useRoute()
const router = useRouter()
const tabbar = useTabbar()

const settingsStore = useSettingsStore()

const loading = ref(false)
const formRef = ref<FormInstance>()
const form = ref({
  id: route.query.id as string ?? '',
  parentId: route.query.parentId ?? '',
  path: '',
  redirect: '',
  name: '',
  component: '',
  sort: 0,
  meta: {
    title: '',
    icon: '',
    activeIcon: '',
    defaultOpened: false,
    permanent: false,
    auth: [] as string[],
    sidebar: true,
    breadcrumb: true,
    activeMenu: '',
    cache: [] as string[] | boolean,
    noCache: [] as string[],
    badge: '',
    link: '',
    iframe: '',
    copyright: false,
    paddingBottom: '0px',
  },
  auths: [] as {
    name: string
    value: string
  }[],
})
const formRules = ref<FormRules>({
  'path': [
    { required: true, message: '请输入路由地址', trigger: 'blur' },
  ],
  'meta.title': [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
  ],
})

onMounted(() => {
  console.log('菜单详情--打印传参数', route.query.id,
  )
  console.log('菜单详情--开始', form)
  if (form.value.id !== '') {
    getInfo()
  }
})

function getInfo() {
  loading.value = true
  Api.detail({ id: form.value.id }).then((res: any) => {
    console.log(res, '菜单详情')
    // if (res.data.auths) {
    // }
    // else {
    //   res.data.auths = []
    // }
    // res.data.meta = JSON.parse(res.data.meta)
    const obj = res.data ? res.data : res
    loading.value = false
    form.value.id = obj.id
    form.value.parentId = obj.parentId
    form.value.path = obj.path
    form.value.redirect = obj.redirect
    form.value.name = obj.name
    form.value.component = obj.component
    form.value.sort = obj.sort

    Object.assign(form.value.meta, obj.meta)
    form.value.auths = obj.auths
  })
}

// 鉴权标识
const inputAuth = ref('')
const inputAuthVisible = ref(false)
const InputAuthRef = ref()
function onInputAuthClose(tag: string) {
  form.value.meta.auth.splice(form.value.meta.auth.indexOf(tag), 1)
}
function onInputAuthShow() {
  inputAuthVisible.value = true
  nextTick(() => {
    InputAuthRef.value!.input!.focus()
  })
}
function onInputAuthConfirm() {
  if (inputAuth.value) {
    if (!form.value.meta.auth.includes(inputAuth.value)) {
      form.value.meta.auth.push(inputAuth.value)
    }
    else {
      ElMessage.warning('标识已存在')
    }
  }
  inputAuthVisible.value = false
  inputAuth.value = ''
}

// 缓存规则
const inputCache = ref('')
const inputCacheVisible = ref(false)
const InputCacheRef = ref()
function onInputCacheClose(tag: string) {
  typeof form.value.meta.cache === 'object' && form.value.meta.cache.splice(form.value.meta.cache.indexOf(tag), 1)
}
function onInputCacheShow() {
  inputCacheVisible.value = true
  nextTick(() => {
    InputCacheRef.value!.input!.focus()
  })
}
function onInputCacheConfirm() {
  if (inputCache.value && typeof form.value.meta.cache === 'object') {
    if (!form.value.meta.cache.includes(inputCache.value)) {
      form.value.meta.cache.push(inputCache.value)
    }
    else {
      ElMessage.warning('规则已存在')
    }
  }
  inputCacheVisible.value = false
  inputCache.value = ''
}

// 不缓存规则
const inputNoCache = ref('')
const inputNoCacheVisible = ref(false)
const InputNoCacheRef = ref()
function onInputNoCacheClose(tag: string) {
  form.value.meta.noCache.splice(form.value.meta.noCache.indexOf(tag), 1)
}
function onInputNoCacheShow() {
  inputNoCacheVisible.value = true
  nextTick(() => {
    InputNoCacheRef.value!.input!.focus()
  })
}
function onInputNoCacheConfirm() {
  if (inputNoCache.value) {
    if (!form.value.meta.noCache.includes(inputNoCache.value)) {
      form.value.meta.noCache.push(inputNoCache.value)
    }
    else {
      ElMessage.warning('规则已存在')
    }
  }
  inputNoCacheVisible.value = false
  inputNoCache.value = ''
}

const authsTableRef = ref()
const authsTableKey = ref(0)
onMounted(() => {
  onAuthDarg()
})
function onAuthAdd() {
  form.value.auths.push({
    name: '',
    value: '',
  })
  nextTick(() => {
    authsTableRef.value.setScrollTop(form.value.auths.length * 50)
  })
}
function onAuthDelete(index: number) {
  form.value.auths.splice(index, 1)
}
function onAuthDarg() {
  const tbody = authsTableRef.value.$el.querySelector('.el-table__body-wrapper tbody')
  Sortable.create(tbody, {
    handle: '.sortable',
    animation: 300,
    ghostClass: 'ghost',
    onEnd: ({ newIndex, oldIndex }) => {
      if (newIndex === undefined || oldIndex === undefined) {
        return
      }
      const currRow = form.value.auths.splice(oldIndex, 1)[0]
      form.value.auths.splice(newIndex, 0, currRow)
      authsTableKey.value += 1
      nextTick(() => onAuthDarg())
    },
  })
}
const obj: any = ref({})
function onSubmit() {
  console.log(form.value, 'form.value')
  if (form.value.id === '') {
    formRef.value && formRef.value.validate((valid) => {
      if (valid) {
        form.value.sort = Number(form.value.sort)
        obj.value = Object.assign(obj.value, form.value)
        obj.value.auths = obj.value.auths.map((i: any) => {
          return { ...i, id: '' }
        })
        obj.value.auths = JSON.stringify(obj.value.auths)
        obj.value.meta = JSON.stringify(obj.value.meta)
        Api.edit(obj.value).then((res: any) => {
          goBack()
          ElMessage({ message: res.msg, type: 'success' })
        })
      }
    })
  }
  else {
    formRef.value && formRef.value.validate((valid) => {
      if (valid) {
        obj.value = Object.assign(obj.value, form.value)
        obj.value.auths = obj.value.auths.map((i: any) => {
          return { ...i, id: i.id ? i.id : '' }
        })
        obj.value.auths = JSON.stringify(obj.value.auths)
        obj.value.meta = JSON.stringify(obj.value.meta)
        Api.edit(obj.value).then((res: any) => {
          ElMessage({ message: res.msg, type: 'success' })
          goBack()
        })
      }
    })
  }
}

function onCancel() {
  goBack()
}

// 返回列表页
function goBack() {
  if (settingsStore.settings.tabbar.enable && settingsStore.settings.tabbar.mergeTabsBy !== 'activeMenu') {
    tabbar.close({ name: 'menuindex' })
  }
  else {
    router.push({ name: 'menuindex' })
  }
}
</script>

<template>
  <div class="absolute-container">
    <page-header :title="route.name === 'menudetail' ? '新增菜单' : '编辑菜单'">
      <el-button size="default" round @click="goBack">
        <template #icon>
          <svg-icon name="ep:arrow-left" />
        </template>
        返回
      </el-button>
    </page-header>
    <div v-loading="loading" class="page-main" style="height: calc(100vh - 240px);overflow-y: auto;">
      <el-form ref="formRef" :model="form" :rules="formRules" label-position="top">
        <LayoutContainer right-side-width="500px" hide-right-side-toggle>
          <page-header v-if="!!form.parentId" title="基础配置" content="标准路由配置，包含 path/redirect/name/component" />
          <el-row v-if="!!form.parentId" :gutter="30" style="padding: 20px;">
            <el-col :xl="12" :lg="24">
              <el-form-item label="路由地址" prop="path">
                <el-input v-model="form.path" clearable placeholder="请输入路由地址" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item label="重定向" prop="redirect">
                <el-input v-model="form.redirect" clearable placeholder="请输入重定向地址" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item prop="name">
                <template #label>
                  路由命名
                  <span class="label-tip">即 name ，系统唯一</span>
                </template>
                <el-input v-model="form.name" clearable placeholder="请输入路由命名" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item prop="component">
                <template #label>
                  组件路径
                  <span class="label-tip">
                    顶级路由请设置“<el-link
                      type="primary" :underline="false"
                      @click.prevent="form.component = 'Layout'"
                    >Layout</el-link>”，中间层级路由无需设置
                  </span>
                </template>
                <el-input v-model="form.component" clearable placeholder="请输入完整组件路径">
                  <template v-if="form.component !== 'Layout'" #prepend>
                    /src/views/
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <page-header title="扩展配置">
            <template #content>
              <!-- <div style="display: flex;">
                框架扩展配置，详细配置介绍请查看<el-link type="primary" href="https://fantastic-admin.gitee.io/guide/router.html#%E5%AF%BC%E8%88%AA%E9%85%8D%E7%BD%AE" target="_blank">
                  框架文档
                </el-link>
              </div> -->
            </template>
          </page-header>
          <el-row :gutter="30" style="padding: 20px;">
            <el-col :xl="12" :lg="24">
              <el-form-item label="显示名称" prop="meta.title">
                <el-input v-model="form.meta.title" clearable placeholder="请输入显示名称" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item prop="meta.auth">
                <template #label>
                  鉴权标识
                  <el-tooltip content="当设置多个标识时，只要命中其中一个则鉴权通过" placement="top">
                    <svg-icon name="ri:question-line" />
                  </el-tooltip>
                </template>
                <el-space>
                  <el-tag
                    v-for="item in (form.meta.auth as string[])" :key="item" size="large" closable
                    :disable-transitions="false" @close="onInputAuthClose(item)"
                  >
                    {{ item }}
                  </el-tag>
                  <el-input
                    v-if="inputAuthVisible" ref="InputAuthRef" v-model="inputAuth" style="width: 200px;"
                    @keyup.enter="onInputAuthConfirm" @blur="onInputAuthConfirm"
                  />
                  <el-button v-else @click="onInputAuthShow">
                    新增
                  </el-button>
                </el-space>
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item label="默认图标" prop="meta.icon">
                <icon-picker v-model="form.meta.icon" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item label="激活图标" prop="meta.activeIcon">
                <icon-picker v-model="form.meta.activeIcon" />
              </el-form-item>
            </el-col>
            <el-col :xl="12" :lg="24">
              <el-form-item label="排序:" prop="sort">
                <el-input
                  v-model="form.sort" type="number" :min="0" controls-position="right"
                  class="ele-fluid ele-text-left"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :xl="12" :lg="24">
              <el-form-item label="父级ID:">
                <el-input v-model="form.parentId" clearable placeholder="请输入父级ID" />
              </el-form-item>
            </el-col> -->

            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item label="默认展开" prop="meta.defaultOpened">
                <el-switch v-model="form.meta.defaultOpened" inline-prompt active-text="是" inactive-text="否" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.permanent">
                <template #label>
                  常驻标签页
                  <span class="label-tip">请勿在带有参数的路由地址上开启该设置</span>
                </template>
                <el-switch v-model="form.meta.permanent" inline-prompt active-text="是" inactive-text="否" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item label="在导航显示" prop="meta.sidebar">
                <el-switch v-model="form.meta.sidebar" inline-prompt active-text="显示" inactive-text="隐藏" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item label="在面包屑显示" prop="meta.breadcrumb">
                <el-switch v-model="form.meta.breadcrumb" inline-prompt active-text="显示" inactive-text="隐藏" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.cache">
                <template #label>
                  缓存规则
                  <el-tooltip content="当跳转到设置的路由时，则会对当前路由进行缓存" placement="top">
                    <svg-icon name="ri:question-line" />
                  </el-tooltip>
                  <span v-show="typeof form.meta.cache === 'object'" class="label-tip">切换为<el-link
                    type="primary"
                    :underline="false" @click.prevent="form.meta.cache = true"
                  >始终缓存</el-link></span>
                  <span v-show="typeof form.meta.cache === 'boolean'" class="label-tip">切换为<el-link
                    type="primary"
                    :underline="false" @click.prevent="form.meta.cache = []"
                  >规则模式</el-link></span>
                </template>
                <el-space v-show="typeof form.meta.cache === 'object'">
                  <el-tag
                    v-for="item in (form.meta.cache as string[])" :key="item" size="large" closable
                    :disable-transitions="false" @close="onInputCacheClose(item)"
                  >
                    {{ item }}
                  </el-tag>
                  <el-input
                    v-if="inputCacheVisible" ref="InputCacheRef" v-model="inputCache" style="width: 200px;"
                    @keyup.enter="onInputCacheConfirm" @blur="onInputCacheConfirm"
                  />
                  <el-button v-else @click="onInputCacheShow">
                    新增
                  </el-button>
                </el-space>
                <div v-show="typeof form.meta.cache === 'boolean'">
                  始终缓存
                </div>
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.noCache">
                <template #label>
                  不缓存规则
                  <el-tooltip content="当跳转到设置的路由时，则会对当前路由取消缓存" placement="top">
                    <svg-icon name="ri:question-line" />
                  </el-tooltip>
                  <span class="label-tip">当缓存规则为“始终缓存”时生效</span>
                </template>
                <el-space>
                  <el-tag
                    v-for="item in (form.meta.noCache as string[])" :key="item" size="large" closable
                    :disable-transitions="false" @close="onInputNoCacheClose(item)"
                  >
                    {{ item }}
                  </el-tag>
                  <el-input
                    v-if="inputNoCacheVisible" ref="InputNoCacheRef" v-model="inputNoCache"
                    style="width: 200px;" @keyup.enter="onInputNoCacheConfirm" @blur="onInputNoCacheConfirm"
                  />
                  <el-button v-else @click="onInputNoCacheShow">
                    新增
                  </el-button>
                </el-space>
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.activeMenu">
                <template #label>
                  高亮导航
                  <span class="label-tip">如果子路由不在导航显示，则需要设置高亮的上级路由地址</span>
                </template>
                <el-input v-model="form.meta.activeMenu" clearable placeholder="请输入高亮导航的完整路由地址" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.badge">
                <template #label>
                  徽标
                  <span class="label-tip">不宜设置太长，建议控制在4个字符内</span>
                </template>
                <el-input v-model="form.meta.badge" clearable placeholder="请输入徽标显示内容" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.link">
                <template #label>
                  访问外链
                  <span class="label-tip">请设置 http/https 开头的完整外链地址</span>
                </template>
                <el-input v-model="form.meta.link" clearable placeholder="请输入网址" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.iframe">
                <template #label>
                  内嵌网页
                  <span class="label-tip">请勿与外链同时设置，同时设置时，本设置会失效</span>
                </template>
                <el-input v-model="form.meta.iframe" clearable placeholder="请输入网址" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item label="底部版权" prop="meta.copyright">
                <el-switch v-model="form.meta.copyright" inline-prompt active-text="显示" inactive-text="隐藏" />
              </el-form-item>
            </el-col>
            <el-col v-if="!!form.parentId" :xl="12" :lg="24">
              <el-form-item prop="meta.paddingBottom">
                <template #label>
                  底部填充高度
                  <span class="label-tip">请设置有效的长度单位，例如：px/em/rem等</span>
                </template>
                <el-input v-model="form.meta.paddingBottom" clearable placeholder="请输入显示名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <template #rightSide>
            <page-header title="权限池">
              <template #content>
                <p>设置导航所具备的所有权限，权限池内的权限会用于角色管理</p>
                <p style="margin-bottom: 0;">
                  通常只在最子级导航上进行设置
                </p>
              </template>
            </page-header>
            <el-table ref="authsTableRef" :key="authsTableKey" :data="form.auths" stripe highlight-current-row border>
              <el-table-column width="60" align="center" fixed>
                <template #header>
                  <el-button type="primary" size="small" plain circle @click="onAuthAdd">
                    <template #icon>
                      <svg-icon name="ep:plus" />
                    </template>
                  </el-button>
                </template>
                <template #default="scope">
                  <span class="index">{{ scope.$index + 1 }}</span>
                  <el-button type="danger" size="small" plain circle class="delete" @click="onAuthDelete(scope.$index)">
                    <template #icon>
                      <svg-icon name="ep:delete" />
                    </template>
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column width="80" align="center" fixed>
                <template #header>
                  排序
                </template>
                <template #default>
                  <el-tag type="info" class="sortable">
                    <svg-icon name="ep:d-caret" />
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="名称">
                <template #default="scope">
                  <el-input v-model="scope.row.name" />
                </template>
              </el-table-column>
              <el-table-column label="标识">
                <template #default="scope">
                  <el-input v-model="scope.row.value" />
                </template>
              </el-table-column>
            </el-table>
          </template>
        </LayoutContainer>
      </el-form>
    </div>
    <fixed-action-bar>
      <el-button type="primary" size="large" @click="onSubmit">
        提交
      </el-button>
      <el-button size="large" @click="onCancel">
        取消
      </el-button>
    </fixed-action-bar>
  </div>
</template>

<style lang="scss" scoped>
  .absolute-container {
    position: absolute;
    // display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    .page-header {
      margin-bottom: 0;
    }

    .page-main,
    .el-form {
      display: flex;
      // 让 page-main 的高度自适应
      flex: 1;
      flex-direction: column;
      overflow: auto;
    }

    .page-main {
      .flex-container {
        position: static;

        .page-header {
          margin-inline: -15px;
          margin-top: -15px;
          background-color: var(--el-disabled-bg-color);
        }

        :deep(.main) {
          .label-tip {
            display: inline-flex;
            margin-left: 10px;
            color: var(--el-text-color-disabled);
          }
        }

        :deep(.right-side) {
          display: flex;
          flex-direction: column;

          .el-table {
            height: 100%;
            margin-top: 15px;

            .el-table__row {
              &.ghost {
                opacity: 0;
              }

              .index {
                display: inline-block;
              }

              .delete {
                display: none;
              }

              &:hover {
                .index {
                  display: none;
                }

                .delete {
                  display: inline-block;
                }
              }

              .el-tag.sortable,
              .el-tag.sortable .icon {
                cursor: ns-resize;
              }
            }
          }
        }
      }
    }
  }
</style>
