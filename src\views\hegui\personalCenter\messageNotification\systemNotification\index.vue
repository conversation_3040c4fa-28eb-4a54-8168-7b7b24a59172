<script lang="ts" setup>
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import messageApi, { MESSAGE_PRIORITY, MESSAGE_STATUS, MESSAGE_TYPES } from '@/api/personal/message'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

// 筛选条件 - 固定为系统通知
const filter = ref({
  status: 'all',
  timeRange: 'week',
  priority: 'all',
  messageType: 'SYSTEM', // 固定为系统通知
})
const searchQuery = ref('')

// 视图模式
const viewMode = ref<'list' | 'timeline'>('list')

// 分页
const pagination = ref({
  page: 1,
  size: 10,
})
const totalMessages = ref(0)

// 消息数据
const messages = ref<any[]>([])
const loading = ref(false)

// 消息统计
const messageStats = ref({
  total: 0,
  unread: 0,
  urgent: 0,
})

// Chart ref
const chart = ref<HTMLElement>()

// 最近通知数据（示例数据）
const recentNotifications = ref([
  { id: 1, title: '系统维护通知', time: '2小时前' },
  { id: 2, title: '新功能上线', time: '1天前' },
  { id: 3, title: '安全更新提醒', time: '2天前' },
])

// 获取消息列表
async function getMessageList() {
  try {
    loading.value = true
    const params = {
      userId: userStore.userId,
      categoryName: 'SYSTEM', // 固定为系统通知
      keyword: searchQuery.value || undefined,
      page: pagination.value.page - 1,
      size: pagination.value.size,
    }

    const response = await messageApi.getCategoryList(params)
    if (response.content) {
      messages.value = response.content.map((item: any) => ({
        ...item,
        expanded: false,
        type: MESSAGE_TYPES[item.notification?.category as keyof typeof MESSAGE_TYPES] || item.notification?.category,
        title: item.notification?.title || '',
        content: item.notification?.content || '',
        time: item.sentTime || '',
        priority: item.notification?.priority || 'normal',
      }))
      totalMessages.value = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取系统通知失败:', error)
    ElMessage.error('获取系统通知失败')
  }
  finally {
    loading.value = false
  }
}

// 按日期分组消息（时间线视图）
const groupedMessages = computed(() => {
  const groups: { date: string, messages: any[] }[] = []
  const dateMap = new Map<string, any[]>()

  messages.value.forEach((msg) => {
    const date = msg.time?.split(' ')[0] || ''
    if (!dateMap.has(date)) {
      dateMap.set(date, [])
    }
    dateMap.get(date)?.push(msg)
  })

  dateMap.forEach((msgs, date) => {
    groups.push({ date, messages: msgs })
  })

  groups.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  return groups
})

// 切换消息状态
async function toggleStatus(message: any) {
  try {
    if (message.status !== 'read') {
      await messageApi.markAsRead(message.id)
      message.status = 'read'
      ElMessage.success('已标记为已读')
    }
  }
  catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除消息
async function deleteMessage(message: any) {
  try {
    await ElMessageBox.confirm('确定要删除这条系统通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await messageApi.deleteMessage(message.id)
    ElMessage.success('删除成功')
    getMessageList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('删除消息失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量标记已读
async function batchMarkAsRead() {
  try {
    const userId = userStore.userId
    if (!userId) {
      ElMessage.warning('用户信息获取失败')
      return
    }

    await messageApi.batchMarkAsRead(userId)
    ElMessage.success('已全部标记为已读')
    getMessageList()
  }
  catch (error) {
    console.error('批量标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 清空已读消息
async function clearReadMessages() {
  try {
    await ElMessageBox.confirm('确定要清空所有已读的系统通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 这里需要后端提供清空已读消息的接口
    ElMessage.success('清空成功')
    getMessageList()
  }
  catch (error) {
    if (error !== 'cancel') {
      console.error('清空已读消息失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 展开/收起消息
function toggleExpand(row: any) {
  row.expanded = !row.expanded
}

// 筛选消息
function handleFilter() {
  pagination.value.page = 1
  getMessageList()
}

// 重置筛选
function resetFilter() {
  filter.value = {
    status: 'all',
    timeRange: 'week',
    priority: 'all',
    messageType: 'SYSTEM',
  }
  searchQuery.value = ''
  pagination.value.page = 1
  getMessageList()
}

// 搜索消息
function handleSearch() {
  pagination.value.page = 1
  getMessageList()
}

// 分页变化
function handlePageChange() {
  getMessageList()
}

// Initialize chart
function initChart() {
  if (!chart.value) return

  const myChart = echarts.init(chart.value)
  const option = {
    animation: false,
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
    },
    series: [
      {
        name: '系统通知类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 45, name: '系统维护' },
          { value: 30, name: '功能更新' },
          { value: 25, name: '安全提醒' },
        ],
      },
    ],
  }

  myChart.setOption(option)

  // Resize chart on window resize
  const handleResize = () => {
    myChart.resize()
  }
  window.addEventListener('resize', handleResize)

  // Cleanup
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    myChart.dispose()
  })
}

onMounted(() => {
  getMessageList()
  nextTick(() => {
    initChart()
  })
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              系统通知
            </h1>
            <el-tag type="info" class="ml-4">
              系统消息
            </el-tag>
          </div>
          <div class="flex space-x-3">
            <el-button
              v-auth="['systemNotification/index/batchMarkAsRead']"
              type="primary"
              plain
              @click="batchMarkAsRead"
            >
              全部标为已读
            </el-button>
            <el-button
              v-auth="['systemNotification/index/clearReadMessages']"
              plain
              @click="clearReadMessages"
            >
              清空已读消息
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <div class="flex items-center justify-between">
                <div class="flex space-x-4">
                  <el-select v-model="filter.status" placeholder="阅读状态" class="w-32">
                    <el-option label="全部" value="all" />
                    <el-option label="未读" value="unread" />
                    <el-option label="已读" value="read" />
                  </el-select>

                  <el-select v-model="filter.priority" placeholder="紧急程度" class="w-32">
                    <el-option label="全部" value="all" />
                    <el-option label="紧急" value="urgent" />
                    <el-option label="普通" value="normal" />
                  </el-select>

                  <el-button
                    type="primary"
                    @click="handleFilter"
                  >
                    筛选
                  </el-button>
                  <el-button
                    @click="resetFilter"
                  >
                    重置
                  </el-button>
                </div>

                <div class="relative">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索系统通知内容..."
                    class="w-64"
                    clearable
                    @keyup.enter="handleSearch"
                    @clear="handleSearch"
                  >
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <i class="fas fa-search" />
                      </el-icon>
                    </template>
                    <template #append>
                      <el-button @click="handleSearch">
                        搜索
                      </el-button>
                    </template>
                  </el-input>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!-- List View -->
              <div v-if="viewMode === 'list'">
                <el-table
                  v-loading="loading"
                  :data="messages"
                  style="width: 100%;"
                  element-loading-text="加载中..."
                  @row-click="toggleExpand"
                >
                  <el-table-column width="60">
                    <template #default="{ row }">
                      <el-icon v-if="row.status !== 'read'" class="text-blue-500">
                        <i class="fas fa-circle" />
                      </el-icon>
                      <el-icon v-else class="text-gray-400">
                        <i class="far fa-circle" />
                      </el-icon>
                    </template>
                  </el-table-column>
                  <el-table-column prop="title" label="标题" width="180" show-overflow-tooltip />
                  <el-table-column prop="type" label="类型" width="120" />
                  <el-table-column prop="time" label="发送时间" width="150" />
                  <el-table-column label="紧急程度" width="100">
                    <template #default="{ row }">
                      <el-tag
                        v-if="row.priority === 'urgent'"
                        type="danger"
                        size="small"
                      >
                        紧急
                      </el-tag>
                      <el-tag
                        v-else
                        type="info"
                        size="small"
                      >
                        普通
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button
                        v-auth="['allMessages/index/toggleStatus']"
                        v-if="row.status !== 'read'"
                        size="small"
                        @click.stop="toggleStatus(row)"
                      >
                        标为已读
                      </el-button>
                      <el-button
                        v-auth="['allMessages/index/deleteMessage']"
                        type="danger"
                        size="small"
                        @click.stop="deleteMessage(row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column type="expand" width="1">
                    <template #default="{ row }">
                      <div v-if="row.expanded" class="bg-gray-50 p-4">
                        <div class="mb-2 font-medium">
                          {{ row.title }}
                        </div>
                        <div class="text-gray-600">
                          {{ row.content }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- Timeline View -->
              <div v-else class="p-6">
                <div v-loading="loading" element-loading-text="加载中...">
                  <div v-for="(group, index) in groupedMessages" :key="index" class="mb-8">
                    <div class="mb-4 text-lg text-gray-700 font-medium">
                      {{ group.date }}
                    </div>
                    <div class="border-l-2 border-gray-200 pl-6 space-y-4">
                      <div
                        v-for="message in group.messages" :key="message.id"
                        class="cursor-pointer rounded-lg bg-gray-50 p-4 transition-shadow hover:shadow-sm"
                        @click="toggleStatus(message)"
                      >
                        <div class="flex items-start justify-between">
                          <div>
                            <div class="font-medium">
                              {{ message.title }}
                            </div>
                            <div class="mt-1 text-sm text-gray-500">
                              {{ message.content.substring(0, 60) }}...
                            </div>
                          </div>
                          <div class="flex items-center space-x-2">
                            <el-tag
                              v-if="message.priority === 'urgent'"
                              type="danger"
                              size="small"
                            >
                              紧急
                            </el-tag>
                            <el-tag
                              v-else
                              type="info"
                              size="small"
                            >
                              普通
                            </el-tag>
                            <span class="text-sm text-gray-400">{{ message.time }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Pagination -->
              <div class="flex items-center justify-between border-t p-4">
                <div class="text-sm text-gray-500">
                  共 {{ totalMessages }} 条系统通知
                </div>
                <el-pagination
                  v-model:current-page="pagination.page"
                  v-model:page-size="pagination.size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalMessages"
                  layout="total, sizes, prev, pager, next, jumper"
                  @current-change="handlePageChange"
                  @size-change="handlePageChange"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  系统通知统计
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex justify-between">
                  <span class="text-gray-500">总通知数</span>
                  <span class="font-medium">{{ messageStats.total }} 条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">未读通知</span>
                  <span class="text-blue-500 font-medium">{{ messageStats.unread }} 条</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">紧急通知</span>
                  <span class="text-red-500 font-medium">{{ messageStats.urgent }} 条</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近系统通知
                </div>
              </template>
              <div class="space-y-3">
                <div
                  v-for="item in recentNotifications" :key="item.id"
                  class="border-b border-gray-100 pb-3 last:border-0 last:pb-0"
                >
                  <div class="text-sm font-medium">
                    {{ item.title }}
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    {{ item.time }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-table :deep(.el-table__row) {
    cursor: pointer;
  }

  .el-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }

  .el-table :deep(.el-table__expanded-cell) {
    padding: 0;
  }
</style>
