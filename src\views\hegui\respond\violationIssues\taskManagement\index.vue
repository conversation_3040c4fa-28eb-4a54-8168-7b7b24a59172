<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import popMode from './pop.vue'
import PageHeader from '@/components/PageHeader/index.vue'
import PageMain from '@/components/PageMain/index.vue'
import problemTaskApi from '@/api/problemTask'

const router = useRouter()

// 分页数据
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
})

// 查询参数
const queryParams = reactive({
  title: null,
  investigateCode: null,
  investigateType: null,
  investigateSource: null,
  level: null,
  startDate: null,
  finishDate: null,
  status: null,
})

// 表格数据
const tableData = ref<any[]>([])
const loading = ref(false)

// 弹窗相关
const dialogVisible = ref(false)
const form = ref<any>({})
const dialogRef = ref<any>(null)

// 统计数据
const statistics = reactive({
  total: 0,
  finished: 0,
  progressing: 0,
  paused: 0,
  completed: 0,
  inProgress: 0,
  myResponsible: 0,
  myCompleted: 0,
  myInProgress: 0,
})

// 获取表格数据
async function fetchTableData() {
  loading.value = true
  try {
    const res = await problemTaskApi.searchTasks(queryParams, pagination.page, pagination.size)
    if (res) {
      tableData.value = res.content || []
      pagination.total = res.totalElements || 0

      // 更新统计数据
      // updateStatistics(res.data.content || [])
    }
    else {
      ElMessage.error('获取数据失败')
    }
  }
  catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}

// 更新统计数据
function updateStatistics(data: any[]) {
  statistics.total = data.length
  statistics.finished = data.filter(item => item.status === 'FINISHED').length
  statistics.progressing = data.filter(item => item.status === 'PROGRESSING').length
  statistics.paused = data.filter(item => item.status === 'PAUSED').length

  // 更新卡片统计数据
  statistics.completed = statistics.finished
  statistics.inProgress = statistics.progressing

  // 模拟我负责的数据
  const currentUser = 'currentUser' // 假设当前用户ID
  const myTasks = data.filter(item => item.dutyEmployeeId === currentUser)
  statistics.myResponsible = myTasks.length
  statistics.myCompleted = myTasks.filter(item => item.status === 'FINISHED').length
  statistics.myInProgress = myTasks.filter(item => item.status === 'PROGRESSING').length
}

// 处理页码变化
function handleCurrentChange(page: number) {
  pagination.page = page - 1 // 后端从0开始计数
  fetchTableData()
}

// 处理每页条数变化
function handleSizeChange(size: number) {
  pagination.size = size
  pagination.page = 0
  fetchTableData()
}

// 搜索
function handleSearch() {
  pagination.page = 0
  fetchTableData()
}

// 重置
function handleReset() {
  Object.keys(queryParams).forEach((key) => {
    queryParams[key as keyof typeof queryParams] = ''
  })
  pagination.page = 0
  fetchTableData()
}

// 跳转到新增/编辑页面
function goAddEdit(item?: any, type?: string) {
  if (type === 'edit' && item) {
    // 编辑调查任务
    router.push({
      path: '/respond/violationIssues/taskManagement/addEdit',
      query: { id: item.id },
    })
  }
  else {
    // 新增调查任务
    router.push({
      path: '/respond/violationIssues/taskManagement/addEdit',
    })
  }
}

// 查看详情
function viewDetail(id: string) {
  router.push({
    path: '/respond/violationIssues/taskManagement/detail',
    query: { id },
  })
}

// 提交表单
function submitForm() {
  // 提交表单逻辑
}

// 打开弹窗
// 暂时未使用，保留以备后续功能扩展
function _openDialog(id?: string) {
  dialogVisible.value = true
  if (id) {
    // 加载数据逻辑
  }
}

// 格式化状态
function formatStatus(status: string) {
  const statusMap: Record<string, { type: 'info' | 'success' | 'primary' | 'warning' | 'danger', label: string }> = {
    NO_START: { type: 'info', label: '未开始' },
    PROGRESSING: { type: 'success', label: '进行中' },
    FINISHED: { type: 'primary', label: '已完成' },
    PAUSED: { type: 'warning', label: '已暂停' },
    CANCELED: { type: 'danger', label: '已取消' },
  }
  return statusMap[status] || { type: 'info', label: '未知' }
}

// 格式化优先级
function formatLevel(level: string) {
  const levelMap: Record<string, { type: 'info' | 'warning' | 'danger', label: string }> = {
    LOW: { type: 'info', label: '低' },
    MIDDLE: { type: 'warning', label: '中' },
    HIGH: { type: 'danger', label: '高' },
  }
  return levelMap[level] || { type: 'info', label: '未知' }
}

// 格式化调查类型
function formatInvestigateType(type: string) {
  const typeMap: Record<string, string> = {
    ADVERTISING_COMPLIANCE: '广告合规',
    SUPPLIER_MANAGEMENT: '供应商管理',
    EMPLOYEE_TRAINING: '员工培训',
    FINANCIAL_AUDITING: '财务审计',
  }
  return typeMap[type] || '未知'
}

// 格式化调查来源
function formatInvestigateSource(source: string) {
  const sourceMap: Record<string, string> = {
    MARKETING: '市场部',
    PROCUREMENT: '采购部',
    HR: '人力资源部',
    FINANCE: '财务部',
  }
  return sourceMap[source] || '未知'
}

onMounted(() => {
  fetchTableData()
})
</script>

<template>
  <div class="absolute-container">
    <PageHeader title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              违规问题调查
            </h1>
            <!-- <el-tag type="warning" class="ml-4">审查中</el-tag> -->
          </div>
          <div class="flex items-center space-x-3">
            <el-button v-auth="['violationIssues/taskManagement/index/add']" type="primary" class="!rounded-button" @click="goAddEdit()">
              新增调查任务
            </el-button>
            <el-button v-auth="['violationIssues/taskManagement/index/import']" plain class="!rounded-button">
              批量导入
            </el-button>
            <el-button v-auth="['violationIssues/taskManagement/index/export']" plain class="!rounded-button">
              导出
            </el-button>
            <el-button v-auth="['violationIssues/taskManagement/index/analysis']" plain class="!rounded-button">
              统计分析
            </el-button>
            <!-- <el-button plain class="!rounded-button">
              <el-icon class="mr-2"><arrow-left /></el-icon>返回
            </el-button> -->
          </div>
        </div>
      </template>
    </PageHeader>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row v-if="false" :gutter="20" class="">
          <el-col :span="12">
            <el-card class="h-full">
              <template #header>
                <div class="flex items-center justify-between">
                  <span class="font-medium">审查进度</span>
                  <el-button text @click="handleSearch">
                    刷新数据
                  </el-button>
                </div>
              </template>
              <div class="mb-4 flex items-center justify-between">
                <div class="text-center">
                  <div class="text-3xl text-blue-600 font-bold">
                    {{ statistics.total }}
                  </div>
                  <div class="text-sm text-gray-500">
                    总调查
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-3xl text-green-600 font-bold">
                    {{ statistics.inProgress }}
                  </div>
                  <div class="text-sm text-gray-500">
                    进行中
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-3xl text-purple-600 font-bold">
                    {{ statistics.completed }}
                  </div>
                  <div class="text-sm text-gray-500">
                    已完成
                  </div>
                </div>
              </div>
              <div class="mb-4">
                <div class="mb-1 flex justify-between">
                  <span class="text-sm text-gray-600">完成率</span>
                  <span class="text-sm text-gray-600">{{ statistics.total > 0 ? Math.round((statistics.completed / statistics.total) * 100) : 0 }}%</span>
                </div>
                <el-progress :percentage="statistics.total > 0 ? Math.round((statistics.completed / statistics.total) * 100) : 0" />
              </div>
              <div>
                <div class="mb-1 flex justify-between">
                  <span class="text-sm text-gray-600">进行中比例</span>
                  <span class="text-sm text-gray-600">{{ statistics.total > 0 ? Math.round((statistics.inProgress / statistics.total) * 100) : 0 }}%</span>
                </div>
                <el-progress :percentage="statistics.total > 0 ? Math.round((statistics.inProgress / statistics.total) * 100) : 0" status="warning" />
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="h-full">
              <template #header>
                <div class="flex items-center justify-between">
                  <span class="font-medium">我负责的</span>
                  <el-button text @click="handleSearch">
                    刷新数据
                  </el-button>
                </div>
              </template>
              <div class="mb-4 flex items-center justify-between">
                <div class="text-center">
                  <div class="text-3xl text-blue-600 font-bold">
                    {{ statistics.myResponsible }}
                  </div>
                  <div class="text-sm text-gray-500">
                    总调查
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-3xl text-green-600 font-bold">
                    {{ statistics.myInProgress || 0 }}
                  </div>
                  <div class="text-sm text-gray-500">
                    进行中
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-3xl text-purple-600 font-bold">
                    {{ statistics.myCompleted || 0 }}
                  </div>
                  <div class="text-sm text-gray-500">
                    已完成
                  </div>
                </div>
              </div>
              <div class="mb-4">
                <div class="mb-1 flex justify-between">
                  <span class="text-sm text-gray-600">完成率</span>
                  <span class="text-sm text-gray-600">{{ statistics.myResponsible > 0 ? Math.round((statistics.myCompleted / statistics.myResponsible) * 100) : 0 }}%</span>
                </div>
                <el-progress :percentage="statistics.myResponsible > 0 ? Math.round((statistics.myCompleted / statistics.myResponsible) * 100) : 0" />
              </div>
              <div>
                <div class="mb-1 flex justify-between">
                  <span class="text-sm text-gray-600">进行中比例</span>
                  <span class="text-sm text-gray-600">{{ statistics.myResponsible > 0 ? Math.round((statistics.myInProgress / statistics.myResponsible) * 100) : 0 }}%</span>
                </div>
                <el-progress :percentage="statistics.myResponsible > 0 ? Math.round((statistics.myInProgress / statistics.myResponsible) * 100) : 0" status="warning" />
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card class="mt-20">
          <div class="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div class="grid grid-cols-4 mb-4 gap-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">调查状态</label>
                <el-select v-model="queryParams.status" placeholder="全部状态" class="w-full">
                  <el-option label="全部状态" value="" />
                  <el-option label="未开始" value="NO_START" />
                  <el-option label="进行中" value="PROGRESSING" />
                  <el-option label="已完成" value="FINISHED" />
                  <el-option label="已暂停" value="PAUSED" />
                  <el-option label="已取消" value="CANCELED" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">优先级</label>
                <el-select v-model="queryParams.level" placeholder="全部优先级" class="w-full">
                  <el-option label="全部优先级" value="" />
                  <el-option label="高" value="HIGH" />
                  <el-option label="中" value="MIDDLE" />
                  <el-option label="低" value="LOW" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">来源部门</label>
                <el-select v-model="queryParams.investigateSource" placeholder="全部部门" class="w-full">
                  <el-option label="全部部门" value="" />
                  <el-option label="财务部" value="FINANCE" />
                  <el-option label="市场部" value="MARKETING" />
                  <el-option label="人力资源部" value="HR" />
                  <el-option label="采购部" value="PROCUREMENT" />
                </el-select>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">调查类型</label>
                <el-select v-model="queryParams.investigateType" placeholder="全部类型" class="w-full">
                  <el-option label="全部类型" value="" />
                  <el-option label="广告合规" value="ADVERTISING_COMPLIANCE" />
                  <el-option label="供应商管理" value="SUPPLIER_MANAGEMENT" />
                  <el-option label="员工培训" value="EMPLOYEE_TRAINING" />
                  <el-option label="财务审计" value="FINANCIAL_AUDITING" />
                </el-select>
              </div>
            </div>
            <div class="grid grid-cols-2 mb-4 gap-4">
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">调查日期</label>
                <div class="flex items-center">
                  <el-date-picker
                    v-model="queryParams.startDate"
                    type="date"
                    placeholder="开始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                  <span class="mx-2 text-gray-500">至</span>
                  <el-date-picker
                    v-model="queryParams.finishDate"
                    type="date"
                    placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="w-full"
                  />
                </div>
              </div>
              <div>
                <label class="mb-1 block text-sm text-gray-700 font-medium">关键词</label>
                <el-input
                  v-model="queryParams.title"
                  placeholder="输入调查标题或编号搜索..."
                  clearable
                />
              </div>
            </div>
            <div class="flex items-center justify-end">
              <div class="flex space-x-2">
                <el-button
                  v-auth="['violationIssues/taskManagement/index/reset']"
                  class="!rounded-button whitespace-nowrap"
                  plain
                  @click="handleReset"
                >
                  <i class="fas fa-redo mr-2" />重置
                </el-button>
                <el-button
                  v-auth="['violationIssues/taskManagement/index/search']"
                  type="primary"
                  class="!rounded-button whitespace-nowrap"
                  @click="handleSearch"
                >
                  <i class="fas fa-search mr-2" />搜索
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
        <el-card class="mt-20">
          <el-table
            v-loading="loading"
            :data="tableData"
            style="width: 100%"
            class="mt-4"
            element-loading-text="加载中..."
          >
            <el-table-column prop="investigateCode" label="调查编号" width="120" />
            <el-table-column prop="title" label="调查标题" min-width="200" />
            <el-table-column prop="investigateType" label="调查类型" width="120">
              <template #default="scope">
                {{ formatInvestigateType(scope.row.investigateType) }}
              </template>
            </el-table-column>
            <el-table-column prop="level" label="优先级" width="100">
              <template #default="scope">
                <el-tag
                  :type="formatLevel(scope.row.level).type"
                  size="small"
                >
                  {{ formatLevel(scope.row.level).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="investigateSource" label="来源" width="120">
              <template #default="scope">
                {{ formatInvestigateSource(scope.row.investigateSource) }}
              </template>
            </el-table-column>
            <el-table-column prop="startDate" label="开始日期" width="120">
              <template #default="scope">
                {{ scope.row.startDate ? dayjs(scope.row.startDate).format('YYYY-MM-DD') : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="formatStatus(scope.row.status).type"
                  size="small"
                >
                  {{ formatStatus(scope.row.status).label }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dutyEmployeeName" label="负责人" width="120" />
            <el-table-column fixed="right" label="操作" width="150">
              <template #default="scope">
                <el-button
                  v-auth="['violationIssues/taskManagement/index/view']"
                  type="primary"
                  size="small"
                  @click="viewDetail(scope.row.id)"
                >
                  查看
                </el-button>
                <el-button
                  v-auth="['violationIssues/taskManagement/index/edit']"
                  type="warning"
                  size="small"
                  @click="goAddEdit(scope.row.id, 'edit')"
                >
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              :current-page="pagination.page + 1"
              :page-size="pagination.size"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
        <el-row v-if="false" :gutter="20" class="">
          <el-col :span="18">
            <el-card class="mt-20">
              <h3 class="mb-4 text-gray-800 font-medium" />
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    调查统计概览
                  </div>
                </div>
              </template>
              <div class="grid grid-cols-4 mb-4 gap-4">
                <div class="rounded-lg bg-blue-50 p-4">
                  <div class="mb-1 text-sm text-blue-800">
                    总调查数
                  </div>
                  <div class="text-2xl text-blue-600 font-bold">
                    24
                  </div>
                </div>
                <div class="rounded-lg bg-green-50 p-4">
                  <div class="mb-1 text-sm text-green-800">
                    已完成
                  </div>
                  <div class="text-2xl text-green-600 font-bold">
                    8
                  </div>
                </div>
                <div class="rounded-lg bg-blue-100 p-4">
                  <div class="mb-1 text-sm text-blue-800">
                    进行中
                  </div>
                  <div class="text-2xl text-blue-600 font-bold">
                    12
                  </div>
                </div>
                <div class="rounded-lg bg-yellow-50 p-4">
                  <div class="mb-1 text-sm text-yellow-800">
                    已暂停
                  </div>
                  <div class="text-2xl text-yellow-600 font-bold">
                    4
                  </div>
                </div>
              </div>
              <div class="h-64 flex items-center justify-center rounded-lg bg-gray-50">
                <div class="text-gray-400">
                  调查状态趋势图表
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="mt-20">
              <template #header>
                <div class="aic jcsb flex">
                  <div class="f-16 fw-600">
                    AI助手
                  </div>
                </div>
              </template>
              <div class="mb-4 space-y-3">
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-chart-line mr-2" />分析调查趋势
                </button>
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-exclamation-circle mr-2" />建议优先处理项
                </button>
                <button
                  class="!rounded-button w-full flex items-center whitespace-nowrap border border-blue-500 px-4 py-2 text-sm text-blue-500 hover:bg-blue-50"
                >
                  <i class="fas fa-calendar-alt mr-2" />生成工作计划
                </button>
              </div>
              <div class="rounded-lg bg-gray-50 p-3">
                <div class="mb-2 text-sm text-gray-700">
                  AI建议：
                </div>
                <div class="text-sm text-gray-600">
                  根据当前调查任务状态分析，建议优先处理市场部广告合规审查任务，该任务优先级高且截止日期临近。
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
    <!-- modulewidth="800px" -->
    <HDialog v-model="dialogVisible" :title="form.id ? '调查记录详情' : '调查记录详情'" modulewidth="1000px">
      <popMode v-if="dialogVisible" ref="dialogRef" />
      <template #footer>
        <div class="fotterbtn">
          <el-button class="cancel" @click="dialogVisible = false, form = {} ">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm">
            保存
          </el-button>
        </div>
      </template>
    </HDialog>
  </div>
</template>

<style scoped>
  /* 自定义样式 */
  .fixed {
    position: fixed;
  }
</style>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>
