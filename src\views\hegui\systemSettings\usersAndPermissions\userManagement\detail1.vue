<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Edit as ElIconEdit,
  Close as ElIconClose,
  Refresh as ElIconRefresh,
  // Success as ElIconSuccess,
  Lock as ElIconLock,
  Link as ElIconLink,
} from '@element-plus/icons-vue'

const router = useRouter()
const activeTab = ref('org')

const permissions = ref([
  { module: '合同管理', name: '合同审核', level: '读写' },
  { module: '合同管理', name: '合同归档', level: '读写' },
  { module: '合规培训', name: '培训管理', level: '读写' },
  { module: '合规培训', name: '考试管理', level: '读写' },
  { module: '风险管理', name: '风险评估', level: '只读' },
])

const operationLogs = ref([
  { date: '2024-04-26 09:30', content: '登录系统' },
  { date: '2024-04-25 16:20', content: '修改个人资料' },
  { date: '2024-04-25 14:45', content: '完成"反洗钱合规"培训' },
  { date: '2024-04-23 10:30', content: '提交合同审核申请' },
  { date: '2024-04-20 15:10', content: '创建了新的合规检查任务' },
])

const relatedPersons = ref([
  {
    name: '李明',
    relation: '直属上级',
    department: '法务部',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/5f10b8dbc3092a4961d1e92e673fc6c4.jpg',
  },
  {
    name: '王芳',
    relation: '同事',
    department: '法务部',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/4b5463fb56e277237643f3f69e426190.jpg',
  },
  {
    name: '赵伟',
    relation: 'IT支持',
    department: '信息技术部',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/d79cc2d351756479285b2f72862f83db.jpg',
  },
])
function handleAdd() {
  router.push({
    path: '/systemSettings/usersAndPermissions/userManagement/addEdit',
  })
}
</script>

<template>
  <div class="absolute-container" style="">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              权限设置
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" @click="handleAdd">
              <el-icon class="mr-1">
                <plus />
              </el-icon>
              新增用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <upload />
              </el-icon>
              导入用户
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <download />
              </el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <div class="min-h-screen flex">
          <!-- 主内容区 -->
          <div class="flex flex-1 flex-col">
            <!-- 主内容 -->
            <div class="flex-1 overflow-auto bg-gray-100 p-6">
              <div class="mb-6 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <h2 class="text-xl font-bold">
                    用户详情 - 张三
                  </h2>
                  <el-tag type="success" effect="dark" class="!rounded-button">
                    启用
                  </el-tag>
                </div>
                <div class="flex space-x-2">
                  <el-button type="primary" class="!rounded-button whitespace-nowrap">
                    <el-icon><ElIconEdit /></el-icon>
                    <span>编辑</span>
                  </el-button>
                  <el-button type="danger" class="!rounded-button whitespace-nowrap">
                    <el-icon><ElIconClose /></el-icon>
                    <span>禁用</span>
                  </el-button>
                  <el-button type="warning" class="!rounded-button whitespace-nowrap">
                    <el-icon><ElIconRefresh /></el-icon>
                    <span>重置密码</span>
                  </el-button>
                </div>
              </div>

              <div class="grid grid-cols-4 gap-6">
                <!-- 左侧内容区 -->
                <div class="col-span-3 space-y-6">
                  <!-- 基本信息卡片 -->
                  <el-card shadow="hover" class="!border-none">
                    <div class="flex">
                      <el-avatar
                        :size="80" class="mr-6"
                        src="https://ai-public.mastergo.com/ai/img_res/18bc8e6314850787d28ffa40adbef392.jpg"
                      >
                        ZS
                      </el-avatar>
                      <div class="grid grid-cols-2 flex-1 gap-y-4">
                        <div class="flex">
                          <span class="w-24 text-gray-500">用户名</span>
                          <span>zhangsan</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">姓名</span>
                          <span>张三</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">手机号码</span>
                          <span>139****5678</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">邮箱地址</span>
                          <span><EMAIL></span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">性别</span>
                          <span>男</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">状态</span>
                          <span>启用</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">员工编号</span>
                          <span>EMP2024001</span>
                        </div>
                        <div class="flex">
                          <span class="w-24 text-gray-500">入职日期</span>
                          <span>2023-10-15</span>
                        </div>
                      </div>
                    </div>
                  </el-card>

                  <!-- 标签页 -->
                  <el-card shadow="hover" class="!border-none">
                    <el-tabs v-model="activeTab">
                      <el-tab-pane label="组织信息" name="org">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              组织与岗位信息
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">所属部门</span>
                                <span>法务部</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">岗位</span>
                                <span>合规专员</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">直属上级</span>
                                <span>法务总监（李明）</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">职级</span>
                                <span>P5</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              联系信息
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">办公电话</span>
                                <span>010-88888888-1234</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">即时通讯</span>
                                <span><EMAIL></span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">办公地点</span>
                                <span>总部大楼5层</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">备注信息</span>
                                <span>负责合同审核与合规培训</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="角色权限" name="role">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              角色信息
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">用户类型</span>
                                <span>普通用户</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">所属角色</span>
                                <span>合规管理员</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">角色说明</span>
                                <span>负责系统内合规管理相关功能</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              权限详情
                            </h3>
                            <el-table :data="permissions" border style="width: 100%;">
                              <el-table-column prop="module" label="功能模块" width="180" />
                              <el-table-column prop="name" label="功能名称" width="180" />
                              <el-table-column prop="level" label="权限级别" />
                            </el-table>
                            <div class="mt-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">数据权限</span>
                                <span>本部门及下级部门数据</span>
                              </div>
                              <div class="mt-2 text-sm text-gray-500">
                                数据权限范围说明：可以查看和操作本部门及下级部门的所有合规相关数据，但不能访问其他平行部门的数据。
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="账号安全" name="security">
                        <div class="space-y-6">
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              账号安全信息
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">密码强度</span>
                                <el-tag type="success">
                                  强
                                </el-tag>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">密码最后修改时间</span>
                                <span>2024-03-15</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">密码过期时间</span>
                                <span>2024-06-15</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">账号创建时间</span>
                                <span>2023-10-15</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              登录信息
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">最近登录时间</span>
                                <span>2024-04-26 09:30</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">最近登录IP</span>
                                <span>*************</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">最近登录设备</span>
                                <span>Chrome/Windows</span>
                              </div>
                            </div>
                          </div>
                          <div>
                            <h3 class="mb-4 text-lg font-semibold">
                              登录限制
                            </h3>
                            <div class="grid grid-cols-2 gap-y-4">
                              <div class="flex">
                                <span class="w-24 text-gray-500">IP限制</span>
                                <span>无</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">时间限制</span>
                                <span>无</span>
                              </div>
                              <div class="flex">
                                <span class="w-24 text-gray-500">设备限制</span>
                                <span>无</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="操作记录" name="logs">
                        <div class="timeline">
                          <div v-for="(log, index) in operationLogs" :key="index" class="timeline-item">
                            <div class="timeline-dot" />
                            <div class="timeline-content">
                              <div class="timeline-date">
                                {{ log.date }}
                              </div>
                              <div class="timeline-card">
                                {{ log.content }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </el-card>
                </div>

                <!-- 右侧信息区 -->
                <div class="space-y-6">
                  <!-- 账号状态卡片 -->
                  <el-card shadow="hover" class="!border-none">
                    <div class="space-y-4">
                      <h3 class="text-lg font-semibold">
                        账号状态
                      </h3>
                      <div class="flex items-center space-x-3">
                        <el-icon color="#4CAF50" :size="24">
                          <el-icon-success />
                        </el-icon>
                        <span>账号状态正常</span>
                      </div>
                      <div class="text-sm text-gray-500">
                        该账号当前处于启用状态，可以正常登录系统并使用所有被授权的功能。
                      </div>
                    </div>
                  </el-card>

                  <!-- 快捷操作卡片 -->
                  <el-card shadow="hover" class="!border-none">
                    <div class="space-y-4">
                      <h3 class="text-lg font-semibold">
                        快捷操作
                      </h3>
                      <div class="space-y-2">
                        <el-button type="warning" class="!rounded-button w-full whitespace-nowrap">
                          <el-icon><ElIconRefresh /></el-icon>
                          <span>重置密码</span>
                        </el-button>
                        <el-button type="danger" class="!rounded-button w-full whitespace-nowrap">
                          <el-icon><ElIconLock /></el-icon>
                          <span>锁定账号</span>
                        </el-button>
                        <el-button type="info" class="!rounded-button w-full whitespace-nowrap">
                          <el-icon><ElIconLink /></el-icon>
                          <span>解除关联</span>
                        </el-button>
                      </div>
                    </div>
                  </el-card>

                  <!-- 相关人员卡片 -->
                  <el-card shadow="hover" class="!border-none">
                    <div class="space-y-4">
                      <h3 class="text-lg font-semibold">
                        相关人员
                      </h3>
                      <div class="space-y-4">
                        <div v-for="(person, index) in relatedPersons" :key="index" class="flex items-center space-x-3">
                          <el-avatar :size="40" :src="person.avatar" />
                          <div>
                            <div class="font-medium">
                              {{ person.name }}
                            </div>
                            <div class="text-sm text-gray-500">
                              {{ person.relation }} · {{ person.department }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .timeline {
    position: relative;
    padding-left: 24px;
  }

  .timeline-item {
    position: relative;
    padding-bottom: 20px;
  }

  .timeline-dot {
    position: absolute;
    top: 4px;
    left: -12px;
    z-index: 1;
    width: 8px;
    height: 8px;
    background-color: #409eff;
    border-radius: 50%;
  }

  .timeline-item:not(:last-child)::after {
    position: absolute;
    top: 12px;
    bottom: 0;
    left: -8px;
    width: 2px;
    content: "";
    background-color: #dcdfe6;
  }

  .timeline-content {
    position: relative;
  }

  .timeline-date {
    margin-bottom: 8px;
    font-size: 14px;
    color: #909399;
  }

  .timeline-card {
    padding: 12px;
    font-size: 14px;
    color: #606266;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
</style>
