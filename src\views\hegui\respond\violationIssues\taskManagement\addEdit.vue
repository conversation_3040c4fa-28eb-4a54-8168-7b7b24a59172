<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, reactive, ref, watch } from 'vue'
import { ElBreadcrumb, ElBreadcrumbItem, ElButton, ElCard, ElCheckbox, ElCheckboxGroup, ElDatePicker, ElForm, ElFormItem, ElInput, ElInputNumber, ElMessage, ElOption, ElRadio, ElRadioGroup, ElSelect, ElSwitch, ElTable, ElTableColumn, ElUpload } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Delete, Plus } from '@element-plus/icons-vue'
import DepartPerson from '@/components/departPerson/index.vue'
import problemTaskApi from '@/api/problemTask'
import dictApi from '@/api/modules/system/dict'
import { useTaskStore } from '@/store/modules/task'

const router = useRouter()
const route = useRoute()
const taskStore = useTaskStore()

// 表单引用
const formRef = ref()

// 定义涉及人员/部门的接口
// interface InvolveItem {
//   involve: string
//   involveId: string
//   remark: string
// }

// 涉及人员/部门列表
// const involveList = ref<InvolveItem[]>([
//   { involve: 'EMPLOYEE', involveId: '', remark: '' },
// ])

// 添加涉及对象
function addInvolve(): void {
  involveList.value.push({
    involve: 'EMPLOYEE', // 默认为人员类型
    involveId: '',
    remark: '',
  })
}

// 删除涉及对象
function removeInvolve(index: number): void {
  involveList.value.splice(index, 1)
}

// 自动生成编号标志
const autoGenerateCode = ref(true)

// 定义表单数据接口
interface FormData {
  id: any
  title: string
  investigateCode?: string
  investigateType: string
  investigateSource: string
  level: string
  startDate: string
  finishDate: string
  dutyEmployeeId: string
  dutyEmployeeName: string
  coordinateEmployeeId: string
  coordinateEmployeeName: string
  status: string
  investigateBackground: string
  investigateTarget: string
  investigateRange: string
  [key: string]: any // 允许动态删除属性
}

// 表单数据，对应接口字段
const formData = ref<FormData>({
  // 基本字段
  id: null,
  title: '', // 调查标题
  investigateCode: '', // 调查编号
  investigateType: '', // 调查类型
  investigateSource: '', // 调查来源
  level: 'MIDDLE', // 优先级
  startDate: '', // 开始日期
  finishDate: '', // 完成日期
  dutyEmployeeId: '', // 负责人ID
  dutyEmployeeName: '', // 负责人姓名
  coordinateEmployeeId: '', // 协调人ID
  coordinateEmployeeName: '', // 协调人姓名
  status: 'PROGRESSING', // 调查状态
  investigateBackground: '', // 调查背景
  investigateTarget: '', // 调查目标
  investigateRange: '', // 调查范围
  // violationDetailId: '', // 关联来源ID
})

// 调查类型选项 - 字典34
const investigateTypeOptions = ref([
])

// 调查来源选项 - 字典33
const investigateSourceOptions = ref([
])

// 优先级选项
const levelOptions = [
  { label: '高', value: 'HIGH' },
  { label: '中', value: 'MIDDLE' },
  { label: '低', value: 'LOW' },
]

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'NOT_STARTED' },
  { label: '进行中', value: 'PROGRESSING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已暂停', value: 'PAUSED' },
  { label: '已取消', value: 'CANCELLED' },
]

// 表单校验规则
const formRules = {
  title: [
    { required: true, message: '请输入调查标题', trigger: 'blur' },
    { min: 1, max: 100, message: '调查标题长度应在1-100个字符之间', trigger: 'blur' },
  ],
  investigateType: [
    { required: true, message: '请选择调查类型', trigger: 'change' },
  ],
  level: [
    { required: true, message: '请选择优先级', trigger: 'change' },
  ],
  investigateSource: [
    { required: true, message: '请选择调查来源', trigger: 'change' },
  ],
  dutyEmployeeId: [
    { required: true, message: '请选择负责人', trigger: 'change' },
  ],
  investigateBackground: [
    { required: true, message: '请输入调查背景', trigger: 'blur' },
    { min: 1, max: 1000, message: '调查背景长度应在1-1000个字符之间', trigger: 'blur' },
  ],
  investigateTarget: [
    { required: true, message: '请输入调查目标', trigger: 'blur' },
    { min: 1, max: 1000, message: '调查目标长度应在1-1000个字符之间', trigger: 'blur' },
  ],
  investigateRange: [
    { required: true, message: '请输入调查范围', trigger: 'blur' },
    { min: 1, max: 1000, message: '调查范围长度应在1-1000个字符之间', trigger: 'blur' },
  ],
}

// 获取调查编号
async function fetchInvestigateCode() {
  try {
    if (autoGenerateCode.value) {
      const response = await dictApi.getCode('INVESTIGATE_TASK')
      if (response) {
        formData.value.investigateCode = response
      }
    }
  }
  catch (error) {
    console.error('获取调查编号失败:', error)
    ElMessage.error('获取调查编号失败')
  }
}

// 提交表单
async function submitForm(): Promise<void> {
  try {
    // 表单验证
    if (!formRef.value) { return }
    const valid = await formRef.value.validate()
    if (!valid) {
      ElMessage.error('请填写完整的表单信息')
      return
    }

    // 处理涉及人员/部门数据
    const submitData = { ...formData.value }
    // 如果是从举报页面跳转过来的，添加关联的举报ID
    if (route.query.reportId) {
      // submitData.violationDetailId = route.query.reportId
    }

    // submitData.involveList = involveList.value.map(item => ({
    //   involve: item.involve,
    //   involveId: item.involveId,
    //   remark: item.remark,
    // }))
    // 发送请求
    let response
    if (formData.value.id) {
      // 更新任务
      response = await problemTaskApi.updateTask(formData.value.id, submitData)
    }
    else {
      // 创建任务
      response = await problemTaskApi.createOrUpdateTask(submitData)
    }

    if (response) {
      ElMessage.success('保存成功')

      // 如果是从举报页面跳转过来的，将任务ID存储到 Pinia store 中
      if (route.query.type === 'report') {
        const taskId = response.id || response
        console.log('设置 taskId 到 store:', taskId)
        console.log('route.query:', route.query)
        taskStore.setTaskId(taskId)
        console.log('store 中的 taskId:', taskStore.currentTaskId)
      }

      // 返回上级页面
      router.back()
    }
    else {
      ElMessage.error('保存失败')
    }
  }
  catch (error: any) {
    console.error('提交表单出错:', error)
  }
}

// 保存并开始调查
async function saveAndStart(): Promise<void> {
  try {
    // 表单验证
    if (!formRef.value) { return }
    const valid = await formRef.value.validate()
    if (!valid) {
      ElMessage.error('请填写完整的表单信息')
      return
    }

    formData.value.status = 'PROGRESSING'

    // 处理涉及人员/部门数据
    const submitData = { ...formData.value }
    // 如果是从举报页面跳转过来的，添加关联的举报ID
    if (route.query.reportId) {
      // submitData.violationDetailId = route.query.reportId
    }

    // 发送请求
    let response
    if (formData.value.id) {
      // 更新任务
      response = await problemTaskApi.updateTask(formData.value.id, submitData)
    }
    else {
      // 创建任务
      response = await problemTaskApi.createOrUpdateTask(submitData)
    }

    if (response) {
      ElMessage.success('保存成功，调查已开始')

      // 如果是从举报页面跳转过来的，将任务ID存储到 Pinia store 中
      if (route.query.type === 'report') {
        const taskId = response.id || response
        console.log('saveAndStart - 设置 taskId 到 store:', taskId)
        console.log('saveAndStart - route.query:', route.query)
        taskStore.setTaskId(taskId)
        console.log('saveAndStart - store 中的 taskId:', taskStore.currentTaskId)
      }

      // 返回上级页面
      router.back()
    }
    else {
      ElMessage.error('保存失败')
    }
  }
  catch (error: any) {
    console.error('保存并开始调查出错:', error)
    ElMessage.error(`操作失败: ${error.message || error}`)
  }
}

// 取消
function cancel(): void {
  router.back()
}

// 从字典获取调查类型和调查来源选项
async function fetchDictOptions() {
  try {
    // 获取调查类型选项 (34)
    const typeResponse = await dictApi.dictAll(34)
    if (typeResponse) {
      investigateTypeOptions.value = typeResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }

    // 获取调查来源选项 (33)
    const sourceResponse = await dictApi.dictAll(33)
    if (sourceResponse) {
      investigateSourceOptions.value = sourceResponse.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
    ElMessage.error('获取字典数据失败')
  }
}

// 监听自动生成编号标志变化
watch(autoGenerateCode, (newValue) => {
  if (newValue) {
    fetchInvestigateCode()
  }
})

// 初始化数据
onMounted(async () => {
  // 获取字典数据
  await fetchDictOptions()

  const id = route.query.id
  if (id) {
    // 确保 id 是字符串类型
    const taskId = id
    // 获取任务详情的逻辑
    problemTaskApi.getTaskDetail(taskId).then((res: { code: number, data: any, message?: string }) => {
      if (res) {
        const taskData = res
        formData.value = { ...taskData }
        // involveList.value = taskData.involveList || []
        autoGenerateCode.value = false
      }
    }).catch((error: any) => {
      console.error('获取任务详情失败:', error)
      ElMessage.error(`获取任务详情失败: ${error.response?.data?.message || error.message}`)
    })
  }
  else {
    // 新增模式：如果自动生成编号已启用，则获取调查编号
    if (autoGenerateCode.value) {
      await fetchInvestigateCode()
    }
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <!-- 顶部导航 -->
    <div class="bg-white shadow-sm">
      <div class="mx-auto flex items-center justify-between px-6 py-4 container">
        <div class="flex items-center space-x-4">
          <ElBreadcrumb separator="/">
            <ElBreadcrumbItem>应对之翼</ElBreadcrumbItem>
            <ElBreadcrumbItem>违规问题调查</ElBreadcrumbItem>
            <ElBreadcrumbItem>新增调查任务</ElBreadcrumbItem>
          </ElBreadcrumb>
        </div>
        <div class="flex items-center space-x-3">
          <ElButton type="primary" class="!rounded-button whitespace-nowrap" @click="submitForm">
            保存
          </ElButton>
          <ElButton class="!rounded-button whitespace-nowrap" @click="saveAndStart">
            保存并开始调查
          </ElButton>
          <ElButton class="!rounded-button whitespace-nowrap">
            预览
          </ElButton>
          <ElButton class="!rounded-button whitespace-nowrap" @click="cancel">
            取消
          </ElButton>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="mx-auto flex px-6 py-6 container">
      <!-- 左侧表单区 -->
      <div class="w-3/4 pr-6">
        <!-- 表单容器 -->
        <ElForm ref="formRef" label-position="right" label-width="120px" :model="formData" :rules="formRules">
          <!-- 基本信息卡片 -->
          <ElCard class="mb-6">
            <div class="mb-4 text-lg font-bold">
              基本信息
            </div>
            <ElFormItem label="调查标题" prop="title" required>
              <ElInput v-model="formData.title" placeholder="请输入调查标题" />
            </ElFormItem>
            <ElFormItem label="调查编号">
              <ElInput v-model="formData.investigateCode" placeholder="自动生成" readonly />
              <!-- <ElCheckbox v-model="autoGenerateCode" class="ml-2">
                  自动生成
                </ElCheckbox>
                <ElButton v-if="autoGenerateCode" class="ml-2" type="primary" size="small" @click="() => fetchInvestigateCode()">
                  刷新编号
                </ElButton> -->
            </ElFormItem>
            <ElFormItem label="调查类型" prop="investigateType" required>
              <ElSelect v-model="formData.investigateType" placeholder="请选择调查类型" class="w-full">
                <ElOption
                  v-for="option in investigateTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="优先级" prop="level" required>
              <ElRadioGroup v-model="formData.level">
                <el-radio-button v-for="option in levelOptions" :key="option.value" :label="option.value">
                  {{ option.label }}
                </el-radio-button>
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="调查来源" prop="investigateSource" required>
              <ElSelect v-model="formData.investigateSource" placeholder="请选择调查来源" class="w-full">
                <ElOption
                  v-for="option in investigateSourceOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </ElSelect>
            </ElFormItem>
            <!-- <ElFormItem label="关联来源">
              <div class="flex items-center">
                <ElInput v-model="formData.violationDetailId" placeholder="请输入关联编号" />
                <ElButton class="!rounded-button ml-2 whitespace-nowrap">
                  查找
                </ElButton>
              </div>
            </ElFormItem> -->
            <ElFormItem label="开始日期">
              <ElDatePicker v-model="formData.startDate" type="date" placeholder="选择日期" class="w-full" value-format="YYYY-MM-DD" />
            </ElFormItem>
            <ElFormItem label="计划完成日期">
              <ElDatePicker v-model="formData.finishDate" type="date" placeholder="选择日期" class="w-full" value-format="YYYY-MM-DD" />
            </ElFormItem>
            <ElFormItem label="负责人" prop="dutyEmployeeId" required>
              <DepartPerson
                v-model="formData.dutyEmployeeId"
                placeholder="请选择负责人"
                @change="(employee) => { if (employee) formData.dutyEmployeeName = employee.name }"
              />
            </ElFormItem>
            <ElFormItem label="协调人">
              <DepartPerson
                v-model="formData.coordinateEmployeeId"
                placeholder="请选择协调人"
                @change="(employee) => { if (employee) formData.coordinateEmployeeName = employee.name }"
              />
            </ElFormItem>
            <!-- <ElFormItem label="调查状态">
              <ElRadioGroup v-model="formData.status">
                <ElRadio v-for="option in statusOptions" :key="option.value" :label="option.value">
                  {{ option.label }}
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem> -->
          </ElCard>

          <!-- 调查内容卡片 -->
          <ElCard class="mb-6">
            <div class="mb-4 text-lg font-bold">
              调查内容
            </div>
            <ElFormItem label="调查背景" prop="investigateBackground" required>
              <ElInput v-model="formData.investigateBackground" type="textarea" :rows="4" placeholder="请输入调查背景和原因" />
            </ElFormItem>
            <ElFormItem label="调查目标" prop="investigateTarget" required>
              <ElInput v-model="formData.investigateTarget" type="textarea" :rows="4" placeholder="请输入调查的具体目标" />
            </ElFormItem>
            <ElFormItem label="调查范围" prop="investigateRange" required>
              <ElInput v-model="formData.investigateRange" type="textarea" :rows="4" placeholder="请输入调查的范围和限制" />
            </ElFormItem>
            <!-- <ElFormItem label="涉及人员/部门">
              <div v-for="(item, index) in involveList" :key="index" class="mb-2 flex items-center">
                <ElSelect v-model="item.involve" placeholder="选择类型" class="mr-2" style="width: 120px">
                  <ElOption label="人员" value="EMPLOYEE" />
                  <ElOption label="部门" value="DEPARTMENT" />
                </ElSelect>
                <ElInput v-model="item.involveId" placeholder="请输入ID" class="mr-2" style="width: 120px" />
                <ElInput v-model="item.remark" placeholder="备注" class="mr-2 flex-1" />
                <ElButton type="danger" circle @click="removeInvolve(index)">
                  <el-icon><Delete /></el-icon>
                </ElButton>
              </div>
              <ElButton type="primary" @click="addInvolve">
                <el-icon><Plus /></el-icon> 添加涉及人员/部门
              </ElButton>
            </ElFormItem> -->
          </ElCard>
        </ElForm>

        <!-- 调查计划卡片 -->
        <ElCard v-if="false" class="mb-6">
          <div class="mb-4 text-lg font-bold">
            调查计划
          </div>
          <ElForm label-position="right" label-width="120px">
            <ElFormItem label="调查方法">
              <ElCheckboxGroup>
                <ElCheckbox label="文件审阅" />
                <ElCheckbox label="人员访谈" />
                <ElCheckbox label="现场调查" />
                <ElCheckbox label="数据分析" />
                <ElCheckbox label="专家咨询" />
                <ElCheckbox label="其他" />
              </ElCheckboxGroup>
            </ElFormItem>
            <ElFormItem label="调查步骤">
              <ElTable :data="[]" border class="mb-4 w-full">
                <ElTableColumn prop="step" label="步骤序号" width="100" />
                <ElTableColumn prop="name" label="步骤名称" />
                <ElTableColumn prop="startDate" label="计划开始日期" width="120" />
                <ElTableColumn prop="endDate" label="计划完成日期" width="120" />
                <ElTableColumn prop="owner" label="负责人" />
                <ElTableColumn prop="desc" label="步骤描述" />
                <ElTableColumn label="操作" width="120">
                  <template #default>
                    <ElButton size="small" class="!rounded-button whitespace-nowrap">
                      上移
                    </ElButton>
                    <ElButton size="small" class="!rounded-button whitespace-nowrap">
                      下移
                    </ElButton>
                    <ElButton size="small" type="danger" class="!rounded-button whitespace-nowrap">
                      删除
                    </ElButton>
                  </template>
                </ElTableColumn>
              </ElTable>
              <ElButton class="!rounded-button whitespace-nowrap">
                添加步骤
              </ElButton>
            </ElFormItem>
            <ElFormItem label="资源需求">
              <ElInput type="textarea" :rows="4" placeholder="请输入调查所需的资源支持" />
            </ElFormItem>
            <ElFormItem label="风险评估">
              <ElInput type="textarea" :rows="4" placeholder="请输入调查可能面临的风险和应对措施" />
            </ElFormItem>
          </ElForm>
        </ElCard>

        <!-- 相关信息卡片 -->
        <ElCard v-if="false" class="mb-6">
          <div class="mb-4 text-lg font-bold">
            相关信息
          </div>
          <ElForm label-position="right" label-width="120px">
            <ElFormItem label="关联制度">
              <div class="w-full">
                <ElButton class="!rounded-button mb-2 whitespace-nowrap">
                  添加制度
                </ElButton>
                <ElTable :data="[]" border class="w-full">
                  <ElTableColumn prop="name" label="制度名称" />
                  <ElTableColumn prop="version" label="版本号" width="120" />
                  <ElTableColumn label="操作" width="80">
                    <template #default>
                      <ElButton size="small" type="danger" class="!rounded-button whitespace-nowrap">
                        删除
                      </ElButton>
                    </template>
                  </ElTableColumn>
                </ElTable>
              </div>
            </ElFormItem>
            <ElFormItem label="关联法规">
              <div class="w-full">
                <ElButton class="!rounded-button mb-2 whitespace-nowrap">
                  添加法规
                </ElButton>
                <ElTable :data="[]" border class="w-full">
                  <ElTableColumn prop="name" label="法规名称" />
                  <ElTableColumn prop="clause" label="条款" width="120" />
                  <ElTableColumn label="操作" width="80">
                    <template #default>
                      <ElButton size="small" type="danger" class="!rounded-button whitespace-nowrap">
                        删除
                      </ElButton>
                    </template>
                  </ElTableColumn>
                </ElTable>
              </div>
            </ElFormItem>
            <ElFormItem label="关联系统">
              <div class="w-full">
                <ElButton class="!rounded-button mb-2 whitespace-nowrap">
                  添加系统
                </ElButton>
                <ElTable :data="[]" border class="w-full">
                  <ElTableColumn prop="name" label="系统名称" />
                  <ElTableColumn prop="module" label="模块" width="120" />
                  <ElTableColumn label="操作" width="80">
                    <template #default>
                      <ElButton size="small" type="danger" class="!rounded-button whitespace-nowrap">
                        删除
                      </ElButton>
                    </template>
                  </ElTableColumn>
                </ElTable>
              </div>
            </ElFormItem>
            <ElFormItem label="附件上传">
              <div class="w-full">
                <ElUpload action="#" class="mb-2">
                  <ElButton type="primary" class="!rounded-button whitespace-nowrap">
                    上传附件
                  </ElButton>
                </ElUpload>
                <ElTable :data="[]" border class="w-full">
                  <ElTableColumn prop="name" label="文件名" />
                  <ElTableColumn prop="size" label="大小" width="120" />
                  <ElTableColumn prop="time" label="上传时间" width="180" />
                  <ElTableColumn label="操作" width="150">
                    <template #default>
                      <ElButton size="small" class="!rounded-button whitespace-nowrap">
                        预览
                      </ElButton>
                      <ElButton size="small" type="danger" class="!rounded-button whitespace-nowrap">
                        删除
                      </ElButton>
                    </template>
                  </ElTableColumn>
                </ElTable>
              </div>
            </ElFormItem>
          </ElForm>
        </ElCard>

        <!-- 通知设置卡片 -->
        <ElCard v-if="false" class="mb-6">
          <div class="mb-4 text-lg font-bold">
            通知设置
          </div>
          <ElForm label-position="right" label-width="120px">
            <ElFormItem label="通知对象">
              <ElSelect multiple placeholder="请选择通知对象" class="w-full">
                <ElOption label="张明 (合规部)" value="1" />
                <ElOption label="李华 (审计部)" value="2" />
                <ElOption label="王强 (法务部)" value="3" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="通知方式">
              <ElCheckboxGroup>
                <ElCheckbox label="系统消息" />
                <ElCheckbox label="电子邮件" />
                <ElCheckbox label="短信" />
                <ElCheckbox label="其他" />
              </ElCheckboxGroup>
            </ElFormItem>
            <ElFormItem label="重要更新通知">
              <ElCheckboxGroup>
                <ElCheckbox label="调查开始" />
                <ElCheckbox label="新增证据" />
                <ElCheckbox label="调查发现" />
                <ElCheckbox label="调查结论" />
                <ElCheckbox label="调查完成" />
              </ElCheckboxGroup>
            </ElFormItem>
            <ElFormItem label="自动提醒">
              <div class="flex items-center">
                <ElSwitch />
                <span class="ml-2">调查到期前</span>
                <ElInputNumber :min="1" :max="30" class="ml-2 w-20" />
                <span class="ml-2">天提醒</span>
              </div>
            </ElFormItem>
          </ElForm>
        </ElCard>

        <!-- 权限设置卡片 -->
        <ElCard v-if="false">
          <div class="mb-4 text-lg font-bold">
            权限设置
          </div>
          <ElForm label-position="right" label-width="120px">
            <ElFormItem label="查看权限">
              <ElRadioGroup>
                <ElRadio label="仅调查团队" />
                <ElRadio label="所有合规人员" />
                <ElRadio label="指定人员" />
              </ElRadioGroup>
              <ElSelect v-if="false" multiple placeholder="请选择查看人员" class="mt-2 w-full">
                <ElOption label="张明 (合规部)" value="1" />
                <ElOption label="李华 (审计部)" value="2" />
                <ElOption label="王强 (法务部)" value="3" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="编辑权限">
              <ElRadioGroup>
                <ElRadio label="仅负责人" />
                <ElRadio label="调查团队" />
                <ElRadio label="指定人员" />
              </ElRadioGroup>
              <ElSelect v-if="false" multiple placeholder="请选择编辑人员" class="mt-2 w-full">
                <ElOption label="张明 (合规部)" value="1" />
                <ElOption label="李华 (审计部)" value="2" />
                <ElOption label="王强 (法务部)" value="3" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="保密级别">
              <ElRadioGroup>
                <ElRadio label="普通" />
                <ElRadio label="保密" />
                <ElRadio label="高度保密" />
              </ElRadioGroup>
            </ElFormItem>
          </ElForm>
        </ElCard>
      </div>

      <!-- 右侧辅助区 -->
      <div class="w-1/4">
        <!-- 任务模板卡片 -->
        <ElCard class="mb-6">
          <div class="mb-4 text-lg font-bold">
            任务模板
          </div>
          <div class="space-y-3">
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                常规合规调查模板
              </div>
              <div class="text-sm text-gray-500">
                适用于一般合规违规调查
              </div>
            </div>
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                财务专项调查模板
              </div>
              <div class="text-sm text-gray-500">
                适用于财务违规调查
              </div>
            </div>
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                信息安全事件模板
              </div>
              <div class="text-sm text-gray-500">
                适用于信息安全事件调查
              </div>
            </div>
          </div>
          <ElButton class="!rounded-button mt-4 w-full whitespace-nowrap">
            保存为模板
          </ElButton>
        </ElCard>

        <!-- AI辅助卡片 -->
        <ElCard class="mb-6">
          <div class="mb-4 text-lg font-bold">
            AI辅助
          </div>
          <div class="space-y-3">
            <ElButton class="!rounded-button w-full whitespace-nowrap">
              AI生成调查计划
            </ElButton>
            <ElButton class="!rounded-button w-full whitespace-nowrap">
              AI风险评估
            </ElButton>
            <ElButton class="!rounded-button w-full whitespace-nowrap">
              AI建议资源
            </ElButton>
          </div>
          <div class="mt-4 rounded bg-gray-50 p-3">
            <div class="text-sm text-gray-700">
              AI建议：根据当前调查内容，建议优先采用文件审阅和人员访谈相结合的方式，重点关注近3个月的财务数据...
            </div>
          </div>
        </ElCard>

        <!-- 相似案例卡片 -->
        <ElCard>
          <div class="mb-4 text-lg font-bold">
            相似案例
          </div>
          <div class="space-y-3">
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                2023年财务违规调查
              </div>
              <div class="flex justify-between text-sm text-gray-500">
                <span>已完成</span>
                <span>相似度85%</span>
              </div>
            </div>
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                2022年合同违规调查
              </div>
              <div class="flex justify-between text-sm text-gray-500">
                <span>已完成</span>
                <span>相似度72%</span>
              </div>
            </div>
            <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
              <div class="font-medium">
                2023年信息安全事件
              </div>
              <div class="flex justify-between text-sm text-gray-500">
                <span>进行中</span>
                <span>相似度68%</span>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 z-10 flex justify-center border-t bg-white p-4 space-x-4">
      <ElButton type="primary" size="large" @click="submitForm">
        保存
      </ElButton>
      <ElButton type="success" size="large" @click="saveAndStart">
        保存并开始调查
      </ElButton>
      <ElButton size="large" @click="cancel">
        取消
      </ElButton>
    </div>
  </div>
</template>

<style scoped>
.container {
  max-width: 1440px;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-input,
.el-select,
.el-date-editor {
  width: 100%;
}

.el-textarea {
  width: 100%;
}

.el-table {
  width: 100%;
  margin-bottom: 12px;
}

.el-card {
  margin-bottom: 24px;
}

.el-radio-group,
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.el-radio-button,
.el-checkbox {
  margin-right: 8px;
}
</style>
