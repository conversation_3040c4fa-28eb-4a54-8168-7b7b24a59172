<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemApi from '@/api/complianceApi/one/systemManagement'

// 级联选择器配置
const props1 = {
  children: 'children',
  label: 'label',
  value: 'value',
}

// 发布机构列表
const groupList: any = ref([])

// 级联选择器变化处理
function changegroup(value: any) {
}
const paging: any = ref({
  page: 1,
  limit: 10,
  total: 0,
})
const dataList: any = ref([])

// 搜索参数
const searchParams: any = ref({
  regulationName: null,
  status: null,
})

// 状态映射
const statusMap: any = {
  PASS: '通过',
  CONDITIONALPASS: '有条件通过',
  NOPASS: '不通过',
  REVIEWING: '待审查',
  EXPIRED: '过期',
}

// 审查类型映射
const auditTypeMap: any = {
  CONVENTION: '常规审查',
  EMERGENCY: '紧急审查',
}

// 审核报告弹窗相关
const reportDialogVisible = ref(false)
const reportData = ref<any>(null)
const reportLoading = ref(false)

const router = useRouter()

// 获取审核记录列表
async function getList() {
  try {
    reportLoading.value = true
    const params = {
      ...searchParams.value,
    }
    const page = {
      page: paging.value.page,
      limit: paging.value.limit,
    }
    const res = await systemApi.reviewSystem(params, 'list', page)
    // 处理数据，添加文本映射
    dataList.value = (res.content || []).map(item => ({
      ...item,
      statusText: statusMap[item.status] || item.status,
      auditTypeText: auditTypeMap[item.auditType] || item.auditType,
      deadlineTimeText: item.deadlineTime,
    }))
    paging.value.total = res.totalElements || 0
    reportLoading.value = false
  }
  catch (error) {
    console.error('获取审核记录列表失败:', error)
    ElMessage.error('获取审核记录列表失败')
    reportLoading.value = false
  }
}

// 分页变化
function pagChange(params: { page: number, limit: number }) {
  paging.value.page = params.page
  paging.value.limit = params.limit
  getList()
}

// 搜索
function handleSearch() {
  paging.value.page = 1
  getList()
}

// 重置搜索
function handleReset() {
  searchParams.value = {
    regulationName: null,
    status: null,
  }
  paging.value.page = 1
  getList()
}

async function goReport(item: { id: string | number, status: string, regulationName: string }) {
  try {
    reportLoading.value = true
    reportDialogVisible.value = true
    const res = await systemApi.reviewDetail(item.id)
    if (res) {
      reportData.value = res
      reportData.value.status = item.status
      reportData.value.regulationName = item.regulationName
    }
    else {
      ElMessage.error('获取审核报告失败')
      reportDialogVisible.value = false
    }
  }
  catch (error) {
    console.error('获取审核报告失败:', error)
    ElMessage.error('获取审核报告失败')
    reportDialogVisible.value = false
  }
  finally {
    reportLoading.value = false
  }
}

// 关闭审核报告弹窗
function closeReportDialog() {
  reportDialogVisible.value = false
  reportData.value = null
}

// 删除审核记录
function deleteRecord(item: any) {
  ElMessageBox.confirm(
    '确定要删除这条审核记录吗？删除后将无法恢复。',
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    },
  ).then(async () => {
    try {
      await systemApi.reviewSystem({ id: item.id }, 'delete')
      ElMessage.success('删除成功')
      getList()
    }
    catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 页面加载时获取数据
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="absolute-container">
    <PageMain style="background-color: transparent;">
      <el-row v-if="false" :gutter="20">
        <el-col v-for="i, j in 4" :key="j" :span="6">
          <div class="card jcsb flex p-16">
            <div class="fdc flex flex-1">
              <div class="f-14 aic mt-10 flex fw-400">
                <img
                  v-if="j === 0" style="width: 20px;height: 20px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAc9JREFUOE+tlE1OG0EQhd/rUSZGSpCXIWNb9g3MCYJPQHKCwAmADT8rzAIxzibOCZgbwA3MDTAncMcaRyxnayymop4fg+dHYwl6N62qb15Vvyqi5NT7k/rmRm1X5LlLoG7CBAggeFjAuns829JFqcxeGtCnDftAAYeQGFR0BPAWoi6y4BWg88vvWiFvALTLQKv31EqFP/RxY5zeL4ERTDgqUTUWMCBkJ/cjIlCUXgqNgF+uJm2b9qhIGcG7v6dfeyauNZjdQ9DNq6dWH+fb+qgTRMCmO7smsFfc5Bdg0/VHBPMqTSLRn544F0zUTcp69lphBTBQ9lOHrYH/E0LvzUBjK5F9Nl3/N8HD9wCCHLLl+tcAC/sXt2bNHsaKvEogiPH0xNk20e2rSTsscUNSoVdZMjI+M5P0uWYbV3zPe9KUXPEoy6TEFua74f47UJBhFhg9SrQEaraxTencLseKvA2VeAzhFU2UEtWJjN0YzM6VoL/e/JZGedNTZz8CJirv118KWSi1Evb02ZZ+WQ6XfteyaOa5svQVXNFySAOcGPo+6yuFJl47R8myiOKIACH+qPnTUPc7wWvFuY29AlYfvsXrikkbJBDBgzVf3GZBad5/6SvGvVFP4TQAAAAASUVORK5CYII="
                  alt=""
                >
                <img
                  v-if="j === 1" style="width: 20px;height: 20px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAASdJREFUOE/VlMFRw0AMRZ8yA2e3QAd0QFIBNXBgSE6ECjAdJDfDBVNBoIK4hHQQl5ArDEHMrr2OEzTZPTETnbwj6fv/L+0KkdCCeyD3ZcJM7ng61iLHklpwDbwDVVs3RBnJpDv/ad8D1BeuUIZdVfPtQC44Q/mm9uDSAxywkls+Qk8H2GNjkd4xtLI91jvAZx7R1quYsYd5IQ/eni5gifKGsAA2CKXpQrAoKlnIOWfOJ2uEGmFq7EfG1q+U28+T9/BQ8g8PpofCMk2yMGNAxbYdhjvbCx3u+P97uAFW/h6nRHTKyo1MKLVgDdQyZmQqLtBUDx27CvX7F9juYwoZymUaYIrMfo0puXmZ7WnGfzCVMfPm0rShr2R8sexkxEFChbPGee4eX34BcPaWFcHRx5sAAAAASUVORK5CYII="
                  alt=""
                >
                <img
                  v-if="j === 2" style="width: 20px;height: 20px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAVZJREFUOE+t1EFOwkAUBuD/USEKqPUGPUIX0rhTgiTuwBMgN8EjeAN6AutagW6BhdxAOAHGYGjadJ6ZIlikkmns7JrMfPO/NzMlZDwoYw+0qFktYhi+8O0zdzL97wYSZIkQo1Pqj+5VwEW9Ymo4OI3P9YLlTAZKDX5enzeYc87vjQnklHrD29Tg8ubCEIEYMGBso+SWe8Pqn+BHzWpqQCsE7JPeaCuRLBmCXpXBZc2SUHezgLld7o+j7/mVqee1wgCAqQTuYOtVzO1ABE4cI2ACQF+Vn1CyvDp5rfC2NgjUZfDdT1JMQaveSawYFqveoaeHgXhkwtPxy6iz1UNf+A+bBN9lJiVeY+S67zunnXQPZdPLz2NZTjTi6D4sSq56seUmOYHLo7BkJyXbtEkVVHlBqRKmBlUX7J3HkG+5MgdIzwSMDqVeMSmkRhYgg2fZ/2CzSBY3vgDJQtKX38w3wQAAAABJRU5ErkJggg=="
                  alt=""
                >
                <img
                  v-if="j === 3" style="width: 20px;height: 20px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAABQwhVRxBlRxBlSxBlRxBlRxBlTxBtRxRlSxBpRxBlQxxhQvxhSwxpSxRpQxBhRwhpSxBtRxxhSxBpHtlq3AAAAE3RSTlMAEI8/38+/vzDvgCAg739QoHBfK2xoZQAAALNJREFUOMu9kkkOwyAMRQ04zEna/vvftRJRatlCZZe3QnoWHulZQo0M7NHnuY74EQNZnIfCO+M3GDZn/d8IjwlefMAUqTQaU1obvdw+w3BSV1/YCip1Ho9DMmhPoynJUZRP8iXThfLcqOJGBbRWLn/CBlwVdcoFyNR3aVcVyZ0+eI8GpJyLAwN2lMnxZNhB1qNnku2oX1RVw8tlrde9Ppj1ya2PVghJdAo0I/tUAE410KN8AU9ZIuaixP0wAAAAAElFTkSuQmCC"
                  alt=""
                >
                <span class="f-16 ml-6 c-[#4B5563] fw-400">待审查任务</span>
              </div>
              <div class="aic jcsb mt-12 flex flex-1">
                <span class="f-24 c-[#111827] fw-700"> 12</span>
                <span class="f-16 ml-6 c-[#22C55E] fw-400">待审查任务</span>
              </div>
              <!-- <div class="f-14 c-[#4B5563]">
                待审查任务
              </div>
              <div class="f-24 fw-700 mt-16">
                128
              </div>

              <div v-else class="f-14 fw-400 mt-10 flex aic">
                <img style="width: 14px;height: 14px;"
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAL9JREFUOE+90dENgjAUBdD7yBtAov/iBDoCsQ2/4ga4gW6gEzgCOoEDtMG4ASM4gEkXaMDQiInoR8XEfve8+3pL6Hmop8Nv8DafZ6OiOHyT7hKNEDmIrqHWO1/8XLWLTRxHYM4BlKHWm+7Alze2GNYewXwC0IC4IoqGSmVuOykXsPbyVo5Jkj3qOgWwDJUqH5e3LTZSnsG88m7VSOlwUNdjb+jWC4IBqmoNYAbmiVeiESIF0bQtqGnfC376ov/DOxDLPmuyNMqVAAAAAElFTkSuQmCC"
                  alt="" />
                <span class="f-14 c-[#EF4444] ml-6">128</span>
              </div> -->
            </div>
            <!-- <div>
              <img v-if="j==0" style="width: 32px;height: 32px;"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgAgMAAAAOFJJnAAAADFBMVEUAAAAYj/8Ykf8YkP+pTBVBAAAAA3RSTlMAgH8BTzA4AAAANklEQVQY02NAAtw/oAz+P8Qx9jdAGf8PkM7QfwBl2H8gksH0D8pg/k8rBuNf9i/yF+od4GECAMK5alogF28rAAAAAElFTkSuQmCC"
                alt="" />
              <img v-if="j==1" style="width: 32px;height: 32px;"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAAD6rRT6qxP6qxD5rBT6rhT7rhT6rRP6rhT7rRT/rxD5rRT6rxX4rRT8rBP0rxX0qhX6rhT6rBT6rRSpzDs9AAAAE3RSTlMA318fQL9/n++PEK9vb08wMM/Pyv5s0wAAAJZJREFUOMvN0UsOgzAMRVE7Xwjl077977WkchnUjtUhd2IJzJFI6H6tXBs5BQAzOTHOogf0sgv0kgO4ROwAO0T5/EIaEhFnG1EfgYyqvOmD2wBAkFkGAB6yMDUNyJcHbCJ/nycIYZ9RlQV1Z3wtbJCiBYD3BVL+AXTJAMBhgSaiAHIOmii4eu4zNDFhUPp34Xix2Uo36Q1wZBX3HCYTeAAAAABJRU5ErkJggg=="
                alt="" />
              <img v-if="j==2" style="width: 32px;height: 32px;"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgBAMAAACBVGfHAAAAKlBMVEUAAABwKM9wMM9yLtFyLdJxLdFyLtFxLtBwMM9xLtFyLs9xLdJ0LM9yLtF8/bHUAAAADXRSTlMAICDfv1/vzxDPcE9AyHB/wgAAAGZJREFUKM9jIAKI3IWBQojAXLjAFYjAXQQgXkCBgQmEgQJgc8ESdwUYGEECV+ACYACkcApAzICyQfpBGC4AA3gEFEDOQTUDCIaWGQ25l4ECtgiBA8GpQIG1CAELsGGsCCWXNxAT9wB5c+51+TZEoQAAAABJRU5ErkJggg=="
                alt="" />
              <img v-if="j==3" style="width: 32px;height: 32px;"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAPFBMVEUAAABQwhVRxBlRxBlSxBlRxBlRxBlTxBtRxRlSxBpRxBlQxxhQvxhSwxpSxRpQxBhRwhpSxBtRxxhSxBpHtlq3AAAAE3RSTlMAEI8/38+/vzDvgCAg739QoHBfK2xoZQAAALNJREFUOMu9kkkOwyAMRQ04zEna/vvftRJRatlCZZe3QnoWHulZQo0M7NHnuY74EQNZnIfCO+M3GDZn/d8IjwlefMAUqTQaU1obvdw+w3BSV1/YCip1Ho9DMmhPoynJUZRP8iXThfLcqOJGBbRWLn/CBlwVdcoFyNR3aVcVyZ0+eI8GpJyLAwN2lMnxZNhB1qNnku2oX1RVw8tlrde9Ppj1ya2PVghJdAo0I/tUAE410KN8AU9ZIuaixP0wAAAAAElFTkSuQmCC"
                alt="" />
            </div> -->
          </div>
        </el-col>
      </el-row>
      <div class="card mt-10 flex p-16">
        <!-- <div class="aic jcsb flex">
          <div class="f-20 fw-600">
            制度新增与审查
          </div>
        </div> -->
        <div class="ml-14">
          <!-- <el-button v-auth="['systemAdditionAndReview/index/batchExport']" type="primary" plain>
            <svg-icon name="ep:download" />
            <span class="ml-4">批量导出</span>
          </el-button> -->
        </div>
        <div class="ml-32">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="名称:">
              <el-input v-model="searchParams.regulationName" clearable placeholder="搜索制度名称..." @clear="searchParams.title = ''" />
            </el-form-item>
            <!-- <el-form-item label="发布机构:">
              <el-cascader
                v-model="searchParams.group_id" :props="props1" clearable class="w-full" :options="groupList"
                @change="changegroup" @clear="searchParams.group_id = ''"
              />
            </el-form-item> -->
            <el-form-item label="全部状态" style="width: 200px;">
              <el-select v-model="searchParams.status" clearable placeholder="请选择状态" @clear="searchParams.status = ''">
                <el-option label="通过" value="PASS" />
                <el-option label="有条件通过" value="CONDITIONALPASS" />
                <el-option label="不通过" value="NOPASS" />
                <el-option label="待审查" value="REVIEWING" />
                <el-option label="过期" value="EXPIRED" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button v-auth="['systemAdditionAndReview/index/search']" type="primary" @click="handleSearch()">
                查询
              </el-button>
              <el-button v-auth="['systemAdditionAndReview/index/reset']" @click="handleReset()">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="mt-24">
        <el-table v-loading="reportLoading" :data="dataList" min-height="200" border>
          <el-table-column prop="regulationName" label="制度名称" width="180" align="center" />
          <el-table-column prop="auditTypeText" label="审查类型" width="120" align="center" />
          <el-table-column prop="createdBy" label="提交人" width="100" align="center" />
          <!-- <el-table-column prop="auditBy" label="审核人" width="100" align="center" /> -->
          <el-table-column prop="statusText" label="状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'PASS' ? 'success' : row.status === 'REVIEWING' ? 'warning' : row.status === 'NOPASS' ? 'danger' : 'info'"
              >
                {{ row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建日期" width="120" align="center" />
          <el-table-column prop="deadlineTimeText" label="截止日期" width="120" align="center" />
          <el-table-column prop="opinion" label="审核意见" min-width="150" align="center">
            <template #default="{ row }">
              <span :title="row.opinion">{{ row.opinion || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="{ row }">
              <div class="aic jcse flex">
                <!-- <el-button type="primary" link size="small" @click="goDetail(row)">
                  查看详情
                </el-button> -->
                <el-button v-auth="['systemAdditionAndReview/index/auditReport']" type="primary" link size="small" @click="goReport(row)">
                  审核报告
                </el-button>
                <el-button v-auth="['systemAdditionAndReview/index/delete']" type="danger" link size="small" @click="deleteRecord(row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <page-compon
          :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
          @pag-change="pagChange"
        />
      </div>
      <div v-if="false" class="mt-24">
        <el-row>
          <el-col :span="8">
            <div class="br-8 mr-10 bg-[#fff] p-24">
              <div class="f-18 fw-600">
                制度基本信息
              </div>
              <div class="f-14 mt-16 fw-400">
                <div class="c-[#4B5563]">
                  制度名称
                </div>
                <div class="f-16 mt-8 c-[#111827]">
                  员工行为规范管理制度
                </div>
              </div>
              <div class="f-14 mt-16 fw-400">
                <div class="c-[#4B5563]">
                  制度编号
                </div>
                <div class="f-16 mt-8 c-[#111827]">
                  HR-2024-001
                </div>
              </div>
              <div class="f-14 mt-16 fw-400">
                <div class="c-[#4B5563]">
                  版本号
                </div>
                <div class="f-16 mt-8 c-[#111827]">
                  V2.0
                </div>
              </div>
              <div class="f-14 mt-16 fw-400">
                <div class="c-[#4B5563]">
                  制度类型
                </div>
                <div class="f-16 mt-8 c-[#111827]">
                  人力资源管理制度
                </div>
              </div>
              <div class="f-14 mt-16 fw-400">
                <div class="c-[#4B5563]">
                  生效日期
                </div>
                <div class="f-16 mt-8 c-[#111827]">
                  2024-02-01
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="br-8 ml-10 mr-10 bg-[#fff] p-24">
              <div class="f-18 fw-600">
                制度内容
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 fw-500">
                  第一条 总则
                </div>
                <div class="f-14 mt-8 c-[#4B5563] fw-400">
                  为规范员工行为，维护公司正常的工作秩序，特制定本制度。
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(251 234 211 / 10%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 fw-500">
                  第一条 总则
                </div>
                <div class="f-14 mt-8 c-[#4B5563] fw-400">
                  为规范员工行为，维护公司正常的工作秩序，特制定本制度。
                </div>
                <div class="br-4 aic mt-10 flex bg-[#FBEAD3] p-8">
                  <svg-icon name="ep:edit" class="c-[#4B5563]" />
                  <span class="f-14 ml-4 c-[#333333]">
                    缺少关键内容，要明确说明适用范围
                  </span>
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: #fefce8;border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="f-16 fw-500">
                  第一条 总则
                </div>
                <div class="f-14 mt-8 c-[#4B5563] fw-400">
                  为规范员工行为，维护公司正常的工作秩序，特制定本制度。
                </div>
                <div class="br-4 aic mt-10 flex bg-[#FEF9C3] p-8">
                  <svg-icon name="ep:edit" class="c-[#4B5563]" />
                  <span class="f-14 ml-4 c-[#333333]">
                    缺少关键内容，要明确说明适用范围
                  </span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="br-8 ml-10 bg-[#fff] p-24">
              <div class="f-18 fw-600">
                合规评估
              </div>
              <div class="aic jcsb mt-16 flex bg-[#F0FDF4] p-8 c-[#166534]">
                <div>完整性评分</div>
                <div>85分</div>
              </div>
              <div class="aic jcsb mt-16 flex bg-[#EFF6FF] p-8 c-[#1E40AF]">
                <div>完整性评分</div>
                <div>85分</div>
              </div>
              <div class="aic jcsb mt-16 flex bg-[#FEFCE8] p-8 c-[#854D0E]">
                <div>完整性评分</div>
                <div>85分</div>
              </div>
              <div class="f-18 mt-24 fw-600">
                合规评估
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="aic jcsb flex">
                  <div class="f-16 aic flex fw-500">
                    <div style="width: 8px;height: 8px;background-color: #d32f2f;border-radius: 50%;" />
                    <span class="ml-8 c-[#111827]">高风险</span>
                  </div>
                  <div class="f-16 c-[#4B5563] fw-400">
                    2个问题
                  </div>
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="aic jcsb flex">
                  <div class="f-16 aic flex fw-500">
                    <div style="width: 8px;height: 8px;background-color: #f57c00;border-radius: 50%;" />
                    <span class="ml-8 c-[#111827]">中风险</span>
                  </div>
                  <div class="f-16 c-[#4B5563] fw-400">
                    2个问题
                  </div>
                </div>
              </div>
              <div
                class="mt-16"
                style="flex: 1;padding: 17px;background: rgb(0 0 0 / 0%);border: 1px solid #e5e7eb;border-radius: 4px;"
              >
                <div class="aic jcsb flex">
                  <div class="f-16 aic flex fw-500">
                    <div style="width: 8px;height: 8px;background-color: #ffc107;border-radius: 50%;" />
                    <span class="ml-8 c-[#111827]">低风险</span>
                  </div>
                  <div class="f-16 c-[#4B5563] fw-400">
                    2个问题
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!--      <div class="card p-16 flex">
        <div>
          <el-button type="primary">
            <svg-icon name="ep:plus" />
            <span class="ml-4">新建转化任务</span>
          </el-button>
        </div>
        <div class="ml-14">
          <el-button type="primary" plain>
            <svg-icon name="ep:download" />
            <span class="ml-4">批量导出</span>
          </el-button>
        </div>
        <div class="ml-32">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="名称:">
              <el-input v-model="paging.title" clearable placeholder="搜索法规名称..." @clear="paging.title = null" />
            </el-form-item>
            <el-form-item label="发布机构:">
              <el-cascader v-model="paging.group_id" :props="props1" clearable class="w-full" :options="groupList"
                @change="changegroup" @clear="paging.group_id = null" />
            </el-form-item>
            <el-form-item label="全部状态" style="width: 200px;">
              <el-select v-model="paging.status" clearable placeholder="请选择状态" @clear="paging.status = null">
                <el-option label="正常" value="1" />
                <el-option label="禁用" value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                查询
              </el-button>
              <el-button @click="paging = {
                  page: 1,
                  limit: 10,
                }, getList()">
                重置
              </el-button>
              <el-button>
                <svg-icon name="ep:filter" />
                <span class="ml-4">高级筛选</span>
              </el-button>
              <el-button type="danger" @click="removealldata()">
                <template #icon>
                  <svg-icon name="ep:search" />
                </template>
                批量删除
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div> -->
      <!--      <div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="i,j in tabsList" :key="j" :label="i.name" :name="i.id"></el-tab-pane>
        </el-tabs>
      </div> -->
      <!-- <div class="mt-20 conBox">
        <LayoutContainer style="padding:0;" :enable-left-side="true" :enable-right-side="true" :left-side-width="280">
          <template #leftSide>
            <div class="flex aic jcsb">
              <div class="f-16 f-500">
                法规分类
              </div>
              <div>
                <svg-icon name="ep:setting" />
              </div>
            </div>
            <div class="mt-16">
              <el-tree :data="data" :props="defaultProps" :default-expand-all="true" @node-click="handleNodeClick" />
            </div>
          </template>
          <div>
            <image-preview :src="i.image" :width="60" :height="60" />
            <el-table :data="dataList" @selection-change="handleSelectionChange" min-height="200" border>
              <el-table-column prop="id" label="法规名称" min-width="150" align="center" />
              <el-table-column prop="id" label="发布机构" width="100" align="center" />
              <el-table-column prop="id" label="制度类型" width="100" align="center" />
              <el-table-column prop="sort" label="发布日期" width="80" align="center" />
              <el-table-column prop="send_time" label="实施日期" width="180" align="center">
                <template #default="{ row: i }">
                  {{ i.send_time>0?dayjs(i.send_time * 1000).format('YYYY-MM-DD'):'' }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80" align="center">
                <template #default="{ row }">
                  <div v-if="row.status === 1" class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty">true</span>
                    </div>
                    正常
                  </div>
                  <div v-else class="relative flex items-center justify-center">
                    <div data-v-7edfc4d9="" class="badge downcol relative mr-2 inline-flex">
                      <span
                        class="absolute left-[50%] top-0 z-20 h-1.5 w-1.5 whitespace-nowrap rounded-full bg-ui-primary px-1.5 text-xs text-ui-text ring-1 ring-light before:absolute before:left-0 before:top-0 left-[100%]! before:block before:h-full before:w-full -translate-x-[50%] -translate-y-[50%] before:animate-ping before:rounded-full before:bg-ui-primary px-0! -indent-9999 dark:ring-dark before:content-empty">true</span>
                    </div>
                    暂停
                  </div>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="100" align="center">
                <template #default="scope">
                  <div class="flex aic jcse">
                    <div>
                      <svg-icon name="ep:view" class="c-[#1E88E5]" />
                    </div>
                    <div>
                      <svg-icon name="ep:right" class="c-[#1E88E5]" />
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <page-compon :page="paging.page" :size="paging.limit" :total="paging.total" style="margin-top: 16px;"
              @pag-change="pagChange" />
            <div class="flex mt-24 jcsb">
              <div style="width: 49%;">
                <el-card class="box-card">
                  <template #header>
                    <div class="card-header">
                      <span>法规详情</span>
                    </div>
                  </template>
                  <div>
                    <div class="f-18 fw-500">
                      《金融机构反洗钱规定》
                    </div>
                    <div class="f-14 flex mt-16">
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布机构：</span>
                        <span class="c-[#666]">中国人民银行</span>
                      </div>
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布日期：</span>
                        <span class="c-[#666]">2024-01-15</span>
                      </div>
                    </div>
                    <div class="f-14 flex mt-16">
                      <div style="width: 50%;">
                        <span class="c-[#999]">文号：</span>
                        <span class="c-[#666]">2024-01-15</span>
                      </div>
                      <div style="width: 50%;">
                        <span class="c-[#999]">发布日期：</span>
                        <span class="c-[#666]">银发〔2024〕1号</span>
                      </div>
                    </div>
                    <div class="mt-20 mb-20" style="border-top: 1px solid #E0E0E0;"></div>
                    <div>
                      <div class="flex aic">
                        <div class="flex fcc br-4 bg-[#F5F7FA]" style="width: 52px;height: 28px;">原文</div>
                        <div class="flex fcc br-4 bg-[#fff] ml-28" style="width: 100px;height: 28px;">智能解读</div>
                      </div>
                      <div class="flex fcc br-4 bg-[#fff] c-666 f-14 mt-14">第一条
                        为了预防洗钱活动，维护金融秩序，根据《中华人民共和国反洗钱法》等法律规定，制定本规定。
                      </div>
                      <div class="flex fcc br-4 bg-[#fff] c-666 f-14 mt-14">第一条
                        第二条 本规定适用于在中华人民共和国境内依法设立的金融机构。 金融机构包括：政策性银行、商业银行、农村合作银行、城市信用合作社、农村信用合作社等吸收公众存款的金融机构...
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
              <div style="width: 49%;">
                <el-card class="box-card">
                  <template #header>
                    <div class="card-header">
                      <span>AI 智能分析</span>
                    </div>
                  </template>
                  <div>
                    <div class="bg-[#F5F7FA] br-4" style="padding: 14px 12px;">
                      <div class="flex aic">
                        <img :src="yxfwImg" class="wh-20" alt="" />
                        <span class="ml-8">影响范围</span>
                      </div>
                      <div class="mt-10 f-16 c-[#4B5563]">
                        涉及业务部门：金融科技部、风控部、合规部
                      </div>
                    </div>
                    <div class="bg-[#F5F7FA] br-4 mt-16" style="padding: 14px 12px;">
                      <div class="flex aic">
                        <img :src="gjyqImg" class="wh-20" alt="" />
                        <span class="ml-8">关键要求</span>
                      </div>
                      <div class="mt-10 f-16 c-[#4B5563]">
                        需要建立专门的金融科技创新管理制度
                      </div>
                    </div>
                  </div>
                </el-card>
                <el-card class="box-card mt-16">
                  <template #header>
                    <div class="card-header">
                      <span>转化任务</span>
                    </div>
                  </template>
                  <div>
                    <div class="flex aic jcsb">
                      <div class="flex aic">
                        <img :src="dqjdImg" class="wh-20" alt="" />
                        <span class="ml-8">当前进度</span>
                      </div>
                      <div class="f-14 c-[#4CAF50]">
                        40%
                      </div>
                    </div>
                    <div class="mt-8">
                      <el-slider v-model="sliderValue" disabled />
                    </div>
                    <div class="flex jcsb flex-wrap">
                      <div class="flex aic mt-16" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="" />
                        <span class="ml-8 c-[#666666]">法规解析</span>
                      </div>
                      <div class="flex aic mt-16" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="" />
                        <span class="ml-8 c-[#666666]">要求提取</span>
                      </div>
                      <div class="flex aic mt-16" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="" />
                        <span class="ml-8 c-[#666666]">框架生成</span>
                      </div>
                      <div class="flex aic mt-16" style="width: 50%;">
                        <img :src="gjyqImg" class="wh-20" alt="" />
                        <span class="ml-8 c-[#666666]">内容撰写</span>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </LayoutContainer>
      </div> -->
    </PageMain>

    <!-- 审核报告弹窗 -->
    <el-dialog
      v-model="reportDialogVisible"
      title="审核报告详情"
      width="800px"
      :close-on-click-modal="false"
      @close="closeReportDialog"
    >
      <div v-loading="reportLoading" class="report-dialog-content">
        <div v-if="reportData" class="space-y-6">
          <!-- 基本信息 -->
          <div class="metadata-box">
            <h4>基本信息</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="制度名称">
                {{ reportData.regulationName }}
              </el-descriptions-item>
              <el-descriptions-item label="版本">
                {{ reportData.version }}
              </el-descriptions-item>
              <el-descriptions-item label="创建人">
                {{ reportData.createdBy }}
              </el-descriptions-item>
              <el-descriptions-item label="更新人">
                {{ reportData.updatedBy }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ reportData.createdAt }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ reportData.updatedAt }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                {{ statusMap[reportData.status] }}
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 审核报告内容 -->
          <div class="content-box">
            <h4>审核报告内容</h4>
            <div v-if="reportData.content" class="report-content">
              <el-scrollbar height="400px">
                <pre class="whitespace-pre-wrap p-4">{{ reportData.content }}</pre>
              </el-scrollbar>

              <!-- <div v-html="reportData.content.replace(/\n/g, '<br>')" /> -->
            </div>
            <div v-else class="no-content">
              暂无审核报告内容
            </div>
          </div>

          <!-- 元数据信息 -->
          <div v-if="reportData.metadata" class="metadata-box">
            <h4>元数据信息</h4>
            <pre>{{ JSON.stringify(reportData.metadata, null, 2) }}</pre>
          </div>
        </div>
        <div v-else-if="!reportLoading" class="no-content">
          <el-empty description="未找到审核报告数据" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-auth="['systemAdditionAndReview/index/closeReportDialog']" @click="closeReportDialog">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }

  .conBox {
    :deep(.main) {
      background-color: transparent;

      .main-container {
        padding: 0 !important;
      }

      .el-slider__button-wrapper {
        display: none;
      }

      .el-slider__runway.is-disabled .el-slider__bar {
        height: 8px;
        background: #4caf50;
        border-radius: 9999px;
      }
    }
  }

  // 审核报告弹窗样式
  .report-dialog-content {
    .metadata-box {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background-color: #f9fafb;
      margin-bottom: 16px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }
    }

    .content-box {
      padding: 16px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      background-color: #ffffff;
      margin-bottom: 16px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }

      .report-content {
        max-height: 400px;
        overflow-y: auto;
        line-height: 1.6;
        color: #4b5563;
        white-space: pre-wrap;
      }

      .no-content {
        text-align: center;
        color: #9ca3af;
        padding: 20px;
      }
    }

    .space-y-6 > * + * {
      margin-top: 24px;
    }

    pre {
      background-color: #f3f4f6;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      overflow-x: auto;
    }
  }
</style>
