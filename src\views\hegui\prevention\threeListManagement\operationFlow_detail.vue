<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowRight,
  Check,
  Delete,
  Document,
  Download,
  Edit,
} from '@element-plus/icons-vue'
import threeListApi from '@/api/complianceApi/prevention/threeList'

const router = useRouter()
const route = useRoute()
const activeTab = ref('risk')
const loading = ref(false)
const processDetail = ref<any>({})

// 解析AI返回的责任部门/岗位数据
const parseResponsibleData = (data: any) => {
  if (!data) return []

  // 如果已经是数组，直接返回
  if (Array.isArray(data)) {
    return data.map((item: any) => ({
      ...item,
      isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
    }))
  }

  // 如果是字符串，尝试解析JSON
  if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data)
      if (Array.isArray(parsed)) {
        return parsed.map((item: any) => ({
          ...item,
          isExternal: !item.departmentId && !item.positionId // 标记外部数据（无ID）
        }))
      }
    } catch (e) {
      console.warn('解析责任数据失败:', e)
    }
  }

  return []
}

// 获取流程详情
const fetchProcessDetail = async () => {
  const processId = route.query.id || route.params.id
  if (!processId) {
    ElMessage.error('流程ID不能为空')
    return
  }

  loading.value = true
  try {
    const res = await threeListApi.getProcessDetail(Number(processId))
    if (res) {
      console.log('流程详情数据:', res)

      // 更新流程详情数据
      processDetail.value = {
        businessDomainName: res.businessDomainName || '',
        businessDomainType: res.businessDomainType || '',
        businessProcess: res.businessProcess || '',
        processDescription: res.processDescription || '',
        approvalStatus: res.approvalStatus || '',
        processStatus: res.processStatus || '',
        createdBy: res.createdBy || '',
        createdAt: res.createdAt || '',
        createdTime: res.createdTime || '',
        updatedBy: res.updatedBy || '',
        updatedTime: res.updatedTime || ''
      }

      // 处理流程图数据
      if (res.processDiagramUrl) {
        try {
          processDetail.value.processDiagramUrl = typeof res.processDiagramUrl === 'string'
            ? JSON.parse(res.processDiagramUrl)
            : res.processDiagramUrl
        } catch (error) {
          console.error('解析 processDiagramUrl 失败:', error)
          processDetail.value.processDiagramUrl = res.processDiagramUrl
        }
      }

      // 处理管控环节数据
      if (res.controlLinks && res.controlLinks.length > 0) {
        processDetail.value.controlLinks = res.controlLinks.map((link: any) => {
          const controlDetail = link.controlDetail || {}
          return {
            controlLinkName: link.controlLinkName || '',
            linkDescription: link.linkDescription || '',
            approvalDepartment: link.approvalDepartment || '',
            approver: link.approver || '',
            requiresGeneralManager: link.requiresGeneralManager || false,
            requiresChairman: link.requiresChairman || false,
            riskPoints: controlDetail.riskPoints || '',
            riskDescriptions: controlDetail.riskDescriptions || '',
            complianceRequirements: controlDetail.complianceRequirements || '',
            complianceBasis: controlDetail.complianceBasis || '',
            controlMeasures: controlDetail.controlMeasures || '',
            responsibleDepartments: parseResponsibleData(controlDetail.responsibleDepartments),
            responsiblePositions: parseResponsibleData(controlDetail.responsiblePositions)
          }
        })
      } else {
        processDetail.value.controlLinks = []
      }

      // 处理文件数据
      if (res.processFiles && Array.isArray(res.processFiles)) {
        processDetail.value.processFiles = res.processFiles.map((doc: any) => ({
          name: doc.fileName || '',
          fileName: doc.fileName || '',
          url: doc.fileUrl || '',
          type: doc.fileType || '',
          size: doc.fileSize || 0,
          fileSize: doc.fileSize || 0,
          key: doc.fileUrl || '',
          filePath: doc.fileUrl || '',
          fileType: doc.fileType || '',
          status: 'success'
        }))
      } else {
        processDetail.value.processFiles = []
      }
    }
  } catch (error) {
    console.error('获取流程详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 状态计算属性
const statusText = computed(() => {
  switch (processDetail.value.approvalStatus) {
    case 'APPROVED': return '已生效'
    case 'PENDING': return '待审核'
    case 'DRAFT': return '草稿'
    case 'REJECTED': return '已驳回'
    default: return '待确认'
  }
})

const statusClass = computed(() => {
  switch (processDetail.value.approvalStatus) {
    case 'APPROVED': return 'bg-green-500'
    case 'PENDING': return 'bg-orange-500'
    case 'DRAFT': return 'bg-gray-500'
    case 'REJECTED': return 'bg-red-500'
    default: return 'bg-orange-500'
  }
})

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleDateString('zh-CN')
}

// 获取风险等级样式
const getRiskLevelClass = (level: string) => {
  switch (level) {
    case '高风险':
    case 'HIGH':
      return 'bg-red-100 text-red-800'
    case '中风险':
    case 'MEDIUM':
      return 'bg-orange-100 text-orange-800'
    case '低风险':
    case 'LOW':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取风险等级文本
const getRiskLevelText = (level: string) => {
  switch (level) {
    case 'HIGH': return '高风险'
    case 'MEDIUM': return '中风险'
    case 'LOW': return '低风险'
    default: return level || '未知'
  }
}

function goAddEdit(item: any) {
  const processId = route.query.id || route.params.id
  if (processId) {
    // 编辑清单
    router.push({
      name: '/threeListManagement/operationFlow/edit',
      query: { id: processId },
    })
  }
  else {
    // 新增清单
    router.push({
      name: '/threeListManagement/operationFlow/edit',
    })
  }
}

// 生命周期
onMounted(() => {
  fetchProcessDetail()
})
</script>

<template>
  <div>
    <!-- 页面标题区域 -->
    <div class="mx-auto px-6 pb-6 container">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <h1 class="mr-4 text-xl font-bold">
            {{ processDetail.businessProcess || '流程管控' }}
          </h1>
          <span :class="['rounded px-3 py-1 text-sm text-white', statusClass]">{{ statusText }}</span>
        </div>
        <div class="flex space-x-3">
          <el-button v-auth="'threeListManagement/detail/edit'" type="primary" @click="goAddEdit(null)">
            <el-icon class="mr-1">
              <Edit />
            </el-icon>
            编辑
          </el-button>
          <el-button v-auth="'threeListManagement/detail/confirm'" type="success" class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Check />
            </el-icon>
            确认
          </el-button>
          <el-button v-auth="'threeListManagement/detail/export'" plain class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Download />
            </el-icon>
            导出
          </el-button>
          <el-button v-auth="'threeListManagement/detail/delete'" type="danger" plain class="!rounded-button whitespace-nowrap">
            <el-icon class="mr-1">
              <Delete />
            </el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>
    <!-- 基本信息卡片 -->
    <div class="mx-auto px-6 pb-6 container">
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 text-lg font-bold">
          基本信息
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="flex items-center">
              <span class="w-24 text-sm text-gray-500">
                流程编号：
              </span>
              <span class="font-medium">
                {{ processDetail.id || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                业务领域：
              </span>
              <span class="font-medium">
                {{ processDetail.businessDomainName || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                业务流程：
              </span>
              <span class="font-medium">
                {{ processDetail.businessProcess || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-30 text-sm text-gray-500">
                责任部门/岗位：
              </span>
              <span class="font-medium">
                <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                  <span v-for="(link, index) in processDetail.controlLinks.slice(0, 2)" :key="index">
                    <template v-if="link.responsibleDepartments && link.responsibleDepartments.length > 0">
                      {{ link.responsibleDepartments.map(dept => dept.departmentName).join('、') }}
                    </template>
                    <template v-if="link.responsiblePositions && link.responsiblePositions.length > 0">
                      /{{ link.responsiblePositions.map(pos => pos.positionName).join('、') }}
                    </template>
                    <span v-if="index < Math.min(processDetail.controlLinks.length, 2) - 1">；</span>
                  </span>
                  <span v-if="processDetail.controlLinks.length > 2">...</span>
                </template>
                <span v-else>-</span>
              </span>
            </div>
          </div>
          <div>
            <div class="flex items-center">
              <span class="w-24 text-sm text-gray-500">
                风险等级：
              </span>
              <span class="font-medium">
                <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                  <span v-for="(link, index) in processDetail.controlLinks.slice(0, 1)" :key="index" 
                        :class="getRiskLevelClass(link.riskLevel || 'MEDIUM')" 
                        class="px-2 py-1 rounded text-xs">
                    {{ getRiskLevelText(link.riskLevel || 'MEDIUM') }}
                  </span>
                </template>
                <span v-else class="text-gray-500">-</span>
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                审核状态：
              </span>
              <span class="font-medium">
                {{ statusText }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                创建时间：
              </span>
              <span class="font-medium">
                {{ formatTime(processDetail.createdAt || processDetail.createdTime) || '-' }}
              </span>
            </div>
            <div class="mt-3 flex items-center">
              <span class="w-24 text-sm text-gray-500">
                最后更新：
              </span>
              <span class="font-medium">
                {{ formatTime(processDetail.updatedTime) || '-' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mx-auto px-6 pb-10 container">
      <div class="rounded-lg bg-white p-6 shadow-sm">
        <div class="mb-4 text-lg font-bold">
          流程示意图
        </div>
        <div class="flex items-center gap-2">
          <button class="!rounded-button whitespace-nowrap bg-gray-100 px-2 py-1 text-sm">
            -
          </button>
          <span class="text-sm">100%</span>
          <button class="!rounded-button whitespace-nowrap bg-gray-100 px-2 py-1 text-sm">
            +
          </button>
          <button class="!rounded-button flex items-center gap-1 whitespace-nowrap border border-gray-300 bg-white px-2 py-1 text-sm">
            <el-icon><FullScreen /></el-icon>
          </button>
          <button class="!rounded-button flex items-center gap-1 whitespace-nowrap border border-gray-300 bg-white px-2 py-1 text-sm">
            <el-icon><Picture /></el-icon>
          </button>
        </div>

        <div class="h-96 flex items-center justify-center border border-gray-200 rounded bg-gray-50 p-4">
          <div class="text-gray-400">
            流程示意图区域
          </div>
        </div>
      </div>
    </div>
    <!-- 标签页内容区 -->
    <div class="mx-auto px-6 pb-10 container">
      <div class="rounded-lg bg-white shadow-sm">
        <el-tabs v-model="activeTab" class="p-6">
          <el-tab-pane label="关键风险" name="risk">
            <div class="space-y-6">
              <h3 class="mb-4 font-bold">
                风险点识别
              </h3>
              <div class="overflow-x-auto">
                <table class="min-w-full border border-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                        风险点
                      </th>
                      <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                        风险描述
                      </th>
                      <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                        风险等级
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                    <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                      <template v-for="(link, linkIndex) in processDetail.controlLinks" :key="linkIndex">
                        <tr v-if="link.riskPoints">
                          <td class="px-4 py-3 text-sm text-gray-900 font-medium">
                            {{ link.riskPoints }}
                          </td>
                          <td class="px-4 py-3 text-sm text-gray-700">
                            {{ link.riskDescriptions || '-' }}
                          </td>
                          <td class="px-4 py-3">
                            <span :class="['rounded-full px-2 py-1 text-xs font-medium', getRiskLevelClass(link.riskLevel || 'MEDIUM')]">
                              {{ getRiskLevelText(link.riskLevel || 'MEDIUM') }}
                            </span>
                          </td>
                        </tr>
                      </template>
                    </template>
                    <tr v-else>
                      <td colspan="3" class="px-4 py-8 text-center text-gray-500">
                        暂无风险点数据
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="合规管控" name="control">
            <div class="space-y-6">
              <div>
                <h3 class="mb-2 font-bold">
                  合规要求
                </h3>
                <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                  <div v-for="(link, index) in processDetail.controlLinks" :key="index" class="mb-4">
                    <p v-if="link.complianceRequirements" class="text-gray-700 mb-2">
                      {{ link.complianceRequirements }}
                    </p>
                  </div>
                </template>
                <p v-else class="text-gray-500">
                  暂无合规要求数据
                </p>
              </div>
              <div>
                <h3 class="mb-2 font-bold">
                  合规依据
                </h3>
                <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                  <ul class="text-gray-700 space-y-2">
                    <template v-for="(link, index) in processDetail.controlLinks" :key="index">
                      <li v-if="link.complianceBasis" class="flex items-center">
                        <el-icon class="mr-2 text-blue-500">
                          <Document />
                        </el-icon>
                        {{ link.complianceBasis }}
                      </li>
                    </template>
                  </ul>
                </template>
                <p v-else class="text-gray-500">
                  暂无合规依据数据
                </p>
              </div>
              <div>
                <h3 class="mb-4 font-bold">
                  管控措施
                </h3>
                <div class="overflow-x-auto">
                  <table class="min-w-full border border-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                          管控措施
                        </th>
                        <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                          责任部门
                        </th>
                        <th class="border-b border-gray-200 px-4 py-3 text-left text-sm text-gray-900 font-medium">
                          责任岗位
                        </th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                      <template v-if="processDetail.controlLinks && processDetail.controlLinks.length > 0">
                        <template v-for="(link, linkIndex) in processDetail.controlLinks" :key="linkIndex">
                          <tr v-if="link.controlMeasures">
                            <td class="px-4 py-3 text-sm text-gray-900">
                              {{ link.controlMeasures }}
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-700">
                              <template v-if="link.responsibleDepartments && link.responsibleDepartments.length > 0">
                                {{ link.responsibleDepartments.map(dept => dept.departmentName).join('、') }}
                              </template>
                              <span v-else>-</span>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-700">
                              <template v-if="link.responsiblePositions && link.responsiblePositions.length > 0">
                                {{ link.responsiblePositions.map(pos => pos.positionName).join('、') }}
                              </template>
                              <span v-else>-</span>
                            </td>
                          </tr>
                        </template>
                      </template>
                      <tr v-else>
                        <td colspan="3" class="px-4 py-8 text-center text-gray-500">
                          暂无管控措施数据
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="更新记录" name="history">
            <div class="space-y-4">
              <template v-if="processDetail.updatedTime && processDetail.updatedTime !== processDetail.createdTime">
                <div class="relative border-l-2 border-gray-200 pb-4 pl-8">
                  <div class="absolute top-0 h-4 w-4 rounded-full bg-blue-500 -left-0.1" />
                  <div class="text-sm font-medium">
                    {{ formatTime(processDetail.updatedTime) }}
                  </div>
                  <div class="mb-1 text-sm text-gray-500">
                    {{ processDetail.updatedBy || '系统' }}
                  </div>
                  <div class="text-gray-700">
                    更新流程管控信息
                  </div>
                </div>
              </template>
              <template v-if="processDetail.createdAt || processDetail.createdTime">
                <div class="relative border-l-2 border-gray-200 pl-8">
                  <div class="absolute top-0 h-4 w-4 rounded-full bg-blue-500 -left-0.1" />
                  <div class="text-sm font-medium">
                    {{ formatTime(processDetail.createdAt || processDetail.createdTime) }}
                  </div>
                  <div class="mb-1 text-sm text-gray-500">
                    {{ processDetail.createdBy || '系统' }}
                  </div>
                  <div class="text-gray-700">
                    创建流程管控清单
                  </div>
                </div>
              </template>
              <div v-if="!processDetail.createdAt && !processDetail.createdTime" class="text-center text-gray-500 py-8">
                暂无更新记录
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.container {
  max-width: none;
  width: 100%;
  padding-left: 80px;
  padding-right: 80px;
}
</style>
