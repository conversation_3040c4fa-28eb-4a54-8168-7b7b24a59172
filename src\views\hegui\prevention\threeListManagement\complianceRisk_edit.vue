<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowDown,
  InfoFilled,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import useUserStore from '@/store/modules/user'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import threeListApi from '@/api/complianceApi/prevention/threeList'
import dictApi from '@/api/modules/system/dict'
import threeAi from '@/api/complianceApi/prevention/threeAi'

const userStore = useUserStore()
// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const isLoading = ref(false)
const isSubmitting = ref(false)
const _lawyer = ref(false)
_lawyer.value = userStore.lawyer
const isEdit = ref(false)
const riskId = ref<string | number>('')

// AI 生成状态
const isGenerating = ref(false)
const conversationId = ref('')

// 进度弹窗相关变量
const progressDialogVisible = ref(false)
const currentProgress = ref('正在智能识别中...')

// 防抖定时器
let debounceTimer: any = null

// Form Data
const form = reactive({
  // 基础信息
  orgUnitId: null,
  businessType: undefined,
  riskLevelModel: 'UNKNOWN',
  approvalStatus: 'DRAFT',
  metadata: '',

  // 风险信息
  riskDescription: '',
  riskCause: '',
  riskConsequence: '',

  // 合规义务
  lawsRegulations: '',
  regulatoryRequirements: '',
  rulesRegulations: '',

  // 管理信息
  controlMeasures: '',
  responsibleOrgUnitId: [],
  cooperatingOrgUnitId: [],

  // 扩展字段
  creator: '',
  customBusinessType: '',
  primaryDepartment: '',
  supportDepartments: [] as string[],
  riskAssessmentAdvice: '',
})
// Business Types - 从API获取
const businessTypes = ref([] as { value: number, name: string, description?: string }[])

// Risk Level Labels
const riskLevelLabels: Record<string, string> = {
  HIGH: '高风险',
  MEDIUM: '中风险',
  LOW: '低风险',
  UNKNOWN: '未知风险',
}
// Section Toggle
const sections = reactive({
  basicInfo: true,
  riskInfo: true,
  complianceInfo: true,
  managementInfo: true,
})
function toggleSection(section: keyof typeof sections) {
  sections[section] = !sections[section]
}

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取业务类型数据
    const response = await dictApi.dictAll(88)
    businessTypes.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取业务类型数据失败')
  }
}

// 防抖处理函数
function debounce(func: Function, delay: number = 2000) {
  if (debounceTimer) {
    ElMessage.warning('操作过于频繁，请稍后再试')
    return false
  }

  debounceTimer = setTimeout(() => {
    debounceTimer = null
  }, delay)

  return true
}

// 获取会话ID
async function getConversationId() {
  if (!conversationId.value) {
    try {
      const response = await threeAi.getSessionId()
      conversationId.value = response?.conversationId || ''
    }
    catch (error) {
      console.error('获取会话ID失败:', error)
    }
  }
  return conversationId.value
}

// 风险智能识别（综合分析）
async function handleSmartIdentify() {
  if (!debounce(handleSmartIdentify)) {
    return
  }

  if (isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  if (!form.businessType) {
    ElMessage.warning('请先选择业务类型')
    return
  }

  try {
    isGenerating.value = true

    // 显示进度弹窗
    progressDialogVisible.value = true
    currentProgress.value = '正在智能识别中...'

    const businessTypeName = businessTypes.value.find(item => item.value === form.businessType)?.name || ''
    const sessionId = await getConversationId()

    const params = {
      businessType: form.businessType,
      businessTypeName,
      toolKey: 'kimi',
      orgUnitName: '', // 可以根据需要获取部门名称
      riskDescription: form.riskDescription || '',
      riskCause: form.riskCause || '',
      riskConsequence: form.riskConsequence || '',
      conversationId: sessionId,
    }

    // 更新进度文本
    currentProgress.value = '正在分析业务类型...'

    currentProgress.value = '正在生成风险信息...'
    const result = await threeAi.getRiskAI(params)

    if (result) {
      // 填充风险信息
      if (result.riskDescription) {
        form.riskDescription = result.riskDescription
      }
      if (result.riskCause) {
        form.riskCause = result.riskCause
      }
      if (result.riskConsequence) {
        form.riskConsequence = result.riskConsequence
      }
      if (result.controlMeasures) {
        form.controlMeasures = result.controlMeasures
      }
      if (result.lawsRegulations) {
        form.lawsRegulations = result.lawsRegulations
      }
      if (result.regulatoryRequirements) {
        form.regulatoryRequirements = result.regulatoryRequirements
      }
      if (result.rulesRegulations) {
        form.rulesRegulations = result.rulesRegulations
      }
      if (result.responsibleOrgUnitId) {
        form.responsibleOrgUnitId = Array.isArray(result.responsibleOrgUnitId) ? result.responsibleOrgUnitId : [result.responsibleOrgUnitId]
      }
      if (result.cooperatingOrgUnitId) {
        form.cooperatingOrgUnitId = Array.isArray(result.cooperatingOrgUnitId) ? result.cooperatingOrgUnitId : [result.cooperatingOrgUnitId]
      }
      if (result.riskLevelModel) {
        form.riskLevelModel = result.riskLevelModel
      }

      // 延迟关闭弹窗
      setTimeout(() => {
        progressDialogVisible.value = false
        ElMessage.success('智能识别完成')
      }, 1000)
    }
    else {
      progressDialogVisible.value = false
      ElMessage.error('识别失败，请重试')
    }
  }
  catch (error) {
    console.error('风险智能识别失败:', error)
    progressDialogVisible.value = false
    ElMessage.error('风险智能识别失败，请重试')
  }
  finally {
    isGenerating.value = false
  }
}

// 风险信息智能生成
async function handleRiskInfoGenerate() {
  if (!debounce(handleRiskInfoGenerate)) {
    return
  }

  if (isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  if (!form.businessType) {
    ElMessage.warning('请先选择业务类型')
    return
  }

  try {
    isGenerating.value = true

    // 显示进度弹窗
    progressDialogVisible.value = true
    currentProgress.value = '正在生成风险信息...'

    const businessTypeName = businessTypes.value.find(item => item.value === form.businessType)?.name || ''
    const sessionId = await getConversationId()

    const params = {
      businessType: form.businessType,
      businessTypeName,
      toolKey: 'kimi',
      orgUnitName: '', // 可以根据需要获取部门名称
      conversationId: sessionId,
    }

    const result = await threeAi.getRiskTypeAI(params)

    if (result) {
      if (result.riskDescription) {
        form.riskDescription = result.riskDescription
      }
      if (result.riskCause) {
        form.riskCause = result.riskCause
      }
      if (result.riskConsequence) {
        form.riskConsequence = result.riskConsequence
      }

      // 延迟关闭弹窗
      setTimeout(() => {
        progressDialogVisible.value = false
        ElMessage.success('风险信息生成成功')
      }, 800)
    }
    else {
      progressDialogVisible.value = false
      ElMessage.error('生成失败，请重试')
    }
  }
  catch (error) {
    console.error('风险信息生成失败:', error)
    progressDialogVisible.value = false
    ElMessage.error('风险信息生成失败，请重试')
  }
  finally {
    isGenerating.value = false
  }
}

// 合规义务智能生成
async function handleComplianceGenerate() {
  if (!debounce(handleComplianceGenerate)) {
    return
  }

  if (isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  if (!form.businessType) {
    ElMessage.warning('请先选择业务类型')
    return
  }

  try {
    isGenerating.value = true

    // 显示进度弹窗
    progressDialogVisible.value = true
    currentProgress.value = '正在生成合规义务...'

    const businessTypeName = businessTypes.value.find(item => item.value === form.businessType)?.name || ''
    const sessionId = await getConversationId()

    const params = {
      businessType: form.businessType,
      businessTypeName,
      toolKey: 'kimi',
      orgUnitName: '', // 可以根据需要获取部门名称
      riskDescription: form.riskDescription || '',
      riskCause: form.riskCause || '',
      riskConsequence: form.riskConsequence || '',
      conversationId: sessionId,
    }

    const result = await threeAi.getRiskLawAI(params)

    if (result) {
      if (result.lawsRegulations) {
        form.lawsRegulations = result.lawsRegulations
      }
      if (result.regulatoryRequirements) {
        form.regulatoryRequirements = result.regulatoryRequirements
      }
      if (result.rulesRegulations) {
        form.rulesRegulations = result.rulesRegulations
      }

      // 延迟关闭弹窗
      setTimeout(() => {
        progressDialogVisible.value = false
        ElMessage.success('合规义务生成成功')
      }, 800)
    }
    else {
      progressDialogVisible.value = false
      ElMessage.error('生成失败，请重试')
    }
  }
  catch (error) {
    console.error('合规义务生成失败:', error)
    progressDialogVisible.value = false
    ElMessage.error('合规义务生成失败，请重试')
  }
  finally {
    isGenerating.value = false
  }
}

// 管理信息智能生成
async function handleManagementGenerate() {
  if (!debounce(handleManagementGenerate)) {
    return
  }

  if (isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  if (!form.businessType) {
    ElMessage.warning('请先选择业务类型')
    return
  }

  try {
    isGenerating.value = true

    // 显示进度弹窗
    progressDialogVisible.value = true
    currentProgress.value = '正在生成管理信息...'

    const businessTypeName = businessTypes.value.find(item => item.value === form.businessType)?.name || ''
    const sessionId = await getConversationId()

    const params = {
      businessTypeName,
      businessType: form.businessType,
      toolKey: 'kimi',
      orgUnitName: '', // 可以根据需要获取部门名称
      riskDescription: form.riskDescription || '',
      riskCause: form.riskCause || '',
      riskConsequence: form.riskConsequence || '',
      lawsRegulations: form.lawsRegulations || '',
      regulatoryRequirements: form.regulatoryRequirements || '',
      rulesRegulations: form.rulesRegulations || '',
      conversationId: sessionId,
    }

    const result = await threeAi.getRiskControlAI(params)

    if (result) {
      if (result.controlMeasures) {
        form.controlMeasures = result.controlMeasures
      }
      if (result.responsibleOrgUnitId) {
        form.responsibleOrgUnitId = Array.isArray(result.responsibleOrgUnitId) ? result.responsibleOrgUnitId : [result.responsibleOrgUnitId]
      }
      if (result.cooperatingOrgUnitId) {
        form.cooperatingOrgUnitId = Array.isArray(result.cooperatingOrgUnitId) ? result.cooperatingOrgUnitId : [result.cooperatingOrgUnitId]
      }
      if (result.riskLevelModel) {
        form.riskLevelModel = result.riskLevelModel
      }

      // 延迟关闭弹窗
      setTimeout(() => {
        progressDialogVisible.value = false
        ElMessage.success('管理信息生成成功')
      }, 800)
    }
    else {
      progressDialogVisible.value = false
      ElMessage.error('生成失败，请重试')
    }
  }
  catch (error) {
    console.error('管理信息生成失败:', error)
    progressDialogVisible.value = false
    ElMessage.error('管理信息生成失败，请重试')
  }
  finally {
    isGenerating.value = false
  }
}

// 风险等级智能生成
async function handleRiskLevelGenerate() {
  if (!debounce(handleRiskLevelGenerate)) {
    return
  }

  if (isGenerating.value) {
    ElMessage.warning('正在生成中，请稍候...')
    return
  }

  if (!form.businessType) {
    ElMessage.warning('请先选择业务类型')
    return
  }

  try {
    isGenerating.value = true

    // 显示进度弹窗
    progressDialogVisible.value = true
    currentProgress.value = '正在生成风险等级...'

    const businessTypeName = businessTypes.value.find(item => item.value === form.businessType)?.name || ''
    const sessionId = await getConversationId()

    const params = {
      businessTypeName,
      businessType: form.businessType,
      toolKey: 'kimi',
      orgUnitName: '', // 可以根据需要获取部门名称
      riskDescription: form.riskDescription || '',
      riskCause: form.riskCause || '',
      riskConsequence: form.riskConsequence || '',
      lawsRegulations: form.lawsRegulations || '',
      regulatoryRequirements: form.regulatoryRequirements || '',
      rulesRegulations: form.rulesRegulations || '',
      conversationId: sessionId,
    }

    const result = await threeAi.getRiskRankAI(params)

    if (result && result.riskLevelModel) {
      form.riskLevelModel = result.riskLevelModel

      // 延迟关闭弹窗
      setTimeout(() => {
        progressDialogVisible.value = false
        ElMessage.success('风险等级生成成功')
      }, 800)
    }
    else {
      progressDialogVisible.value = false
      ElMessage.error('生成失败，请重试')
    }
  }
  catch (error) {
    console.error('风险等级生成失败:', error)
    progressDialogVisible.value = false
    ElMessage.error('风险等级生成失败，请重试')
  }
  finally {
    isGenerating.value = false
  }
}

// 初始化页面数据
function initializePage() {
  const id = route.query.id as string
  if (id) {
    isEdit.value = true
    riskId.value = id
    loadRiskDetail(id)
  }
}

// 加载风险详情
async function loadRiskDetail(id: string | number) {
  try {
    isLoading.value = true
    const _response = await threeListApi.getComplianceRiskDetail(id)
    if (_response) {
      const data = _response
      // 映射API返回数据到表单
      Object.assign(form, {
        orgUnitId: data.orgUnitId,
        businessType: data.businessType,
        riskLevelModel: data.riskLevelModel || 'UNKNOWN',
        approvalStatus: data.approvalStatus,
        metadata: data.metadata,
        riskDescription: data.riskDescription,
        riskCause: data.riskCause,
        riskConsequence: data.riskConsequence,
        lawsRegulations: data.lawsRegulations,
        regulatoryRequirements: data.regulatoryRequirements,
        rulesRegulations: data.rulesRegulations,
        controlMeasures: data.controlMeasures,
        responsibleOrgUnitId: Array.isArray(data.responsibleOrgUnitId) ? data.responsibleOrgUnitId : [],
        cooperatingOrgUnitId: Array.isArray(data.cooperatingOrgUnitId) ? data.cooperatingOrgUnitId : [],
      })
    }
  }
  catch (error) {
    console.error('加载风险详情失败:', error)
    ElMessage.error('加载数据失败')
  }
  finally {
    isLoading.value = false
  }
}

// 保存草稿
async function saveDraft() {
  try {
    isSubmitting.value = true
    let submitData
    let _response

    if (_lawyer.value) {
      // 律师模式：直接审批通过
      submitData = prepareSubmitData('APPROVED')
      _response = await threeListApi.updateComplianceRisk({
        ...submitData,
        complianceRiskMainId: riskId.value,
      })
      ElMessage.success('保存成功')
    }
    else {
      // 普通用户模式：保存草稿
      submitData = prepareSubmitData('DRAFT')

      if (isEdit.value && riskId.value) {
        // 更新草稿
        _response = await threeListApi.updateComplianceRisk({
          ...submitData,
          complianceRiskMainId: riskId.value,
        })
      }
      else {
        // 新建草稿
        _response = await threeListApi.saveComplianceRiskDraft(submitData)
        if (_response) {
          riskId.value = _response.complianceRiskMainId
          isEdit.value = true
        }
      }
      ElMessage.success('草稿保存成功')
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
  finally {
    isSubmitting.value = false
  }
}

// 表单验证
const formRef = ref()

// 提交审核
async function submitForReview() {
  // 验证表单
  if (formRef.value) {
    const valid = await formRef.value.validate().catch(() => false)
    if (!valid) {
      ElMessage.warning('请完善必填信息')
      return
    }
  }

  try {
    isSubmitting.value = true

    // 当lawyer为false时，始终调用submitComplianceRisk接口
    const submitData = prepareSubmitData('PENDING', isEdit.value && riskId.value ? riskId.value : undefined)

    const _response = await threeListApi.submitComplianceRisk(submitData)
    if (_response && _response.data && _response.data.complianceRiskMainId) {
      riskId.value = _response.data.complianceRiskMainId
      isEdit.value = true
    }

    ElMessage.success('提交成功')
    router.push('/threeListManagement/complianceRisk')
  }
  catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
  finally {
    isSubmitting.value = false
  }
}

// 准备提交数据
function prepareSubmitData(approvalStatus: string, complianceRiskMainId?: string | number) {
  const data: any = {
    orgUnitId: form.orgUnitId,
    businessType: form.businessType,
    riskLevelModel: form.riskLevelModel,
    approvalStatus,
    metadata: form.metadata,
    riskDescription: form.riskDescription,
    riskCause: form.riskCause,
    riskConsequence: form.riskConsequence,
    lawsRegulations: form.lawsRegulations,
    regulatoryRequirements: form.regulatoryRequirements,
    rulesRegulations: form.rulesRegulations,
    controlMeasures: form.controlMeasures,
    responsibleOrgUnitId: form.responsibleOrgUnitId,
    cooperatingOrgUnitId: form.cooperatingOrgUnitId,
  }

  if (complianceRiskMainId) {
    data.complianceRiskMainId = complianceRiskMainId
  }

  return data
}

// 表单验证规则
const formRules = {
  businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  orgUnitId: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
  riskDescription: [{ required: true, message: '请输入风险描述', trigger: 'blur' }],
  riskCause: [{ required: true, message: '请输入风险原因', trigger: 'blur' }],
  riskConsequence: [{ required: true, message: '请输入风险后果', trigger: 'blur' }],
  controlMeasures: [{ required: true, message: '请输入管控措施', trigger: 'blur' }],
  riskLevelModel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
}

// Initialize page
onMounted(async () => {
  await loadBasicData()
  // 获取当前用户信息
  // form.creator = await getCurrentUser() // 这里应该调用获取当前用户的API
  form.creator = '系统用户' // 临时默认值
  // 初始化会话ID
  await getConversationId()
  initializePage()
})
</script>

<template>
  <div class="w-full">
    <!-- Header -->
    <header class="mx-auto bg-white px-6 py-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl text-gray-800 font-bold">
          合规风险识别清单编辑
        </h1>
        <el-button
          type="primary"
          class="!rounded-button ai-button flex items-center whitespace-nowrap bg-blue-500"
          :loading="isGenerating"
          @click="handleSmartIdentify"
        >
          风险智能识别
        </el-button>
        <div class="flex space-x-2">
          <!-- 律师在PENDING状态下可以操作 -->
          <template v-if="_lawyer && form.approvalStatus === 'PENDING' && !isLoading">
            <el-button
              type="primary"
              class="!rounded-button whitespace-nowrap"
              :loading="isSubmitting"
              @click="saveDraft"
            >
              发布
            </el-button>
          </template>
          <!-- 非律师在草稿状态或无状态下可以操作，或者数据正在加载时显示默认按钮 -->
          <template v-if="(!_lawyer && (form.approvalStatus === 'DRAFT' || !form.approvalStatus)) || isLoading">
            <el-button
              class="!rounded-button whitespace-nowrap"
              :loading="isSubmitting || isLoading"
              :disabled="isLoading"
              v-auth="'threeListManagement/complianceRisk_edit/save'"
              @click="saveDraft"
            >
              保存草稿
            </el-button>
            <el-button
              type="success"
              class="!rounded-button whitespace-nowrap bg-green-600"
              :loading="isSubmitting || isLoading"
              :disabled="isLoading"
              v-auth="'threeListManagement/complianceRisk_edit/submit'"
              @click="submitForReview"
            >
              {{ isEdit ? '更新并提交' : '提交审核' }}
            </el-button>
          </template>
          <el-button
            class="!rounded-button whitespace-nowrap"
            v-auth="'threeListManagement/complianceRisk_edit/cancel'"
            @click="router.push('/threeListManagement/complianceRisk')"
          >
            取消
          </el-button>
        </div>
      </div>
    </header>
    <!-- Main Content -->
    <main class="mx-auto px-6 pb-10 pt-6 container">
      <el-form ref="formRef" label-position="top" :model="form" :rules="formRules">
        <!-- Basic Info Section -->
        <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
          <div class="item-title flex cursor-pointer items-center justify-between px-6 py-1" @click="toggleSection('basicInfo')">
            <h2 class="text-base text-gray-800 font-bold">
              基础信息
            </h2>
            <el-icon :class="{ 'transform rotate-180': sections.basicInfo }">
              <ArrowDown />
            </el-icon>
          </div>
          <div v-show="sections.basicInfo" class="p-6 space-y-6">
            <el-form-item prop="businessType" label="业务类型" required>
              <el-select
                v-model="form.businessType"
                placeholder="请选择业务类型"
                class="w-full"
              >
                <el-option
                  v-for="item in businessTypes"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                  <div class="flex items-center justify-between">
                    <span>{{ item.name }}</span>
                    <el-tooltip
                      effect="light"
                      :content="item.description"
                      placement="right"
                    >
                      <el-icon class="ml-2 text-gray-400">
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </el-option>
              </el-select>
              <!-- <div v-if="form.businessType === 8" class="mt-2">
                <el-input
                  v-model="form.customBusinessType"
                  placeholder="请输入自定义业务类型"
                  class="w-full"
                  style="width: 300px"
                />
              </div> -->
            </el-form-item>
            <el-form-item prop="orgUnitId" label="所属部门">
              <DepartmentTreeSelect
                v-model="form.orgUnitId"
                placeholder="请选择所属部门"
                :multiple="false"
                clearable
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="创建人员">
              <el-input
                v-model="form.creator"
                placeholder="系统自动生成"
                readonly
                class="w-full bg-gray-100"
              />
            </el-form-item>
          </div>
        </div>
        <!-- Risk Info Section -->
        <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
          <div
            class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1"
            @click="toggleSection('riskInfo')"
          >
            <div class="w-full flex items-center justify-between">
              <h2 class="text-base text-gray-800 font-bold">
                风险信息
              </h2>
              <div class="flex items-center space-x-2">
                <el-button
                  type="success"
                  size="small"
                  :loading="isGenerating"
                  @click.stop="handleRiskInfoGenerate"
                >
                  智能生成
                </el-button>
              </div>
            </div>
            <el-icon :class="{ 'transform rotate-180': sections.riskInfo }">
              <ArrowDown />
            </el-icon>
          </div>
          <div v-show="sections.riskInfo" class="p-6 space-y-6">
            <el-form-item prop="riskDescription" label="风险描述" required>
              <el-input
                v-model="form.riskDescription"
                type="textarea"
                :rows="4"
                placeholder="请详细描述风险内容"
                class="w-full"
              />
            </el-form-item>
            <el-form-item prop="riskCause" label="风险原因" required>
              <el-input
                v-model="form.riskCause"
                type="textarea"
                :rows="4"
                placeholder="请输入风险产生的原因"
                class="w-full"
              />
            </el-form-item>
            <el-form-item prop="riskConsequence" label="风险后果" required>
              <el-input
                v-model="form.riskConsequence"
                type="textarea"
                :rows="4"
                placeholder="请输入风险可能造成的后果"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>
        <!-- Compliance Info Section -->
        <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
          <div
            class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1"
            @click="toggleSection('complianceInfo')"
          >
            <div class="w-full flex items-center justify-between">
              <h2 class="text-base text-gray-800 font-bold">
                合规义务
              </h2>
              <div class="flex items-center space-x-2">
                <el-button
                  type="success"
                  size="small"
                  :loading="isGenerating"
                  @click.stop="handleComplianceGenerate"
                >
                  智能生成
                </el-button>
              </div>
            </div>
            <el-icon :class="{ 'transform rotate-180': sections.complianceInfo }">
              <ArrowDown />
            </el-icon>
          </div>
          <div v-show="sections.complianceInfo" class="p-6 space-y-6">
            <!-- 新的合规义务字段 -->
            <el-form-item label="法律法规">
              <el-input
                v-model="form.lawsRegulations"
                type="textarea"
                :rows="3"
                placeholder="请输入相关法律法规"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="监管规定">
              <el-input
                v-model="form.regulatoryRequirements"
                type="textarea"
                :rows="3"
                placeholder="请输入监管规定"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="规章制度">
              <el-input
                v-model="form.rulesRegulations"
                type="textarea"
                :rows="3"
                placeholder="请输入规章制度"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>
        <!-- Management Info Section -->
        <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
          <div
            class="item-title flex cursor-pointer items-center justify-between border-b border-gray-200 px-6 py-1"
            @click="toggleSection('managementInfo')"
          >
            <div class="w-full flex items-center justify-between">
              <h2 class="text-base text-gray-800 font-bold">
                管理信息
              </h2>
              <div class="flex items-center space-x-2">
                <el-button
                  type="success"
                  size="small"
                  :loading="isGenerating"
                  @click.stop="handleManagementGenerate"
                >
                  智能生成
                </el-button>
              </div>
            </div>
            <el-icon :class="{ 'transform rotate-180': sections.managementInfo }">
              <ArrowDown />
            </el-icon>
          </div>
          <div v-show="sections.managementInfo" class="p-6 space-y-6">
            <el-form-item prop="controlMeasures" label="管控措施" required>
              <el-input
                v-model="form.controlMeasures"
                type="textarea"
                :rows="4"
                placeholder="请输入风险管控措施"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="归口部门">
              <DepartmentTreeSelect
                v-model="form.responsibleOrgUnitId"
                placeholder="请选择归口部门"
                :multiple="true"
                clearable
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="配合部门">
              <DepartmentTreeSelect
                v-model="form.cooperatingOrgUnitId"
                placeholder="请选择配合部门"
                :multiple="true"
                clearable
                class="w-full"
              />
            </el-form-item>
            <el-form-item prop="riskLevelModel" label="风险等级" required>
              <div class="flex items-center space-x-2">
                <el-select
                  v-model="form.riskLevelModel"
                  placeholder="请选择风险等级"
                  class="w-full"
                  style="width: 200px"
                >
                  <el-option
                    v-for="(label, value) in riskLevelLabels"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
                <el-button
                  type="success"
                  size="small"
                  :loading="isGenerating"
                  @click="handleRiskLevelGenerate"
                >
                  智能生成
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="风险评估建议">
              <el-input
                v-model="form.riskAssessmentAdvice"
                type="textarea"
                :rows="3"
                placeholder="系统自动生成的风险评估建议"
                readonly
                class="w-full bg-gray-50"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </main>

    <!-- AI生成进度弹窗 -->
    <el-dialog
      v-model="progressDialogVisible"
      title=""
      width="400px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="dialog-content">
        <div class="dialog-title">
          猫伯伯智能识别中
        </div>
        <div class="progress-body">
          <img
            src="https://maobobo-1346546874.cos.ap-nanjing.myqcloud.com/whiskerguard-front/mbblogo.png"
            alt="猫伯伯"
            class="pulse"
            style="width: 80px; height: 80px;"
          >
          <div class="progress-text">
            {{ currentProgress }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.ai-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.ai-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.ai-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.risk-level-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.risk-level-high {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.risk-level-medium {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.risk-level-low {
  background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.risk-level-unknown {
  background: linear-gradient(135deg, #a4b0be 0%, #747d8c 100%);
}

/* 弹窗样式 */
.dialog-content {
  text-align: center;
  padding: 20px;
}

.dialog-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
}

.progress-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.progress-text {
  font-size: 14px;
  color: #666;
  margin-top: 15px;
}

.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
