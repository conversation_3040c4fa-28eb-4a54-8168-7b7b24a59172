import api from '@/api/index'

// 职责主数据DTO类型定义
export interface DutyMainDTO {
  id?: number
  tenantId?: number
  orgUnitId?: number
  postId?: number
  postName?: string
  principalId?: number
  basicDuty?: string
  riskLevelId?: number
  metadata?: string
  createdAt?: {
    seconds: number
    nanos: number
  }
  createdBy?: string
  updatedAt?: {
    seconds: number
    nanos: number
  }
  updatedBy?: string
  isDeleted?: boolean
}

// 分页查询参数
export interface PageParams {
  pageNumber?: number
  pageSize?: number
  tenantId?: number
  sortField?: string
  sortOrder?: string
  orgUnitId?: number
  postId?: number
  principalId?: number
  riskLevelId?: number
  basicDuty?: string
}

// 风险识别清单相关接口
export interface RiskIdentificationDTO {
  id?: number
  tenantId?: number
  riskName?: string
  riskDescription?: string
  riskLevel?: string
  businessArea?: string
  responsibleDept?: string
  controlMeasures?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 岗位职责规范清单相关接口
export interface JobSpecificationDTO {
  id?: number
  tenantId?: number
  postName?: string
  postCode?: string
  deptName?: string
  jobDescription?: string
  responsibilities?: string
  qualifications?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

// 操作流程清单相关接口
export interface OperationFlowDTO {
  id?: number
  tenantId?: number
  flowName?: string
  flowCode?: string
  businessArea?: string
  flowDescription?: string
  flowSteps?: string
  responsibleRole?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  updatedBy?: string
}

export default {
  // ==================== 职责主数据相关接口 ====================

  // 分页获取职责主数据列表
  getDutyMainList: (params: PageParams) => {
    return api.get('/compliancelistservice/api/duty/mains', {
      params,
    })
  },

  // 创建职责主数据
  createDutyMain: (data: DutyMainDTO) => {
    return api.post('/compliancelistservice/api/duty/mains', data)
  },

  // 获取职责主数据详情
  getDutyMainDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/duty/mains/${id}`)
  },

  // 更新职责主数据
  updateDutyMain: (id: number, data: DutyMainDTO) => {
    return api.put(`/compliancelistservice/api/duty/mains/${id}`, data)
  },

  // 删除职责主数据
  deleteDutyMain: (id: number) => {
    return api.delete(`/compliancelistservice/api/duty/mains/${id}`)
  },

  // ==================== 合规风险识别清单相关接口 ====================

  // 分页获取风险识别清单
  getRiskIdentificationList: (params: any) => {
    return api.get('/compliancelistservice/api/compliance/risk/mains', {
      params,
    })
  },

  // 创建风险识别记录
  createRiskIdentification: (data: RiskIdentificationDTO) => {
    return api.post('/compliancelistservice/api/risk/identifications', data)
  },

  // 获取风险识别详情
  getRiskIdentificationDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/risk/identifications/${id}`)
  },

  // 更新风险识别记录
  updateRiskIdentification: (id: number, data: RiskIdentificationDTO) => {
    return api.put(`/compliancelistservice/api/risk/identifications/${id}`, data)
  },

  // 删除风险识别记录
  deleteRiskIdentification: (id: number) => {
    return api.delete(`/compliancelistservice/api/risk/identifications/${id}`)
  },

  // 批量删除风险识别记录
  batchDeleteRiskIdentification: (ids: number[]) => {
    return api.post('/compliancelistservice/api/risk/identifications/batch-delete', { ids })
  },

  // 获取风险统计数据
  getRiskStatistics: () => {
    return api.get('/compliancelistservice/api/risk/statistics')
  },

  // ==================== 岗位职责规范清单相关接口 ====================

  // 分页获取岗位职责规范清单
  getJobSpecificationList: (params: any) => {
    return api.get('/compliancelistservice/api/job/specifications', {
      params,
    })
  },

  // 创建岗位职责规范
  createJobSpecification: (data: JobSpecificationDTO) => {
    return api.post('/compliancelistservice/api/job/specifications', data)
  },

  // 获取岗位职责规范详情
  getJobSpecificationDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/job/specifications/${id}`)
  },

  // 更新岗位职责规范
  updateJobSpecification: (id: number, data: JobSpecificationDTO) => {
    return api.put(`/compliancelistservice/api/job/specifications/${id}`, data)
  },

  // 删除岗位职责规范
  deleteJobSpecification: (id: number) => {
    return api.delete(`/compliancelistservice/api/job/specifications/${id}`)
  },

  // 批量删除岗位职责规范
  batchDeleteJobSpecification: (ids: number[]) => {
    return api.post('/compliancelistservice/api/job/specifications/batch-delete', { ids })
  },

  // ==================== 操作流程清单相关接口 ====================

  // 分页获取操作流程清单
  getOperationFlowList: (params: any) => {
    return api.get('/compliancelistservice/api/operation/flows', {
      params,
    })
  },

  // 创建操作流程
  createOperationFlow: (data: OperationFlowDTO) => {
    return api.post('/compliancelistservice/api/operation/flows', data)
  },

  // 获取操作流程详情
  getOperationFlowDetail: (id: number) => {
    return api.get(`/compliancelistservice/api/operation/flows/${id}`)
  },

  // 更新操作流程
  updateOperationFlow: (id: number, data: OperationFlowDTO) => {
    return api.put(`/compliancelistservice/api/operation/flows/${id}`, data)
  },

  // 删除操作流程
  deleteOperationFlow: (id: number) => {
    return api.delete(`/compliancelistservice/api/operation/flows/${id}`)
  },

  // 批量删除操作流程
  batchDeleteOperationFlow: (ids: number[]) => {
    return api.post('/compliancelistservice/api/operation/flows/batch-delete', { ids })
  },

  // ==================== 通用接口 ====================

  // 获取部门列表
  getDepartmentList: () => {
    return api.get('/compliancelistservice/api/departments')
  },

  // 获取岗位列表
  getPostList: (deptId?: number) => {
    return api.get('/compliancelistservice/api/posts', {
      params: { deptId },
    })
  },

  // 获取人员列表
  getPersonnelList: (params: any) => {
    return api.get('/compliancelistservice/api/personnel', {
      params,
    })
  },

  // 获取风险等级列表
  getRiskLevelList: () => {
    return api.get('/compliancelistservice/api/risk/levels')
  },

  // 导出数据
  exportData: (type: string, params: any) => {
    return api.get(`/compliancelistservice/api/export/${type}`, {
      params,
      responseType: 'blob',
    })
  },

  // 批量导入
  importData: (type: string, file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/compliancelistservice/api/import/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}
