<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'
import {
  Download as ElIconDownload,
  Plus as ElIconPlus,
  Upload as ElIconUpload,
} from '@element-plus/icons-vue'
  // 筛选条件
const filter = ref({
  status: 'all',
  responsibilities: [],
  training: 'all',
})
// 组织架构树
const orgTree = ref([
  {
    id: 1,
    label: '总公司',
    children: [
      {
        id: 2,
        label: '财务部',
        children: [
          { id: 3, label: '财务一组' },
          { id: 4, label: '财务二组' },
        ],
      },
      {
        id: 5,
        label: '人力资源部',
        children: [
          { id: 6, label: '招聘组' },
          { id: 7, label: '培训组' },
        ],
      },
      {
        id: 8,
        label: '法务部',
      },
    ],
  },
])
const treeProps = {
  children: 'children',
  label: 'label',
}
// 联络人列表
const contactList = ref([
  {
    id: 1,
    name: '王明',
    department: '财务部',
    position: '财务主管',
    appointmentDate: '2023/10/15',
    responsibility: '财务合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '在职',
  },
  {
    id: 2,
    name: '李红',
    department: '人力资源部',
    position: 'HR经理',
    appointmentDate: '2023/10/20',
    responsibility: '人事合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '在职',
  },
  {
    id: 3,
    name: '张伟',
    department: '法务部',
    position: '法务专员',
    appointmentDate: '2023/09/05',
    responsibility: '法务合规',
    contact: '<EMAIL>',
    trainingStatus: '未完成',
    status: '在职',
  },
  {
    id: 4,
    name: '陈芳',
    department: '财务部',
    position: '财务专员',
    appointmentDate: '2023/08/12',
    responsibility: '财务合规',
    contact: '<EMAIL>',
    trainingStatus: '已完成',
    status: '离职',
  },
])
// 分页
const pagination = ref({
  current: 1,
  size: 10,
  total: 40,
})
// 选中的联络人
const selectedContacts = ref([])
// 图表引用
const coverageChart = ref(null)
const trainingChart = ref(null)
// 处理节点点击
function handleNodeClick(data) {
  console.log('点击节点:', data)
}
// 处理选择变化
function handleSelectionChange(selection) {
  selectedContacts.value = selection
}
// 查看详情
function handleView(row) {
  console.log('查看:', row)
}
// 编辑
function handleEdit(row) {
  console.log('编辑:', row)
}
// 初始化图表
function initCharts() {
  nextTick(() => {
    // 部门覆盖率图表
    const coverage = echarts.init(coverageChart.value)
    coverage.setOption({
      animation: false,
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 80, name: '覆盖率', itemStyle: { color: '#1E88E5' } },
            { value: 20, name: '未覆盖', itemStyle: { color: '#E8E8E8' } },
          ],
        },
      ],
    })
    // 培训完成率图表
    const training = echarts.init(trainingChart.value)
    training.setOption({
      animation: false,
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 75, name: '完成率', itemStyle: { color: '#4CAF50' } },
            { value: 25, name: '未完成', itemStyle: { color: '#E8E8E8' } },
          ],
        },
      ],
    })
  })
}
onMounted(() => {
  initCharts()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              合规联络人
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconPlus /></el-icon>
              <span>新增联络人</span>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconUpload /></el-icon>
              <span>批量导入</span>
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon><ElIconDownload /></el-icon>
              <span>导出</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <!--          <template #header>
                <div class="f-16 fw-600">会议基本信息</div>
              </template> -->
              <div class="flex items-center space-x-4">
                <el-select v-model="filter.status" placeholder="状态" class="w-32">
                  <el-option label="全部" value="all" />
                  <el-option label="在职" value="active" />
                  <el-option label="离职" value="inactive" />
                </el-select>
                <el-select v-model="filter.responsibilities" placeholder="职责范围" multiple class="w-48">
                  <el-option label="财务合规" value="finance" />
                  <el-option label="人事合规" value="hr" />
                  <el-option label="法务合规" value="legal" />
                </el-select>
                <el-select v-model="filter.training" placeholder="培训状态" class="w-32">
                  <el-option label="全部" value="all" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="未完成" value="incomplete" />
                </el-select>
                <el-button class="!rounded-button whitespace-nowrap">
                  重置
                </el-button>
                <el-button type="primary" class="!rounded-button whitespace-nowrap">
                  筛选
                </el-button>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!--          <template #header>
                  <div class="f-16 fw-600">会议基本信息</div>
                </template> -->
              <!-- 组织架构树 -->
              <el-tree
                :data="orgTree" :props="treeProps" node-key="id" default-expand-all :expand-on-click-node="false"
                @node-click="handleNodeClick"
              />
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!--          <template #header>
                  <div class="f-16 fw-600">会议基本信息</div>
                </template> -->
              <el-table :data="contactList" style="width: 100%;" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" />
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="department" label="所属部门" width="150" />
                <el-table-column prop="position" label="职位" width="150" />
                <el-table-column prop="appointmentDate" label="任命日期" width="120" />
                <el-table-column prop="responsibility" label="职责范围" width="150" />
                <el-table-column prop="contact" label="联系方式" width="180" />
                <el-table-column prop="trainingStatus" label="培训状态" width="120">
                  <template #default="{ row }">
                    <el-tag :type="row.trainingStatus === '已完成' ? 'success' : 'warning'" size="small">
                      {{ row.trainingStatus }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === '在职' ? 'success' : 'danger'" size="small">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                  <template #default="{ row }">
                    <el-button size="small" class="!rounded-button whitespace-nowrap" @click="handleView(row)">
                      查看
                    </el-button>
                    <el-button
                      size="small" type="primary" class="!rounded-button whitespace-nowrap"
                      @click="handleEdit(row)"
                    >
                      编辑
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="flex items-center justify-between border-t bg-gray-50 p-4">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-600">已选择：{{ selectedContacts.length }}项</span>
                  <el-button
                    v-if="selectedContacts.length > 0" size="small" type="danger"
                    class="!rounded-button whitespace-nowrap"
                  >
                    批量移除
                  </el-button>
                  <el-button
                    v-if="selectedContacts.length > 0" size="small" type="primary"
                    class="!rounded-button whitespace-nowrap"
                  >
                    批量分配任务
                  </el-button>
                  <el-button
                    v-if="selectedContacts.length > 0" size="small" type="success"
                    class="!rounded-button whitespace-nowrap"
                  >
                    批量培训通知
                  </el-button>
                  <el-button
                    v-if="selectedContacts.length > 0" size="small" class="!rounded-button whitespace-nowrap"
                    @click="selectedContacts = []"
                  >
                    取消选择
                  </el-button>
                </div>
                <el-pagination
                  v-model:current-page="pagination.current" :page-size="pagination.size"
                  :total="pagination.total" layout="prev, pager, next, jumper"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  联络人概览
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-600">总联络人数</span>
                  <span class="font-medium">28 人</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">在职联络人</span>
                  <span class="font-medium">25 人</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">离职待补充</span>
                  <span class="font-medium">3 人</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">覆盖部门数</span>
                  <span class="font-medium">12/15 个</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  部门覆盖率
                </div>
              </template>
              <div class="flex justify-center">
                <div ref="coverageChart" class="h-40 w-40" />
              </div>
              <div class="mt-2 text-center">
                <span class="text-2xl font-bold">80%</span>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  培训完成率
                </div>
              </template>
              <div class="flex justify-center">
                <div ref="trainingChart" class="h-40 w-40" />
              </div>
              <div class="mt-2 text-center">
                <span class="text-2xl font-bold">75%</span>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  最近活动
                </div>
              </template>
              <div class="space-y-3">
                <div class="flex space-x-3">
                  <div class="flex-shrink-0">
                    <el-avatar
                      size="small"
                      src="https://ai-public.mastergo.com/ai/img_res/bb6ea68d553afb6dd7bbfaf2f230a857.jpg"
                    />
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      李红
                    </p>
                    <p class="text-xs text-gray-500">
                      今天 10:30
                    </p>
                    <p class="mt-1 text-xs">
                      完成了人事合规培训
                    </p>
                  </div>
                </div>
                <div class="flex space-x-3">
                  <div class="flex-shrink-0">
                    <el-avatar
                      size="small"
                      src="https://ai-public.mastergo.com/ai/img_res/af092bd0408f89a2a9ff073e1943b91e.jpg"
                    />
                  </div>
                  <div>
                    <p class="text-sm font-medium">
                      王明
                    </p>
                    <p class="text-xs text-gray-500">
                      昨天 15:45
                    </p>
                    <p class="mt-1 text-xs">
                      更新了财务合规手册
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-menu {
    border-right: none;
  }

  .el-table {
    --el-table-border-color: #f0f0f0;
  }

  .el-tree {
    --el-tree-node-hover-bg-color: #f5f7fa;
  }

  .el-tag {
    --el-tag-border-radius: 4px;
  }
</style>
