<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowDown as ElIconArrowDown,
  Bottom as ElIconBottom,
  DataBoard as ElIconDataBoard,
  Filter as ElIconFilter,
  <PERSON>clip as ElIconPaperc<PERSON>,
  Search as ElIconSearch,
  Tickets as ElIconTickets,
  Top as ElIconTop,
  Warning as ElIconWarning,
} from '@element-plus/icons-vue'
import intelligentReportingApi from '@/api/report/intelligentReporting'

const activeName = ref('first')
const isListView = ref(false)
const multipleSelection = ref([])
const loading = ref(false)
const tableData = ref([])
const pagination = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  type: '',
  priority: '',
  dateRange: null,
})

// 风险等级映射
const levelMap = {
  LOW: '低',
  MIDDLE: '中',
  HIGH: '高',
}

// 获取风险等级显示文本
function getLevelText(level: string) {
  return levelMap[level] || level
}

// 获取风险等级标签类型
function getLevelTagType(level: string) {
  switch (level) {
    case 'HIGH': return 'danger'
    case 'MIDDLE': return 'warning'
    case 'LOW': return 'success'
    default: return 'info'
  }
}

// 时间格式化
function formatTime(instant: any) {
  if (!instant || !instant.seconds) { return '' }
  return new Date(instant.seconds * 1000).toLocaleString('zh-CN')
}

function handleSelectionChange(val) {
  multipleSelection.value = val
}
// 获取处理列表
async function getHandlingList() {
  try {
    loading.value = true
    const params = {
      page: pagination.value.page,
      size: pagination.value.size,
      detailId: 1,
      // ...searchForm
    }

    // 处理日期范围
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }
    delete params.dateRange

    const response = await intelligentReportingApi.handling(params)
    if (response && response.content) {
      tableData.value = response.content || []
      pagination.value.total = response.totalElements || 0
    }
  }
  catch (error) {
    console.error('获取处理列表失败:', error)
    ElMessage.error('获取处理列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取举报处理详情
async function getHandlingDetail(id: number) {
  try {
    const response = await intelligentReportingApi.handling({ id }, 'info')
    return response
  }
  catch (error) {
    console.error('获取举报处理详情失败:', error)
    ElMessage.error('获取详情失败')
    return null
  }
}

// 分页改变
function handlePageChange(page: number) {
  pagination.value.page = page
  getHandlingList()
}

// 每页数量改变
function handleSizeChange(size: number) {
  pagination.value.size = size
  pagination.value.page = 1
  getHandlingList()
}

// 搜索
function handleSearch() {
  pagination.value.page = 1
  getHandlingList()
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    type: '',
    priority: '',
    dateRange: null,
  })
  pagination.value.page = 1
  getHandlingList()
}

// 查看详情
async function handleView(row: any) {
  const detail = await getHandlingDetail(row.id)
  if (detail) {
    console.log('举报处理详情:', detail)
    // 这里可以跳转到详情页面或打开详情弹窗
  }
}

// 处理操作
function handleProcess(row: any) {
  console.log('处理举报:', row)
  // 这里可以跳转到处理页面
}

function handleSortChange({ column, prop, order }) {
  console.log('Sort changed:', prop, order)
  // 这里可以添加排序逻辑
  getHandlingList()
}

// 组件挂载时获取数据
onMounted(() => {
  getHandlingList()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              举报处理工作台
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button-group>
              <el-button
                type="primary" class="!rounded-button whitespace-nowrap"
                :class="{ '!bg-indigo-600': !isListView }" @click="isListView = false"
              >
                <el-icon class="mr-1">
                  <ElIconDataBoard />
                </el-icon>
                看板视图
              </el-button>
              <el-button
                class="!rounded-button whitespace-nowrap" :class="{ '!bg-indigo-600 !text-white': isListView }"
                @click="isListView = true"
              >
                <el-icon class="mr-1">
                  <ElIconTickets />
                </el-icon>
                列表视图
              </el-button>
            </el-button-group>
            <el-button class="!rounded-button whitespace-nowrap">
              <el-icon class="mr-1">
                <ElIconFilter />
              </el-icon>
              筛选/排序
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-card shadow="hover" class="mt-20 cursor-pointer">
          <!-- 筛选区 -->
          <!-- <el-card shadow="never" class="mb-6"> -->
          <div class="flex flex-wrap items-center gap-4">
            <el-select v-model="searchForm.status" placeholder="举报状态" size="small" style="width: 120px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="待受理" value="pending" />
              <el-option label="受理中" value="processing" />
              <el-option label="调查中" value="investigating" />
              <el-option label="已处理" value="resolved" />
              <el-option label="已关闭" value="closed" />
            </el-select>
            <el-select v-model="searchForm.type" placeholder="举报类型" size="small" style="width: 120px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="财务违规" value="finance" />
              <el-option label="人事违规" value="hr" />
              <el-option label="数据安全" value="security" />
              <el-option label="商业道德" value="ethics" />
            </el-select>
            <el-select v-model="searchForm.priority" placeholder="优先级" size="small" style="width: 100px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="高" value="HIGH" />
              <el-option label="中" value="MIDDLE" />
              <el-option label="低" value="LOW" />
            </el-select>
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              size="small" style="width: 240px;"
            />
            <el-input v-model="searchForm.keyword" placeholder="关键字搜索" size="small" style="width: 200px;" @keyup.enter="handleSearch">
              <template #prefix>
                <el-icon><ElIconSearch /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" size="small" @click="handleSearch">
              搜索
            </el-button>
            <el-button size="small" @click="handleReset">
              重置
            </el-button>
          </div>
          <!-- </el-card> -->
        </el-card>
        <el-row :gutter="20" class="mt-20">
          <el-col :span="18">
            <!-- 卡片视图 -->
            <div v-if="!isListView" v-loading="loading" class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
              <el-card
                v-for="item in tableData"
                :key="item.id"
                shadow="hover"
                class="cursor-pointer border-l-4" :class="[
                  item.level === 'HIGH' ? 'border-red-500'
                  : item.level === 'MIDDLE' ? 'border-yellow-500' : 'border-green-500',
                ]"
                @click="handleView(item)"
              >
                <div class="h-full flex flex-col">
                  <div class="flex items-start justify-between">
                    <div>
                      <el-tag size="small" :type="getLevelTagType(item.level)">
                        {{ getLevelText(item.level) }}
                      </el-tag>
                      <el-tag size="small" class="ml-2">
                        {{ item.violationDetail?.type || '未知类型' }}
                      </el-tag>
                      <el-tag size="small" type="primary" class="ml-2">
                        {{ item.violationDetail?.status || '未知状态' }}
                      </el-tag>
                    </div>
                    <el-icon v-if="item.violationDetail?.attachmentList?.length" class="text-gray-400">
                      <ElIconPaperclip />
                    </el-icon>
                  </div>
                  <h3 class="mt-2 text-lg font-medium">
                    {{ item.violationDetail?.title || '无标题' }}
                  </h3>
                  <p class="mt-1 text-sm text-gray-500">
                    编号: {{ item.violationDetail?.reportNumber || item.id }}
                  </p>
                  <div class="mt-3 text-sm">
                    <div class="flex justify-between text-gray-500">
                      <span>接收时间</span>
                      <span>{{ formatTime(item.createdAt) }}</span>
                    </div>
                    <div class="mt-1 flex justify-between" :class="item.level === 'HIGH' ? 'text-red-500' : 'text-yellow-500'">
                      <span>更新时间</span>
                      <span>{{ formatTime(item.updatedAt) }}</span>
                    </div>
                  </div>
                  <div class="mt-3">
                    <el-progress :percentage="Math.random() * 100" :show-text="false" />
                  </div>
                  <div class="mt-4 flex items-center justify-between">
                    <div class="flex items-center">
                      <el-avatar :size="24" />
                      <span class="ml-2 text-sm">{{ item.updatedBy || '未知' }}</span>
                    </div>
                    <div class="flex space-x-2">
                      <el-button size="small" class="!rounded-button whitespace-nowrap" @click.stop="handleView(item)">
                        查看
                      </el-button>
                      <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap" @click.stop="handleProcess(item)">
                        处理
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
            <!-- 列表视图 -->
            <el-card v-else shadow="hover" class="overflow-hidden">
              <el-table
                v-loading="loading"
                :data="tableData"
                style="width: 100%;"
                highlight-current-row
                border
                @sort-change="handleSortChange"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="id" label="举报编号" sortable width="140">
                  <template #default="{ row }">
                    {{ row.violationDetail?.reportNumber || row.id }}
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="举报标题" sortable min-width="180">
                  <template #default="{ row }">
                    {{ row.violationDetail?.title || '无标题' }}
                  </template>
                </el-table-column>
                <el-table-column prop="type" label="举报类型" sortable width="120">
                  <template #default="{ row }">
                    <el-tag size="small">
                      {{ row.violationDetail?.type || '未知类型' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="level" label="优先级" sortable width="100">
                  <template #default="{ row }">
                    <el-tag
                      size="small"
                      :type="getLevelTagType(row.level)"
                    >
                      {{ getLevelText(row.level) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" sortable width="120">
                  <template #default="{ row }">
                    <el-tag
                      size="small"
                      type="primary"
                    >
                      {{ row.violationDetail?.status || '未知状态' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="接收时间" sortable width="160">
                  <template #default="{ row }">
                    {{ formatTime(row.createdAt) }}
                  </template>
                </el-table-column>
                <el-table-column prop="updatedBy" label="处理人" sortable width="120" />
                <el-table-column prop="updatedAt" label="更新时间" sortable width="160">
                  <template #default="{ row }">
                    {{ formatTime(row.updatedAt) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row }">
                    <div class="flex space-x-2">
                      <el-button size="small" class="!rounded-button whitespace-nowrap" @click="handleView(row)">
                        查看
                      </el-button>
                      <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap" @click="handleProcess(row)">
                        处理
                      </el-button>
                      <el-dropdown>
                        <el-button size="small" class="!rounded-button whitespace-nowrap">
                          更多<el-icon class="ml-1">
                            <ElIconArrowDown />
                          </el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item>分配</el-dropdown-item>
                            <el-dropdown-item>转交</el-dropdown-item>
                            <el-dropdown-item divided>
                              关闭
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="mt-4 flex justify-end">
                <el-pagination
                  v-model:current-page="pagination.page"
                  v-model:page-size="pagination.size"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="pagination.total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handlePageChange"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="never">
              <div class="mb-4 flex items-center justify-between">
                <h3 class="font-bold">
                  待办提醒
                </h3>
                <el-button type="text" size="small">
                  全部
                </el-button>
              </div>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <div class="flex-1">
                    <p class="text-sm font-medium">
                      完成差旅费用调查报告
                    </p>
                    <div class="mt-1 flex items-center">
                      <el-icon class="mr-1 text-red-500">
                        <ElIconWarning />
                      </el-icon>
                      <p class="text-xs text-red-500">
                        剩余 2小时
                      </p>
                    </div>
                  </div>
                  <el-tag size="small" type="danger">
                    高
                  </el-tag>
                </li>
                <li class="flex items-start">
                  <div class="flex-1">
                    <p class="text-sm font-medium">
                      面试歧视问题调查
                    </p>
                    <div class="mt-1 flex items-center">
                      <el-icon class="mr-1 text-yellow-500">
                        <ElIconWarning />
                      </el-icon>
                      <p class="text-xs text-yellow-500">
                        剩余 1天
                      </p>
                    </div>
                  </div>
                  <el-tag size="small" type="warning">
                    中
                  </el-tag>
                </li>
                <li class="flex items-start">
                  <div class="flex-1">
                    <p class="text-sm font-medium">
                      供应商礼品政策审查
                    </p>
                    <p class="mt-1 text-xs text-green-500">
                      剩余 3天
                    </p>
                  </div>
                  <el-tag size="small" type="success">
                    低
                  </el-tag>
                </li>
              </ul>
            </el-card>
            <el-card shadow="never">
              <div class="mb-4 flex items-center justify-between">
                <h3 class="font-bold">
                  最近处理
                </h3>
                <el-button type="text" size="small">
                  全部
                </el-button>
              </div>
              <ul class="space-y-3">
                <li class="flex items-start justify-between">
                  <div>
                    <p class="text-sm font-medium">
                      数据泄露事件调查
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      2023-11-14
                    </p>
                  </div>
                  <span class="text-xs text-green-500">已处理</span>
                </li>
                <li class="flex items-start justify-between">
                  <div>
                    <p class="text-sm font-medium">
                      加班费计算问题
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      2023-11-13
                    </p>
                  </div>
                  <span class="text-xs text-gray-500">已关闭</span>
                </li>
                <li class="flex items-start justify-between">
                  <div>
                    <p class="text-sm font-medium">
                      采购流程违规
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      2023-11-10
                    </p>
                  </div>
                  <span class="text-xs text-green-500">已处理</span>
                </li>
              </ul>
            </el-card>
            <el-card shadow="never">
              <h3 class="mb-4 font-bold">
                团队活动
              </h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <el-avatar
                    :size="32"
                    src="https://mastergo.com/ai/api/search-image?query=professional business portrait of a young asian woman in formal attire with neutral background&width=100&height=100&orientation=portrait"
                  />
                  <div class="ml-3">
                    <p class="text-sm font-medium">
                      陈思思
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      完成了"采购流程审查"
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <el-avatar
                    :size="32"
                    src="https://ai-public.mastergo.com/ai/img_res/a7e5cfa1fa9c07a919dc2ea3f9ef4b33.jpg"
                  />
                  <div class="ml-3">
                    <p class="text-sm font-medium">
                      刘建国
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      受理了"加班费投诉"
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <el-avatar
                    :size="32"
                    src="https://ai-public.mastergo.com/ai/img_res/9825790a2b5817ebde1b1153670a9c26.jpg"
                  />
                  <div class="ml-3">
                    <p class="text-sm font-medium">
                      赵雨晴
                    </p>
                    <p class="mt-1 text-xs text-gray-500">
                      关闭了"数据泄露调查"
                    </p>
                  </div>
                </li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-card {
    border-radius: 8px;
  }

  .el-card__body {
    padding: 16px;
  }

  /* Table styles */
  :deep(.el-table) {
    --el-table-border-color: #f0f0f0;
    --el-table-header-bg-color: #f8fafc;
    --el-table-row-hover-bg-color: #f5f7fa;
  }

  :deep(.el-table th.el-table__cell) {
    font-weight: 600;
    color: #4b5563;
    background-color: #f8fafc !important;
  }

  :deep(.el-table .el-table__cell) {
    padding: 12px 0;
  }

  :deep(.el-table .el-table__cell .cell) {
    padding: 0 12px;
    line-height: 1.5;
  }

  :deep(.el-table--border .el-table__cell) {
    border-right: 1px solid #f0f0f0;
  }

  :deep(.el-table--border) {
    border: 1px solid #f0f0f0;
    border-bottom: none;
  }

  :deep(.el-table--border::after) {
    background-color: #f0f0f0;
  }
</style>
