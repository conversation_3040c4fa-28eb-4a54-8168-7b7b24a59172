import '@/utils/system.copyright'

import FloatingVue from 'floating-vue'
import 'floating-vue/dist/style.css'

import Message from 'vue-m-message'
import 'vue-m-message/dist/style.css'

import VWave from 'v-wave'

import 'overlayscrollbars/overlayscrollbars.css'

import VueUeditorWrap from 'vue-ueditor-wrap'
import App from './App.vue'
import pinia from './store'
import router from './router'
import ui from './ui-provider'
import { setupI18n } from './locales'

// 自定义指令
import directive from '@/utils/directive'

// 错误日志上报
import errorLog from '@/utils/error.log'

// 加载 svg 图标
import 'virtual:svg-icons-register'

// 加载 iconify 图标
import { downloadAndInstall } from '@/iconify'
import icons from '@/iconify/index.json'

import 'virtual:uno.css'
import pagination from './components/Pagination/index.vue'
import pageCompon from './components/pageComponent/index.vue'
import drawer from './components/drawer/index.vue'
import richText from './components/richText/index.vue'



// 全局样式
import '@/assets/styles/globals.scss'
import tools from '@/publicMethod/index'

const app = createApp(App)
app.config.globalProperties.$tools = tools // 挂载全局公共方法
app.use(FloatingVue, {
  distance: 12,
})
app.component('pageCompon', pageCompon)
app.component('Pagination', pagination)
app.component('richText', richText)
app.component('Drawer', drawer)
// app.component('VueUeditorWrap', )
app.use(VueUeditorWrap)
app.use(Message)
app.use(VWave, {})
app.use(pinia)
app.use(router)
app.use(ui)
app.use(setupI18n())
directive(app)
errorLog(app)
if (icons.isOfflineUse) {
  for (const info of icons.collections) {
    downloadAndInstall(info)
  }
}

app.mount('#app')
