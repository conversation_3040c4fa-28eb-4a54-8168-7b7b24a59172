<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import {
  Document,
  DocumentAdd,
  Download,
  Edit,
  MoreFilled,
  Plus,
  Printer,
  Upload,
  VideoCamera,
} from '@element-plus/icons-vue'
import handlingMeasuresApi from '@/api/problemTask/handlingMeasures'
import dictApi from '@/api/modules/system/dict'

const route = useRoute()

// 详情数据
const detailData = ref({
  id: '',
  title: '',
  dealCode: '',
  dealType: '',
  level: '',
  investigateId: null,
  dutyEmployeeId: '',
  dutyEmployeeOrgId: '',
  dealEmployeeId: '',
  startDate: '',
  finishDate: '',
  status: '',
  violationDesc: '',
  violationImpact: '',
  responsibility: '',
  metadata: '',
  measureList: [],
  // 扩展字段
  dutyEmployeeName: '',
  dutyEmployeeOrgName: '',
  dealEmployeeName: '',
  investigateName: '',
  createTime: '',
  actualFinishDate: '',
})

// 字典数据
const dealTypeOptions = ref<Array<{ name: string, value: string }>>([])
const measureTypeOptions = ref<Array<{ name: string, value: string }>>([])

const activeTab = ref('measures')
const showAddMeasureDialog = ref(false)
const showAddRecordDialog = ref(false)
const showMeasureDetail = ref(false)

const newMeasure = ref({
  id: '',
  name: '',
  measureType: '',
  startDate: '',
  finishDate: '',
  desc: '',
  status: '',
})

const newRecord = ref({
  content: '',
  result: '',
})

const currentMeasure = ref({
  name: '纪律处分通知',
  type: '纪律处分',
  startDate: '2023-04-28',
  endDate: '2023-05-05',
  owner: '李华',
  status: '进行中',
  metadata: '对责任人张明进行书面警告处分，并记入个人档案。',
  plan: '1. 人力资源部起草处分通知\n2. 部门负责人签字确认\n3. 送达责任人签字确认\n4. 归档处理',
  resource: '人力资源部1人日',
  expectation: '对责任人产生警示作用，防止类似行为再次发生',
  standard: '处分通知经责任人签字确认并归档',
})

const _measures = ref([
  {
    name: '纪律处分通知',
    type: '纪律处分',
    startDate: '2023-04-28',
    endDate: '2023-05-05',
    owner: '李华',
    status: '进行中',
  },
  {
    name: '经济处罚',
    type: '经济处罚',
    startDate: '2023-04-28',
    endDate: '2023-05-05',
    owner: '王伟',
    status: '已完成',
  },
  {
    name: '财务系统权限整改',
    type: '系统整改',
    startDate: '2023-05-01',
    endDate: '2023-05-15',
    owner: '王伟',
    status: '进行中',
  },
  {
    name: '财务规范培训',
    type: '培训教育',
    startDate: '2023-05-08',
    endDate: '2023-05-10',
    owner: '李华',
    status: '未开始',
  },
])

const records = ref([
  {
    time: '2023-04-26 09:30',
    title: '开始处理',
    content: '收到处理任务，开始收集相关资料',
    handler: '李华',
    attachment: false,
  },
  {
    time: '2023-04-27 14:15',
    title: '初步调查',
    content: '与IT部门沟通，获取系统操作日志',
    handler: '李华',
    attachment: true,
  },
  {
    time: '2023-04-28 11:00',
    title: '约谈责任人',
    content: '约谈张明，了解违规操作原因和过程',
    handler: '李华',
    attachment: false,
  },
  {
    time: '2023-05-02 16:30',
    title: '经济处罚执行',
    content: '完成对张明的经济处罚，扣除当月奖金2000元',
    handler: '王伟',
    attachment: true,
  },
])

const evidences = ref([
  {
    name: '系统操作日志',
    type: '日志文件',
    uploadTime: '2023-04-26 10:15',
    uploader: '李华',
    metadata: '2023年4月财务系统操作日志',
  },
  {
    name: '报销单修改记录',
    type: '截图',
    uploadTime: '2023-04-27 15:30',
    uploader: '李华',
    metadata: '违规修改的报销单前后对比',
  },
  {
    name: '约谈记录',
    type: '文档',
    uploadTime: '2023-04-28 14:00',
    uploader: '李华',
    metadata: '与张明的约谈记录',
  },
])

const improvements = ref([
  {
    content: '加强财务系统权限管理，设置多级审批',
    proposer: '李华',
    proposeTime: '2023-05-03',
    status: '进行中',
  },
  {
    content: '定期开展财务规范培训',
    proposer: '王伟',
    proposeTime: '2023-05-04',
    status: '已完成',
  },
  {
    content: '建立财务操作异常预警机制',
    proposer: 'IT部门',
    proposeTime: '2023-05-05',
    status: '未开始',
  },
])

const logs = ref([
  {
    time: '2023-04-25 14:30',
    action: '创建处理',
    content: '创建处理任务"员工违规操作处理"',
    operator: '王伟',
    ip: '*************',
  },
  {
    time: '2023-04-26 09:30',
    action: '开始处理',
    content: '李华开始处理此任务',
    operator: '李华',
    ip: '*************',
  },
  {
    time: '2023-04-28 11:00',
    action: '添加措施',
    content: '添加处理措施"纪律处分通知"',
    operator: '李华',
    ip: '*************',
  },
  {
    time: '2023-05-02 16:30',
    action: '更新状态',
    content: '将"经济处罚"措施状态更新为"已完成"',
    operator: '王伟',
    ip: '*************',
  },
])

const changes = ref([
  {
    time: '2023-04-26 09:30',
    operator: '李华',
    field: '处理状态',
    oldValue: '待处理',
    newValue: '处理中',
    reason: '开始处理任务',
  },
  {
    time: '2023-04-28 11:00',
    operator: '李华',
    field: '处理人',
    oldValue: '-',
    newValue: '李华',
    reason: '分配处理人',
  },
  {
    time: '2023-05-02 16:30',
    operator: '王伟',
    field: '计划完成日期',
    oldValue: '2023-05-08',
    newValue: '2023-05-10',
    reason: '延长处理期限',
  },
])

function getStatusTagType(status: string) {
  switch (status) {
    case '已完成':
    case 'FINISHED':
      return 'success'
    case '进行中':
    case 'PROGRESSING':
      return 'primary'
    case '未开始':
    case 'NO_START':
      return 'info'
    case '已逾期':
      return 'danger'
    case '已暂停':
    case 'PAUSED':
      return 'warning'
    case '已取消':
    case 'CANCELED':
      return 'danger'
    default:
      return 'info'
  }
}

function handleExceed() {
  ElMessage.warning('最多上传3个文件')
}

function _addMeasure() {
  createMeasure()
}

function addRecord() {
  records.value.unshift({
    time: `${new Date().toISOString().split('T')[0]} ${new Date().toLocaleTimeString('zh-CN', { hour12: false })}`,
    title: '处理记录',
    content: newRecord.value.content,
    handler: '李华',
    attachment: false,
  })
  showAddRecordDialog.value = false
  newRecord.value = {
    content: '',
    result: '',
  }
  ElMessage.success('添加处理记录成功')
}

function _viewMeasureDetail(measure: any) {
  currentMeasure.value = measure
  showMeasureDetail.value = true
}

// 编辑措施
function editMeasure(measure: any) {
  // 填充表单数据
  newMeasure.value = {
    id: measure.id || '',
    name: measure.name || '',
    measureType: measure.measureType || '',
    startDate: measure.startDate || '',
    finishDate: measure.finishDate || '',
    desc: measure.desc || '',
    status: measure.status || '',
  }

  // 显示编辑对话框
  showAddMeasureDialog.value = true
}

// 删除措施
function deleteMeasure(measure: any) {
  ElMessageBox.confirm(
    `确定要删除措施"${measure.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    _deleteMeasure(measure.id)
  }).catch(() => {
    // 用户取消删除
  })
}

// 获取处理措施详情
async function fetchHandlingMeasureDetail(id: any) {
  try {
    const response = await handlingMeasuresApi.getHandlingMeasureDetail(id)
    if (response) {
      // 将获取到的数据赋值给详情数据
      Object.assign(detailData.value, response)
    }
  }
  catch (error) {
    console.error('获取处理措施详情失败:', error)
    ElMessage.error('获取处理措施详情失败')
  }
}

// 获取字典数据
async function fetchDictOptions() {
  try {
    const response = await dictApi.dictAll(25)
    const response2 = await dictApi.dictAll(35)
    if (response) {
      dealTypeOptions.value = response
      measureTypeOptions.value = response2
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 获取处理类型名称
function getDealTypeName(value: string) {
  const option = dealTypeOptions.value.find(item => item.value === value)
  return option ? option.name : value
}

// 获取措施类型名称
function getMeasureTypeName(value: string) {
  const option = measureTypeOptions.value.find(item => item.value === value)
  return option ? option.name : value
}

// 获取严重程度显示文本
function getLevelText(level: string) {
  switch (level) {
    case 'HIGH':
      return '严重'
    case 'MIDDLE':
      return '中等'
    case 'LOW':
      return '轻微'
    default:
      return level
  }
}

// 获取状态显示文本
function getStatusText(status: string) {
  switch (status) {
    case 'NO_START':
      return '未开始'
    case 'PROGRESSING':
      return '进行中'
    case 'FINISHED':
      return '已完成'
    case 'PAUSED':
      return '已暂停'
    case 'CANCELED':
      return '已取消'
    default:
      return status
  }
}

// 新增或编辑责任追究措施
async function createMeasure() {
  try {
    const dealId = route.query.id || route.params.id
    const measureData: any = {
      name: newMeasure.value.name,
      measureType: newMeasure.value.measureType,
      startDate: newMeasure.value.startDate,
      finishDate: newMeasure.value.finishDate,
      desc: newMeasure.value.desc,
      status: newMeasure.value.status || 'NO_START',
    }

    if (newMeasure.value.id) {
      // 编辑模式
      await handlingMeasuresApi.updateResponsibilityMeasure(newMeasure.value.id, measureData)
      ElMessage.success('编辑处理措施成功')
    }
    else {
      // 新增模式
      measureData.dealId = dealId // 详情页面的ID
      await handlingMeasuresApi.createResponsibilityMeasure(measureData)
      ElMessage.success('新增处理措施成功')
    }

    showAddMeasureDialog.value = false

    // 重新获取详情数据
    if (dealId) {
      fetchHandlingMeasureDetail(dealId)
    }

    // 重置表单
    newMeasure.value = {
      id: '',
      name: '',
      measureType: '',
      startDate: '',
      finishDate: '',
      desc: '',
      status: '',
    }
  }
  catch (error) {
    console.error('处理措施操作失败:', error)
    ElMessage.error('处理措施操作失败')
  }
}

// 编辑责任追究措施
async function _editMeasure(measure: any) {
  try {
    const measureData = {
      name: measure.name,
      measureType: measure.measureType,
      startDate: measure.startDate,
      finishDate: measure.finishDate,
      metadata: measure.metadata,
      status: measure.status,
    }

    await handlingMeasuresApi.updateResponsibilityMeasure(measure.id, measureData)
    ElMessage.success('编辑处理措施成功')

    // 重新获取详情数据
    const dealId = route.query.id || route.params.id
    if (dealId) {
      fetchHandlingMeasureDetail(dealId)
    }
  }
  catch (error) {
    console.error('编辑处理措施失败:', error)
    ElMessage.error('编辑处理措施失败')
  }
}

// 删除责任追究措施
async function _deleteMeasure(measureId: string) {
  try {
    await handlingMeasuresApi.deleteResponsibilityMeasure(measureId)
    ElMessage.success('删除处理措施成功')

    // 重新获取详情数据
    const dealId = route.query.id || route.params.id
    if (dealId) {
      fetchHandlingMeasureDetail(dealId)
    }
  }
  catch (error) {
    console.error('删除处理措施失败:', error)
    ElMessage.error('删除处理措施失败')
  }
}

// 页面初始化
onMounted(() => {
  const id = route.query.id || route.params.id
  if (id) {
    fetchHandlingMeasureDetail(id)
  }
  fetchDictOptions()
})
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              {{ detailData.title || '处理措施详情' }}
            </h1>
            <el-tag :type="getStatusTagType(detailData.status)" class="ml-4">
              {{ getStatusText(detailData.status) }}
            </el-tag>
          </div>
          <div class="flex items-center space-x-2">
            <el-button v-auth="'handlingMeasures/detail/startProcess'" type="primary" class="!rounded-button whitespace-nowrap">
              开始处理
            </el-button>
            <el-button v-auth="'handlingMeasures/detail/edit'" type="primary" plain class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button v-auth="'handlingMeasures/detail/download'" type="primary" plain class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Download />
              </el-icon>
            </el-button>
            <el-button v-auth="'handlingMeasures/detail/print'" type="primary" plain class="!rounded-button whitespace-nowrap">
              <el-icon>
                <Printer />
              </el-icon>
            </el-button>
            <el-dropdown>
              <el-button type="primary" plain class="!rounded-button whitespace-nowrap">
                <el-icon>
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-auth="'handlingMeasures/detail/pause'">
                    暂停
                  </el-dropdown-item>
                  <el-dropdown-item v-auth="'handlingMeasures/detail/cancel'">
                    取消
                  </el-dropdown-item>
                  <el-dropdown-item v-auth="'handlingMeasures/detail/share'">
                    共享
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <div class="grid grid-cols-2 gap-4">
                <div class="grid grid-cols-2 gap-2">
                  <div class="text-right text-gray-500">
                    处理编号
                  </div>
                  <div>{{ detailData.dealCode }}</div>
                  <div class="text-right text-gray-500">
                    处理标题
                  </div>
                  <div>{{ detailData.title }}</div>
                  <div class="text-right text-gray-500">
                    处理类型
                  </div>
                  <div>{{ getDealTypeName(detailData.dealType) }}</div>
                  <div class="text-right text-gray-500">
                    严重程度
                  </div>
                  <div>
                    <el-tag :type="detailData.level === 'HIGH' ? 'danger' : detailData.level === 'MIDDLE' ? 'warning' : 'info'">
                      {{ getLevelText(detailData.level) }}
                    </el-tag>
                  </div>
                  <div class="text-right text-gray-500">
                    来源调查
                  </div>
                  <div>
                    <el-link v-if="detailData.investigateName" type="primary">
                      {{ detailData.investigateName }}
                    </el-link>
                    <span v-else>-</span>
                  </div>
                </div>
                <div class="grid grid-cols-2 gap-2">
                  <div class="text-right text-gray-500">
                    责任人
                  </div>
                  <div>{{ detailData.dutyEmployeeName }}</div>
                  <div class="text-right text-gray-500">
                    部门
                  </div>
                  <div>{{ detailData.dutyEmployeeOrgName }}</div>
                  <div class="text-right text-gray-500">
                    处理人
                  </div>
                  <div>{{ detailData.dealEmployeeName }}</div>
                  <div class="text-right text-gray-500">
                    提出日期
                  </div>
                  <div>{{ detailData.createTime || '-' }}</div>
                  <div class="text-right text-gray-500">
                    处理状态
                  </div>
                  <div>{{ getStatusText(detailData.status) }}</div>
                  <div class="text-right text-gray-500">
                    开始日期
                  </div>
                  <div>{{ detailData.startDate || '-' }}</div>
                  <div class="text-right text-gray-500">
                    计划完成日期
                  </div>
                  <div>{{ detailData.finishDate || '-' }}</div>
                  <div class="text-right text-gray-500">
                    实际完成日期
                  </div>
                  <div>{{ detailData.actualFinishDate || '-' }}</div>
                </div>
              </div>
              <div v-if="false" class="mt-6">
                <div class="mb-2 flex justify-between">
                  <span class="text-sm text-gray-500">处理进度</span>
                  <span class="text-sm font-medium">步骤 2/4</span>
                </div>
                <el-steps :active="2" align-center>
                  <el-step title="立案" description="2023-04-25" />
                  <el-step title="调查" description="2023-04-26" />
                  <el-step title="处理" />
                  <el-step title="结案" />
                </el-steps>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  违规情况
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <h3 class="mb-2 text-sm text-gray-500 font-medium">
                    违规描述
                  </h3>
                  <p class="text-sm">
                    {{ detailData.violationDesc || '暂无违规描述' }}
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-500 font-medium">
                    违规影响
                  </h3>
                  <p class="text-sm">
                    {{ detailData.violationImpact || '暂无违规影响描述' }}
                  </p>
                </div>
                <div>
                  <h3 class="mb-2 text-sm text-gray-500 font-medium">
                    责任认定
                  </h3>
                  <p class="text-sm">
                    {{ detailData.responsibility || '暂无责任认定' }}
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <!-- <template #header>
                <div class="f-16 fw-600">相关调查</div>
              </template> -->
              <el-tabs v-model="activeTab">
                <el-tab-pane label="处理措施" name="measures">
                  <div class="flex justify-between">
                    <h3 class="mb-4 text-lg font-bold">
                      处理措施列表
                    </h3>
                    <el-button
                      v-auth="'handlingMeasures/detail/addNewMeasure'" type="primary"
                      class="!rounded-button whitespace-nowrap"
                      @click="showAddMeasureDialog = true"
                    >
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>新增措施
                    </el-button>
                  </div>
                  <el-table :data="detailData.measureList" style="width: 100%;">
                    <el-table-column prop="name" label="措施名称" width="180" />
                    <el-table-column prop="measureType" label="措施类型" width="120">
                      <template #default="{ row }">
                        {{ getMeasureTypeName(row.measureType) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="startDate" label="开始日期" width="120" />
                    <el-table-column prop="finishDate" label="完成日期" width="120" />
                    <!-- <el-table-column prop="executorName" label="负责人" width="120" /> -->
                    <el-table-column prop="status" label="状态" width="120">
                      <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                          {{ getStatusText(row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                      <template #default="{ row }">
                        <!-- <el-button type="text" size="small" @click="viewMeasureDetail(row)">
                          查看
                        </el-button> -->
                        <el-button type="primary" text size="small" @click="editMeasure(row)">
                          编辑
                        </el-button>
                        <el-button type="danger" text size="small" @click="deleteMeasure(row)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
                <el-tab-pane label="处理记录" name="records">
                  <div class="flex justify-between">
                    <h3 class="mb-4 text-lg font-bold">
                      处理记录
                    </h3>
                    <el-button
                      v-auth="'handlingMeasures/detail/addRecord'" type="primary"
                      class="!rounded-button whitespace-nowrap"
                      @click="showAddRecordDialog = true"
                    >
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>添加记录
                    </el-button>
                  </div>
                  <el-timeline>
                    <el-timeline-item
                      v-for="(record, index) in records" :key="index" :timestamp="record.time"
                      placement="top"
                    >
                      <el-card>
                        <h4>{{ record.title }}</h4>
                        <p>{{ record.content }}</p>
                        <div class="mt-2 text-sm text-gray-500">
                          <span>处理人: {{ record.handler }}</span>
                          <el-link v-if="record.attachment" type="primary" class="ml-4">
                            查看附件
                          </el-link>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </el-tab-pane>
                <el-tab-pane label="相关证据" name="evidences">
                  <div class="flex justify-between">
                    <h3 class="mb-4 text-lg font-bold">
                      相关证据
                    </h3>
                    <el-button v-auth="'handlingMeasures/detail/uploadEvidence'" type="primary" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Plus />
                      </el-icon>上传证据
                    </el-button>
                  </div>
                  <el-table :data="evidences" style="width: 100%;">
                    <el-table-column prop="name" label="证据名称" width="180" />
                    <el-table-column prop="type" label="证据类型" width="120" />
                    <el-table-column prop="uploadTime" label="上传时间" width="180" />
                    <el-table-column prop="uploader" label="上传人" width="120" />
                    <el-table-column prop="metadata" label="描述" />
                    <el-table-column label="操作" width="120">
                      <template #default>
                        <el-button v-auth="'handlingMeasures/detail/viewAttachment'" type="primary" size="small">
                          查看
                        </el-button>
                        <el-button v-auth="'handlingMeasures/detail/download'" type="primary" size="small">
                          下载
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-tab-pane>
                <el-tab-pane label="处理效果" name="effects">
                  <div class="space-y-6">
                    <div>
                      <div class="flex justify-between">
                        <h3 class="mb-2 text-lg font-bold">
                          效果评估
                        </h3>
                        <el-button v-auth="'handlingMeasures/detail/editAssessment'" type="text">
                          编辑评估
                        </el-button>
                      </div>
                      <div class="grid grid-cols-2 gap-4 border rounded bg-gray-50 p-4">
                        <div>
                          <div class="text-sm text-gray-500">
                            评估结果
                          </div>
                          <div class="mt-1">
                            部分有效
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-500">
                            评估人
                          </div>
                          <div class="mt-1">
                            王伟
                          </div>
                        </div>
                        <div>
                          <div class="text-sm text-gray-500">
                            评估日期
                          </div>
                          <div class="mt-1">
                            2023-05-08
                          </div>
                        </div>
                        <div class="col-span-2">
                          <div class="text-sm text-gray-500">
                            评估说明
                          </div>
                          <div class="mt-1">
                            处理措施对直接责任人产生了震慑效果，但系统漏洞尚未完全修复，存在再次发生类似风险的可能。建议加强IT系统审计和权限管理。
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div class="flex justify-between">
                        <h3 class="mb-2 text-lg font-bold">
                          改进建议
                        </h3>
                        <el-button v-auth="'handlingMeasures/detail/addSuggestion'" type="primary" class="!rounded-button whitespace-nowrap">
                          <el-icon class="mr-1">
                            <Plus />
                          </el-icon>添加建议
                        </el-button>
                      </div>
                      <el-table :data="improvements" style="width: 100%;">
                        <el-table-column prop="content" label="建议内容" />
                        <el-table-column prop="proposer" label="提出人" width="120" />
                        <el-table-column prop="proposeTime" label="提出时间" width="180" />
                        <el-table-column prop="status" label="处理状态" width="120">
                          <template #default="{ row }">
                            <el-tag :type="getStatusTagType(row.status)" size="small">
                              {{ row.status }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                          <template #default>
                            <el-button v-auth="'handlingMeasures/detail/handleSuggestion'" type="primary" size="small">
                              处理
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="操作历史" name="history">
                  <h3 class="mb-4 text-lg font-bold">
                    操作日志
                  </h3>
                  <el-timeline>
                    <el-timeline-item v-for="(log, index) in logs" :key="index" :timestamp="log.time" placement="top">
                      <el-card>
                        <h4>{{ log.action }}</h4>
                        <p>{{ log.content }}</p>
                        <div class="mt-2 text-sm text-gray-500">
                          <span>操作人: {{ log.operator }}</span>
                          <span class="ml-4">IP: {{ log.ip }}</span>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                  <h3 class="mb-4 mt-8 text-lg font-bold">
                    变更记录
                  </h3>
                  <el-table :data="changes" style="width: 100%;">
                    <el-table-column prop="time" label="变更时间" width="180" />
                    <el-table-column prop="operator" label="变更人" width="120" />
                    <el-table-column prop="field" label="变更字段" width="180" />
                    <el-table-column prop="oldValue" label="变更前值" />
                    <el-table-column prop="newValue" label="变更后值" />
                    <el-table-column prop="reason" label="变更原因" />
                  </el-table>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  处理时间线
                </div>
              </template>
              <el-timeline>
                <el-timeline-item timestamp="2023-04-25" placement="top">
                  <p>创建处理</p>
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-26" placement="top" type="primary">
                  <p>开始处理</p>
                </el-timeline-item>
                <el-timeline-item timestamp="2023-04-28" placement="top">
                  <p>初步调查完成</p>
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-05" placement="top">
                  <p>责任人确认</p>
                </el-timeline-item>
                <el-timeline-item timestamp="2023-05-10" placement="top">
                  <p>计划完成日期</p>
                </el-timeline-item>
              </el-timeline>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关调查
                </div>
              </template>
              <div class="space-y-3">
                <el-link type="primary">
                  2023年Q2财务审计调查
                </el-link>
                <div class="flex justify-between text-sm text-gray-500">
                  <span>状态: 已完成</span>
                  <span>2023-04-20</span>
                </div>
                <el-divider />
                <el-link type="primary">
                  财务系统权限审计
                </el-link>
                <div class="flex justify-between text-sm text-gray-500">
                  <span>状态: 进行中</span>
                  <span>2023-05-01</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  相关处理
                </div>
              </template>
              <div class="space-y-3">
                <el-link type="primary">
                  财务系统权限整改
                </el-link>
                <div class="flex justify-between text-sm text-gray-500">
                  <span>状态: 进行中</span>
                  <span>2023-05-03</span>
                </div>
                <el-divider />
                <el-link type="primary">
                  财务部内部培训
                </el-link>
                <div class="flex justify-between text-sm text-gray-500">
                  <span>状态: 已完成</span>
                  <span>2023-04-30</span>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <div>
                  <el-button v-auth="'handlingMeasures/detail/addRecord'" type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-1">
                      <DocumentAdd />
                    </el-icon>添加处理记录
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'handlingMeasures/detail/uploadEvidence'" type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Upload />
                    </el-icon>上传证据
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'handlingMeasures/detail/generateReport'" type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-1">
                      <Document />
                    </el-icon>生成报告
                  </el-button>
                </div>
                <div>
                  <el-button v-auth="'handlingMeasures/detail/startMeeting'" type="primary" class="!rounded-button w-full whitespace-nowrap">
                    <el-icon class="mr-1">
                      <VideoCamera />
                    </el-icon>发起会议
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="">
          <!-- 新增措施弹窗 -->
          <el-dialog v-model="showAddMeasureDialog" title="新增处理措施" width="50%">
            <el-form :model="newMeasure" label-width="120px">
              <el-form-item label="措施名称">
                <el-input v-model="newMeasure.name" />
              </el-form-item>
              <el-form-item label="措施类型">
                <el-select v-model="newMeasure.measureType" placeholder="请选择措施类型" class="w-full">
                  <el-option
                    v-for="option in measureTypeOptions"
                    :key="option.value"
                    :label="option.name"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="开始日期">
                <el-date-picker v-model="newMeasure.startDate" type="date" class="w-full" />
              </el-form-item>
              <el-form-item label="完成日期">
                <el-date-picker v-model="newMeasure.finishDate" type="date" class="w-full" />
              </el-form-item>
              <el-form-item label="措施描述">
                <el-input v-model="newMeasure.desc" type="textarea" :rows="3" />
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="showAddMeasureDialog = false">
                取消
              </el-button>
              <el-button type="primary" @click="createMeasure">
                确认
              </el-button>
            </template>
          </el-dialog>

          <!-- 添加记录弹窗 -->
          <el-dialog v-model="showAddRecordDialog" title="添加处理记录" width="50%">
            <el-form :model="newRecord" label-width="120px">
              <el-form-item label="处理内容">
                <el-input v-model="newRecord.content" type="textarea" :rows="3" />
              </el-form-item>
              <el-form-item label="处理结果">
                <el-input v-model="newRecord.result" type="textarea" :rows="3" />
              </el-form-item>
              <el-form-item label="上传附件">
                <el-upload action="#" :limit="3" :on-exceed="handleExceed">
                  <el-button v-auth="'handlingMeasures/detail/clickUpload'" type="primary">
                    <el-icon class="mr-1">
                      <Upload />
                    </el-icon>点击上传
                  </el-button>
                </el-upload>
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="showAddRecordDialog = false">
                取消
              </el-button>
              <el-button v-auth="'handlingMeasures/detail/confirm'" type="primary" @click="addRecord">
                确认
              </el-button>
            </template>
          </el-dialog>

          <!-- 措施详情弹窗 -->
          <el-dialog v-model="showMeasureDetail" :title="currentMeasure.name" width="60%">
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm text-gray-500">
                    措施类型
                  </div>
                  <div class="mt-1">
                    {{ currentMeasure.type }}
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    负责人
                  </div>
                  <div class="mt-1">
                    {{ currentMeasure.owner }}
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    开始日期
                  </div>
                  <div class="mt-1">
                    {{ currentMeasure.startDate }}
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    完成日期
                  </div>
                  <div class="mt-1">
                    {{ currentMeasure.endDate }}
                  </div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">
                    状态
                  </div>
                  <div class="mt-1">
                    <el-tag
                      :type="getStatusTagType(currentMeasure.status)"
                      size="small"
                    >
                      {{ currentMeasure.status }}
                    </el-tag>
                  </div>
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  措施描述
                </div>
                <div class="mt-1">
                  {{ currentMeasure.metadata }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  实施计划
                </div>
                <div class="mt-1">
                  {{ currentMeasure.plan }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  资源需求
                </div>
                <div class="mt-1">
                  {{ currentMeasure.resource }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  预期效果
                </div>
                <div class="mt-1">
                  {{ currentMeasure.expectation }}
                </div>
              </div>
              <div>
                <div class="text-sm text-gray-500">
                  完成标准
                </div>
                <div class="mt-1">
                  {{ currentMeasure.standard }}
                </div>
              </div>
            </div>
          </el-dialog>
        </div>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .container {
    max-width: 1440px;
    margin: 0 auto;
  }
</style>
