<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'
import * as echarts from 'echarts'

// 权责详情数据
const showDetailModal = ref(false)
const currentDetail = ref({
  itemName: '',
  roleName: '',
  type: '',
  description: '',
  tasks: [] as string[],
  notes: [] as string[],
  relatedDocs: [] as string[],
  references: [] as string[],
})

// 按角色查看数据
const roles = ref([
  { id: 1, name: '财务部经理' },
  { id: 2, name: '合规专员' },
  { id: 3, name: '法务主管' },
  { id: 4, name: '人力资源总监' },
  { id: 5, name: '采购部主管' },
])
const selectedRole = ref(1)

const selectedRoleData = computed(() => {
  if (selectedRole.value === 1) {
    return {
      id: 1,
      name: '财务部经理',
      department: '财务部',
      level: '部门负责人',
      supervisor: 'CFO',
      stats: { R: 8, A: 5, C: 3, I: 2 },
      responsibilities: [
        {
          id: 101,
          itemName: '年度预算编制',
          businessArea: '财务管理',
          type: 'R',
          description: '负责组织年度预算编制工作，协调各部门预算需求',
        },
        {
          id: 102,
          itemName: '大额资金审批',
          businessArea: '资金管理',
          type: 'A',
          description: '审批单笔超过50万元的资金支出',
        },
        {
          id: 103,
          itemName: '税务合规审查',
          businessArea: '税务管理',
          type: 'C',
          description: '为业务部门提供税务合规建议',
        },
        {
          id: 104,
          itemName: '财务系统升级',
          businessArea: 'IT管理',
          type: 'I',
          description: '知悉财务系统升级计划及影响',
        },
      ],
    }
  }
  else if (selectedRole.value === 2) {
    return {
      id: 2,
      name: '合规专员',
      department: '合规部',
      level: '专员',
      supervisor: '合规经理',
      stats: { R: 5, A: 2, C: 6, I: 3 },
      responsibilities: [
        {
          id: 201,
          itemName: '合规培训组织',
          businessArea: '培训管理',
          type: 'R',
          description: '负责组织全公司合规培训',
        },
        {
          id: 202,
          itemName: '合规举报处理',
          businessArea: '风险管理',
          type: 'A',
          description: '审批合规举报处理方案',
        },
      ],
    }
  }
  // 其他角色数据...
  return {
    id: 0,
    name: '',
    department: '',
    level: '',
    supervisor: '',
    stats: { R: 0, A: 0, C: 0, I: 0 },
    responsibilities: [],
  }
})

// 按事项查看数据
const items = ref([
  { id: 1, name: '年度预算编制' },
  { id: 2, name: '供应商准入审核' },
  { id: 3, name: '员工绩效考核' },
  { id: 4, name: '合同审批' },
  { id: 5, name: '数据隐私保护' },
])
const selectedItem = ref(1)

const selectedItemData = computed(() => {
  if (selectedItem.value === 1) {
    return {
      id: 1,
      name: '年度预算编制',
      businessArea: '财务管理',
      riskLevel: '高',
      updateDate: '2023-10-15',
      roleCount: 6,
      mainDepartment: '财务部',
      approvalLevel: 'CFO',
      responsibilities: [
        {
          id: 101,
          roleName: '财务部经理',
          department: '财务部',
          type: 'R',
          description: '负责组织年度预算编制工作，协调各部门预算需求',
        },
        {
          id: 102,
          roleName: '各部门主管',
          department: '各部门',
          type: 'R',
          description: '负责本部门预算编制',
        },
        {
          id: 103,
          roleName: 'CFO',
          department: '财务部',
          type: 'A',
          description: '审批最终年度预算方案',
        },
        {
          id: 104,
          roleName: '董事会',
          department: '公司治理',
          type: 'A',
          description: '审批年度预算最终方案',
        },
      ],
    }
  }
  else if (selectedItem.value === 2) {
    return {
      id: 2,
      name: '供应商准入审核',
      businessArea: '采购管理',
      riskLevel: '中',
      updateDate: '2023-09-20',
      roleCount: 4,
      mainDepartment: '采购部',
      approvalLevel: '采购总监',
      responsibilities: [
        {
          id: 201,
          roleName: '采购专员',
          department: '采购部',
          type: 'R',
          description: '负责供应商初步筛选和资料收集',
        },
        {
          id: 202,
          roleName: '合规专员',
          department: '合规部',
          type: 'C',
          description: '提供供应商合规风险评估',
        },
      ],
    }
  }
  // 其他事项数据...
  return {
    id: 0,
    name: '',
    businessArea: '',
    riskLevel: '',
    updateDate: '',
    roleCount: 0,
    mainDepartment: '',
    approvalLevel: '',
    responsibilities: [],
  }
})

// 图表引用
const rolePieChart = ref<HTMLElement>()
const itemPieChart = ref<HTMLElement>()
const workloadChart = ref<HTMLElement>()
const comparisonChart = ref<HTMLElement>()
const flowChart = ref<HTMLElement>()

// 初始化图表
function initCharts() {
  nextTick(() => {
    // 角色权责分布饼图
    if (rolePieChart.value) {
      const chart = echarts.init(rolePieChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
        },
        series: [
          {
            name: '权责分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: selectedRoleData.value.stats.R, name: '负责(R)', itemStyle: { color: '#3b82f6' } },
              { value: selectedRoleData.value.stats.A, name: '审批(A)', itemStyle: { color: '#10b981' } },
              { value: selectedRoleData.value.stats.C, name: '咨询(C)', itemStyle: { color: '#8b5cf6' } },
              { value: selectedRoleData.value.stats.I, name: '知悉(I)', itemStyle: { color: '#f59e0b' } },
            ],
          },
        ],
      })
    }

    // 事项权责分布饼图
    if (itemPieChart.value) {
      const chart = echarts.init(itemPieChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
        },
        series: [
          {
            name: '权责分布',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 4, name: '负责(R)', itemStyle: { color: '#3b82f6' } },
              { value: 2, name: '审批(A)', itemStyle: { color: '#10b981' } },
              { value: 1, name: '咨询(C)', itemStyle: { color: '#8b5cf6' } },
              { value: 0, name: '知悉(I)', itemStyle: { color: '#f59e0b' } },
            ],
          },
        ],
      })
    }

    // 工作负荷图表
    if (workloadChart.value) {
      const chart = echarts.init(workloadChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        },
        yAxis: {
          type: 'value',
          name: '工作小时数',
        },
        series: [
          {
            name: '工作负荷',
            type: 'bar',
            data: [120, 132, 145, 160, 172, 190, 210, 232, 201, 154, 190, 230],
            itemStyle: {
              color: '#3b82f6',
            },
          },
        ],
      })
    }

    // 同类角色比较图表
    if (comparisonChart.value) {
      const chart = echarts.init(comparisonChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['当前角色', '同类角色平均'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          name: '权责数量',
        },
        yAxis: {
          type: 'category',
          data: ['负责(R)', '审批(A)', '咨询(C)', '知悉(I)'],
        },
        series: [
          {
            name: '当前角色',
            type: 'bar',
            data: [
              selectedRoleData.value.stats.R,
              selectedRoleData.value.stats.A,
              selectedRoleData.value.stats.C,
              selectedRoleData.value.stats.I,
            ],
            itemStyle: {
              color: '#3b82f6',
            },
          },
          {
            name: '同类角色平均',
            type: 'bar',
            data: [6, 4, 5, 3],
            itemStyle: {
              color: '#94a3b8',
            },
          },
        ],
      })
    }

    // 责任流程图表
    if (flowChart.value) {
      const chart = echarts.init(flowChart.value)
      chart.setOption({
        animation: false,
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
        },
        series: [
          {
            type: 'sankey',
            layout: 'none',
            data: [
              { name: '财务专员' },
              { name: '财务经理' },
              { name: 'CFO' },
              { name: '董事会' },
              { name: '合规部' },
            ],
            links: [
              { source: '财务专员', target: '财务经理', value: 5 },
              { source: '财务经理', target: 'CFO', value: 3 },
              { source: 'CFO', target: '董事会', value: 1 },
              { source: '财务经理', target: '合规部', value: 2 },
            ],
            label: {
              color: '#4b5563',
              fontSize: 12,
            },
            lineStyle: {
              color: 'source',
              curveness: 0.5,
              opacity: 0.3,
            },
            itemStyle: {
              borderWidth: 0,
            },
            levels: [
              {
                depth: 0,
                itemStyle: {
                  color: '#f97316',
                },
              },
              {
                depth: 1,
                itemStyle: {
                  color: '#3b82f6',
                },
              },
              {
                depth: 2,
                itemStyle: {
                  color: '#10b981',
                },
              },
              {
                depth: 3,
                itemStyle: {
                  color: '#8b5cf6',
                },
              },
            ],
          },
        ],
      })
    }
  })
}

// 显示详情弹窗
function showDetail(item: any) {
  currentDetail.value = {
    itemName: item.itemName || item.name,
    roleName: item.roleName || selectedRoleData.value.name,
    type: item.type,
    description: item.description,
    tasks: [
      '收集各部门预算需求并汇总',
      '组织预算编制会议，协调各部门预算',
      '编制初步预算方案',
      '与CFO沟通预算方案调整',
      '提交最终预算方案审批',
    ],
    notes: [
      '预算编制需符合公司战略目标',
      '重大预算调整需经CFO批准',
      '预算执行情况需每月跟踪报告',
    ],
    relatedDocs: [
      '公司预算管理制度 V3.2',
      '财务审批权限表 2023版',
      '预算编制操作指南',
    ],
    references: [
      '行业预算编制最佳实践',
      '去年预算执行分析报告',
      '预算编制培训材料',
    ],
  }
  showDetailModal.value = true
}

// 获取权责类型名称
function getTypeName(type: string) {
  switch (type) {
    case 'R':
      return '负责(R)'
    case 'A':
      return '审批(A)'
    case 'C':
      return '咨询(C)'
    case 'I':
      return '知悉(I)'
    default:
      return ''
  }
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', initCharts)
})
</script>

<template>
  <div class="absolute-container">
    <PageMain style="background-color: transparent;">
      <div>
        <el-card shadow="hover" class="">
          <!-- 按角色查看权责分配 -->
          <div class="mb-8 rounded-lg bg-white shadow">
            <div class="border-b border-gray-200 p-6">
              <h2 class="text-xl text-gray-800 font-bold">
                按角色查看权责分配
              </h2>
              <div class="mt-4 flex items-center">
                <el-select v-model="selectedRole" placeholder="请选择角色" class="w-64">
                  <el-option v-for="role in roles" :key="role.id" :label="role.name" :value="role.id" />
                </el-select>
              </div>
            </div>

            <div class="p-6">
              <!-- 角色权责概述 -->
              <div class="grid grid-cols-1 mb-8 gap-6 md:grid-cols-3">
                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    角色基本信息
                  </h3>
                  <div class="text-sm text-gray-600 space-y-2">
                    <p><span class="font-medium">部门：</span>{{ selectedRoleData.department }}</p>
                    <p><span class="font-medium">层级：</span>{{ selectedRoleData.level }}</p>
                    <p><span class="font-medium">直接上级：</span>{{ selectedRoleData.supervisor }}</p>
                  </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    权责统计
                  </h3>
                  <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="rounded bg-blue-50 p-2 text-center">
                      <p class="text-2xl text-blue-600 font-bold">
                        {{ selectedRoleData.stats.R }}
                      </p>
                      <p class="text-xs text-blue-500">
                        负责(R)
                      </p>
                    </div>
                    <div class="rounded bg-green-50 p-2 text-center">
                      <p class="text-2xl text-green-600 font-bold">
                        {{ selectedRoleData.stats.A }}
                      </p>
                      <p class="text-xs text-green-500">
                        审批(A)
                      </p>
                    </div>
                    <div class="rounded bg-purple-50 p-2 text-center">
                      <p class="text-2xl text-purple-600 font-bold">
                        {{ selectedRoleData.stats.C }}
                      </p>
                      <p class="text-xs text-purple-500">
                        咨询(C)
                      </p>
                    </div>
                    <div class="rounded bg-yellow-50 p-2 text-center">
                      <p class="text-2xl text-yellow-600 font-bold">
                        {{ selectedRoleData.stats.I }}
                      </p>
                      <p class="text-xs text-yellow-500">
                        知悉(I)
                      </p>
                    </div>
                  </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    权责分布
                  </h3>
                  <div ref="rolePieChart" class="h-40" />
                </div>
              </div>

              <!-- 权责列表 -->
              <div class="mb-8">
                <h3 class="mb-4 text-lg text-gray-800 font-bold">
                  权责列表
                </h3>
                <el-table :data="selectedRoleData.responsibilities" border style="width: 100%;">
                  <el-table-column prop="itemName" label="事项名称" width="180" />
                  <el-table-column prop="businessArea" label="业务领域" width="120" />
                  <el-table-column prop="type" label="权责类型" width="100">
                    <template #default="{ row }">
                      <span
                        class="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium" :class="{
                          'bg-blue-100 text-blue-800': row.type === 'R',
                          'bg-green-100 text-green-800': row.type === 'A',
                          'bg-purple-100 text-purple-800': row.type === 'C',
                          'bg-yellow-100 text-yellow-800': row.type === 'I',
                        }"
                      >
                        {{ getTypeName(row.type) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="工作说明" />
                  <el-table-column label="操作" width="100">
                    <template #default="{ row }">
                      <button
                        class="!rounded-button inline-flex items-center whitespace-nowrap bg-blue-600 px-3 py-1 text-xs text-white font-medium hover:bg-blue-700"
                        @click="showDetail(row)"
                      >
                        查看详情
                      </button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 工作负荷分析 -->
              <div>
                <h3 class="mb-4 text-lg text-gray-800 font-bold">
                  工作负荷分析
                </h3>
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="mb-2 text-sm text-gray-800 font-bold">
                      月度工作负荷
                    </h4>
                    <div ref="workloadChart" class="h-64" />
                  </div>
                  <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="mb-2 text-sm text-gray-800 font-bold">
                      同类角色比较
                    </h4>
                    <div ref="comparisonChart" class="h-64" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
        <el-card shadow="hover" class="mt-20">
          <!--          <template #header>
            <div class="f-16 fw-600">按事项查看权责分配</div>
          </template> -->
          <!-- 按事项查看权责分配 -->
          <div class="rounded-lg bg-white shadow">
            <div class="border-b border-gray-200 p-6">
              <h2 class="text-xl text-gray-800 font-bold">
                按事项查看权责分配
              </h2>
              <div class="mt-4 flex items-center">
                <el-select v-model="selectedItem" placeholder="请选择事项" class="w-64">
                  <el-option v-for="item in items" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </div>
            </div>

            <div class="p-6">
              <!-- 事项概述 -->
              <div class="grid grid-cols-1 mb-8 gap-6 md:grid-cols-3">
                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    事项基本信息
                  </h3>
                  <div class="text-sm text-gray-600 space-y-2">
                    <p><span class="font-medium">业务领域：</span>{{ selectedItemData.businessArea }}</p>
                    <p><span class="font-medium">风险等级：</span>{{ selectedItemData.riskLevel }}</p>
                    <p><span class="font-medium">更新日期：</span>{{ selectedItemData.updateDate }}</p>
                  </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    权责分配概况
                  </h3>
                  <div class="text-sm text-gray-600 space-y-2">
                    <p><span class="font-medium">涉及角色：</span>{{ selectedItemData.roleCount }} 个</p>
                    <p><span class="font-medium">主要责任部门：</span>{{ selectedItemData.mainDepartment }}</p>
                    <p><span class="font-medium">审批层级：</span>{{ selectedItemData.approvalLevel }}</p>
                  </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                  <h3 class="mb-2 text-base text-gray-800 font-bold">
                    权责类型分布
                  </h3>
                  <div ref="itemPieChart" class="h-40" />
                </div>
              </div>

              <!-- 角色权责列表 -->
              <div class="mb-8">
                <h3 class="mb-4 text-lg text-gray-800 font-bold">
                  角色权责列表
                </h3>
                <el-table :data="selectedItemData.responsibilities" border style="width: 100%;">
                  <el-table-column prop="roleName" label="角色名称" width="180" />
                  <el-table-column prop="department" label="所属部门" width="150" />
                  <el-table-column prop="type" label="权责类型" width="100">
                    <template #default="{ row }">
                      <span
                        class="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium" :class="{
                          'bg-blue-100 text-blue-800': row.type === 'R',
                          'bg-green-100 text-green-800': row.type === 'A',
                          'bg-purple-100 text-purple-800': row.type === 'C',
                          'bg-yellow-100 text-yellow-800': row.type === 'I',
                        }"
                      >
                        {{ getTypeName(row.type) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="职责说明" />
                  <el-table-column label="操作" width="100">
                    <template #default="{ row }">
                      <button
                        class="!rounded-button inline-flex items-center whitespace-nowrap bg-blue-600 px-3 py-1 text-xs text-white font-medium hover:bg-blue-700"
                        @click="showDetail(row)"
                      >
                        查看详情
                      </button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 责任流程分析 -->
              <div>
                <h3 class="mb-4 text-lg text-gray-800 font-bold">
                  责任流程分析
                </h3>
                <div class="border border-gray-200 rounded-lg p-4">
                  <div ref="flowChart" class="h-96" />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义样式 */
  .el-select {
    width: 100%;
  }

  .el-table {
    margin-top: 16px;
  }

  .el-tabs {
    margin-top: 16px;
  }

  /* 弹窗动画 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
