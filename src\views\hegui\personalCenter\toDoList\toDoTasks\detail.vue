<script lang="ts" setup>
import { ref } from 'vue'
import {
  ArrowLeft,
  ArrowRight,
  Bell,
  Check,
  Clock,
  Document,
  Download,
  Share,
  Upload,
  User,
} from '@element-plus/icons-vue'

const approvalResult = ref('agree')
const approvalComment = ref('')

function handleExceed() {
  // 处理文件超出限制
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              任务详情
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <el-button
              v-auth="['toDoTasks/detail/handleTask']"
              type="primary" class="!rounded-button whitespace-nowrap">
              处理任务
            </el-button>
            <el-button class="!rounded-button whitespace-nowrap">
              返回
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div>
        <el-row :gutter="20" class="">
          <el-col :span="18">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="mb-4 flex items-start justify-between">
                  <div class="f-16 fw-600">
                    客户合同审批
                  </div>
                  <div class="flex space-x-2">
                    <el-tag type="warning">
                      待处理
                    </el-tag>
                    <el-tag type="danger">
                      紧急
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="mb-4 text-sm text-gray-500">
                任务ID: T20240425001 | 类型: 合同审批
              </div>
              <div class="grid grid-cols-2 mb-6 gap-4">
                <div>
                  <p class="text-sm text-gray-500">
                    发起人
                  </p>
                  <p class="font-medium">
                    张三（销售部）
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    发起时间
                  </p>
                  <p class="font-medium">
                    2024-04-25 10:30
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    截止时间
                  </p>
                  <p class="font-medium">
                    2024-04-30 18:00
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    处理人
                  </p>
                  <p class="font-medium">
                    李四（当前用户）
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    预计完成时间
                  </p>
                  <p class="font-medium">
                    2024-04-28
                  </p>
                </div>
              </div>

              <div class="mb-6">
                <h4 class="mb-2 font-medium">
                  任务详情
                </h4>
                <div class="border rounded bg-gray-50 p-4">
                  <p>请审批与客户"ABC科技有限公司"签订的年度服务合同，合同金额为¥1,200,000.00，服务期限为2024年5月1日至2025年4月30日。</p>
                  <p class="mt-2">
                    合同主要条款包括：
                  </p>
                  <ul class="mt-2 list-disc pl-5">
                    <li>服务内容：系统维护与技术支持</li>
                    <li>服务级别协议(SLA)：99.9%可用性</li>
                    <li>付款方式：季度支付，首付30%</li>
                    <li>违约责任：每日0.1%违约金</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 class="mb-2 font-medium">
                  附件
                </h4>
                <div class="border rounded divide-y">
                  <div class="flex items-center justify-between p-3">
                    <div class="flex items-center">
                      <el-icon class="mr-2 text-blue-500">
                        <Document />
                      </el-icon>
                      <div>
                        <p class="font-medium">
                          ABC科技年度服务合同.pdf
                        </p>
                        <p class="text-xs text-gray-500">
                          PDF文档 · 2.4MB
                        </p>
                      </div>
                    </div>
                    <el-button v-auth="['toDoTasks/detail/download']" size="small" class="!rounded-button whitespace-nowrap">
                      下载
                    </el-button>
                  </div>
                  <div class="flex items-center justify-between p-3">
                    <div class="flex items-center">
                      <el-icon class="mr-2 text-green-500">
                        <Document />
                      </el-icon>
                      <div>
                        <p class="font-medium">
                          客户资质证明.zip
                        </p>
                        <p class="text-xs text-gray-500">
                          压缩文件 · 5.7MB
                        </p>
                      </div>
                    </div>
                    <el-button v-auth="['toDoTasks/detail/download']" size="small" class="!rounded-button whitespace-nowrap">
                      下载
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  审批处理
                </div>
              </template>
              <div class="space-y-4">
                <div>
                  <p class="mb-2 text-sm text-gray-600">
                    审批结果
                  </p>
                  <div class="flex space-x-4">
                    <el-radio-group v-model="approvalResult">
                      <el-radio-button label="agree">
                        同意
                      </el-radio-button>
                      <el-radio-button label="reject">
                        拒绝
                      </el-radio-button>
                      <el-radio-button label="transfer">
                        转交
                      </el-radio-button>
                    </el-radio-group>
                  </div>
                </div>

                <div>
                  <p class="mb-2 text-sm text-gray-600">
                    审批意见
                  </p>
                  <el-input v-model="approvalComment" type="textarea" :rows="4" placeholder="请输入审批意见" />
                </div>

                <div>
                  <p class="mb-2 text-sm text-gray-600">
                    上传附件
                  </p>
                  <el-upload action="" multiple :limit="3" :on-exceed="handleExceed">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                      <el-icon class="mr-1">
                        <Upload />
                      </el-icon>上传文件
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip text-xs text-gray-500">
                        支持PDF、Word、Excel、图片格式，单个文件不超过10MB
                      </div>
                    </template>
                  </el-upload>
                </div>

                <div class="flex justify-end pt-4 space-x-3">
                  <el-button class="!rounded-button whitespace-nowrap">
                    取消
                  </el-button>
                  <el-button v-auth="['toDoTasks/detail/saveDrafts']" class="!rounded-button whitespace-nowrap">
                    保存草稿
                  </el-button>
                  <el-button v-auth="['toDoTasks/detail/submit']" type="primary" class="!rounded-button whitespace-nowrap">
                    提交
                  </el-button>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  任务流转记录
                </div>
              </template>
              <div class="space-y-4">
                <div class="flex">
                  <div class="mr-4 flex flex-col items-center">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white">
                      <el-icon>
                        <Check />
                      </el-icon>
                    </div>
                    <div class="my-1 w-px flex-1 bg-gray-200" />
                  </div>
                  <div class="flex-1 pb-4">
                    <p class="font-medium">
                      任务创建
                    </p>
                    <p class="text-sm text-gray-500">
                      2024-04-25 10:30
                    </p>
                    <p class="mt-1 text-sm">
                      张三（销售部）创建了此审批任务
                    </p>
                  </div>
                </div>
                <div class="flex">
                  <div class="mr-4 flex flex-col items-center">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white">
                      <el-icon>
                        <User />
                      </el-icon>
                    </div>
                    <div class="my-1 w-px flex-1 bg-gray-200" />
                  </div>
                  <div class="flex-1 pb-4">
                    <p class="font-medium">
                      任务分配
                    </p>
                    <p class="text-sm text-gray-500">
                      2024-04-25 11:15
                    </p>
                    <p class="mt-1 text-sm">
                      系统将此任务分配给李四（法务部）
                    </p>
                  </div>
                </div>
                <div class="flex">
                  <div class="mr-4 flex flex-col items-center">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-gray-300 text-white">
                      <el-icon>
                        <Clock />
                      </el-icon>
                    </div>
                  </div>
                  <div>
                    <p class="font-medium">
                      待处理
                    </p>
                    <p class="text-sm text-gray-500">
                      当前状态
                    </p>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="">
              <template #header>
                <div class="f-16 fw-600">
                  相关任务
                </div>
              </template>
              <div class="space-y-3">
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <p class="font-medium">
                    ABC科技合同补充协议
                  </p>
                  <p class="text-sm text-gray-500">
                    待处理 · 截止: 2024-04-28
                  </p>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <p class="font-medium">
                    ABC科技项目启动会
                  </p>
                  <p class="text-sm text-gray-500">
                    已完成 · 2024-04-20
                  </p>
                </div>
                <div class="cursor-pointer border rounded p-3 hover:bg-gray-50">
                  <p class="font-medium">
                    ABC科技付款申请
                  </p>
                  <p class="text-sm text-gray-500">
                    处理中 · 截止: 2024-05-05
                  </p>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  任务导航
                </div>
              </template>
              <div class="flex justify-between">
                <div>
                  <el-button class="!rounded-button whitespace-nowrap" :disabled="true">
                    <el-icon class="mr-1">
                      <ArrowLeft />
                    </el-icon>上一个任务
                  </el-button>
                </div>
                <div>
                  <el-button class="!rounded-button whitespace-nowrap">
                    下一个任务<el-icon class="ml-1">
                      <ArrowRight />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </el-card>
            <el-card shadow="hover" class="mt-20">
              <template #header>
                <div class="f-16 fw-600">
                  快捷操作
                </div>
              </template>
              <div class="space-y-3">
                <el-button v-auth="['toDoTasks/detail/transferTask']" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Share />
                  </el-icon>转交任务
                </el-button>
                <el-button v-auth="['toDoTasks/detail/remindPeopleInvolved']" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Bell />
                  </el-icon>提醒相关人
                </el-button>
                <el-button v-auth="['toDoTasks/detail/exportTaskDetails']" class="!rounded-button w-full whitespace-nowrap">
                  <el-icon class="mr-1">
                    <Download />
                  </el-icon>导出任务详情
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>
