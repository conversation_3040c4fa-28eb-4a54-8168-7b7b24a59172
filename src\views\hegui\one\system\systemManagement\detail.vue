<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElLoading, ElMessage } from 'element-plus'
import dictApi from '@/api/modules/system/dict'
import UploadMbb from '@/components/uploadMbb/index.vue'

import systemApi from '@/api/complianceApi/one/systemManagement.ts'

const route = useRoute()
const router = useRouter()

// 制度详情数据
const regulationDetail = ref<any>({})
const loading = ref(false)

// 状态选项
const statusOptions = ref([] as { value: string, name: string, description?: string }[])

// 智能审查相关状态
const aiReviewVisible = ref(false)
const aiReviewContent = ref('')
const reviewStatus = ref('PASS') // PASS 或 NOPASS
const reviewOpinion = ref('')
const aiReviewLoading = ref(false)

// 制度类型映射
const regulationTypeMap: Record<string, string> = {
  REGULATION: '规章制度',
  MEASURES: '管理办法',
  CONDUCT: '行为准则',
}

// 获取基础数据
async function loadBasicData() {
  try {
    // 通过字典API获取状态数据
    const response = await dictApi.dictAll(4)
    statusOptions.value = response
  }
  catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('获取状态数据失败')
  }
}

// 初始化
onMounted(() => {
  loadBasicData()
  getRegulationDetail()
})

// 获取制度详情
function getRegulationDetail() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少制度ID参数')
    return
  }

  loading.value = true
  systemApi.system({ id }, 'info').then((res: any) => {
    if (res) {
      regulationDetail.value = res.data || res
    }
  }).catch((error: any) => {
    console.error('获取制度详情失败:', error)
    ElMessage.error('获取制度详情失败')
  }).finally(() => {
    loading.value = false
  })
}

// 返回列表
function goBack() {
  router.push('/one/systemManagement/index')
}

// 编辑制度
function editRegulation() {
  router.push({
    path: '/one/systemManagement/addEdit',
    query: { id: regulationDetail.value.id },
  })
}

// 格式化制度类型
function formatRegulationType(type: string) {
  return regulationTypeMap[type] || type
}

// 格式化状态
function formatStatus(status: string) {
  const statusItem = statusOptions.value.find(item => item.value === status)
  return statusItem?.name || status
}

// 智能审查
async function aiReview() {
  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少制度ID参数')
    return
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在进行智能审查...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    const response = await systemApi.aiReview(id)
    if (response) {
      aiReviewContent.value = response
      aiReviewVisible.value = true
      reviewStatus.value = 'PASS'
      reviewOpinion.value = ''
    }
    else {
      ElMessage.error('智能审查失败，未获取到审查内容')
    }
  }
  catch (error) {
    console.error('智能审查失败:', error)
    ElMessage.error('智能审查失败，请稍后重试')
  }
  finally {
    loading.close()
  }
}

// 提交审核结果
async function submitReview() {
  if (reviewStatus.value === 'NOPASS' && !reviewOpinion.value.trim()) {
    ElMessage.warning('选择不通过时必须填写意见')
    return
  }

  const id = route.query.id
  if (!id) {
    ElMessage.error('缺少制度ID参数')
    return
  }

  aiReviewLoading.value = true

  try {
    const params = {
      contents: [{
        content: aiReviewContent.value,
        regulationId: id,
      }],
      opinion: reviewOpinion.value,
      regulationId: id,
      status: reviewStatus.value,
    }

    await systemApi.auditReview(params)
    ElMessage.success('审核提交成功')
    aiReviewVisible.value = false
    // 重新获取制度详情以更新状态
    getRegulationDetail()
  }
  catch (error) {
    console.error('审核提交失败:', error)
    ElMessage.error('审核提交失败，请稍后重试')
  }
  finally {
    aiReviewLoading.value = false
  }
}
async function reReview() {
  try {
    if (reviewStatus.value === 'NOPASS' && !reviewOpinion.value.trim()) {
      ElMessage.warning('选择不通过时必须填写意见')
      return
    }

    const loading = ElLoading.service({
      lock: true,
      text: '正在重新审查...',
      background: 'rgba(0, 0, 0, 0.7)',
    })

    try {
      const params = {
        objectId: regulationDetail.value.id,
        content: aiReviewContent.value,
        suggestion: reviewOpinion.value.trim(),
      }
      const response = await systemApi.aiAnalysis(params)
      aiReviewContent.value = response
    }
    finally {
      loading.close()
    }
  }
  catch (error) {
    console.error('智能审查失败:', error)
    ElMessage.error('智能审查失败，请稍后重试')
  }
}
// 关闭审核弹窗
function closeReviewDialog() {
  aiReviewVisible.value = false
  reviewStatus.value = 'PASS'
  reviewOpinion.value = ''
  aiReviewContent.value = ''
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              制度详情
            </h1>
          </div>
          <div class="flex space-x-3">
            <el-button v-if="regulationDetail.status !== 'PUBLISHED'" v-auth="['systemManagement/detail/aiReview']" @click="aiReview">
              <!-- <svg-icon name="ep:" /> -->
              <span class="ml-4">智能审查</span>
            </el-button>
            <el-button v-auth="['systemManagement/detail/goBack']" @click="goBack">
              <svg-icon name="ep:back" />
              <span class="ml-4">返回</span>
            </el-button>
            <el-button v-if="regulationDetail.status !== 'REVIEWING'" v-auth="['systemManagement/detail/edit']" type="primary" @click="editRegulation">
              <svg-icon name="ep:edit" />
              <span class="ml-4">编辑</span>
            </el-button>
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent;">
      <div v-loading="loading" class="card p-24">
        <div v-if="regulationDetail.id" class="space-y-8">
          <!-- 基本信息 -->
          <div class="mb-8">
            <h2 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
              基本信息
            </h2>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">制度名称：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.title }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">制度编号：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.regulationCode }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">制度类型：</label>
                <span class="flex-1 text-gray-800">{{ formatRegulationType(regulationDetail.regulationType) }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">状态：</label>
                <el-tag :type="regulationDetail.status === 'PUBLISHED' ? 'success' : 'info'">
                  {{ formatStatus(regulationDetail.status) }}
                </el-tag>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">所属部门：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.department }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">生效日期：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.effectiveDate }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">失效日期：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.expireDate || '无' }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">创建时间：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.createdAt }}</span>
              </div>
              <div class="flex items-center">
                <label class="mr-2 min-w-24 text-gray-600 font-medium">更新时间：</label>
                <span class="flex-1 text-gray-800">{{ regulationDetail.updatedAt }}</span>
              </div>
            </div>
          </div>

          <!-- 制度摘要 -->
          <div class="mb-8">
            <h2 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
              制度摘要
            </h2>
            <div class="border-l-4 border-blue-500 rounded bg-gray-50 p-4">
              <p class="m-0 text-gray-800 leading-relaxed">
                {{ regulationDetail.summary }}
              </p>
            </div>
          </div>

          <!-- 制度内容 -->
          <div class="mb-8">
            <h2 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
              制度内容
            </h2>
            <div class="border-l-4 border-blue-500 rounded bg-gray-50 p-4">
              <div class="text-gray-800 leading-relaxed" v-html="regulationDetail.content" />
            </div>
          </div>

          <!-- 附件 -->
          <div v-if="regulationDetail.attachments && regulationDetail.attachments.length > 0" class="mb-8">
            <h2 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
              附件
            </h2>
            <UploadMbb
              :model-value="regulationDetail.attachments"
              :max="0"
              :use-file-path="true"
              readonly
            />
          </div>

          <!-- 关联制度 -->
          <div v-if="regulationDetail.relatedRegulations && regulationDetail.relatedRegulations.length > 0" class="mb-8">
            <h2 class="mb-4 border-b-2 border-gray-200 pb-2 text-lg text-gray-800 font-semibold">
              关联制度
            </h2>
            <div class="flex flex-wrap gap-2">
              <el-tag v-for="related in regulationDetail.relatedRegulations" :key="related.id" class="m-0">
                {{ related.title }}
              </el-tag>
            </div>
          </div>
        </div>
        <div v-else-if="!loading" class="py-10 text-center">
          <el-empty description="未找到制度详情" />
        </div>
      </div>
    </PageMain>

    <!-- 智能审查弹窗 -->
    <el-dialog
      v-model="aiReviewVisible"
      title="智能审查结果"
      width="800px"
      :close-on-click-modal="false"
      @close="closeReviewDialog"
    >
      <div class="space-y-6">
        <!-- 审查内容 -->
        <div>
          <h3 class="mb-3 text-base text-gray-800 font-semibold">
            审查内容：
          </h3>
          <div class="max-h-60 overflow-y-auto border border-gray-200 rounded bg-gray-50 p-4">
            <div class="whitespace-pre-wrap text-gray-800 leading-relaxed">
              {{ aiReviewContent }}
            </div>
          </div>
        </div>

        <!-- 审核选择 -->
        <div>
          <h3 class="mb-3 text-base text-gray-800 font-semibold">
            审核结果：
          </h3>
          <el-radio-group v-model="reviewStatus" class="flex gap-6">
            <el-radio value="PASS" size="large">
              <span class="text-green-600 font-medium">通过</span>
            </el-radio>
            <el-radio value="NOPASS" size="large">
              <span class="text-red-600 font-medium">不通过</span>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 意见输入 -->
        <div v-if="reviewStatus === 'NOPASS'">
          <h3 class="mb-3 text-base text-gray-800 font-semibold">
            审核意见：<span class="text-red-500">*</span>
          </h3>
          <el-input
            v-model="reviewOpinion"
            type="textarea"
            :rows="4"
            placeholder="请填写不通过的原因和建议..."
            maxlength="500"
            show-word-limit
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <el-button v-if="reviewStatus === 'NOPASS'" @click="reReview">
            重新审查
          </el-button>
          <el-button @click="closeReviewDialog">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="aiReviewLoading"
            @click="submitReview"
          >
            确认提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .card {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%), 0 0 0 0 rgb(0 0 0 / 0%), 0 0 0 0 rgb(0 0 0 / 0%);
  }
</style>
