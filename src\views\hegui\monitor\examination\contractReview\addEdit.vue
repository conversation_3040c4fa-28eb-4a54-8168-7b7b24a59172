<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  Close,
  Document,
  InfoFilled,
  MagicStick,
  Notebook,
  Plus,
  Promotion,
  ScaleToOriginal,
  SuccessFilled,
  Upload,
  User,
  VideoPlay,
  Warning,
  // Lightbulb,
  // Shield,
} from '@element-plus/icons-vue'
import { disablePastDates } from '@/utils/dateUtils'
import contractApi from '@/api/review/contract'
import dictApi from '@/api/modules/system/dict'
import systemApi from '@/api/complianceApi/one/systemManagement.ts'
import DocumentUpload from '@/components/DocumentUpload/index.vue'
import UploadMbb from '@/components/uploadMbb/index.vue'
import DepartmentTreeSelect from '@/components/DepartmentTreeSelect/index.vue'
import DepartPerson from '@/components/departPerson/index.vue'

const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const loading = ref(false)
const detailLoading = ref(false)
const formRef = ref<FormInstance>()
const contractTypeOptions = ref<Array<{ label: string, value: string }>>([])

// 响应式数据
const payWayOptions = ref<Array<{ label: string, value: string }>>([])
const solveWayOptions = ref<Array<{ label: string, value: string }>>([])
const showLawsPop = ref(false)
const selectedLaws = ref<any[]>([])
const selectedLawsDisplay = ref('')

const form = ref({
  id: null as number | null,
  // tenantId: 1,
  reviewCode: null as string | null,
  name: null as string | null,
  contractType: '' as string,
  level: '' as string,
  department: null as string | null,
  auditBy: null as string | number | null,
  explain: null as string | null,
  deadlineDate: '' as string,
  // riskSelf: null as string | null,
  riskType: '' as string,
  riskDesc: '' as string,
  riskSelf: '' as string,
  // status: null as string | null,
  complianceReview: null as any,
  reviewProcess: '' as string,
  reviewFocus: [] as string[],
  autoSelectLaw: '' as string,
  createdBy: null as string | null,
  createdAt: null as string | null,
  contractMessage: {
    firstParty: '' as string,
    secondParty: '' as string,
    thirdParty: '' as string,
    money: 0 as number,
    signDate: '' as string,
    performancePeriodStart: '' as string,
    performancePeriodEnd: '' as string,
    payWay: '' as string,
    solveWay: '' as string,
    regulationId: '' as string,
    defaultResponsibility: '' as string,
    content: '' as string,
    document: '' as string,
    documentUrl: '' as string,
  },
  contractAttachments: [] as Array<{
    fileName: string
    filePath: string
    fileType: string
    fileSize: number
  }>,
})
const btnLoading = ref(false)
// 表单验证规则
const rules = ref<FormRules>({
  'name': [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { min: 2, max: 100, message: '合同名称长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  'contractType': [
    { required: true, message: '请选择合同类型', trigger: 'change' },
  ],
  'level': [
    { required: true, message: '请选择审查级别', trigger: 'change' },
  ],
  'department': [
    { required: true, message: '请输入发起部门', trigger: 'blur' },
  ],
  'auditBy': [
    { required: true, message: '请输入审查人员', trigger: 'blur' },
  ],
  'contractMessage.firstParty': [
    { required: true, message: '请输入甲方名称', trigger: 'blur' },
  ],
  'contractMessage.secondParty': [
    { required: true, message: '请输入乙方名称', trigger: 'blur' },
  ],
  'contractMessage.money': [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '合同金额必须大于等于0', trigger: 'blur' },
  ],
  'contractMessage.signDate': [
    { required: true, message: '请选择合同签订日期', trigger: 'change' },
  ],
  'contractMessage.performancePeriodStart': [
    { required: true, message: '请选择合同履行开始时间', trigger: 'change' },
  ],
  'contractMessage.performancePeriodEnd': [
    { required: true, message: '请选择合同履行结束时间', trigger: 'change' },
  ],
  'contractMessage.payWay': [
    { required: true, message: '请选择付款方式', trigger: 'change' },
  ],
  'contractMessage.solveWay': [
    { required: true, message: '请选择争议解决方式', trigger: 'change' },
  ],
  'contractMessage.regulationId': [
    { required: true, message: '请选择适用法律', trigger: 'change' },
  ],
})

async function generateReviewNumber() {
  try {
    const response = await dictApi.getCode('CONTRACT')
    if (response) {
      form.value.reviewCode = response
    }
  }
  catch (error) {
    // console.error('获取审查编号失败:', error)
    ElMessage.error('获取审查编号失败')
  }
}

// 获取详情数据
async function getDetailData() {
  if (!route.query.id) {
    return
  }

  try {
    detailLoading.value = true
    const response = await contractApi.contractReview({}, { id: route.query.id }, 'info')

    if (response) {
      const data = response
      form.value = {
        ...data,
        department: +data.department,
      }

      // 设置法规选择的回显
      if (data.contractMessage?.regulationId) {
        await setSelectedLawsFromId(data.contractMessage.regulationId)
      }
    }
  }
  catch (error) {
    // console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
  finally {
    detailLoading.value = false
  }
}

// 根据法规ID设置选中的法规
async function setSelectedLawsFromId(regulationId: string) {
  try {
    // 这里需要调用API获取法规详情，假设有这样的API
    // 如果没有单独的详情API，可以通过列表API搜索
    const response = await systemApi.lawsSystem({ id: regulationId }, 'info')
    if (response) {
      selectedLaws.value = [response]
      selectedLawsDisplay.value = response.title
    }
  }
  catch (error) {
    // 如果获取详情失败，尝试通过列表API搜索
    try {
      const listResponse = await systemApi.lawsSystem({ page: 1, limit: 1000 }, 'page')
      if (listResponse?.data?.content) {
        const foundItem = listResponse.data.content.find((item: any) => item.id === regulationId)
        if (foundItem) {
          selectedLaws.value = [foundItem]
          selectedLawsDisplay.value = foundItem.title
        }
      }
    }
    catch (listError) {
      console.error('获取法规信息失败:', listError)
    }
  }
}
// 智能识别
async function handleSmartRecognition() {
  try {
    btnLoading.value = true
    if (!form.value.contractMessage?.documentUrl) {
      ElMessage.error('请上传合同附件')
      return
    }
    const response = await contractApi.smartRecognition(form.value.contractMessage.documentUrl)
    const data = response
    data.documentUrl = form.value.contractMessage.documentUrl
    Object.assign(form.value.contractMessage, data)
    ElMessage.success('导入成功')
    btnLoading.value = false
  }
  catch (error) {
    btnLoading.value = false
    // console.error('智能识别失败:', error)
    ElMessage.error('导入失败')
  }

  // form.value = {
  //   ...data,
  //   department: +data.department,
  // }
}
// 保存数据
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    // 验证表单
    await formRef.value.validate()

    loading.value = true
    const submitData = {
      ...form.value,
      department: `${form.value.department}`,
      // status: 'DRAFT',
      // 如果 contractAttachments 数组为空，则设置为 null
      contractAttachments: form.value.contractAttachments.length === 0 ? null : form.value.contractAttachments,
    }

    let response
    if (form.value.id) {
      response = await contractApi.contractReview({}, submitData, 'update')
    }
    else {
      response = await contractApi.contractReview({}, submitData, 'create')
    }

    if (response) {
      ElMessage.success(isEdit.value ? '保存成功' : '创建成功')
      router.push('/monitor/examination/contractReview')
    }
  }
  catch (error) {
    if (error === false) {
      ElMessage.error('请检查表单填写是否正确')
    }
    else {
      // console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 返回列表
function handleBack() {
  router.back()
}

// 获取合同类型选项
async function getContractTypeOptions() {
  try {
    const response = await dictApi.dictAll(56)
    if (response) {
      contractTypeOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取合同类型选项失败:', error)
    ElMessage.error('获取合同类型选项失败')
  }
}

// 获取付款方式选项
async function getPayWayOptions() {
  try {
    const response = await dictApi.dictAll(60)
    if (response) {
      payWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取付款方式选项失败:', error)
    ElMessage.error('获取付款方式选项失败')
  }
}

// 获取争议解决方式选项
async function getSolveWayOptions() {
  try {
    const response = await dictApi.dictAll(63)
    if (response) {
      solveWayOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.value,
      }))
    }
  }
  catch (error) {
    // console.error('获取争议解决方式选项失败:', error)
    ElMessage.error('获取争议解决方式选项失败')
  }
}

// 打开法规选择弹窗
function openLawsPop() {
  showLawsPop.value = true
}

// 确认选择法规
function handleLawsConfirm(selectedIds: string[], selectedItems: any[], _selectedNames: string[]) {
  // 由于要求单选，只取第一个选中的项目
  if (selectedItems.length > 0) {
    const selectedItem = selectedItems[0]
    selectedLaws.value = [selectedItem]
    selectedLawsDisplay.value = selectedItem.title
    form.value.contractMessage.regulationId = selectedItem.id
  }
  else {
    selectedLaws.value = []
    selectedLawsDisplay.value = ''
    form.value.contractMessage.regulationId = ''
  }
  showLawsPop.value = false
}

// 清除选择的法规
function clearSelectedLaws() {
  selectedLaws.value = []
  selectedLawsDisplay.value = ''
  form.value.contractMessage.regulationId = ''
}

// 处理附件上传成功
function handleAttachmentUploadSuccess(files: any[]) {
  ElMessage.success(`成功上传 ${files.length} 个附件`)
}

// 处理法规选择确认
function onLawsConfirm(selectedIds: string[], selectedItems: any[], selectedNames: string[]) {
  handleLawsConfirm(selectedIds, selectedItems, selectedNames)
}

// 处理开始时间变化
function handleStartDateChange() {
  // 如果结束时间小于开始时间，清空结束时间
  if (form.value.contractMessage.performancePeriodEnd && form.value.contractMessage.performancePeriodStart) {
    const startTime = new Date(form.value.contractMessage.performancePeriodStart).getTime()
    const endTime = new Date(form.value.contractMessage.performancePeriodEnd).getTime()
    if (endTime < startTime) {
      form.value.contractMessage.performancePeriodEnd = ''
    }
  }
}

// 禁用结束日期的逻辑
function disableEndDate(time: Date) {
  if (!form.value.contractMessage.performancePeriodStart) {
    // 如果没有选择开始时间，禁用今天之前的日期
    return time.getTime() < Date.now() - 8.64e7
  }
  // 如果已选择开始时间，禁用开始时间之前的日期
  const startTime = new Date(form.value.contractMessage.performancePeriodStart).getTime()
  return time.getTime() < startTime
}

// 初始化
onMounted(() => {
  isEdit.value = !!route.query.id
  getContractTypeOptions()
  getPayWayOptions()
  getSolveWayOptions()
  if (isEdit.value) {
    getDetailData()
  }
  else {
    generateReviewNumber()
  }
})
</script>

<template>
  <div class="absolute-container">
    <pageHeader>
      <template #content>
        <!-- 页面标题和操作按钮 -->
        <div class="flex items-center justify-between">
          <h1 class="text-xl text-gray-800 font-bold">
            合同审查
          </h1>
          <div class="flex items-center space-x-3">
            <!-- v-if="!route.query.id || form.status === 'MODIFY'" -->
            <el-button v-if="!route.query.id" v-auth="'contractReview/addEdit/save'" type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="handleSave">
              <el-icon class="mr-2">
                <Plus />
              </el-icon>保存
            </el-button>
            <!-- <el-button v-auth="'contractReview/addEdit/submit'" plain type="primary" class="rounded-button whitespace-nowrap" :loading="loading" @click="handleSubmit">
              <el-icon class="mr-2">
                <Promotion />
              </el-icon>提交审查
            </el-button> -->
            <el-button v-auth="'contractReview/addEdit/cancel'" plain class="rounded-button whitespace-nowrap" @click="handleBack">
              <el-icon class="mr-2">
                <Close />
              </el-icon>取消
            </el-button>
          </div>
        </div>
      </template>
    </pageHeader>
    <!-- 主内容区 -->
    <PageMain style="background-color: transparent;">
      <!-- 详情加载遮罩 -->
      <div v-if="detailLoading" v-loading="detailLoading" class="min-h-96 flex items-center justify-center">
        <div class="text-gray-500">正在加载详情数据...</div>
      </div>
      <!-- 表单内容区 -->
      <div v-else class="m-auto flex space-x-6">
        <!-- 左侧主表单区 -->
        <div class="m-20 space-y-6">
          <!-- 基本信息 -->
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                基本信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="合同名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入合同名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查编号">
                    <div class="w-full">
                      <el-input v-model="form.reviewCode" placeholder="自动生成" />
                      <!-- <el-button class="ml-2" @click="generateReviewNumber">
                        自动生成
                      </el-button> -->
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同类型" prop="contractType">
                    <el-select v-model="form.contractType" placeholder="请选择合同类型">
                      <el-option
                        v-for="option in contractTypeOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查级别" prop="level">
                    <el-radio-group v-model="form.level">
                      <el-radio label="GENERAL" value="GENERAL">
                        一般
                      </el-radio>
                      <el-radio label="IMPORTANT" value="IMPORTANT">
                        重要
                      </el-radio>
                      <el-radio label="CRITICAL" value="CRITICAL">
                        关键
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="发起部门" prop="department">
                    <DepartmentTreeSelect
                      v-model="form.department"
                      placeholder="请选择发起部门"
                      :clearable="true"
                      width="100%"
                    />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查人员" prop="auditBy">
                    <DepartPerson
                      v-model="form.auditBy"
                      placeholder="请选择审查人员"
                    />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查截止日期">
                    <el-date-picker v-model="form.deadlineDate" :disabled-date="disablePastDates" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="审查说明">
                    <el-input v-model="form.explain" type="textarea" :rows="3" placeholder="请输入审查说明" />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 合同信息 -->
            <el-card shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                合同信息
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="甲方" prop="contractMessage.firstParty">
                    <el-input v-model="form.contractMessage.firstParty" placeholder="请输入甲方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="乙方" prop="contractMessage.secondParty">
                    <el-input v-model="form.contractMessage.secondParty" placeholder="请输入乙方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="丙方（如有）">
                    <el-input v-model="form.contractMessage.thirdParty" placeholder="请输入丙方名称" />
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="合同金额" prop="contractMessage.money">
                    <div class="flex space-x-2">
                      <el-input-number v-model="form.contractMessage.money" :min="0" class="flex-1" />
                      <!-- <el-select v-model="form.contractMessage.currency" style="width: 120px">
                        <el-option label="人民币" value="CNY" />
                        <el-option label="美元" value="USD" />
                        <el-option label="欧元" value="EUR" />
                        <el-option label="日元" value="JPY" />
                      </el-select> -->
                    </div>
                  </el-form-item>
                </div>
                <el-form-item class="items-start" label="合同签订日期" prop="contractMessage.signDate">
                  <el-date-picker v-model="form.contractMessage.signDate" :disabled-date="disablePastDates" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="合同履行期限">
                  <div class="space-y-4">
                    <div>
                      <el-form-item prop="contractMessage.performancePeriodStart" class="mb-0">
                        <label class="mb-2 block text-sm text-gray-700 font-medium">开始时间</label>
                        <el-date-picker
                          v-model="form.contractMessage.performancePeriodStart"
                          type="datetime"
                          placeholder="请选择开始日期"
                          class="w-full"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled-date="(time: Date) => time.getTime() < Date.now() - 8.64e7"
                          @change="handleStartDateChange"
                        />
                      </el-form-item>
                    </div>
                    <div>
                      <el-form-item prop="contractMessage.performancePeriodEnd" class="mb-0">
                        <label class="mb-2 block text-sm text-gray-700 font-medium">结束时间</label>
                        <el-date-picker
                          v-model="form.contractMessage.performancePeriodEnd"
                          type="datetime"
                          placeholder="请选择结束日期"
                          class="w-full"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled-date="disableEndDate"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </el-form-item>
                <div>
                  <el-form-item label="付款方式" prop="contractMessage.payWay">
                    <el-select v-model="form.contractMessage.payWay" placeholder="请选择付款方式">
                      <el-option
                        v-for="option in payWayOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="争议解决方式" prop="contractMessage.solveWay">
                    <el-select v-model="form.contractMessage.solveWay" placeholder="请选择争议解决方式">
                      <el-option
                        v-for="option in solveWayOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="适用法律" prop="contractMessage.regulationId">
                    <div class="w-full flex items-center space-x-2">
                      <el-input
                        v-model="selectedLawsDisplay"
                        placeholder="请选择适用法律"
                        readonly
                        class="flex-1"
                        :title="selectedLawsDisplay"
                      />
                      <el-button type="primary" @click="openLawsPop">
                        选择法规
                      </el-button>
                      <el-button
                        v-if="selectedLawsDisplay"
                        type="danger"
                        plain
                        @click="clearSelectedLaws"
                      >
                        清除
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
                <!-- <div>
                  <el-form-item label="法规ID">
                    <el-input-number v-model="form.contractMessage.regulationId" :min="0" placeholder="请输入法规ID" />
                  </el-form-item>
                </div> -->
                <div class="col-span-2">
                  <el-form-item label="合同内容">
                    <el-input v-model="form.contractMessage.content" type="textarea" :rows="4" placeholder="请输入合同内容" />
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="违约责任">
                    <el-input v-model="form.contractMessage.defaultResponsibility" type="textarea" :rows="3" placeholder="请输入违约责任" />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 文档上传 -->
            <el-card shadow="hover" class="p-6">
              <div class="flex items-center justify-between">
                <h2 class="mb-6 text-lg text-gray-800 font-bold">
                  文档上传
                </h2>
                <el-button v-loading="btnLoading" type="success" @click="handleSmartRecognition">
                  合同智能导入
                </el-button>
              </div>

              <div class="space-y-6">
                <div>
                  <el-form-item label-position="top" label="合同文档" required>
                    <div class="w-full">
                      <DocumentUpload
                        v-model="form.contractMessage.documentUrl"
                        placeholder="导入合同文档，系统自动填充信息"
                        tip-text="支持 DOC、DOCX 格式，文件大小不超过 50MB"
                        :max-size="50"
                        @on-success="(fileKey, fileInfo) => {
                          form.contractMessage.documentUrl = fileKey
                          form.contractMessage.document = fileInfo.name
                        }"
                        @on-remove="() => {
                          form.contractMessage.documentUrl = ''
                          form.contractMessage.document = ''
                        }"
                      />
                    </div>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label-position="top" label="合同附件">
                    <UploadMbb
                      v-model="form.contractAttachments"
                      :auto-upload="true"
                      :max="10"
                      :size="20"
                      service-name="whiskerguardgeneralservice"
                      :use-file-path="true"
                      category-name="contract"
                      tip-text="支持 PDF、DOC、XLS、JPG、PNG 格式文件，大小不超过 20MB"
                      @upload-success="handleAttachmentUploadSuccess"
                    />
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 风险评估 -->
            <el-card v-if="false" shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                风险评估
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div class="col-span-2">
                  <el-form-item label="风险自评">
                    <div class="space-y-4">
                      <div>
                        <el-form-item label="风险类型">
                          <el-select v-model="form.riskType" placeholder="请选择风险类型">
                            <el-option label="法律风险" value="LAWS" />
                            <el-option label="财务风险" value="FINANCE" />
                            <el-option label="操作风险" value="OPERATION" />
                            <el-option label="声誉风险" value="REPUTATION" />
                            <el-option label="其他" value="OTHER" />
                          </el-select>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item label="风险等级">
                          <el-select v-model="form.riskSelf" placeholder="请选择风险等级">
                            <el-option label="一般" value="GENERAL" />
                            <el-option label="重要" value="IMPORTANT" />
                            <el-option label="关键" value="CRITICAL" />
                          </el-select>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item label="风险描述">
                          <el-input v-model="form.riskDesc" type="textarea" :rows="3" placeholder="请输入风险描述" />
                        </el-form-item>
                      </div>
                    </div>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="自动风险评估">
                    <div class="border border-gray-200 rounded-md bg-gray-50 p-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-sm text-gray-700 font-medium">
                          AI 风险评估
                        </h3>
                        <el-button v-auth="'contractReview/addEdit/startAssessment'" type="primary" class="rounded-button whitespace-nowrap">
                          <el-icon class="mr-2">
                            <VideoPlay />
                          </el-icon>开始评估
                        </el-button>
                      </div>
                      <div class="border border-gray-200 rounded-md bg-white p-4">
                        <div class="mb-4 flex items-center space-x-4">
                          <el-tag type="success">
                            低风险
                          </el-tag>
                          <div class="text-sm text-gray-600">
                            评估结果将在此处显示
                          </div>
                        </div>
                        <div class="space-y-3">
                          <div class="flex items-start">
                            <el-icon class="mr-2 mt-1 text-yellow-500">
                              <Warning />
                            </el-icon>
                            <p class="ml-2 text-sm text-gray-600">
                              主要风险点将在此列出
                            </p>
                          </div>
                          <div class="flex items-start">
                            <el-icon class="mr-2 mt-1 text-blue-500">
                              <InfoFilled />
                            </el-icon>
                            <p class="ml-2 text-sm text-gray-600">
                              AI 分析建议将在此显示
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-card>

            <!-- 审查规则 -->
            <el-card v-if="false" shadow="hover" class="p-6">
              <h2 class="mb-6 text-lg text-gray-800 font-bold">
                审查规则
              </h2>
              <div class="grid grid-cols-2 gap-6">
                <div>
                  <el-form-item label="审查流程选择">
                    <el-select v-model="form.reviewProcess" placeholder="请选择审查流程">
                      <el-option label="标准流程" value="standard" />
                      <el-option label="简化流程" value="simple" />
                      <el-option label="严格流程" value="strict" />
                      <el-option label="自定义" value="custom" />
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="审查重点配置">
                    <el-checkbox-group v-model="form.reviewFocus">
                      <el-checkbox label="合同主体" />
                      <el-checkbox label="合同金额" />
                      <el-checkbox label="履约条款" />
                      <el-checkbox label="违约责任" />
                      <el-checkbox label="法律适用" />
                    </el-checkbox-group>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="法规匹配范围">
                    <el-checkbox v-model="form.autoSelectLaw">
                      按合同类型自动选择相关法规范围
                    </el-checkbox>
                  </el-form-item>
                </div>
                <div class="col-span-2">
                  <el-form-item label="审批流程">
                    <div class="border border-gray-200 rounded-md bg-gray-50 p-4">
                      <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-sm text-gray-700 font-medium">
                          审批流程图
                        </h3>
                        <el-button v-auth="'contractReview/addEdit/addNode'" plain type="primary" class="rounded-button whitespace-nowrap">
                          <el-icon class="mr-1">
                            <Plus />
                          </el-icon>添加节点
                        </el-button>
                      </div>
                      <div class="flex items-center justify-center py-6 space-x-8">
                        <div class="flex flex-col items-center">
                          <div class="bg-primary h-12 w-12 flex items-center justify-center rounded-full text-white">
                            <el-icon>
                              <User />
                            </el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">发起人</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon><User /></el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">部门主管</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon><ScaleToOriginal /></el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">法务</span>
                        </div>
                        <div class="h-px flex-1 bg-gray-300" />
                        <div class="flex flex-col items-center">
                          <div
                            class="border-primary text-primary h-12 w-12 flex items-center justify-center border-2 rounded-full bg-blue-100"
                          >
                            <el-icon>
                              <Document />
                            </el-icon>
                          </div>
                          <span class="mt-2 text-xs text-gray-600">签署</span>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-card>
          </el-form>
        </div>

        <!-- 右侧辅助功能区 -->
        <div v-if="false" class="sticky-sidebar w-1/4 space-y-6">
          <!-- 审查指南 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              审查指南
            </h2>
            <div class="space-y-3">
              <div class="rounded-md bg-blue-50 p-3">
                <h3 class="text-primary mb-2 text-sm font-medium">
                  采购合同审查要点
                </h3>
                <ul class="text-xs text-gray-600 space-y-1">
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>确认供应商资质和信用状况</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>核对产品规格、数量和质量标准</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>审查付款条件和交付时间</span>
                  </li>
                  <li class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-green-500">
                      <SuccessFilled />
                    </el-icon>
                    <span>检查违约责任和争议解决条款</span>
                  </li>
                </ul>
              </div>
              <el-link v-auth="'contractReview/addEdit/viewGuide'" type="primary" :underline="false" class="inline-flex items-center">
                <el-icon class="mr-2">
                  <Notebook />
                </el-icon>查看详细指南
              </el-link>
            </div>
          </el-card>

          <!-- 审查模板 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              审查模板
            </h2>
            <div class="space-y-3">
              <el-button
                v-auth="'contractReview/addEdit/standardTemplate'"
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  标准采购合同模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于一般采购业务
                </div>
              </el-button>
              <el-button
                v-auth="'contractReview/addEdit/techTemplate'"
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  技术服务合同模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于技术服务类合同
                </div>
              </el-button>
              <el-button
                v-auth="'contractReview/addEdit/strictTemplate'"
                text
                class="hover:border-primary w-full border border-gray-200 rounded-md bg-white px-3 py-2 text-left transition-colors hover:bg-blue-50"
              >
                <div class="text-sm text-gray-800 font-medium">
                  严格审查模板
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  适用于高风险合同
                </div>
              </el-button>
            </div>
          </el-card>

          <!-- AI辅助 -->
          <el-card shadow="hover" class="p-6">
            <h2 class="mb-4 text-lg text-gray-800 font-bold">
              AI 辅助
            </h2>
            <div class="space-y-4">
              <el-button v-auth="'contractReview/addEdit/aiAutoFill'" type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon class="mr-2">
                  <MagicStick />
                </el-icon>AI 自动填写
              </el-button>
              <el-button v-auth="'contractReview/addEdit/aiRiskAnalysis'" plain type="primary" class="rounded-button w-full whitespace-nowrap">
                <el-icon class="mr-2">
                  <Warning />
                </el-icon>AI 风险预分析
              </el-button>
              <div class="border border-blue-100 rounded-md bg-blue-50 p-4">
                <h3 class="text-primary mb-2 text-sm font-medium">
                  AI 建议
                </h3>
                <div class="text-xs text-gray-600 space-y-2">
                  <div class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-blue-500">
                      <InfoFilled />
                    </el-icon>
                    <p class="ml-2">
                      建议补充合同履行地条款
                    </p>
                  </div>
                  <div class="flex items-start">
                    <el-icon class="mr-2 mt-1 text-blue-500">
                      <InfoFilled />
                    </el-icon>
                    <p class="ml-2">
                      付款条件建议增加验收条款
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </PageMain>

    <!-- 底部 -->
    <!-- <footer class="border-t border-gray-200 bg-white py-4">
      <div class="mx-auto max-w-7xl px-6 text-center text-sm text-gray-500">
        <p>© 2023 监控之翼合规管理系统. 保留所有权利.</p>
      </div>
    </footer> -->

    <!-- 法规弹窗 -->
    <LawsPop
      v-model="showLawsPop"
      :selected-values="selectedLaws.length > 0 && selectedLaws[0]?.id ? [selectedLaws[0].id] : []"
      :selected-names="selectedLaws.length > 0 && selectedLaws[0]?.title ? [selectedLaws[0].title] : []"
      :multiple="false"
      @confirm="onLawsConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";
</style>
