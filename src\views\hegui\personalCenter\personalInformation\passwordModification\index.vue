<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { CircleCheck, Lock } from '@element-plus/icons-vue'
import baseInfo from '@/api/personal/baseInfo'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const form = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const formRef = ref()
const submitting = ref(false)
const passwordStrength = ref(0)
const passwordStrengthClass = ref('bg-red-500')

const passwordHistory = ref([
  { time: '2023-05-15 14:30', action: '密码修改' },
  { time: '2023-03-10 09:15', action: '密码修改' },
  { time: '2022-12-01 16:45', action: '密码修改' },
])

function validateCurrentPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入当前密码'))
  }
  else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  }
  else {
    callback()
  }
}

function validateNewPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入新密码'))
  }
  else if (value.length < 8) {
    callback(new Error('密码长度不能少于8位'))
  }
  else if (!/[A-Z]/.test(value)) {
    callback(new Error('必须包含至少一个大写字母'))
  }
  else if (!/[a-z]/.test(value)) {
    callback(new Error('必须包含至少一个小写字母'))
  }
  else if (!/[0-9]/.test(value)) {
    callback(new Error('必须包含至少一个数字'))
  }
  else if (!/[^A-Za-z0-9]/.test(value)) {
    callback(new Error('必须包含至少一个特殊字符'))
  }
  else {
    callback()
  }
}

function validateConfirmPassword(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请再次输入新密码'))
  }
  else if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  }
  else {
    callback()
  }
}

const rules = reactive({
  currentPassword: [{ validator: validateCurrentPassword, trigger: 'blur' }],
  newPassword: [{ validator: validateNewPassword, trigger: 'blur' }],
  confirmPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }],
})

const formValid = computed(() => {
  return form.currentPassword
    && form.newPassword
    && form.confirmPassword
    && form.newPassword === form.confirmPassword
})

function checkPasswordStrength() {
  const password = form.newPassword
  if (!password) {
    passwordStrength.value = 0
    return
  }

  let strength = 0

  // 长度评分
  if (password.length >= 8) {
    strength += 20
  }
  if (password.length >= 12) {
    strength += 10
  }

  // 字符种类评分
  if (/[A-Z]/.test(password)) {
    strength += 15
  }
  if (/[a-z]/.test(password)) {
    strength += 15
  }
  if (/[0-9]/.test(password)) {
    strength += 15
  }
  if (/[^A-Za-z0-9]/.test(password)) {
    strength += 15
  }

  // 特殊规则评分
  if (password.length >= 16) {
    strength += 10
  }

  passwordStrength.value = Math.min(strength, 100)

  if (passwordStrength.value <= 30) {
    passwordStrengthClass.value = 'bg-red-500'
  }
  else if (passwordStrength.value <= 70) {
    passwordStrengthClass.value = 'bg-yellow-500'
  }
  else {
    passwordStrengthClass.value = 'bg-green-500'
  }
}

async function submitForm() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        const userId = userStore.userId
        await baseInfo.updatePassword(userId, {
          currentPassword: form.currentPassword,
          newPassword: form.newPassword,
          confirmPassword: form.confirmPassword,
        })
        ElMessage.success('密码修改成功')
        passwordHistory.value.unshift({
          time: new Date().toLocaleString(),
          action: '密码修改',
        })
        formRef.value.resetFields()
        passwordStrength.value = 0
      }
      catch (error: any) {
        ElMessage.error(error.message || '密码修改失败，请重试')
      }
      finally {
        submitting.value = false
      }
    }
  })
}

function resetForm() {
  formRef.value.resetFields()
  passwordStrength.value = 0
}
</script>

<template>
  <div class="absolute-container">
    <page-header title="" content="">
      <template #content>
        <div class="aic jcsb flex">
          <div class="mt-4 flex items-center justify-between">
            <h1 class="text-xl c-[#000000] font-bold">
              密码修改
            </h1>
            <!-- <el-tag type="warning" class="ml-4">处理中</el-tag> -->
          </div>
          <div class="flex space-x-3">
            <!-- <el-button type="primary" class="!rounded-button whitespace-nowrap">
              <i class="el-icon-check mr-1" />编辑资料
            </el-button> -->
          </div>
        </div>
      </template>
    </page-header>
    <PageMain style="background-color: transparent; overflow: hidden;">
      <div>
        <el-row :gutter="20" class="flex justify-center">
          <el-col :span="8">
            <el-card shadow="hover" class="">
              <el-form ref="formRef" :model="form" :rules="rules" label-position="top" @submit.prevent="submitForm">
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input
                    v-model="form.currentPassword" type="password" placeholder="请输入当前密码" show-password
                    size="large"
                  >
                    <template #prefix>
                      <el-icon>
                        <Lock />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="form.newPassword" type="password" placeholder="请输入新密码" show-password size="large"
                    @input="checkPasswordStrength"
                  >
                    <template #prefix>
                      <el-icon>
                        <Lock />
                      </el-icon>
                    </template>
                  </el-input>
                  <div class="mt-2">
                    <div class="h-1.5 overflow-hidden rounded-full bg-gray-200">
                      <div
                        class="h-full transition-all duration-300" :class="passwordStrengthClass"
                        :style="{ width: `${passwordStrength}%` }"
                      />
                    </div>
                    <div class="mt-1 text-xs text-gray-500">
                      <span v-if="passwordStrength === 0">密码强度</span>
                      <span v-else-if="passwordStrength <= 30">弱</span>
                      <span v-else-if="passwordStrength <= 70">中</span>
                      <span v-else>强</span>
                    </div>
                  </div>
                  <div class="mt-1 text-xs text-gray-500">
                    <p>密码长度不少于8位，包含大小写字母、数字和特殊字符</p>
                  </div>
                </el-form-item>

                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input
                    v-model="form.confirmPassword" type="password" placeholder="请再次输入新密码" show-password
                    size="large"
                  >
                    <template #prefix>
                      <el-icon>
                        <Lock />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <div class="mt-8 flex justify-end gap-3">
                  <el-button @click="resetForm">
                    取消
                  </el-button>
                  <el-button type="primary" native-type="submit" :disabled="!formValid" :loading="submitting">
                    确认修改
                  </el-button>
                </div>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover" class="">
              <ul>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-0.5 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>密码长度不少于8位</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-0.5 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>包含大写字母、小写字母、数字和特殊字符</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-0.5 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>不能与账号相同或包含账号</span>
                </li>
                <li class="flex items-start">
                  <el-icon class="mr-2 mt-0.5 text-green-500">
                    <CircleCheck />
                  </el-icon>
                  <span>不能使用最近3次使用过的密码</span>
                </li>
              </ul>

              <!-- <h2 class="mb-4 mt-8 text-lg font-bold">
                修改记录
              </h2>
              <el-timeline>
                <el-timeline-item v-for="(record, index) in passwordHistory" :key="index" :timestamp="record.time">
                  {{ record.action }}
                </el-timeline-item>
              </el-timeline> -->
            </el-card>
          </el-col>
        </el-row>
      </div>
    </PageMain>
  </div>
</template>

<style lang="scss" scoped>
  @use "@/styles/toolsCss";

  .cssTable {
    :deep(.el-table__header-wrapper .el-table__cell) {
      height: 55px;
      background: #f5f7fa;
    }
  }
</style>

<style scoped>
  .el-card {
    border: none;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    padding-bottom: 0;
    font-weight: 500;
    color: #606266;
  }

  .el-timeline {
    margin-left: 8px;
  }

  .el-timeline-item__timestamp {
    font-size: 12px;
    color: #909399;
  }

  .el-dropdown-menu__item {
    min-width: 120px;
  }
</style>
