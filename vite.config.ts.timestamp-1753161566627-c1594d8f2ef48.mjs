// vite.config.ts
import fs2 from "node:fs";
import path3 from "node:path";
import process2 from "node:process";
import { defineConfig, loadEnv } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.14_sass@1.87.0_terser@5.39.0/node_modules/vite/dist/node/index.js";
import dayjs2 from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";

// package.json
var package_default = {
  type: "module",
  version: "4.7.0",
  engines: {
    node: "^18.0.0 || ^20.0.0"
  },
  scripts: {
    dev: "vite",
    build: "vite build",
    "build:test": "vite build --mode test",
    serve: "http-server ./dist -o",
    "serve:test": "http-server ./dist-test -o",
    svgo: "svgo -f src/assets/icons",
    new: "plop",
    "generate:icons": "esno ./scripts/generate.icons.ts",
    lint: "npm-run-all -s lint:tsc lint:eslint lint:stylelint",
    "lint:tsc": "vue-tsc",
    "lint:eslint": "eslint . --cache --fix",
    "lint:stylelint": 'stylelint "src/**/*.{css,scss,vue}" --cache --fix',
    postinstall: "simple-git-hooks",
    preinstall: "npx only-allow pnpm",
    commit: "git cz",
    release: "bumpp"
  },
  dependencies: {
    "@antv/g2plot": "^2.4.31",
    "@headlessui/vue": "^1.7.19",
    "@imengyu/vue3-context-menu": "^1.3.9",
    "@qiniu/wechat-miniprogram-upload": "^1.0.3",
    "@tinymce/tinymce-vue": "^5.1.1",
    "@vueuse/core": "^10.9.0",
    "@vueuse/integrations": "^10.9.0",
    "@wangeditor/editor": "^5.1.23",
    axios: "^1.6.8",
    "bignumber.js": "^9.1.2",
    dayjs: "^1.11.10",
    "decimal.js": "^10.4.3",
    defu: "^6.1.4",
    echarts: "^5.5.0",
    "element-plus": "^2.7.0",
    eruda: "^3.0.1",
    "floating-vue": "5.2.2",
    "highlight.js": "^11.11.1",
    "hotkeys-js": "^3.13.7",
    html2canvas: "^1.4.1",
    "js-pinyin": "^0.2.5",
    "lodash-es": "^4.17.21",
    marked: "^12.0.2",
    "medium-zoom": "^1.1.0",
    mitt: "^3.0.1",
    mockjs: "^1.1.0",
    moment: "^2.30.1",
    nprogress: "^0.2.0",
    overlayscrollbars: "^2.6.1",
    "overlayscrollbars-vue": "^0.5.8",
    "path-browserify": "^1.0.1",
    "path-to-regexp": "^6.2.1",
    pinia: "^2.1.7",
    "pinyin-pro": "^3.19.6",
    pnpm: "^10.8.1",
    "qiniu-js": "4.0.0-beta.4",
    qs: "^6.12.0",
    scule: "^1.3.0",
    sortablejs: "^1.15.2",
    spinkit: "^2.0.1",
    "timeago.js": "^4.0.2",
    tinymce: "^7.0.1",
    "v-wave": "^2.0.0",
    vconsole: "^3.15.1",
    vue: "^3.4.21",
    "vue-clipboard3": "^2.0.0",
    "vue-i18n": "^9.10.2",
    "vue-m-message": "^4.0.2",
    "vue-router": "^4.3.0",
    "vue-ueditor-wrap": "^3.0.8",
    vuedraggable: "^4.1.0"
  },
  devDependencies: {
    "@antfu/eslint-config": "2.11.6",
    "@iconify/json": "^2.2.196",
    "@iconify/vue": "^4.1.1",
    "@intlify/unplugin-vue-i18n": "^4.0.0",
    "@stylistic/stylelint-config": "^1.0.1",
    "@types/lodash-es": "^4.17.12",
    "@types/mockjs": "^1.0.10",
    "@types/nprogress": "^0.2.3",
    "@types/path-browserify": "^1.0.2",
    "@types/qs": "^6.9.14",
    "@types/sortablejs": "^1.15.8",
    "@unocss/eslint-plugin": "^0.58.8",
    "@vitejs/plugin-legacy": "^5.3.2",
    "@vitejs/plugin-vue": "^5.0.4",
    "@vitejs/plugin-vue-jsx": "^3.1.0",
    archiver: "^7.0.1",
    autoprefixer: "^10.4.19",
    boxen: "^7.1.1",
    bumpp: "^9.4.0",
    "cz-git": "^1.9.1",
    eslint: "^8.57.0",
    esno: "^4.7.0",
    "fs-extra": "^11.2.0",
    "http-server": "^14.1.1",
    inquirer: "^9.2.17",
    "lint-staged": "^15.2.2",
    "npm-run-all": "^4.1.5",
    picocolors: "^1.0.0",
    plop: "^4.0.1",
    sass: "^1.72.0",
    "simple-git-hooks": "^2.11.1",
    stylelint: "^16.3.1",
    "stylelint-config-recess-order": "^5.0.0",
    "stylelint-config-standard-scss": "^13.0.0",
    "stylelint-config-standard-vue": "^1.0.0",
    "stylelint-scss": "^6.2.1",
    svgo: "^3.2.0",
    terser: "^5.30.0",
    typescript: "^5.4.3",
    unocss: "^0.58.8",
    "unplugin-auto-import": "^0.17.5",
    "unplugin-turbo-console": "^1.5.1",
    "unplugin-vue-components": "^0.26.0",
    vite: "^5.2.7",
    "vite-plugin-banner": "^0.7.1",
    "vite-plugin-compression2": "^1.0.0",
    "vite-plugin-fake-server": "^2.1.1",
    "vite-plugin-pages": "^0.32.1",
    "vite-plugin-svg-icons": "^2.0.1",
    "vite-plugin-vue-devtools": "^7.0.25",
    "vite-plugin-vue-meta-layouts": "^0.4.2",
    "vue-tsc": "^2.0.7"
  },
  "simple-git-hooks": {
    "pre-commit": "pnpm lint-staged",
    preserveUnused: true
  },
  config: {
    commitizen: {
      path: "node_modules/cz-git"
    }
  }
};

// vite/plugins/index.ts
import vue from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-vue@5.2.3_vi_5960c829049eace3eb509c9d22a86fca/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-vue-jsx@3.1._fd1fc3bd4b6c69f570502946e18d5e0c/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import vueLegacy from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@vitejs+plugin-legacy@5.4.3_aa2f7e9daefa38009b7721b5153d982f/node_modules/@vitejs/plugin-legacy/dist/index.mjs";

// vite/plugins/app-info.ts
import boxen from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/boxen@7.1.1/node_modules/boxen/index.js";
import picocolors from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/picocolors@1.1.1/node_modules/picocolors/picocolors.js";
function appInfo() {
  return {
    name: "appInfo",
    apply: "serve",
    async buildStart() {
      const { bold, green, magenta, bgGreen, underline } = picocolors;
      console.log(
        boxen(
          `${bold(green(`\u7531 ${bgGreen("Fantastic-admin")} \u9A71\u52A8`))}

${underline("https://fantastic-admin.gitee.io")}

\u5F53\u524D\u4F7F\u7528\uFF1A${magenta("\u4E13\u4E1A\u7248")}`,
          {
            padding: 1,
            margin: 1,
            borderStyle: "double",
            textAlignment: "center"
          }
        )
      );
    }
  };
}

// vite/plugins/devtools.ts
import VueDevTools from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-vue-devtools@7._f3a6fedceb8655d368e323ca6e0ad2d8/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
function createDevtools(env) {
  const { VITE_OPEN_DEVTOOLS } = env;
  return VITE_OPEN_DEVTOOLS === "true" && VueDevTools();
}

// vite/plugins/auto-import.ts
import autoImport from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unplugin-auto-import@0.17.8_548a2333045b7a5305e1651499426e3d/node_modules/unplugin-auto-import/dist/vite.js";
function createAutoImport() {
  return autoImport({
    imports: [
      "vue",
      "vue-router",
      "pinia"
    ],
    dts: "./src/types/auto-imports.d.ts",
    dirs: [
      "./src/utils/composables/**"
    ]
  });
}

// vite/plugins/components.ts
import components from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unplugin-vue-components@0.2_9780cee84be776be6aa399e4bcdeefca/node_modules/unplugin-vue-components/dist/vite.js";
function createComponents() {
  return components({
    dirs: [
      "src/components",
      "src/layouts/ui-kit"
    ],
    include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/],
    dts: "./src/types/components.d.ts"
  });
}

// vite/plugins/unocss.ts
import Unocss from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/unocss@0.58.9_postcss@5.2.1_026d44bff71cf55c59c571dfee0862e8/node_modules/unocss/dist/vite.mjs";
function createUnocss() {
  return Unocss();
}

// vite/plugins/svg-icon.ts
import path from "node:path";
import process from "node:process";
import { createSvgIconsPlugin } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_5721baa32c4bdf4404ff5a34c4a76c58/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons/")],
    symbolId: "icon-[dir]-[name]",
    svgoOptions: isBuild
  });
}

// vite/plugins/i18n.ts
import path2 from "node:path";
import vueI18n from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/@intlify+unplugin-vue-i18n@_5be3ae1591f407448b2c562a32ad7eea/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
var __vite_injected_original_dirname = "D:\\newmaobobo\\mbb-system-admin\\whiskerguard-ui-system-admin\\vite\\plugins";
function createI18n() {
  return vueI18n({
    include: path2.resolve(__vite_injected_original_dirname, "../../src/locales/lang/**")
  });
}

// vite/plugins/mock.ts
import { vitePluginFakeServer } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-fake-server@2.2.0/node_modules/vite-plugin-fake-server/dist/index.mjs";
function createMock(env, isBuild) {
  const { VITE_BUILD_MOCK } = env;
  return vitePluginFakeServer({
    logger: !isBuild,
    include: "src/mock",
    infixName: false,
    enableProd: isBuild && VITE_BUILD_MOCK === "true"
  });
}

// vite/plugins/layouts.ts
import Layouts from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-vue-meta-layout_744cef15d0cbd2870a95ddf5a1473bdb/node_modules/vite-plugin-vue-meta-layouts/dist/index.mjs";
function createLayouts() {
  return Layouts({
    defaultLayout: "index"
  });
}

// vite/plugins/pages.ts
import Pages from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-pages@0.32.5_@v_0170eb3df285a8dd9032f83c398d6032/node_modules/vite-plugin-pages/dist/index.js";
function createPages() {
  return Pages({
    dirs: "src/views",
    exclude: [
      "**/components/**/*.vue"
    ]
  });
}

// vite/plugins/compression.ts
import { compression } from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-compression2@1._23ab23b042bc4efc54d7303fd518934d/node_modules/vite-plugin-compression2/dist/index.mjs";
function createCompression(env, isBuild) {
  const plugin = [];
  if (isBuild) {
    const { VITE_BUILD_COMPRESS } = env;
    const compressList = VITE_BUILD_COMPRESS.split(",");
    if (compressList.includes("gzip")) {
      plugin.push(
        compression()
      );
    }
    if (compressList.includes("brotli")) {
      plugin.push(
        compression({
          exclude: [/\.(br)$/, /\.(gz)$/],
          algorithm: "brotliCompress"
        })
      );
    }
  }
  return plugin;
}

// vite/plugins/archiver.ts
import fs from "node:fs";
import dayjs from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js";
import archiver from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/archiver@7.0.1/node_modules/archiver/index.js";
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function createArchiver(env) {
  const { VITE_BUILD_ARCHIVE } = env;
  let outDir;
  return {
    name: "vite-plugin-archiver",
    apply: "build",
    configResolved(resolvedConfig) {
      outDir = resolvedConfig.build.outDir;
    },
    async closeBundle() {
      if (["zip", "tar"].includes(VITE_BUILD_ARCHIVE)) {
        await sleep(1e3);
        const archive = archiver(VITE_BUILD_ARCHIVE, {
          ...VITE_BUILD_ARCHIVE === "zip" && { zlib: { level: 9 } },
          ...VITE_BUILD_ARCHIVE === "tar" && { gzip: true, gzipOptions: { level: 9 } }
        });
        const output = fs.createWriteStream(`${outDir}.${dayjs().format("YYYY-MM-DD-HH-mm-ss")}.${VITE_BUILD_ARCHIVE === "zip" ? "zip" : "tar.gz"}`);
        archive.pipe(output);
        archive.directory(outDir, false);
        archive.finalize();
      }
    }
  };
}

// vite/plugins/banner.ts
import banner from "file:///D:/newmaobobo/mbb-system-admin/whiskerguard-ui-system-admin/node_modules/.pnpm/vite-plugin-banner@0.7.1/node_modules/vite-plugin-banner/dist/index.mjs";
function createBanner() {
  return banner(`
/**
 * \u7531 Fantastic-admin \u63D0\u4F9B\u6280\u672F\u652F\u6301
 * Powered by Fantastic-admin
 * Gitee  https://fantastic-admin.gitee.io
 * Github https://fantastic-admin.github.io
 */
`);
}

// vite/plugins/index.ts
function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    appInfo(),
    vue(),
    vueJsx(),
    vueLegacy({
      renderLegacyChunks: false,
      modernPolyfills: [
        "es.array.at",
        "es.array.find-last"
      ]
    })
  ];
  vitePlugins.push(createDevtools(viteEnv));
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createComponents());
  vitePlugins.push(createUnocss());
  vitePlugins.push(createSvgIcon(isBuild));
  vitePlugins.push(createI18n());
  vitePlugins.push(createMock(viteEnv, isBuild));
  vitePlugins.push(createLayouts());
  vitePlugins.push(createPages());
  vitePlugins.push(...createCompression(viteEnv, isBuild));
  vitePlugins.push(createArchiver(viteEnv));
  vitePlugins.push(createBanner());
  return vitePlugins;
}

// vite.config.ts
var __vite_injected_original_dirname2 = "D:\\newmaobobo\\mbb-system-admin\\whiskerguard-ui-system-admin";
var vite_config_default = async ({ mode, command }) => {
  const env = loadEnv(mode, process2.cwd());
  const scssResources = [];
  fs2.readdirSync("src/assets/styles/resources").forEach((dirname) => {
    if (fs2.statSync(`src/assets/styles/resources/${dirname}`).isFile()) {
      scssResources.push(`@use "src/assets/styles/resources/${dirname}" as *;`);
    }
  });
  return defineConfig({
    base: "./",
    // 开发服务器选项 https://cn.vitejs.dev/config/#server-options
    server: {
      open: true,
      port: 9e3,
      proxy: {
        "/proxy": {
          target: env.VITE_APP_API_BASEURL,
          // changeOrigin: command === 'serve' && env.VITE_OPEN_PROXY === 'true',
          changeOrigin: true,
          // secure: false,
          rewrite: (path4) => path4.replace(/\/proxy/, "")
        }
      }
    },
    // 构建选项 https://cn.vitejs.dev/config/#server-fsserve-root
    build: {
      outDir: mode === "production" ? "dist" : `dist-${mode}`,
      sourcemap: env.VITE_BUILD_SOURCEMAP === "true"
    },
    define: {
      __SYSTEM_INFO__: JSON.stringify({
        pkg: {
          version: package_default.version,
          dependencies: package_default.dependencies,
          devDependencies: package_default.devDependencies
        },
        lastBuildTime: dayjs2().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      alias: {
        "@": path3.resolve(__vite_injected_original_dirname2, "src"),
        "#": path3.resolve(__vite_injected_original_dirname2, "src/types")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: scssResources.join(""),
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
